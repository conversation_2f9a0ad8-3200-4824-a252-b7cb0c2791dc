// Common types for the 3D Portfolio

export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  technologies: string[];
  imageUrl?: string;
  demoUrl?: string;
  githubUrl?: string;
  featured: boolean;
  category: ProjectCategory;
  completedAt: Date;
}

export type ProjectCategory = 
  | 'web-development'
  | 'mobile-development'
  | 'backend'
  | 'fullstack'
  | 'ai-ml'
  | 'devops'
  | 'other';

export interface Skill {
  id: string;
  name: string;
  category: SkillCategory;
  level: SkillLevel;
  icon?: string;
  description?: string;
  yearsOfExperience: number;
}

export type SkillCategory = 
  | 'frontend'
  | 'backend'
  | 'database'
  | 'devops'
  | 'mobile'
  | 'ai-ml'
  | 'tools'
  | 'soft-skills';

export type SkillLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert';

export interface Experience {
  id: string;
  company: string;
  position: string;
  startDate: Date;
  endDate?: Date;
  description: string;
  achievements: string[];
  technologies: string[];
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship';
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: Date;
  endDate?: Date;
  gpa?: number;
  achievements?: string[];
  location: string;
}

export interface ContactInfo {
  email: string;
  phone?: string;
  location: string;
  linkedin?: string;
  github?: string;
  twitter?: string;
  website?: string;
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  publishedAt: Date;
  updatedAt?: Date;
  tags: string[];
  readingTime: number;
  featured: boolean;
  slug: string;
}

// 3D Related Types
export interface Vector3D {
  x: number;
  y: number;
  z: number;
}

export interface Rotation3D {
  x: number;
  y: number;
  z: number;
}

export interface Transform3D {
  position: Vector3D;
  rotation: Rotation3D;
  scale: Vector3D;
}

export interface MicrochipModel {
  id: string;
  name: string;
  modelUrl: string;
  textureUrl?: string;
  scale: number;
  animations?: string[];
}

// Theme Types
export type Theme = 'light' | 'dark';

export interface ThemeConfig {
  theme: Theme;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  enableGlowEffects: boolean;
  enableAnimations: boolean;
}

// Animation Types
export interface AnimationConfig {
  duration: number;
  delay?: number;
  easing?: string;
  repeat?: boolean | number;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface InteractiveElementProps extends BaseComponentProps {
  onClick?: () => void;
  onHover?: () => void;
  onFocus?: () => void;
  disabled?: boolean;
  loading?: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

// Form Types
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ContactFormErrors {
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
}

// Navigation Types
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon?: string;
  external?: boolean;
}

// Performance Types
export interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  renderTime: number;
  triangleCount: number;
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/warn-once.mjs"], "sourcesContent": ["const warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, element) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(message);\n    if (element)\n        console.warn(element);\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,SAAS,IAAI;AACnB,SAAS,UAAU,OAAO;IACtB,OAAO,OAAO,GAAG,CAAC;AACtB;AACA,SAAS,SAAS,SAAS,EAAE,OAAO,EAAE,OAAO;IACzC,IAAI,aAAa,OAAO,GAAG,CAAC,UACxB;IACJ,QAAQ,IAAI,CAAC;IACb,IAAI,SACA,QAAQ,IAAI,CAAC;IACjB,OAAO,GAAG,CAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/noop.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,MAAM,OAAO,CAAC,MAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/global-config.mjs"], "sourcesContent": ["const MotionGlobalConfig = {};\n\nexport { MotionGlobalConfig };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/array.mjs"], "sourcesContent": ["function addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\nexport { addUniqueItem, moveItem, removeItem };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,cAAc,GAAG,EAAE,IAAI;IAC5B,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,GACvB,IAAI,IAAI,CAAC;AACjB;AACA,SAAS,WAAW,GAAG,EAAE,IAAI;IACzB,MAAM,QAAQ,IAAI,OAAO,CAAC;IAC1B,IAAI,QAAQ,CAAC,GACT,IAAI,MAAM,CAAC,OAAO;AAC1B;AACA,0BAA0B;AAC1B,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE,SAAS,EAAE,OAAO;IAC1C,MAAM,aAAa,YAAY,IAAI,IAAI,MAAM,GAAG,YAAY;IAC5D,IAAI,cAAc,KAAK,aAAa,IAAI,MAAM,EAAE;QAC5C,MAAM,WAAW,UAAU,IAAI,IAAI,MAAM,GAAG,UAAU;QACtD,MAAM,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,WAAW;QACrC,IAAI,MAAM,CAAC,UAAU,GAAG;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/subscription-manager.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from './array.mjs';\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        addUniqueItem(this.subscriptions, handler);\n        return () => removeItem(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\nexport { SubscriptionManager };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,aAAa,GAAG,EAAE;IAC3B;IACA,IAAI,OAAO,EAAE;QACT,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;QAClC,OAAO,IAAM,CAAA,GAAA,uJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;IAChD;IACA,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACZ,MAAM,mBAAmB,IAAI,CAAC,aAAa,CAAC,MAAM;QAClD,IAAI,CAAC,kBACD;QACJ,IAAI,qBAAqB,GAAG;YACxB;;aAEC,GACD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG;QAChC,OACK;YACD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;gBACvC;;;iBAGC,GACD,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBACrC,WAAW,QAAQ,GAAG,GAAG;YAC7B;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM;IACpC;IACA,QAAQ;QACJ,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/velocity-per-second.mjs"], "sourcesContent": ["/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;AACA,SAAS,kBAAkB,QAAQ,EAAE,aAAa;IAC9C,OAAO,gBAAgB,WAAW,CAAC,OAAO,aAAa,IAAI;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/errors.mjs"], "sourcesContent": ["let warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "names": [], "mappings": ";;;;AAAA,IAAI,UAAU,KAAQ;AACtB,IAAI,YAAY,KAAQ;AACxB,wCAA2C;IACvC,UAAU,CAAC,OAAO;QACd,IAAI,CAAC,SAAS,OAAO,YAAY,aAAa;YAC1C,QAAQ,IAAI,CAAC;QACjB;IACJ;IACA,YAAY,CAAC,OAAO;QAChB,IAAI,CAAC,OAAO;YACR,MAAM,IAAI,MAAM;QACpB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/pipe.mjs"], "sourcesContent": ["/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACD,MAAM,mBAAmB,CAAC,GAAG,IAAM,CAAC,IAAM,EAAE,EAAE;AAC9C,MAAM,OAAO,CAAC,GAAG,eAAiB,aAAa,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/clamp.mjs"], "sourcesContent": ["const clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\nexport { clamp };\n"], "names": [], "mappings": ";;;AAAA,MAAM,QAAQ,CAAC,KAAK,KAAK;IACrB,IAAI,IAAI,KACJ,OAAO;IACX,IAAI,IAAI,KACJ,OAAO;IACX,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/time-conversion.mjs"], "sourcesContent": ["/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GACD,sBAAsB;;;;AACtB,MAAM,wBAAwB,CAAC,UAAY,UAAU;AACrD,sBAAsB,GACtB,MAAM,wBAAwB,CAAC,eAAiB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/cubic-bezier.mjs"], "sourcesContent": ["import { noop } from '../noop.mjs';\n\n/*\n  Bezier function generator\n  This has been modified from Gaë<PERSON>eau's BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticeably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) *\n    t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return noop;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;;;;;;;;;;;;;AAgBA,GACA,iEAAiE;AACjE,MAAM,aAAa,CAAC,GAAG,IAAI,KAAO,CAAC,CAAC,CAAC,MAAM,MAAM,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,IACvG;AACJ,MAAM,uBAAuB;AAC7B,MAAM,2BAA2B;AACjC,SAAS,gBAAgB,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG;IACxD,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI;IACR,GAAG;QACC,WAAW,aAAa,CAAC,aAAa,UAAU,IAAI;QACpD,WAAW,WAAW,UAAU,KAAK,OAAO;QAC5C,IAAI,WAAW,KAAK;YAChB,aAAa;QACjB,OACK;YACD,aAAa;QACjB;IACJ,QAAS,KAAK,GAAG,CAAC,YAAY,wBAC1B,EAAE,IAAI,yBAA0B;IACpC,OAAO;AACX;AACA,SAAS,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,qDAAqD;IACrD,IAAI,QAAQ,OAAO,QAAQ,KACvB,OAAO,sJAAA,CAAA,OAAI;IACf,MAAM,WAAW,CAAC,KAAO,gBAAgB,IAAI,GAAG,GAAG,KAAK;IACxD,wDAAwD;IACxD,OAAO,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,IAAI,KAAK;AACxE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/ease.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\n\nconst easeIn = /*@__PURE__*/ cubicBezier(0.42, 0, 1, 1);\nconst easeOut = /*@__PURE__*/ cubicBezier(0, 0, 0.58, 1);\nconst easeInOut = /*@__PURE__*/ cubicBezier(0.42, 0, 0.58, 1);\n\nexport { easeIn, easeInOut, easeOut };\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,SAAS,WAAW,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,GAAG,GAAG;AACrD,MAAM,UAAU,WAAW,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,GAAG,GAAG,MAAM;AACtD,MAAM,YAAY,WAAW,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,GAAG,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/utils/is-easing-array.mjs"], "sourcesContent": ["const isEasingArray = (ease) => {\n    return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\n\nexport { isEasingArray };\n"], "names": [], "mappings": ";;;AAAA,MAAM,gBAAgB,CAAC;IACnB,OAAO,MAAM,OAAO,CAAC,SAAS,OAAO,IAAI,CAAC,EAAE,KAAK;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/modifiers/mirror.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = (easing) => (p) => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\n\nexport { mirrorEasing };\n"], "names": [], "mappings": "AAAA,oFAAoF;AACpF,iEAAiE;;;;AACjE,MAAM,eAAe,CAAC,SAAW,CAAC,IAAM,KAAK,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/modifiers/reverse.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = (easing) => (p) => 1 - easing(1 - p);\n\nexport { reverseEasing };\n"], "names": [], "mappings": "AAAA,iFAAiF;AACjF,6BAA6B;;;;AAC7B,MAAM,gBAAgB,CAAC,SAAW,CAAC,IAAM,IAAI,OAAO,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/back.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst backOut = /*@__PURE__*/ cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = /*@__PURE__*/ reverseEasing(backOut);\nconst backInOut = /*@__PURE__*/ mirrorEasing(backIn);\n\nexport { backIn, backInOut, backOut };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,WAAW,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,MAAM,MAAM;AAC5D,MAAM,SAAS,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;AAC3C,MAAM,YAAY,WAAW,GAAG,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/anticipate.mjs"], "sourcesContent": ["import { backIn } from './back.mjs';\n\nconst anticipate = (p) => (p *= 2) < 1 ? 0.5 * backIn(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n\nexport { anticipate };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD,EAAE,KAAK,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/circ.mjs"], "sourcesContent": ["import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst circIn = (p) => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circIn);\n\nexport { circIn, circInOut, circOut };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,MAAM,SAAS,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC;AAC7C,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;AAC9B,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/utils/is-bezier-definition.mjs"], "sourcesContent": ["const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC,SAAW,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,CAAC,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/easing/utils/map.mjs"], "sourcesContent": ["import { invariant } from '../../errors.mjs';\nimport { noop } from '../../noop.mjs';\nimport { anticipate } from '../anticipate.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { isBezierDefinition } from './is-bezier-definition.mjs';\n\nconst easingLookup = {\n    linear: noop,\n    easeIn,\n    easeInOut,\n    easeOut,\n    circIn,\n    circInOut,\n    circOut,\n    backIn,\n    backInOut,\n    backOut,\n    anticipate,\n};\nconst isValidEasing = (easing) => {\n    return typeof easing === \"string\";\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (isBezierDefinition(definition)) {\n        // If cubic bezier definition, create bezier curve\n        invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`);\n        const [x1, y1, x2, y2] = definition;\n        return cubicBezier(x1, y1, x2, y2);\n    }\n    else if (isValidEasing(definition)) {\n        // Else lookup from table\n        invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`);\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\nexport { easingDefinitionToFunction };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,eAAe;IACjB,QAAQ,sJAAA,CAAA,OAAI;IACZ,QAAA,gKAAA,CAAA,SAAM;IACN,WAAA,gKAAA,CAAA,YAAS;IACT,SAAA,gKAAA,CAAA,UAAO;IACP,QAAA,gKAAA,CAAA,SAAM;IACN,WAAA,gKAAA,CAAA,YAAS;IACT,SAAA,gKAAA,CAAA,UAAO;IACP,QAAA,gKAAA,CAAA,SAAM;IACN,WAAA,gKAAA,CAAA,YAAS;IACT,SAAA,gKAAA,CAAA,UAAO;IACP,YAAA,sKAAA,CAAA,aAAU;AACd;AACA,MAAM,gBAAgB,CAAC;IACnB,OAAO,OAAO,WAAW;AAC7B;AACA,MAAM,6BAA6B,CAAC;IAChC,IAAI,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa;QAChC,kDAAkD;QAClD,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,WAAW,MAAM,KAAK,GAAG,CAAC,uDAAuD,CAAC;QAC5F,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG;QACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,IAAI,IAAI,IAAI;IACnC,OACK,IAAI,cAAc,aAAa;QAChC,yBAAyB;QACzB,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAC,WAAW,KAAK,WAAW,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;QACvF,OAAO,YAAY,CAAC,WAAW;IACnC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/progress.mjs"], "sourcesContent": ["/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA,GACA,sBAAsB;;;AACtB,MAAM,WAAW,CAAC,MAAM,IAAI;IACxB,MAAM,mBAAmB,KAAK;IAC9B,OAAO,qBAAqB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/memo.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\nexport { memo };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,SAAS,KAAK,QAAQ;IAClB,IAAI;IACJ,OAAO;QACH,IAAI,WAAW,WACX,SAAS;QACb,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/is-object.mjs"], "sourcesContent": ["function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/is-numerical-string.mjs"], "sourcesContent": ["/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)$/u.test(v);\n\nexport { isNumericalString };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,oBAAoB,CAAC,IAAM,+BAA+B,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/motion-utils/dist/es/is-zero-value-string.mjs"], "sourcesContent": ["/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/u.test(v);\n\nexport { isZeroValueString };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,oBAAoB,CAAC,IAAM,cAAc,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B,gBACE;YACE,KAAK,KAAK,GAAG;YACb,KAAK,WAAW,GAAG;YACnB,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;QAC3D,GACA;YAAC;YAAW;YAAO;SAAY;QAEjC,UACE;YACE,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;YACzD,OAAO,UAAU;gBACf,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;QACF,GACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,gJACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,gJACF,uHACA,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,uBAAuB,KAAK,oBAAoB,EAChD,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,gBAAgB,MAAM,aAAa;IACrC,QAAQ,gCAAgC,GAAG,SACzC,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACR,OAAO;QAEP,IAAI,UAAU,OAAO;QACrB,IAAI,SAAS,QAAQ,OAAO,EAAE;YAC5B,IAAI,OAAO;gBAAE,UAAU,CAAC;gBAAG,OAAO;YAAK;YACvC,QAAQ,OAAO,GAAG;QACpB,OAAO,OAAO,QAAQ,OAAO;QAC7B,UAAU,QACR;YACE,SAAS,iBAAiB,YAAY;gBACpC,IAAI,CAAC,SAAS;oBACZ,UAAU,CAAC;oBACX,mBAAmB;oBACnB,eAAe,SAAS;oBACxB,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,EAAE;wBACvC,IAAI,mBAAmB,KAAK,KAAK;wBACjC,IAAI,QAAQ,kBAAkB,eAC5B,OAAQ,oBAAoB;oBAChC;oBACA,OAAQ,oBAAoB;gBAC9B;gBACA,mBAAmB;gBACnB,IAAI,SAAS,kBAAkB,eAC7B,OAAO;gBACT,IAAI,gBAAgB,SAAS;gBAC7B,IAAI,KAAK,MAAM,WAAW,QAAQ,kBAAkB,gBAClD,OAAO,AAAC,mBAAmB,cAAe;gBAC5C,mBAAmB;gBACnB,OAAQ,oBAAoB;YAC9B;YACA,IAAI,UAAU,CAAC,GACb,kBACA,mBACA,yBACE,KAAK,MAAM,oBAAoB,OAAO;YAC1C,OAAO;gBACL;oBACE,OAAO,iBAAiB;gBAC1B;gBACA,SAAS,yBACL,KAAK,IACL;oBACE,OAAO,iBAAiB;gBAC1B;aACL;QACH,GACA;YAAC;YAAa;YAAmB;YAAU;SAAQ;QAErD,IAAI,QAAQ,qBAAqB,WAAW,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QAClE,UACE;YACE,KAAK,QAAQ,GAAG,CAAC;YACjB,KAAK,KAAK,GAAG;QACf,GACA;YAAC;SAAM;QAET,cAAc;QACd,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/use-sync-external-store/shim/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB,CAAC;IACvB,IAAI;IACJ,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,MAAM,WAAW,CAAC,SAAS;QACzB,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,QAAQ;YAChC,MAAM,gBAAgB;YACtB,QAAQ,CAAC,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,IAAI,IAAI,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACjI,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,OAAO;QAClD;IACF;IACA,MAAM,WAAW,IAAM;IACvB,MAAM,kBAAkB,IAAM;IAC9B,MAAM,YAAY,CAAC;QACjB,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC;IACA,MAAM,MAAM;QAAE;QAAU;QAAU;QAAiB;IAAU;IAC7D,MAAM,eAAe,QAAQ,YAAY,UAAU,UAAU;IAC7D,OAAO;AACT;AACA,MAAM,cAAc,CAAC,cAAgB,cAAc,gBAAgB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/zustand/esm/traditional.mjs"], "sourcesContent": ["import React from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\n\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nconst identity = (arg) => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getInitialState,\n    selector,\n    equalityFn\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\nexport { createWithEqualityFn, useStoreWithEqualityFn };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,EAAE,gCAAgC,EAAE,GAAG,4KAAA,CAAA,UAA2B;AACxE,MAAM,WAAW,CAAC,MAAQ;AAC1B,SAAS,uBAAuB,GAAG,EAAE,WAAW,QAAQ,EAAE,UAAU;IAClE,MAAM,QAAQ,iCACZ,IAAI,SAAS,EACb,IAAI,QAAQ,EACZ,IAAI,eAAe,EACnB,UACA;IAEF,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC;IACpB,OAAO;AACT;AACA,MAAM,2BAA2B,CAAC,aAAa;IAC7C,MAAM,MAAM,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE;IACxB,MAAM,8BAA8B,CAAC,UAAU,aAAa,iBAAiB,GAAK,uBAAuB,KAAK,UAAU;IACxH,OAAO,MAAM,CAAC,6BAA6B;IAC3C,OAAO;AACT;AACA,MAAM,uBAAuB,CAAC,aAAa,oBAAsB,cAAc,yBAAyB,aAAa,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/react-reconciler/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS;QACP,IAAI,sBAAsB;YACxB,IAAI,cAAc,QAAQ,YAAY;YACtC,YAAY;YACZ,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,GAAG;oBACD,0BAA0B,CAAC;oBAC3B,0BACE,CAAC,AAAC,yBAAyB,CAAC,GAC5B,kBAAkB,gBACjB,gBAAgB,CAAC,CAAE;oBACtB,mBAAmB,CAAC;oBACpB,IAAI,wBAAwB;oBAC5B,IAAI;wBACF,GAAG;4BACD,cAAc;4BACd,IACE,cAAc,KAAK,YACnB,SAAS,eACT,CAAC,CACC,YAAY,cAAc,GAAG,eAC7B,mBACF,GAEA;gCACA,IAAI,WAAW,YAAY,QAAQ;gCACnC,IAAI,eAAe,OAAO,UAAU;oCAClC,YAAY,QAAQ,GAAG;oCACvB,uBAAuB,YAAY,aAAa;oCAChD,IAAI,uBAAuB,SACzB,YAAY,cAAc,IAAI;oCAEhC,cAAc,QAAQ,YAAY;oCAClC,IAAI,eAAe,OAAO,sBAAsB;wCAC9C,YAAY,QAAQ,GAAG;wCACvB,cAAc;wCACd,cAAc,CAAC;wCACf,MAAM;oCACR;oCACA,gBAAgB,KAAK,cAAc,IAAI;oCACvC,cAAc;gCAChB,OAAO,IAAI;gCACX,cAAc,KAAK;4BACrB;4BACA,IAAI,SAAS,aAAa,cAAc,CAAC;iCACpC;gCACH,IAAI,aAAa,KAAK;gCACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;gCAE3B,cAAc,CAAC;4BACjB;wBACF;wBACA,MAAM;oBACR,SAAU;wBACP,cAAc,MACZ,uBAAuB,uBACvB,mBAAmB,CAAC;oBACzB;oBACA,cAAc,KAAK;gBACrB;YACF,SAAU;gBACR,cACI,qCACC,uBAAuB,CAAC;YAC/B;QACF;IACF;IACA,SAAS,KAAK,IAAI,EAAE,IAAI;QACtB,IAAI,QAAQ,KAAK,MAAM;QACvB,KAAK,IAAI,CAAC;QACV,GAAG,MAAO,IAAI,OAAS;YACrB,IAAI,cAAc,AAAC,QAAQ,MAAO,GAChC,SAAS,IAAI,CAAC,YAAY;YAC5B,IAAI,IAAI,QAAQ,QAAQ,OACtB,AAAC,IAAI,CAAC,YAAY,GAAG,MAClB,IAAI,CAAC,MAAM,GAAG,QACd,QAAQ;iBACR,MAAM;QACb;IACF;IACA,SAAS,KAAK,IAAI;QAChB,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,IAAI,CAAC,EAAE;IAC3C;IACA,SAAS,IAAI,IAAI;QACf,IAAI,MAAM,KAAK,MAAM,EAAE,OAAO;QAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE,EACjB,OAAO,KAAK,GAAG;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,EAAE,GAAG;YACV,GAAG,IACD,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,aAAa,WAAW,GAC7D,QAAQ,YAER;gBACA,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,GAChC,OAAO,IAAI,CAAC,UAAU,EACtB,aAAa,YAAY,GACzB,QAAQ,IAAI,CAAC,WAAW;gBAC1B,IAAI,IAAI,QAAQ,MAAM,OACpB,aAAa,UAAU,IAAI,QAAQ,OAAO,QACtC,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,OACf,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ,UAAW,IACpB,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,MACf,IAAI,CAAC,UAAU,GAAG,MAClB,QAAQ,SAAU;qBACpB,IAAI,aAAa,UAAU,IAAI,QAAQ,OAAO,OACjD,AAAC,IAAI,CAAC,MAAM,GAAG,OACZ,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ;qBACR,MAAM;YACb;QACF;QACA,OAAO;IACT;IACA,SAAS,QAAQ,CAAC,EAAE,CAAC;QACnB,IAAI,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC,OAAO,MAAM,OAAO,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;IACxC;IACA,SAAS,cAAc,WAAW;QAChC,IAAK,IAAI,QAAQ,KAAK,aAAa,SAAS,OAAS;YACnD,IAAI,SAAS,MAAM,QAAQ,EAAE,IAAI;iBAC5B,IAAI,MAAM,SAAS,IAAI,aAC1B,IAAI,aACD,MAAM,SAAS,GAAG,MAAM,cAAc,EACvC,KAAK,WAAW;iBACf;YACL,QAAQ,KAAK;QACf;IACF;IACA,SAAS,cAAc,WAAW;QAChC,yBAAyB,CAAC;QAC1B,cAAc;QACd,IAAI,CAAC,yBACH,IAAI,SAAS,KAAK,YAChB,AAAC,0BAA0B,CAAC,GAAI;aAC7B;YACH,IAAI,aAAa,KAAK;YACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;QAE7B;IACJ;IACA,SAAS;QACP,OAAO,QAAQ,YAAY,KAAK,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpE;IACA,SAAS;QACP,wBACE,CAAC,AAAC,uBAAuB,CAAC,GAAI,kCAAkC;IACpE;IACA,SAAS,mBAAmB,QAAQ,EAAE,EAAE;QACtC,gBAAgB,gBAAgB;YAC9B,SAAS,QAAQ,YAAY;QAC/B,GAAG;IACL;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,QAAQ,YAAY,GAAG,KAAK;IAC5B,IACE,aAAa,OAAO,eACpB,eAAe,OAAO,YAAY,GAAG,EACrC;QACA,IAAI,mBAAmB;QACvB,QAAQ,YAAY,GAAG;YACrB,OAAO,iBAAiB,GAAG;QAC7B;IACF,OAAO;QACL,IAAI,YAAY,MACd,cAAc,UAAU,GAAG;QAC7B,QAAQ,YAAY,GAAG;YACrB,OAAO,UAAU,GAAG,KAAK;QAC3B;IACF;IACA,IAAI,YAAY,EAAE,EAChB,aAAa,EAAE,EACf,gBAAgB,GAChB,cAAc,MACd,uBAAuB,GACvB,mBAAmB,CAAC,GACpB,0BAA0B,CAAC,GAC3B,yBAAyB,CAAC,GAC1B,kBAAkB,eAAe,OAAO,aAAa,aAAa,MAClE,oBACE,eAAe,OAAO,eAAe,eAAe,MACtD,oBACE,gBAAgB,OAAO,eAAe,eAAe,MACvD,uBAAuB,CAAC,GACxB,gBAAgB,CAAC,GACjB,gBAAgB,GAChB,YAAY,CAAC;IACf,IAAI,eAAe,OAAO,mBACxB,IAAI,mCAAmC;QACrC,kBAAkB;IACpB;SACG,IAAI,gBAAgB,OAAO,gBAAgB;QAC9C,IAAI,UAAU,IAAI,kBAChB,OAAO,QAAQ,KAAK;QACtB,QAAQ,KAAK,CAAC,SAAS,GAAG;QAC1B,mCAAmC;YACjC,KAAK,WAAW,CAAC;QACnB;IACF,OACE,mCAAmC;QACjC,gBAAgB,0BAA0B;IAC5C;IACF,QAAQ,qBAAqB,GAAG;IAChC,QAAQ,0BAA0B,GAAG;IACrC,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,uBAAuB,GAAG;IAClC,QAAQ,kBAAkB,GAAG;IAC7B,QAAQ,6BAA6B,GAAG;IACxC,QAAQ,uBAAuB,GAAG,SAAU,IAAI;QAC9C,KAAK,QAAQ,GAAG;IAClB;IACA,QAAQ,0BAA0B,GAAG;QACnC,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB;IAC1D;IACA,QAAQ,uBAAuB,GAAG,SAAU,GAAG;QAC7C,IAAI,OAAO,MAAM,MACb,QAAQ,KAAK,CACX,qHAED,gBAAgB,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO;IACzD;IACA,QAAQ,gCAAgC,GAAG;QACzC,OAAO;IACT;IACA,QAAQ,6BAA6B,GAAG;QACtC,OAAO,KAAK;IACd;IACA,QAAQ,aAAa,GAAG,SAAU,YAAY;QAC5C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB;gBACpB;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,uBAAuB,GAAG,YAAa;IAC/C,QAAQ,qBAAqB,GAAG,YAAa;IAC7C,QAAQ,wBAAwB,GAAG,SAAU,aAAa,EAAE,YAAY;QACtE,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,yBAAyB,GAAG,SAClC,aAAa,EACb,QAAQ,EACR,OAAO;QAEP,IAAI,cAAc,QAAQ,YAAY;QACtC,aAAa,OAAO,WAAW,SAAS,UACpC,CAAC,AAAC,UAAU,QAAQ,KAAK,EACxB,UACC,aAAa,OAAO,WAAW,IAAI,UAC/B,cAAc,UACd,WAAY,IACjB,UAAU;QACf,OAAQ;YACN,KAAK;gBACH,IAAI,UAAU,CAAC;gBACf;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE,UAAU;QACd;QACA,UAAU,UAAU;QACpB,gBAAgB;YACd,IAAI;YACJ,UAAU;YACV,eAAe;YACf,WAAW;YACX,gBAAgB;YAChB,WAAW,CAAC;QACd;QACA,UAAU,cACN,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,YAAY,gBACjB,SAAS,KAAK,cACZ,kBAAkB,KAAK,eACvB,CAAC,yBACG,CAAC,kBAAkB,gBAAiB,gBAAgB,CAAC,CAAE,IACtD,yBAAyB,CAAC,GAC/B,mBAAmB,eAAe,UAAU,YAAY,CAAC,IAC3D,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,WAAW,gBAChB,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB,CAAC;QAC7D,OAAO;IACT;IACA,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,qBAAqB,GAAG,SAAU,QAAQ;QAChD,IAAI,sBAAsB;QAC1B,OAAO;YACL,IAAI,wBAAwB;YAC5B,uBAAuB;YACvB,IAAI;gBACF,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;YAC9B,SAAU;gBACR,uBAAuB;YACzB;QACF;IACF;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/react-reconciler/node_modules/scheduler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS;QACP,IAAI,sBAAsB;YACxB,IAAI,cAAc,QAAQ,YAAY;YACtC,YAAY;YACZ,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,GAAG;oBACD,0BAA0B,CAAC;oBAC3B,0BACE,CAAC,AAAC,yBAAyB,CAAC,GAC5B,kBAAkB,gBACjB,gBAAgB,CAAC,CAAE;oBACtB,mBAAmB,CAAC;oBACpB,IAAI,wBAAwB;oBAC5B,IAAI;wBACF,GAAG;4BACD,cAAc;4BACd,IACE,cAAc,KAAK,YACnB,SAAS,eACT,CAAC,CACC,YAAY,cAAc,GAAG,eAC7B,mBACF,GAEA;gCACA,IAAI,WAAW,YAAY,QAAQ;gCACnC,IAAI,eAAe,OAAO,UAAU;oCAClC,YAAY,QAAQ,GAAG;oCACvB,uBAAuB,YAAY,aAAa;oCAChD,IAAI,uBAAuB,SACzB,YAAY,cAAc,IAAI;oCAEhC,cAAc,QAAQ,YAAY;oCAClC,IAAI,eAAe,OAAO,sBAAsB;wCAC9C,YAAY,QAAQ,GAAG;wCACvB,cAAc;wCACd,cAAc,CAAC;wCACf,MAAM;oCACR;oCACA,gBAAgB,KAAK,cAAc,IAAI;oCACvC,cAAc;gCAChB,OAAO,IAAI;gCACX,cAAc,KAAK;4BACrB;4BACA,IAAI,SAAS,aAAa,cAAc,CAAC;iCACpC;gCACH,IAAI,aAAa,KAAK;gCACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;gCAE3B,cAAc,CAAC;4BACjB;wBACF;wBACA,MAAM;oBACR,SAAU;wBACP,cAAc,MACZ,uBAAuB,uBACvB,mBAAmB,CAAC;oBACzB;oBACA,cAAc,KAAK;gBACrB;YACF,SAAU;gBACR,cACI,qCACC,uBAAuB,CAAC;YAC/B;QACF;IACF;IACA,SAAS,KAAK,IAAI,EAAE,IAAI;QACtB,IAAI,QAAQ,KAAK,MAAM;QACvB,KAAK,IAAI,CAAC;QACV,GAAG,MAAO,IAAI,OAAS;YACrB,IAAI,cAAc,AAAC,QAAQ,MAAO,GAChC,SAAS,IAAI,CAAC,YAAY;YAC5B,IAAI,IAAI,QAAQ,QAAQ,OACtB,AAAC,IAAI,CAAC,YAAY,GAAG,MAClB,IAAI,CAAC,MAAM,GAAG,QACd,QAAQ;iBACR,MAAM;QACb;IACF;IACA,SAAS,KAAK,IAAI;QAChB,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,IAAI,CAAC,EAAE;IAC3C;IACA,SAAS,IAAI,IAAI;QACf,IAAI,MAAM,KAAK,MAAM,EAAE,OAAO;QAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE,EACjB,OAAO,KAAK,GAAG;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,EAAE,GAAG;YACV,GAAG,IACD,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,aAAa,WAAW,GAC7D,QAAQ,YAER;gBACA,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,GAChC,OAAO,IAAI,CAAC,UAAU,EACtB,aAAa,YAAY,GACzB,QAAQ,IAAI,CAAC,WAAW;gBAC1B,IAAI,IAAI,QAAQ,MAAM,OACpB,aAAa,UAAU,IAAI,QAAQ,OAAO,QACtC,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,OACf,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ,UAAW,IACpB,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,MACf,IAAI,CAAC,UAAU,GAAG,MAClB,QAAQ,SAAU;qBACpB,IAAI,aAAa,UAAU,IAAI,QAAQ,OAAO,OACjD,AAAC,IAAI,CAAC,MAAM,GAAG,OACZ,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ;qBACR,MAAM;YACb;QACF;QACA,OAAO;IACT;IACA,SAAS,QAAQ,CAAC,EAAE,CAAC;QACnB,IAAI,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC,OAAO,MAAM,OAAO,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;IACxC;IACA,SAAS,cAAc,WAAW;QAChC,IAAK,IAAI,QAAQ,KAAK,aAAa,SAAS,OAAS;YACnD,IAAI,SAAS,MAAM,QAAQ,EAAE,IAAI;iBAC5B,IAAI,MAAM,SAAS,IAAI,aAC1B,IAAI,aACD,MAAM,SAAS,GAAG,MAAM,cAAc,EACvC,KAAK,WAAW;iBACf;YACL,QAAQ,KAAK;QACf;IACF;IACA,SAAS,cAAc,WAAW;QAChC,yBAAyB,CAAC;QAC1B,cAAc;QACd,IAAI,CAAC,yBACH,IAAI,SAAS,KAAK,YAChB,AAAC,0BAA0B,CAAC,GAAI;aAC7B;YACH,IAAI,aAAa,KAAK;YACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;QAE7B;IACJ;IACA,SAAS;QACP,OAAO,QAAQ,YAAY,KAAK,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpE;IACA,SAAS;QACP,wBACE,CAAC,AAAC,uBAAuB,CAAC,GAAI,kCAAkC;IACpE;IACA,SAAS,mBAAmB,QAAQ,EAAE,EAAE;QACtC,gBAAgB,gBAAgB;YAC9B,SAAS,QAAQ,YAAY;QAC/B,GAAG;IACL;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,QAAQ,YAAY,GAAG,KAAK;IAC5B,IACE,aAAa,OAAO,eACpB,eAAe,OAAO,YAAY,GAAG,EACrC;QACA,IAAI,mBAAmB;QACvB,QAAQ,YAAY,GAAG;YACrB,OAAO,iBAAiB,GAAG;QAC7B;IACF,OAAO;QACL,IAAI,YAAY,MACd,cAAc,UAAU,GAAG;QAC7B,QAAQ,YAAY,GAAG;YACrB,OAAO,UAAU,GAAG,KAAK;QAC3B;IACF;IACA,IAAI,YAAY,EAAE,EAChB,aAAa,EAAE,EACf,gBAAgB,GAChB,cAAc,MACd,uBAAuB,GACvB,mBAAmB,CAAC,GACpB,0BAA0B,CAAC,GAC3B,yBAAyB,CAAC,GAC1B,kBAAkB,eAAe,OAAO,aAAa,aAAa,MAClE,oBACE,eAAe,OAAO,eAAe,eAAe,MACtD,oBACE,gBAAgB,OAAO,eAAe,eAAe,MACvD,uBAAuB,CAAC,GACxB,gBAAgB,CAAC,GACjB,gBAAgB,GAChB,YAAY,CAAC;IACf,IAAI,eAAe,OAAO,mBACxB,IAAI,mCAAmC;QACrC,kBAAkB;IACpB;SACG,IAAI,gBAAgB,OAAO,gBAAgB;QAC9C,IAAI,UAAU,IAAI,kBAChB,OAAO,QAAQ,KAAK;QACtB,QAAQ,KAAK,CAAC,SAAS,GAAG;QAC1B,mCAAmC;YACjC,KAAK,WAAW,CAAC;QACnB;IACF,OACE,mCAAmC;QACjC,gBAAgB,0BAA0B;IAC5C;IACF,QAAQ,qBAAqB,GAAG;IAChC,QAAQ,0BAA0B,GAAG;IACrC,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,uBAAuB,GAAG;IAClC,QAAQ,kBAAkB,GAAG;IAC7B,QAAQ,6BAA6B,GAAG;IACxC,QAAQ,uBAAuB,GAAG,SAAU,IAAI;QAC9C,KAAK,QAAQ,GAAG;IAClB;IACA,QAAQ,0BAA0B,GAAG;QACnC,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB;IAC1D;IACA,QAAQ,uBAAuB,GAAG,SAAU,GAAG;QAC7C,IAAI,OAAO,MAAM,MACb,QAAQ,KAAK,CACX,qHAED,gBAAgB,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO;IACzD;IACA,QAAQ,gCAAgC,GAAG;QACzC,OAAO;IACT;IACA,QAAQ,6BAA6B,GAAG;QACtC,OAAO,KAAK;IACd;IACA,QAAQ,aAAa,GAAG,SAAU,YAAY;QAC5C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB;gBACpB;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,uBAAuB,GAAG,YAAa;IAC/C,QAAQ,qBAAqB,GAAG,YAAa;IAC7C,QAAQ,wBAAwB,GAAG,SAAU,aAAa,EAAE,YAAY;QACtE,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,yBAAyB,GAAG,SAClC,aAAa,EACb,QAAQ,EACR,OAAO;QAEP,IAAI,cAAc,QAAQ,YAAY;QACtC,aAAa,OAAO,WAAW,SAAS,UACpC,CAAC,AAAC,UAAU,QAAQ,KAAK,EACxB,UACC,aAAa,OAAO,WAAW,IAAI,UAC/B,cAAc,UACd,WAAY,IACjB,UAAU;QACf,OAAQ;YACN,KAAK;gBACH,IAAI,UAAU,CAAC;gBACf;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE,UAAU;QACd;QACA,UAAU,UAAU;QACpB,gBAAgB;YACd,IAAI;YACJ,UAAU;YACV,eAAe;YACf,WAAW;YACX,gBAAgB;YAChB,WAAW,CAAC;QACd;QACA,UAAU,cACN,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,YAAY,gBACjB,SAAS,KAAK,cACZ,kBAAkB,KAAK,eACvB,CAAC,yBACG,CAAC,kBAAkB,gBAAiB,gBAAgB,CAAC,CAAE,IACtD,yBAAyB,CAAC,GAC/B,mBAAmB,eAAe,UAAU,YAAY,CAAC,IAC3D,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,WAAW,gBAChB,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB,CAAC;QAC7D,OAAO;IACT;IACA,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,qBAAqB,GAAG,SAAU,QAAQ;QAChD,IAAI,sBAAsB;QAC1B,OAAO;YACL,IAAI,wBAAwB;YAC5B,uBAAuB;YACvB,IAAI;gBACF,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;YAC9B,SAAU;gBACR,uBAAuB;YACzB;QACF;IACF;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/fiber/node_modules/scheduler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/suspend-react/index.js"], "sourcesContent": ["const isPromise = promise => typeof promise === 'object' && typeof promise.then === 'function';\n\nconst globalCache = [];\n\nfunction shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n\n  return true;\n}\n\nfunction query(fn, keys = null, preload = false, config = {}) {\n  // If no keys were given, the function is the key\n  if (keys === null) keys = [fn];\n\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) {\n        if (config.lifespan && config.lifespan > 0) {\n          if (entry.timeout) clearTimeout(entry.timeout);\n          entry.timeout = setTimeout(entry.remove, config.lifespan);\n        }\n\n        return entry.response;\n      } // If the promise is still unresolved, throw\n\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    remove: () => {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    },\n    promise: // Execute the promise\n    (isPromise(fn) ? fn : fn(...keys) // When it resolves, store its value\n    ).then(response => {\n      entry.response = response; // Remove the entry in time if a lifespan was given\n\n      if (config.lifespan && config.lifespan > 0) {\n        entry.timeout = setTimeout(entry.remove, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\n\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\n\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\n\nconst peek = keys => {\n  var _globalCache$find;\n\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\n\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n    if (entry) entry.remove();\n  }\n};\n\nexport { clear, peek, preload, suspend };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,YAAY,CAAA,UAAW,OAAO,YAAY,YAAY,OAAO,QAAQ,IAAI,KAAK;AAEpF,MAAM,cAAc,EAAE;AAEtB,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,IAAM,MAAM,CAAC;IAC/D,IAAI,SAAS,MAAM,OAAO;IAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;IAC3B,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO;IAEhC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO;IAEnE,OAAO;AACT;AAEA,SAAS,MAAM,EAAE,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,SAAS,CAAC,CAAC;IAC1D,iDAAiD;IACjD,IAAI,SAAS,MAAM,OAAO;QAAC;KAAG;IAE9B,KAAK,MAAM,SAAS,YAAa;QAC/B,eAAe;QACf,IAAI,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG;YACrD,+DAA+D;YAC/D,IAAI,SAAS,OAAO,WAAW,8BAA8B;YAE7D,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,UAAU,MAAM,MAAM,KAAK,EAAE,uCAAuC;YAEpH,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,aAAa;gBAC3D,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;oBAC1C,IAAI,MAAM,OAAO,EAAE,aAAa,MAAM,OAAO;oBAC7C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;gBAC1D;gBAEA,OAAO,MAAM,QAAQ;YACvB,EAAE,4CAA4C;YAG9C,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;QACnC;IACF,EAAE,qCAAqC;IAGvC,MAAM,QAAQ;QACZ;QACA,OAAO,OAAO,KAAK;QACnB,QAAQ;YACN,MAAM,QAAQ,YAAY,OAAO,CAAC;YAClC,IAAI,UAAU,CAAC,GAAG,YAAY,MAAM,CAAC,OAAO;QAC9C;QACA,SACA,CAAC,UAAU,MAAM,KAAK,MAAM,MAAM,oCAAoC;QACtE,EAAE,IAAI,CAAC,CAAA;YACL,MAAM,QAAQ,GAAG,UAAU,mDAAmD;YAE9E,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;gBAC1C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;YAC1D;QACF,GAAG,6FAA6F;SAC/F,KAAK,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG;IAChC,GAAG,qBAAqB;IAExB,YAAY,IAAI,CAAC,QAAQ,2DAA2D;IAEpF,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;IACjC,OAAO;AACT;AAEA,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,MAAM,IAAI,MAAM,OAAO;AAE7D,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,KAAK,MAAM,IAAI,MAAM,MAAM;AAEjE,MAAM,OAAO,CAAA;IACX,IAAI;IAEJ,OAAO,CAAC,oBAAoB,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,kBAAkB,QAAQ;AACzJ;AAEA,MAAM,QAAQ,CAAA;IACZ,IAAI,SAAS,aAAa,KAAK,MAAM,KAAK,GAAG,YAAY,MAAM,CAAC,GAAG,YAAY,MAAM;SAAO;QAC1F,MAAM,QAAQ,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK;QACxF,IAAI,OAAO,MAAM,MAAM;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/its-fine/src/index.tsx"], "sourcesContent": ["import * as React from 'react'\r\nimport type <PERSON>actR<PERSON>onciler from 'react-reconciler'\r\n\r\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\r\nconst useIsomorphicLayoutEffect = /* @__PURE__ */ (() =>\r\n  typeof window !== 'undefined' && (window.document?.createElement || window.navigator?.product === 'ReactNative'))()\r\n  ? React.useLayoutEffect\r\n  : React.useEffect\r\n\r\n/**\r\n * Represents a react-internal Fiber node.\r\n */\r\nexport type Fiber<T = any> = Omit<ReactReconciler.Fiber, 'stateNode'> & { stateNode: T }\r\n\r\n/**\r\n * Represents a {@link Fiber} node selector for traversal.\r\n */\r\nexport type FiberSelector<T = any> = (\r\n  /** The current {@link Fiber} node. */\r\n  node: Fiber<T | null>,\r\n) => boolean | void\r\n\r\n/**\r\n * Traverses up or down a {@link Fiber}, return `true` to stop and select a node.\r\n */\r\nexport function traverseFiber<T = any>(\r\n  /** Input {@link Fiber} to traverse. */\r\n  fiber: Fiber | undefined,\r\n  /** Whether to ascend and walk up the tree. Will walk down if `false`. */\r\n  ascending: boolean,\r\n  /** A {@link Fiber} node selector, returns the first match when `true` is passed. */\r\n  selector: FiberSelector<T>,\r\n): Fiber<T> | undefined {\r\n  if (!fiber) return\r\n  if (selector(fiber) === true) return fiber\r\n\r\n  let child = ascending ? fiber.return : fiber.child\r\n  while (child) {\r\n    const match = traverseFiber(child, ascending, selector)\r\n    if (match) return match\r\n\r\n    child = ascending ? null : child.sibling\r\n  }\r\n}\r\n\r\n// In development, React will warn about using contexts between renderers.\r\n// Hide the warning because its-fine fixes this issue\r\n// https://github.com/facebook/react/pull/12779\r\nfunction wrapContext<T>(context: React.Context<T>): React.Context<T> {\r\n  try {\r\n    return Object.defineProperties(context, {\r\n      _currentRenderer: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n      _currentRenderer2: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n    })\r\n  } catch (_) {\r\n    return context\r\n  }\r\n}\r\n\r\nconst FiberContext = /* @__PURE__ */ wrapContext(/* @__PURE__ */ React.createContext<Fiber>(null!))\r\n\r\n/**\r\n * A react-internal {@link Fiber} provider. This component binds React children to the React Fiber tree. Call its-fine hooks within this.\r\n */\r\nexport class FiberProvider extends React.Component<{ children?: React.ReactNode }> {\r\n  private _reactInternals!: Fiber\r\n\r\n  render() {\r\n    return <FiberContext.Provider value={this._reactInternals}>{this.props.children}</FiberContext.Provider>\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the current react-internal {@link Fiber}. This is an implementation detail of [react-reconciler](https://github.com/facebook/react/tree/main/packages/react-reconciler).\r\n */\r\nexport function useFiber(): Fiber<null> | undefined {\r\n  const root = React.useContext(FiberContext)\r\n  if (root === null) throw new Error('its-fine: useFiber must be called within a <FiberProvider />!')\r\n\r\n  const id = React.useId()\r\n  const fiber = React.useMemo(() => {\r\n    for (const maybeFiber of [root, root?.alternate]) {\r\n      if (!maybeFiber) continue\r\n      const fiber = traverseFiber<null>(maybeFiber, false, (node) => {\r\n        let state = node.memoizedState\r\n        while (state) {\r\n          if (state.memoizedState === id) return true\r\n          state = state.next\r\n        }\r\n      })\r\n      if (fiber) return fiber\r\n    }\r\n  }, [root, id])\r\n\r\n  return fiber\r\n}\r\n\r\n/**\r\n * Represents a react-reconciler container instance.\r\n */\r\nexport interface ContainerInstance<T = any> {\r\n  containerInfo: T\r\n}\r\n\r\n/**\r\n * Returns the current react-reconciler container info passed to {@link ReactReconciler.Reconciler.createContainer}.\r\n *\r\n * In react-dom, a container will point to the root DOM element; in react-three-fiber, it will point to the root Zustand store.\r\n */\r\nexport function useContainer<T = any>(): T | undefined {\r\n  const fiber = useFiber()\r\n  const root = React.useMemo(\r\n    () => traverseFiber<ContainerInstance<T>>(fiber, true, (node) => node.stateNode?.containerInfo != null),\r\n    [fiber],\r\n  )\r\n\r\n  return root?.stateNode.containerInfo\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler child instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestChild<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const childRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    childRef.current = traverseFiber<T>(\r\n      fiber,\r\n      false,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return childRef\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler parent instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestParent<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const parentRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    parentRef.current = traverseFiber<T>(\r\n      fiber,\r\n      true,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return parentRef\r\n}\r\n\r\nexport type ContextMap = Map<React.Context<any>, any> & {\r\n  get<T>(context: React.Context<T>): T | undefined\r\n}\r\n\r\nconst REACT_CONTEXT_TYPE = Symbol.for('react.context')\r\n\r\nconst isContext = <T,>(type: unknown): type is React.Context<T> =>\r\n  type !== null && typeof type === 'object' && '$$typeof' in type && type.$$typeof === REACT_CONTEXT_TYPE\r\n\r\n/**\r\n * Returns a map of all contexts and their values.\r\n */\r\nexport function useContextMap(): ContextMap {\r\n  const fiber = useFiber()\r\n  const [contextMap] = React.useState(() => new Map<React.Context<any>, any>())\r\n\r\n  // Collect live context\r\n  contextMap.clear()\r\n  let node = fiber\r\n  while (node) {\r\n    const context = node.type\r\n    if (isContext(context) && context !== FiberContext && !contextMap.has(context)) {\r\n      contextMap.set(context, React.use(wrapContext(context)))\r\n    }\r\n\r\n    node = node.return!\r\n  }\r\n\r\n  return contextMap\r\n}\r\n\r\n/**\r\n * Represents a react-context bridge provider component.\r\n */\r\nexport type ContextBridge = React.FC<React.PropsWithChildren<{}>>\r\n\r\n/**\r\n * React Context currently cannot be shared across [React renderers](https://reactjs.org/docs/codebase-overview.html#renderers) but explicitly forwarded between providers (see [react#17275](https://github.com/facebook/react/issues/17275)). This hook returns a {@link ContextBridge} of live context providers to pierce Context across renderers.\r\n *\r\n * Pass {@link ContextBridge} as a component to a secondary renderer to enable context-sharing within its children.\r\n */\r\nexport function useContextBridge(): ContextBridge {\r\n  const contextMap = useContextMap()\r\n\r\n  // Flatten context and their memoized values into a `ContextBridge` provider\r\n  return React.useMemo(\r\n    () =>\r\n      Array.from(contextMap.keys()).reduce(\r\n        (Prev, context) => (props) =>\r\n          (\r\n            <Prev>\r\n              <context.Provider {...props} value={contextMap.get(context)} />\r\n            </Prev>\r\n          ),\r\n        (props) => <FiberProvider {...props} />,\r\n      ),\r\n    [contextMap],\r\n  )\r\n}\r\n"], "names": ["useIsomorphicLayoutEffect", "_a", "_b", "React", "traverseFiber", "fiber", "ascending", "selector", "child", "match", "wrapContext", "context", "_", "FiberContext", "FiberProvider", "useFiber", "root", "id", "maybeFiber", "node", "state", "useContainer", "useNearestChild", "type", "childRef", "useNearestParent", "parentRef", "REACT_CONTEXT_TYPE", "isContext", "useContextMap", "contextMap", "useContextBridge", "Prev", "props"], "mappings": ";;;;;;;;;;;;AAYA,MAAMA,IAA6C,aAAA,GAAA,CAAA,MAAA;;IACjD,OAAA,OAAO,UAAW,eAAA,CAAA,CAAA,CAAgBC,IAAA,OAAO,QAAA,KAAP,OAAA,KAAA,IAAAA,EAAiB,aAAA,KAAA,CAAA,CAAiBC,IAAA,OAAO,SAAA,KAAP,OAAA,KAAA,IAAAA,EAAkB,OAAA,MAAY,aAAA;AAAA,CAAA,EAAA,0MAChGC,EAAM,gBAAA,yMACNA,EAAM,UAAA;AAkBM,SAAAC,EAEdC,CAAAA,EAEAC,CAAAA,EAEAC,CAAAA,EACsB;IACtB,IAAI,CAACF,EAAO,CAAA;IACZ,IAAIE,EAASF,CAAK,MAAM,CAAA,EAAa,CAAA,OAAAA;IAErC,IAAIG,IAAQF,IAAYD,EAAM,MAAA,GAASA,EAAM,KAAA;IAC7C,MAAOG,GAAO;QACZ,MAAMC,IAAQL,EAAcI,GAAOF,GAAWC,CAAQ;QACtD,IAAIE,EAAc,CAAA,OAAAA;QAEVD,IAAAF,IAAY,OAAOE,EAAM,OAAA;IAAA;AAErC;AAKA,SAASE,EAAeC,CAAAA,EAA6C;IAC/D,IAAA;QACK,OAAA,OAAO,gBAAA,CAAiBA,GAAS;YACtC,kBAAkB;gBAChB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YACR;YACA,mBAAmB;gBACjB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YAAC;QACT,CACD;IAAA,EAAA,OACMC,GAAG;QACH,OAAAD;IAAA;AAEX;AAEA,MAAME,IAA+B,aAAA,GAAAH,EAAkC,aAAA,6MAAAP,EAAA,cAAA,EAAqB,IAAK,CAAC;AAKrF,MAAAW,gNAAsBX,EAAM,UAAA,CAA0C;IAGjF,SAAS;QACA,OAAA,aAAA,6MAAAA,EAAA,cAAA,EAACU,EAAa,QAAA,EAAb;YAAsB,OAAO,IAAA,CAAK,eAAA;QAAA,GAAkB,IAAA,CAAK,KAAA,CAAM,QAAS;IAAA;AAEpF;AAKO,SAASE,IAAoC;IAC5C,MAAAC,KAAOb,EAAM,oNAAA,EAAWU,CAAY;IAC1C,IAAIG,MAAS,KAAY,CAAA,MAAA,IAAI,MAAM,+DAA+D;IAE5F,MAAAC,8MAAKd,EAAM,MAAA,CAAM;IAehB,WAdOA,EAAM,8MAAA,EAAQ,MAAM;QAChC,KAAA,MAAWe,KAAc;YAACF;YAAMA,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAS;SAAA,CAAG;YAChD,IAAI,CAACE,EAAY,CAAA;YACjB,MAAMb,IAAQD,EAAoBc,GAAY,CAAA,GAAO,CAACC,MAAS;gBAC7D,IAAIC,IAAQD,EAAK,aAAA;gBACjB,MAAOC,GAAO;oBACR,IAAAA,EAAM,aAAA,KAAkBH,EAAW,CAAA,OAAA,CAAA;oBACvCG,IAAQA,EAAM,IAAA;gBAAA;YAChB,CACD;YACD,IAAIf,EAAcA,CAAAA,OAAAA;QAAA;IACpB,GACC;QAACW;QAAMC,CAAE;KAAC;AAGf;AAcO,SAASI,IAAuC;IACrD,MAAMhB,IAAQU,EAAS,GACjBC,8MAAOb,EAAM,QAAA,EACjB,IAAMC,EAAoCC,GAAO,CAAA,GAAM,CAACc,MAAS;;YAAA,OAAA,CAAA,CAAAlB,IAAAkB,EAAK,SAAA,KAAL,OAAA,KAAA,IAAAlB,EAAgB,aAAA,KAAiB;QAAA,CAAI,GACtG;QAACI,CAAK;KAAA;IAGR,OAAOW,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAA,CAAU,aAAA;AACzB;AAOO,SAASM,EAEdC,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBS,8MAAWrB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE1C,OAAAH,EAA0B,MAAM;;QAC9BwB,EAAS,OAAA,GAAA,CAAUvB,IAAAG,EACjBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH/D,OAAA,KAAA,IAAAtB,EAIhB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHmB;AACT;AAOO,SAASC,EAEdF,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBW,8MAAYvB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE3C,OAAAH,EAA0B,MAAM;;QAC9B0B,EAAU,OAAA,GAAA,CAAUzB,IAAAG,EAClBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH9D,OAAA,KAAA,IAAAtB,EAIjB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHqB;AACT;AAMA,MAAMC,IAAqB,OAAO,GAAA,CAAI,eAAe,GAE/CC,IAAY,CAAKL,IACrBA,MAAS,QAAQ,OAAOA,KAAS,YAAY,cAAcA,KAAQA,EAAK,QAAA,KAAaI;AAKhF,SAASE,IAA4B;IAC1C,MAAMxB,IAAQU,EAAS,GACjB,CAACe,CAAU,CAAA,6MAAI3B,EAAM,SAAA,EAAS,IAAM,aAAA,GAAA,IAAI,KAA8B;IAG5E2B,EAAW,KAAA,CAAM;IACjB,IAAIX,IAAOd;IACX,MAAOc,GAAM;QACX,MAAMR,IAAUQ,EAAK,IAAA;QACjBS,EAAUjB,CAAO,KAAKA,MAAYE,KAAgB,CAACiB,EAAW,GAAA,CAAInB,CAAO,KAC3EmB,EAAW,GAAA,CAAInB,6MAASR,EAAM,IAAA,EAAIO,EAAYC,CAAO,CAAC,CAAC,GAGzDQ,IAAOA,EAAK,MAAA;IAAA;IAGP,OAAAW;AACT;AAYO,SAASC,IAAkC;IAChD,MAAMD,IAAaD,EAAc;IAGjC,iNAAO1B,EAAM,QAAA,EACX,IACE,MAAM,IAAA,CAAK2B,EAAW,IAAA,CAAA,CAAM,EAAE,MAAA,CAC5B,CAACE,GAAMrB,IAAY,CAACsB,IAEhB,aAAA,6MAAA9B,EAAA,cAAA,EAAC6B,GAAAA,MACE,aAAA,6MAAA7B,EAAA,cAAA,EAAAQ,EAAQ,QAAA,EAAR;oBAAkB,GAAGsB,CAAAA;oBAAO,OAAOH,EAAW,GAAA,CAAInB,CAAO;gBAAA,CAAG,CAC/D,GAEJ,CAACsB,IAAW,aAAA,6MAAA9B,EAAA,cAAA,EAAAW,GAAA;gBAAe,GAAGmB,CAAAA;YAAO,CAAA,IAEzC;QAACH,CAAU;KAAA;AAEf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1512, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "names": ["createDebounce", "callback", "ms", "timeoutId", "args", "useMeasure", "debounce", "scroll", "polyfill", "offsetSize", "ResizeObserver", "bounds", "set", "useState", "state", "useRef", "scrollDebounce", "resizeDebounce", "mounted", "useEffect", "forceRefresh", "resizeChange", "scrollChange", "useMemo", "left", "top", "width", "height", "bottom", "right", "x", "y", "size", "areBoundsEqual", "removeListeners", "element", "addListeners", "scrollContainer", "ref", "node", "findScrollContainers", "useOnWindowScroll", "useOnWindowResize", "onWindowResize", "cb", "onScroll", "enabled", "result", "overflow", "overflowX", "overflowY", "prop", "keys", "a", "b", "key"], "mappings": ";;;;;AAEA,SAASA,EAAmDC,CAAAA,EAAaC,CAAAA,CAAY;IAC/EC,IAAAA;IAEJ,OAAO,CAAA,GAAIC,IAA8B;QAChC,OAAA,YAAA,CAAaD,CAAS,GAC7BA,IAAY,OAAO,UAAA,CAAW,IAAMF,EAAS,GAAGG,CAAI,GAAGF,CAAE;IAC3D;AACF;AA0CA,SAASG,EACP,EAAE,UAAAC,CAAAA,EAAU,QAAAC,CAAAA,EAAQ,UAAAC,CAAAA,EAAU,YAAAC,CAAW,EAAA,GAAa;IAAE,UAAU;IAAG,QAAQ,CAAA;IAAO,YAAY,CAAA;AAAA,CAAA,CACxF;IACR,MAAMC,IACJF,KAAAA,CAAa,OAAO,UAAW,cAAc,KAAqB;IAAA,IAAM,OAAe,cAAA;IAEzF,IAAI,CAACE,GACH,MAAM,IAAI,MACR,gJACF;IAGF,MAAM,CAACC,GAAQC,CAAG,CAAA,GAAIC,qNAAAA,EAAuB;QAC3C,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,GAAG;QACH,GAAG;IAAA,CACJ,GAGKC,QAAQC,+MAAAA,EAAc;QAC1B,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,YAAYJ;QACZ,oBAAoB;IAAA,CACrB,GAGKK,IAAiBV,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAC1FW,IAAiBX,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAG1FY,uNAAUH,EAAO,CAAA,CAAK;0NAC5BI,EAAU,IAAA,CACRD,EAAQ,OAAA,GAAU,CAAA,GACX,IAAM,KAAA,CAAMA,EAAQ,OAAA,GAAU,CAAA,CAAA,CAAA,CACtC;IAGD,MAAM,CAACE,GAAcC,GAAcC,CAAY,CAAA,GAAIC,oNAAAA,EAAQ,IAAM;QAC/D,MAAMtB,IAAW,IAAM;YACjB,IAAA,CAACa,EAAM,OAAA,CAAQ,OAAA,EAAS;YACtB,MAAA,EAAE,MAAAU,CAAAA,EAAM,KAAAC,CAAAA,EAAK,OAAAC,CAAAA,EAAO,QAAAC,CAAAA,EAAQ,QAAAC,CAAAA,EAAQ,OAAAC,CAAAA,EAAO,GAAAC,CAAAA,EAAG,GAAAC,CAAE,EAAA,GACpDjB,EAAM,OAAA,CAAQ,OAAA,CAAQ,qBAAA,CAAsB,GAExCkB,IAAO;gBACX,MAAAR;gBACA,KAAAC;gBACA,OAAAC;gBACA,QAAAC;gBACA,QAAAC;gBACA,OAAAC;gBACA,GAAAC;gBACA,GAAAC;YACF;YAEIjB,EAAM,OAAA,CAAQ,OAAA,YAAmB,eAAeL,KAAAA,CAC7CuB,EAAA,MAAA,GAASlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,YAAA,EAC/BkB,EAAA,KAAA,GAAQlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,WAAA,GAGrC,OAAO,MAAA,CAAOkB,CAAI,GACdd,EAAQ,OAAA,IAAW,CAACe,EAAenB,EAAM,OAAA,CAAQ,UAAA,EAAYkB,CAAI,KAAGpB,EAAKE,EAAM,OAAA,CAAQ,UAAA,GAAakB,CAAK;QAC/G;QACO,OAAA;YACL/B;YACAgB,IAAiBjB,EAAeC,GAAUgB,CAAc,IAAIhB;YAC5De,IAAiBhB,EAAeC,GAAUe,CAAc,IAAIf,CAC9D;SAAA;IAAA,GACC;QAACW;QAAKH;QAAYO;QAAgBC,CAAc;KAAC;IAGpD,SAASiB,GAAkB;QACrBpB,EAAM,OAAA,CAAQ,gBAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASqB,KAAYA,EAAQ,mBAAA,CAAoB,UAAUb,GAAc,CAAA,CAAI,CAAC,GAC7GR,EAAM,OAAA,CAAQ,gBAAA,GAAmB,IAAA,GAG/BA,EAAM,OAAA,CAAQ,cAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,cAAA,CAAe,UAAA,CAAW,GACxCA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAA,GAG7BA,EAAM,OAAA,CAAQ,kBAAA,IAAA,CACZ,iBAAiB,UAAU,yBAAyB,OAAO,WAAA,GAC7D,OAAO,WAAA,CAAY,mBAAA,CAAoB,UAAUA,EAAM,OAAA,CAAQ,kBAAkB,IACxE,yBAAyB,UAClC,OAAO,mBAAA,CAAoB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAEpF;IAIF,SAASsB,GAAe;QACjBtB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACnBA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAIJ,EAAeY,CAAY,GAC9DR,EAAM,OAAA,CAAQ,cAAA,CAAgB,OAAA,CAAQA,EAAM,OAAA,CAAQ,OAAO,GACvDP,KAAUO,EAAM,OAAA,CAAQ,gBAAA,IAC1BA,EAAM,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASuB,KACtCA,EAAgB,gBAAA,CAAiB,UAAUf,GAAc;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAM,CAAA,CAC3F,GAIIR,EAAA,OAAA,CAAQ,kBAAA,GAAqB,IAAM;YAC1BQ,EAAA;QACf,GAGI,iBAAiB,UAAU,sBAAsB,OAAO,WAAA,GAC1D,OAAO,WAAA,CAAY,gBAAA,CAAiB,UAAUR,EAAM,OAAA,CAAQ,kBAAkB,IACrE,yBAAyB,UAElC,OAAO,gBAAA,CAAiB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAC/E;IAIIwB,MAAAA,KAAOC,GAAkC;QACzC,CAACA,KAAQA,MAASzB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACpBoB,EAAA,GAChBpB,EAAM,OAAA,CAAQ,OAAA,GAAUyB,GAClBzB,EAAA,OAAA,CAAQ,gBAAA,GAAmB0B,EAAqBD,CAAI,GAC7CH,EAAA,CAAA;IACf;IAGkBK,OAAAA,EAAAnB,GAAc,CAAQf,CAAAA,CAAO,GAC/CmC,EAAkBrB,CAAY,yNAG9BF,EAAU,IAAM;QACEe,EAAA,GACHE,EAAA;IACZ,GAAA;QAAC7B;QAAQe;QAAcD,CAAY;KAAC,IAG7BF,qNAAAA,EAAA,IAAMe,GAAiB,EAAE,GAC5B;QAACI;QAAK3B;QAAQS,CAAY;;AACnC;AAGA,SAASsB,EAAkBC,CAAAA,CAAwC;IACjExB,sNAAAA,EAAU,IAAM;QACd,MAAMyB,IAAKD;QACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUC,CAAE,GAC7B,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,CAAE;IAAA,GACxD;QAACD,CAAc;KAAC;AACrB;AACA,SAASF,EAAkBI,CAAAA,EAAsBC,CAAAA,CAAkB;0NACjE3B,EAAU,IAAM;QACd,IAAI2B,GAAS;YACX,MAAMF,IAAKC;YACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUD,GAAI;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAA,CAAM,GAC/D,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,GAAI,CAAA,CAAI;QAAA;IACjE,GACC;QAACC;QAAUC,CAAO;KAAC;AACxB;AAGA,SAASN,EAAqBL,CAAAA,CAAsD;IAClF,MAAMY,IAA6B,CAAC,CAAA;IACpC,IAAI,CAACZ,KAAWA,MAAY,SAAS,IAAA,EAAaY,OAAAA;IAC5C,MAAA,EAAE,UAAAC,CAAAA,EAAU,WAAAC,CAAAA,EAAW,WAAAC,CAAc,EAAA,GAAA,OAAO,gBAAA,CAAiBf,CAAO;IACtE,OAAA;QAACa;QAAUC;QAAWC,CAAS;KAAA,CAAE,IAAA,CAAMC,KAASA,MAAS,UAAUA,MAAS,QAAQ,KAAGJ,EAAO,IAAA,CAAKZ,CAAO,GACvG,CAAC;WAAGY,EAAQ;WAAGP,EAAqBL,EAAQ,aAAa,CAAC;;AACnE;AAGA,MAAMiB,IAA+B;IAAC;IAAK;IAAK;IAAO;IAAU;IAAQ;IAAS;IAAS,QAAQ;CAAA,EAC7FnB,IAAiB,CAACoB,GAAiBC,IAA6BF,EAAK,KAAA,EAAOG,IAAQF,CAAAA,CAAEE,CAAG,CAAA,KAAMD,CAAAA,CAAEC,CAAG,CAAC", "debugId": null}}, {"offset": {"line": 1653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40babel/runtime/helpers/esm/extends.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,2CAMjD,SAAS,KAAK,CAAC,MAAM;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/three-stdlib/node_modules/fflate/esm/index.mjs"], "sourcesContent": ["import { createRequire } from 'module';\nvar require = createRequire('/');\n// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\n// Mediocre shim\nvar Worker;\nvar workerAdd = \";var __w=require('worker_threads');__w.parentPort.on('message',function(m){onmessage({data:m})}),postMessage=function(m,t){__w.parentPort.postMessage(m,t)},close=process.exit;self=global\";\ntry {\n    Worker = require('worker_threads').Worker;\n}\ncatch (e) {\n}\nvar wk = Worker ? function (c, _, msg, transfer, cb) {\n    var done = false;\n    var w = new Worker(c + workerAdd, { eval: true })\n        .on('error', function (e) { return cb(e, null); })\n        .on('message', function (m) { return cb(null, m); })\n        .on('exit', function (c) {\n        if (c && !done)\n            cb(new Error('exited with code ' + c), null);\n    });\n    w.postMessage(msg, transfer);\n    w.terminate = function () {\n        done = true;\n        return Worker.prototype.terminate.call(w);\n    };\n    return w;\n} : function (_, __, ___, ____, cb) {\n    setImmediate(function () { return cb(new Error('async operations unsupported - update to Node 12+ (or Node 10-11 with the --experimental-worker CLI flag)'), null); });\n    var NOP = function () { };\n    return {\n        terminate: NOP,\n        postMessage: NOP\n    };\n};\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new u32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return [b, r];\n};\nvar _a = freb(fleb, 2), fl = _a[0], revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b[0], revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >>> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >>> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >>> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >>> 8) | ((x & 0x00FF) << 8)) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i)\n        ++l[cd[i] - 1];\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 0; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >>> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i) {\n            if (cd[i]) {\n                co[i] = rev[le[cd[i] - 1]++] >>> (15 - cd[i]);\n            }\n        }\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8)) >> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p / 8) | 0) + (p & 7 && 1); };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n    // source length\n    var sl = dat.length;\n    if (!sl || (st && !st.l && sl < 5))\n        return buf || new u8(0);\n    // have to estimate size\n    var noBuf = !buf || st;\n    // no state\n    var noSt = !st || st.i;\n    if (!st)\n        st = {};\n    // Assumes roughly 33% compression ratio average\n    if (!buf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            st.f = final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                // ensure size\n                if (noBuf)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >>> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                throw 'invalid block type';\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17;\n        if (noBuf)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var lpos = pos;\n        for (;; lpos = pos) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >>> 4;\n            pos += c & 15;\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n            if (!c)\n                throw 'invalid length/literal';\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lpos = pos, lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >>> 4;\n                if (!d)\n                    throw 'invalid distance';\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & ((1 << b) - 1), pos += b;\n                }\n                if (pos > tbts) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                if (noBuf)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                for (; bt < end; bt += 4) {\n                    buf[bt] = buf[bt - dt];\n                    buf[bt + 1] = buf[bt + 1 - dt];\n                    buf[bt + 2] = buf[bt + 2 - dt];\n                    buf[bt + 3] = buf[bt + 3 - dt];\n                }\n                bt = end;\n            }\n        }\n        st.l = lm, st.p = lpos, st.b = bt;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n    d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return [et, 0];\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return [v, 1];\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >>> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a[0], mlb = _a[1];\n    var _b = hTree(df, 15), ddt = _b[0], mdb = _b[1];\n    var _c = lc(dlt), lclt = _c[0], nlc = _c[1];\n    var _d = lc(ddt), lcdt = _d[0], ndc = _d[1];\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        lcfreq[lclt[i] & 31]++;\n    for (var i = 0; i < lcdt.length; ++i)\n        lcfreq[lcdt[i] & 31]++;\n    var _e = hTree(lcfreq, 7), lct = _e[0], mlcb = _e[1];\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n    if (flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >>> 5) & 127), p += clct[i] >>> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        if (syms[i] > 255) {\n            var len = (syms[i] >>> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (syms[i] >>> 23) & 31), p += fleb[len];\n            var dst = syms[i] & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (syms[i] >>> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n    var s = dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var pos = 0;\n    if (!lvl || s < 8) {\n        for (var i = 0; i <= s; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e < s) {\n                // write full block\n                pos = wfblk(w, pos, dat.subarray(i, e));\n            }\n            else {\n                // write final block\n                w[i] = lst;\n                pos = wfblk(w, pos, dat.subarray(i, s));\n            }\n        }\n    }\n    else {\n        var opt = deo[lvl - 1];\n        var n = opt >>> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = new u16(32768), head = new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new u32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n        var lc_1 = 0, eb = 0, i = 0, li = 0, wi = 0, bs = 0;\n        for (; i < s; ++i) {\n            // hash value\n            // deopt when i > s - 3 - at end, deopt acceptable\n            var hv = hsh(i);\n            // index mod 32768    previous index mod\n            var imod = i & 32767, pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = (imod - pimod) & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = (i - dif + j + 32768) & 32767;\n                                    var pti = prev[ti];\n                                    var cd = (ti - pti + 32768) & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += (imod - pimod + 32768) & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one Uint32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        // this is the easiest way to avoid needing to maintain state\n        if (!lst && pos & 7)\n            pos = wfblk(w, pos + 1, et);\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new Int32Array(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && -306674912) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = -1;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return ~c; }\n    };\n};\n// Alder32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 2655, l);\n                for (; i < e; ++i)\n                    m += n += d[i];\n                n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n            }\n            a = n, b = m;\n        },\n        d: function () {\n            a %= 65521, b %= 65521;\n            return (a & 255) << 24 | (a >>> 8) << 16 | (b & 255) << 8 | (b >>> 8);\n        }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : (12 + opt.mem), pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32)\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    var _a;\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n        ch[id] = wcln(fns[m], fnStr, td_1);\n    }\n    var td = mrg({}, ch[id][1]);\n    return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8]; };\nvar bDflt = function () { return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zlv]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get u8\nvar gu8 = function (o) { return o && o.size && new u8(o.size); };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) { return strm.push(ev.data[0], ev.data[1]); };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.push = function (d, f) {\n        if (t)\n            throw 'stream finished';\n        if (!strm.ondata)\n            throw 'no stream handler';\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16) | (d[b + 3] << 24)) >>> 0; };\nvar b8 = function (d, b) { return b4(d, b) + (b4(d, b + 4) * 4294967296); };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        throw 'invalid gzip data';\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += d[10] | (d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return ((d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) | (d[l - 1] << 24)) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + ((o.filename && (o.filename.length + 1)) || 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (fl ? (32 - 2 * fl) : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n    if ((d[0] & 15) != 8 || (d[0] >>> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        throw 'invalid zlib data';\n    if (d[1] & 32)\n        throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n    if (!cb && typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (!cb && typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, !f), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        this.d = final;\n        this.p(chunk, final || false);\n    };\n    return Deflate;\n}());\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6);\n    }\n    return AsyncDeflate;\n}());\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n    return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an inflation stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Inflate(cb) {\n        this.s = {};\n        this.p = new u8(0);\n        this.ondata = cb;\n    }\n    Inflate.prototype.e = function (c) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        var l = this.p.length;\n        var n = new u8(l + c.length);\n        n.set(this.p), n.set(c, l), this.p = n;\n    };\n    Inflate.prototype.c = function (final) {\n        this.d = this.s.i = final || false;\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.o, this.s);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) | 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous inflation stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncInflate(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, 0, function () {\n            var strm = new Inflate();\n            onmessage = astrm(strm);\n        }, 7);\n    }\n    return AsyncInflate;\n}());\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gu8(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n    return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        this.c.p(c);\n        this.l += c.length;\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    return Gzip;\n}());\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8);\n    }\n    return AsyncGzip;\n}());\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a GUNZIP stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Gunzip(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            var s = this.p.length > 3 ? gzs(this.p) : 4;\n            if (s >= this.p.length && !final)\n                return;\n            this.p = this.p.subarray(s), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 8)\n                throw 'invalid gzip stream';\n            this.p = this.p.subarray(0, -8);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Gunzip;\n}());\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous GUNZIP stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncGunzip(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, 0, function () {\n            var strm = new Gunzip();\n            onmessage = astrm(strm);\n        }, 9);\n    }\n    return AsyncGunzip;\n}());\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n    return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        this.c.p(c);\n        var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    return Zlib;\n}());\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10);\n    }\n    return AsyncZlib;\n}());\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zlib decompression stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Unzlib(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 2 && !final)\n                return;\n            this.p = this.p.subarray(2), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                throw 'invalid zlib stream';\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous Zlib decompression stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncUnzlib(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, 0, function () {\n            var strm = new Unzlib();\n            onmessage = astrm(strm);\n        }, 11);\n    }\n    return AsyncUnzlib;\n}());\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gu8(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n    return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a decompression stream\n     * @param cb The callback to call whenever data is decompressed\n     */\n    function Decompress(cb) {\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no stream handler';\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                var _this_1 = this;\n                var cb = function () { _this_1.ondata.apply(_this_1, arguments); };\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(cb)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(cb)\n                        : new this.Z(cb);\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    /**\n   * Creates an asynchronous decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n    function AsyncDecompress(cb) {\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, out)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, out)\n            : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k;\n        if (val instanceof u8)\n            t[n] = [val, o];\n        else if (Array.isArray(val))\n            t[n] = [val[0], mrg(o, val[1])];\n        else\n            fltn(val, n + '/', t, o);\n    }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/ new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/ new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n    td.decode(et, { stream: true });\n    tds = 1;\n}\ncatch (e) { }\n// decode UTF8\nvar dutf8 = function (d) {\n    for (var r = '', i = 0;;) {\n        var c = d[i++];\n        var eb = (c > 127) + (c > 223) + (c > 239);\n        if (i + eb > d.length)\n            return [r, slc(d, i - 1)];\n        if (!eb)\n            r += String.fromCharCode(c);\n        else if (eb == 3) {\n            c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n        }\n        else if (eb & 1)\n            r += String.fromCharCode((c & 31) << 6 | (d[i++] & 63));\n        else\n            r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63));\n    }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is decoded\n     */\n    function DecodeUTF8(cb) {\n        this.ondata = cb;\n        if (tds)\n            this.t = new TextDecoder();\n        else\n            this.p = et;\n    }\n    /**\n     * Pushes a chunk to be decoded from UTF-8 binary\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    DecodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        final = !!final;\n        if (this.t) {\n            this.ondata(this.t.decode(chunk, { stream: true }), final);\n            if (final) {\n                if (this.t.decode().length)\n                    throw 'invalid utf-8 data';\n                this.t = null;\n            }\n            return;\n        }\n        if (!this.p)\n            throw 'stream finished';\n        var dat = new u8(this.p.length + chunk.length);\n        dat.set(this.p);\n        dat.set(chunk, this.p.length);\n        var _a = dutf8(dat), ch = _a[0], np = _a[1];\n        if (final) {\n            if (np.length)\n                throw 'invalid utf-8 data';\n            this.p = null;\n        }\n        else\n            this.p = np;\n        this.ondata(ch, final);\n    };\n    return DecodeUTF8;\n}());\nexport { DecodeUTF8 };\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is encoded\n     */\n    function EncodeUTF8(cb) {\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be encoded to UTF-8\n     * @param chunk The string data to push\n     * @param final Whether this is the last chunk\n     */\n    EncodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        if (this.d)\n            throw 'stream finished';\n        this.ondata(strToU8(chunk), this.d = final || false);\n    };\n    return EncodeUTF8;\n}());\nexport { EncodeUTF8 };\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n    if (latin1) {\n        var ar_1 = new u8(str.length);\n        for (var i = 0; i < str.length; ++i)\n            ar_1[i] = str.charCodeAt(i);\n        return ar_1;\n    }\n    if (te)\n        return te.encode(str);\n    var l = str.length;\n    var ar = new u8(str.length + (str.length >> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >> 18)), w(128 | ((c >> 12) & 63)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >> 12)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n    if (latin1) {\n        var r = '';\n        for (var i = 0; i < dat.length; i += 16384)\n            r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n        return r;\n    }\n    else if (td)\n        return td.decode(dat);\n    else {\n        var _a = dutf8(dat), out = _a[0], ext = _a[1];\n        if (ext.length)\n            throw 'invalid utf-8 data';\n        return out;\n    }\n}\n;\n// deflate bit flag\nvar dbf = function (l) { return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0; };\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl, bs = b4(d, b + 20);\n    var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n    var le = 0;\n    if (ex) {\n        for (var k in ex) {\n            var l = ex[k].length;\n            if (l > 65535)\n                throw 'extra field too long';\n            le += l + 4;\n        }\n    }\n    return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n    var fl = fn.length, ex = f.extra, col = co && co.length;\n    var exl = exfl(ex);\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b++] = 20, d[b++] = f.os;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (f.flag << 1) | (c == null && 8), d[b++] = u && 8;\n    d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n    var dt = new Date(f.mtime == null ? Date.now() : f.mtime), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        throw 'date not in range 1980-2099';\n    wbytes(d, b, (y << 25) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >>> 1)), b += 4;\n    if (c != null) {\n        wbytes(d, b, f.crc);\n        wbytes(d, b + 4, c);\n        wbytes(d, b + 8, f.size);\n    }\n    wbytes(d, b + 12, fl);\n    wbytes(d, b + 14, exl), b += 16;\n    if (ce != null) {\n        wbytes(d, b, col);\n        wbytes(d, b + 6, f.attrs);\n        wbytes(d, b + 10, ce), b += 14;\n    }\n    d.set(fn, b);\n    b += fl;\n    if (exl) {\n        for (var k in ex) {\n            var exf = ex[k], l = exf.length;\n            wbytes(d, b, +k);\n            wbytes(d, b + 2, l);\n            d.set(exf, b + 4), b += 4 + l;\n        }\n    }\n    if (col)\n        d.set(co, b), b += col;\n    return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/ (function () {\n    /**\n     * Creates a pass-through stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     */\n    function ZipPassThrough(filename) {\n        this.filename = filename;\n        this.c = crc();\n        this.size = 0;\n        this.compression = 0;\n    }\n    /**\n     * Processes a chunk and pushes to the output stream. You can override this\n     * method in a subclass for custom behavior, but by default this passes\n     * the data through. You must call this.ondata(err, chunk, final) at some\n     * point in this method.\n     * @param chunk The chunk to process\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.process = function (chunk, final) {\n        this.ondata(null, chunk, final);\n    };\n    /**\n     * Pushes a chunk to be added. If you are subclassing this with a custom\n     * compression algorithm, note that you must push data from the source\n     * file only, pre-compression.\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback - add to ZIP archive before pushing';\n        this.c.p(chunk);\n        this.size += chunk.length;\n        if (final)\n            this.crc = this.c.d();\n        this.process(chunk, final || false);\n    };\n    return ZipPassThrough;\n}());\nexport { ZipPassThrough };\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function ZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new Deflate(opts, function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n    }\n    ZipDeflate.prototype.process = function (chunk, final) {\n        try {\n            this.d.push(chunk, final);\n        }\n        catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return ZipDeflate;\n}());\nexport { ZipDeflate };\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function AsyncZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new AsyncDeflate(opts, function (err, dat, final) {\n            _this_1.ondata(err, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n        this.terminate = this.d.terminate;\n    }\n    AsyncZipDeflate.prototype.process = function (chunk, final) {\n        this.d.push(chunk, final);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return AsyncZipDeflate;\n}());\nexport { AsyncZipDeflate };\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an empty ZIP archive to which files can be added\n     * @param cb The callback to call whenever data for the generated ZIP archive\n     *           is available\n     */\n    function Zip(cb) {\n        this.ondata = cb;\n        this.u = [];\n        this.d = 1;\n    }\n    /**\n     * Adds a file to the ZIP archive\n     * @param file The file stream to add\n     */\n    Zip.prototype.add = function (file) {\n        var _this_1 = this;\n        if (this.d & 2)\n            throw 'stream finished';\n        var f = strToU8(file.filename), fl = f.length;\n        var com = file.comment, o = com && strToU8(com);\n        var u = fl != file.filename.length || (o && (com.length != o.length));\n        var hl = fl + exfl(file.extra) + 30;\n        if (fl > 65535)\n            throw 'filename too long';\n        var header = new u8(hl);\n        wzh(header, 0, file, f, u);\n        var chks = [header];\n        var pAll = function () {\n            for (var _i = 0, chks_1 = chks; _i < chks_1.length; _i++) {\n                var chk = chks_1[_i];\n                _this_1.ondata(null, chk, false);\n            }\n            chks = [];\n        };\n        var tr = this.d;\n        this.d = 0;\n        var ind = this.u.length;\n        var uf = mrg(file, {\n            f: f,\n            u: u,\n            o: o,\n            t: function () {\n                if (file.terminate)\n                    file.terminate();\n            },\n            r: function () {\n                pAll();\n                if (tr) {\n                    var nxt = _this_1.u[ind + 1];\n                    if (nxt)\n                        nxt.r();\n                    else\n                        _this_1.d = 1;\n                }\n                tr = 1;\n            }\n        });\n        var cl = 0;\n        file.ondata = function (err, dat, final) {\n            if (err) {\n                _this_1.ondata(err, dat, final);\n                _this_1.terminate();\n            }\n            else {\n                cl += dat.length;\n                chks.push(dat);\n                if (final) {\n                    var dd = new u8(16);\n                    wbytes(dd, 0, 0x8074B50);\n                    wbytes(dd, 4, file.crc);\n                    wbytes(dd, 8, cl);\n                    wbytes(dd, 12, file.size);\n                    chks.push(dd);\n                    uf.c = cl, uf.b = hl + cl + 16, uf.crc = file.crc, uf.size = file.size;\n                    if (tr)\n                        uf.r();\n                    tr = 1;\n                }\n                else if (tr)\n                    pAll();\n            }\n        };\n        this.u.push(uf);\n    };\n    /**\n     * Ends the process of adding files and prepares to emit the final chunks.\n     * This *must* be called after adding all desired files for the resulting\n     * ZIP file to work properly.\n     */\n    Zip.prototype.end = function () {\n        var _this_1 = this;\n        if (this.d & 2) {\n            if (this.d & 1)\n                throw 'stream finishing';\n            throw 'stream finished';\n        }\n        if (this.d)\n            this.e();\n        else\n            this.u.push({\n                r: function () {\n                    if (!(_this_1.d & 1))\n                        return;\n                    _this_1.u.splice(-1, 1);\n                    _this_1.e();\n                },\n                t: function () { }\n            });\n        this.d = 3;\n    };\n    Zip.prototype.e = function () {\n        var bt = 0, l = 0, tl = 0;\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n        }\n        var out = new u8(tl + 22);\n        for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n            var f = _c[_b];\n            wzh(out, bt, f, f.f, f.u, f.c, l, f.o);\n            bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n        }\n        wzf(out, bt, this.u.length, tl, l);\n        this.ondata(null, out, true);\n        this.d = 2;\n    };\n    /**\n     * A method to terminate any internal workers used by the stream. Subsequent\n     * calls to add() will fail.\n     */\n    Zip.prototype.terminate = function () {\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            f.t();\n        }\n        this.d = 2;\n    };\n    return Zip;\n}());\nexport { Zip };\nexport function zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                var l = f.c.length;\n                wzh(out, tot, f, f.f, f.u, l);\n                var badd = 30 + f.f.length + exfl(f.extra);\n                var loc = tot + badd;\n                out.set(f.c, loc);\n                wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n            }\n            catch (e) {\n                return cb(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cb(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), size = file.length;\n        c.p(file);\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        var compression = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = mrg(p, {\n                    size: size,\n                    crc: c.d(),\n                    c: d,\n                    f: f,\n                    m: m,\n                    u: s != fn.length || (m && (com.length != ms)),\n                    compression: compression\n                });\n                o += 30 + s + exl + l;\n                tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (s > 65535)\n            cbl('filename too long', null);\n        if (!compression)\n            cbl(null, file);\n        else if (size < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var compression = p.level == 0 ? 0 : 8;\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        if (s > 65535)\n            throw 'filename too long';\n        var d = compression ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push(mrg(p, {\n            size: file.length,\n            crc: c.d(),\n            c: d,\n            f: f,\n            m: m,\n            u: s != fn.length || (m && (com.length != ms)),\n            o: o,\n            compression: compression\n        }));\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f, f.f, f.u, f.c.length);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        out.set(f.c, f.o + badd);\n        wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/ (function () {\n    function UnzipPassThrough() {\n    }\n    UnzipPassThrough.prototype.push = function (data, final) {\n        this.ondata(null, data, final);\n    };\n    UnzipPassThrough.compression = 0;\n    return UnzipPassThrough;\n}());\nexport { UnzipPassThrough };\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function UnzipInflate() {\n        var _this_1 = this;\n        this.i = new Inflate(function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n    }\n    UnzipInflate.prototype.push = function (data, final) {\n        try {\n            this.i.push(data, final);\n        }\n        catch (e) {\n            this.ondata(e, data, final);\n        }\n    };\n    UnzipInflate.compression = 8;\n    return UnzipInflate;\n}());\nexport { UnzipInflate };\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function AsyncUnzipInflate(_, sz) {\n        var _this_1 = this;\n        if (sz < 320000) {\n            this.i = new Inflate(function (dat, final) {\n                _this_1.ondata(null, dat, final);\n            });\n        }\n        else {\n            this.i = new AsyncInflate(function (err, dat, final) {\n                _this_1.ondata(err, dat, final);\n            });\n            this.terminate = this.i.terminate;\n        }\n    }\n    AsyncUnzipInflate.prototype.push = function (data, final) {\n        if (this.i.terminate)\n            data = slc(data, 0);\n        this.i.push(data, final);\n    };\n    AsyncUnzipInflate.compression = 8;\n    return AsyncUnzipInflate;\n}());\nexport { AsyncUnzipInflate };\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a ZIP decompression stream\n     * @param cb The callback to call whenever a file in the ZIP archive is found\n     */\n    function Unzip(cb) {\n        this.onfile = cb;\n        this.k = [];\n        this.o = {\n            0: UnzipPassThrough\n        };\n        this.p = et;\n    }\n    /**\n     * Pushes a chunk to be unzipped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzip.prototype.push = function (chunk, final) {\n        var _this_1 = this;\n        if (!this.onfile)\n            throw 'no callback';\n        if (!this.p)\n            throw 'stream finished';\n        if (this.c > 0) {\n            var len = Math.min(this.c, chunk.length);\n            var toAdd = chunk.subarray(0, len);\n            this.c -= len;\n            if (this.d)\n                this.d.push(toAdd, !this.c);\n            else\n                this.k[0].push(toAdd);\n            chunk = chunk.subarray(len);\n            if (chunk.length)\n                return this.push(chunk, final);\n        }\n        else {\n            var f = 0, i = 0, is = void 0, buf = void 0;\n            if (!this.p.length)\n                buf = chunk;\n            else if (!chunk.length)\n                buf = this.p;\n            else {\n                buf = new u8(this.p.length + chunk.length);\n                buf.set(this.p), buf.set(chunk, this.p.length);\n            }\n            var l = buf.length, oc = this.c, add = oc && this.d;\n            var _loop_2 = function () {\n                var _a;\n                var sig = b4(buf, i);\n                if (sig == 0x4034B50) {\n                    f = 1, is = i;\n                    this_1.d = null;\n                    this_1.c = 0;\n                    var bf = b2(buf, i + 6), cmp_1 = b2(buf, i + 8), u = bf & 2048, dd = bf & 8, fnl = b2(buf, i + 26), es = b2(buf, i + 28);\n                    if (l > i + 30 + fnl + es) {\n                        var chks_2 = [];\n                        this_1.k.unshift(chks_2);\n                        f = 2;\n                        var sc_1 = b4(buf, i + 18), su_1 = b4(buf, i + 22);\n                        var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n                        if (sc_1 == 4294967295) {\n                            _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n                        }\n                        else if (dd)\n                            sc_1 = -1;\n                        i += es;\n                        this_1.c = sc_1;\n                        var d_1;\n                        var file_1 = {\n                            name: fn_1,\n                            compression: cmp_1,\n                            start: function () {\n                                if (!file_1.ondata)\n                                    throw 'no callback';\n                                if (!sc_1)\n                                    file_1.ondata(null, et, true);\n                                else {\n                                    var ctr = _this_1.o[cmp_1];\n                                    if (!ctr)\n                                        throw 'unknown compression type ' + cmp_1;\n                                    d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                                    d_1.ondata = function (err, dat, final) { file_1.ondata(err, dat, final); };\n                                    for (var _i = 0, chks_3 = chks_2; _i < chks_3.length; _i++) {\n                                        var dat = chks_3[_i];\n                                        d_1.push(dat, false);\n                                    }\n                                    if (_this_1.k[0] == chks_2 && _this_1.c)\n                                        _this_1.d = d_1;\n                                    else\n                                        d_1.push(et, true);\n                                }\n                            },\n                            terminate: function () {\n                                if (d_1 && d_1.terminate)\n                                    d_1.terminate();\n                            }\n                        };\n                        if (sc_1 >= 0)\n                            file_1.size = sc_1, file_1.originalSize = su_1;\n                        this_1.onfile(file_1);\n                    }\n                    return \"break\";\n                }\n                else if (oc) {\n                    if (sig == 0x8074B50) {\n                        is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                    else if (sig == 0x2014B50) {\n                        is = i -= 4, f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                }\n            };\n            var this_1 = this;\n            for (; i < l - 4; ++i) {\n                var state_1 = _loop_2();\n                if (state_1 === \"break\")\n                    break;\n            }\n            this.p = et;\n            if (oc < 0) {\n                var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n                if (add)\n                    add.push(dat, !!f);\n                else\n                    this.k[+(f == 2)].push(dat);\n            }\n            if (f & 2)\n                return this.push(buf.subarray(i), final);\n            this.p = buf.subarray(i);\n        }\n        if (final) {\n            if (this.c)\n                throw 'invalid zip file';\n            this.p = null;\n        }\n    };\n    /**\n     * Registers a decoder with the stream, allowing for files compressed with\n     * the compression type provided to be expanded correctly\n     * @param decoder The decoder constructor\n     */\n    Unzip.prototype.register = function (decoder) {\n        this.o[decoder.compression] = decoder;\n    };\n    return Unzip;\n}());\nexport { Unzip };\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cb('invalid zip file', null);\n            return;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (!lft)\n        cb(null, {});\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50) {\n            cb('invalid zip file', null);\n            return;\n        }\n        c = lft = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    var _loop_3 = function (i) {\n        var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                files[fn] = d;\n                if (!--lft)\n                    cb(null, files);\n            }\n        };\n        if (!c_1)\n            cbl(null, slc(data, b, b + sc));\n        else if (c_1 == 8) {\n            var infl = data.subarray(b, b + sc);\n            if (sc < 320000) {\n                try {\n                    cbl(null, inflateSync(infl, new u8(su)));\n                }\n                catch (e) {\n                    cbl(e, null);\n                }\n            }\n            else\n                term.push(inflate(infl, { size: su }, cbl));\n        }\n        else\n            cbl('unknown compression type ' + c_1, null);\n    };\n    for (var i = 0; i < c; ++i) {\n        _loop_3(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            throw 'invalid zip file';\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!c_2)\n            files[fn] = slc(data, b, b + sc);\n        else if (c_2 == 8)\n            files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));\n        else\n            throw 'unknown compression type ' + c_2;\n    }\n    return files;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA,IAAI,UAAU,CAAA,GAAA,qGAAA,CAAA,gBAAa,AAAD,EAAE;AAC5B,2FAA2F;AAC3F,sCAAsC;AACtC,2EAA2E;AAC3E,qEAAqE;AACrE,4DAA4D;AAC5D,sCAAsC;AACtC,uHAAuH;AACvH,2FAA2F;AAC3F,oDAAoD;AACpD,gBAAgB;AAChB,IAAI;AACJ,IAAI,YAAY;AAChB,IAAI;IACA,SAAS,QAAQ,kBAAkB,MAAM;AAC7C,EACA,OAAO,GAAG,CACV;AACA,IAAI,KAAK,SAAS,SAAU,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE;IAC/C,IAAI,OAAO;IACX,IAAI,IAAI,IAAI,OAAO,IAAI,WAAW;QAAE,MAAM;IAAK,GAC1C,EAAE,CAAC,SAAS,SAAU,CAAC;QAAI,OAAO,GAAG,GAAG;IAAO,GAC/C,EAAE,CAAC,WAAW,SAAU,CAAC;QAAI,OAAO,GAAG,MAAM;IAAI,GACjD,EAAE,CAAC,QAAQ,SAAU,CAAC;QACvB,IAAI,KAAK,CAAC,MACN,GAAG,IAAI,MAAM,sBAAsB,IAAI;IAC/C;IACA,EAAE,WAAW,CAAC,KAAK;IACnB,EAAE,SAAS,GAAG;QACV,OAAO;QACP,OAAO,OAAO,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;IAC3C;IACA,OAAO;AACX,IAAI,SAAU,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IAC9B,aAAa;QAAc,OAAO,GAAG,IAAI,MAAM,8GAA8G;IAAO;IACpK,IAAI,MAAM,YAAc;IACxB,OAAO;QACH,WAAW;QACX,aAAa;IACjB;AACJ;AAEA,oEAAoE;AACpE,IAAI,KAAK,YAAY,MAAM,aAAa,MAAM;AAC9C,0BAA0B;AAC1B,IAAI,OAAO,IAAI,GAAG;IAAC;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,UAAU,GAAG;IAAG;IAAG,cAAc,GAAG;CAAE;AAChJ,4BAA4B;AAC5B,gBAAgB;AAChB,IAAI,OAAO,IAAI,GAAG;IAAC;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI,UAAU,GAAG;IAAG;CAAE;AACvI,wBAAwB;AACxB,IAAI,OAAO,IAAI,GAAG;IAAC;IAAI;IAAI;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAI;IAAG;IAAI;IAAG;IAAI;IAAG;CAAG;AACpF,8CAA8C;AAC9C,IAAI,OAAO,SAAU,EAAE,EAAE,KAAK;IAC1B,IAAI,IAAI,IAAI,IAAI;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACzB,CAAC,CAAC,EAAE,GAAG,SAAS,KAAK,EAAE,CAAC,IAAI,EAAE;IAClC;IACA,kCAAkC;IAClC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACzB,IAAK,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAG;YAClC,CAAC,CAAC,EAAE,GAAG,AAAE,IAAI,CAAC,CAAC,EAAE,IAAK,IAAK;QAC/B;IACJ;IACA,OAAO;QAAC;QAAG;KAAE;AACjB;AACA,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;AACjD,oFAAoF;AACpF,EAAE,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,IAAI,GAAG;AAC3B,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;AACjD,6CAA6C;AAC7C,IAAI,MAAM,IAAI,IAAI;AAClB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;IAC5B,kCAAkC;IAClC,IAAI,IAAI,AAAC,CAAC,IAAI,MAAM,MAAM,IAAM,CAAC,IAAI,MAAM,KAAK;IAChD,IAAI,AAAC,CAAC,IAAI,MAAM,MAAM,IAAM,CAAC,IAAI,MAAM,KAAK;IAC5C,IAAI,AAAC,CAAC,IAAI,MAAM,MAAM,IAAM,CAAC,IAAI,MAAM,KAAK;IAC5C,GAAG,CAAC,EAAE,GAAG,CAAC,AAAC,CAAC,IAAI,MAAM,MAAM,IAAM,CAAC,IAAI,MAAM,KAAK,CAAE,MAAM;AAC9D;AACA,yEAAyE;AACzE,mCAAmC;AACnC,2BAA2B;AAC3B,IAAI,OAAQ,SAAU,EAAE,EAAE,EAAE,EAAE,CAAC;IAC3B,IAAI,IAAI,GAAG,MAAM;IACjB,QAAQ;IACR,IAAI,IAAI;IACR,yDAAyD;IACzD,IAAI,IAAI,IAAI,IAAI;IAChB,8CAA8C;IAC9C,MAAO,IAAI,GAAG,EAAE,EACZ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;IAClB,0DAA0D;IAC1D,IAAI,KAAK,IAAI,IAAI;IACjB,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACrB,EAAE,CAAC,EAAE,GAAG,AAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,IAAK;IACtC;IACA,IAAI;IACJ,IAAI,GAAG;QACH,6DAA6D;QAC7D,KAAK,IAAI,IAAI,KAAK;QAClB,8BAA8B;QAC9B,IAAI,MAAM,KAAK;QACf,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACpB,mBAAmB;YACnB,IAAI,EAAE,CAAC,EAAE,EAAE;gBACP,yCAAyC;gBACzC,IAAI,KAAK,AAAC,KAAK,IAAK,EAAE,CAAC,EAAE;gBACzB,YAAY;gBACZ,IAAI,MAAM,KAAK,EAAE,CAAC,EAAE;gBACpB,cAAc;gBACd,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM;gBAC3B,iBAAiB;gBACjB,IAAK,IAAI,IAAI,IAAK,CAAC,KAAK,GAAG,IAAI,GAAI,KAAK,GAAG,EAAE,EAAG;oBAC5C,mEAAmE;oBACnE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,IAAI,GAAG;gBACzB;YACJ;QACJ;IACJ,OACK;QACD,KAAK,IAAI,IAAI;QACb,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACpB,IAAI,EAAE,CAAC,EAAE,EAAE;gBACP,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,KAAM,KAAK,EAAE,CAAC,EAAE;YAChD;QACJ;IACJ;IACA,OAAO;AACX;AACA,oBAAoB;AACpB,IAAI,MAAM,IAAI,GAAG;AACjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EACvB,GAAG,CAAC,EAAE,GAAG;AACb,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,EACzB,GAAG,CAAC,EAAE,GAAG;AACb,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,EACzB,GAAG,CAAC,EAAE,GAAG;AACb,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,EACzB,GAAG,CAAC,EAAE,GAAG;AACb,sBAAsB;AACtB,IAAI,MAAM,IAAI,GAAG;AACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EACtB,GAAG,CAAC,EAAE,GAAG;AACb,mBAAmB;AACnB,IAAI,MAAM,WAAW,GAAG,KAAK,KAAK,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,KAAK,GAAG;AAC3E,qBAAqB;AACrB,IAAI,MAAM,WAAW,GAAG,KAAK,KAAK,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,KAAK,GAAG;AAC3E,oBAAoB;AACpB,IAAI,MAAM,SAAU,CAAC;IACjB,IAAI,IAAI,CAAC,CAAC,EAAE;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,EAAE,EAAG;QAC/B,IAAI,CAAC,CAAC,EAAE,GAAG,GACP,IAAI,CAAC,CAAC,EAAE;IAChB;IACA,OAAO;AACX;AACA,4CAA4C;AAC5C,IAAI,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACxB,IAAI,IAAI,AAAC,IAAI,IAAK;IAClB,OAAO,AAAC,CAAC,CAAC,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAE,KAAK,CAAC,IAAI,CAAC,IAAK;AACnD;AACA,4DAA4D;AAC5D,IAAI,SAAS,SAAU,CAAC,EAAE,CAAC;IACvB,IAAI,IAAI,AAAC,IAAI,IAAK;IAClB,OAAQ,CAAC,CAAC,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,IAAI,IAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAG,KAAK,CAAC,IAAI,CAAC;AACjE;AACA,kBAAkB;AAClB,IAAI,OAAO,SAAU,CAAC;IAAI,OAAO,CAAC,AAAC,IAAI,IAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;AAAG;AAC/D,2EAA2E;AAC3E,0CAA0C;AAC1C,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACvB,IAAI,KAAK,QAAQ,IAAI,GACjB,IAAI;IACR,IAAI,KAAK,QAAQ,IAAI,EAAE,MAAM,EACzB,IAAI,EAAE,MAAM;IAChB,+CAA+C;IAC/C,IAAI,IAAI,IAAI,CAAC,aAAa,MAAM,MAAM,aAAa,MAAM,MAAM,EAAE,EAAE,IAAI;IACvE,EAAE,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG;IACpB,OAAO;AACX;AACA,2BAA2B;AAC3B,IAAI,QAAQ,SAAU,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9B,gBAAgB;IAChB,IAAI,KAAK,IAAI,MAAM;IACnB,IAAI,CAAC,MAAO,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,GAC5B,OAAO,OAAO,IAAI,GAAG;IACzB,wBAAwB;IACxB,IAAI,QAAQ,CAAC,OAAO;IACpB,WAAW;IACX,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;IACtB,IAAI,CAAC,IACD,KAAK,CAAC;IACV,gDAAgD;IAChD,IAAI,CAAC,KACD,MAAM,IAAI,GAAG,KAAK;IACtB,4CAA4C;IAC5C,IAAI,OAAO,SAAU,CAAC;QAClB,IAAI,KAAK,IAAI,MAAM;QACnB,+BAA+B;QAC/B,IAAI,IAAI,IAAI;YACR,mDAAmD;YACnD,IAAI,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG;YACnC,KAAK,GAAG,CAAC;YACT,MAAM;QACV;IACJ;IACA,6CAA6C;IAC7C,IAAI,QAAQ,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC;IACpG,aAAa;IACb,IAAI,OAAO,KAAK;IAChB,GAAG;QACC,IAAI,CAAC,IAAI;YACL,kDAAkD;YAClD,GAAG,CAAC,GAAG,QAAQ,KAAK,KAAK,KAAK;YAC9B,mEAAmE;YACnE,IAAI,OAAO,KAAK,KAAK,MAAM,GAAG;YAC9B,OAAO;YACP,IAAI,CAAC,MAAM;gBACP,6BAA6B;gBAC7B,IAAI,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,GAAI,GAAG,CAAC,IAAI,EAAE,IAAI,GAAI,IAAI,IAAI;gBACnE,IAAI,IAAI,IAAI;oBACR,IAAI,MACA,MAAM;oBACV;gBACJ;gBACA,cAAc;gBACd,IAAI,OACA,KAAK,KAAK;gBACd,8BAA8B;gBAC9B,IAAI,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,IAAI;gBAC5B,oCAAoC;gBACpC,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,GAAG,MAAM,IAAI;gBACjC;YACJ,OACK,IAAI,QAAQ,GACb,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG,MAAM;iBACpC,IAAI,QAAQ,GAAG;gBAChB,8CAA8C;gBAC9C,IAAI,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,MAAM;gBACvE,IAAI,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG,MAAM;gBACzC,OAAO;gBACP,uBAAuB;gBACvB,IAAI,MAAM,IAAI,GAAG;gBACjB,mBAAmB;gBACnB,IAAI,MAAM,IAAI,GAAG;gBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;oBAC5B,iCAAiC;oBACjC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,IAAI,GAAG;gBAC1C;gBACA,OAAO,QAAQ;gBACf,oBAAoB;gBACpB,IAAI,MAAM,IAAI,MAAM,SAAS,CAAC,KAAK,GAAG,IAAI;gBAC1C,mBAAmB;gBACnB,IAAI,MAAM,KAAK,KAAK,KAAK;gBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAK;oBACrB,IAAI,IAAI,GAAG,CAAC,KAAK,KAAK,KAAK,QAAQ;oBACnC,YAAY;oBACZ,OAAO,IAAI;oBACX,SAAS;oBACT,IAAI,IAAI,MAAM;oBACd,sBAAsB;oBACtB,IAAI,IAAI,IAAI;wBACR,GAAG,CAAC,IAAI,GAAG;oBACf,OACK;wBACD,gBAAgB;wBAChB,IAAI,IAAI,GAAG,IAAI;wBACf,IAAI,KAAK,IACL,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE;6BAClD,IAAI,KAAK,IACV,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO;6BACjC,IAAI,KAAK,IACV,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,OAAO;wBACzC,MAAO,IACH,GAAG,CAAC,IAAI,GAAG;oBACnB;gBACJ;gBACA,+CAA+C;gBAC/C,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,OAAO,KAAK,IAAI,QAAQ,CAAC;gBAClD,kBAAkB;gBAClB,MAAM,IAAI;gBACV,gBAAgB;gBAChB,MAAM,IAAI;gBACV,KAAK,KAAK,IAAI,KAAK;gBACnB,KAAK,KAAK,IAAI,KAAK;YACvB,OAEI,MAAM;YACV,IAAI,MAAM,MAAM;gBACZ,IAAI,MACA,MAAM;gBACV;YACJ;QACJ;QACA,qEAAqE;QACrE,oEAAoE;QACpE,IAAI,OACA,KAAK,KAAK;QACd,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI;QAC7C,IAAI,OAAO;QACX,OAAQ,OAAO,IAAK;YAChB,kBAAkB;YAClB,IAAI,IAAI,EAAE,CAAC,OAAO,KAAK,OAAO,IAAI,EAAE,MAAM,MAAM;YAChD,OAAO,IAAI;YACX,IAAI,MAAM,MAAM;gBACZ,IAAI,MACA,MAAM;gBACV;YACJ;YACA,IAAI,CAAC,GACD,MAAM;YACV,IAAI,MAAM,KACN,GAAG,CAAC,KAAK,GAAG;iBACX,IAAI,OAAO,KAAK;gBACjB,OAAO,KAAK,KAAK;gBACjB;YACJ,OACK;gBACD,IAAI,MAAM,MAAM;gBAChB,+BAA+B;gBAC/B,IAAI,MAAM,KAAK;oBACX,QAAQ;oBACR,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,EAAE;oBAC9B,MAAM,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE;oBAC1C,OAAO;gBACX;gBACA,OAAO;gBACP,IAAI,IAAI,EAAE,CAAC,OAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM;gBACjD,IAAI,CAAC,GACD,MAAM;gBACV,OAAO,IAAI;gBACX,IAAI,KAAK,EAAE,CAAC,KAAK;gBACjB,IAAI,OAAO,GAAG;oBACV,IAAI,IAAI,IAAI,CAAC,KAAK;oBAClB,MAAM,OAAO,KAAK,OAAQ,CAAC,KAAK,CAAC,IAAI,GAAI,OAAO;gBACpD;gBACA,IAAI,MAAM,MAAM;oBACZ,IAAI,MACA,MAAM;oBACV;gBACJ;gBACA,IAAI,OACA,KAAK,KAAK;gBACd,IAAI,MAAM,KAAK;gBACf,MAAO,KAAK,KAAK,MAAM,EAAG;oBACtB,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG;oBACtB,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,IAAI,GAAG;oBAC9B,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,IAAI,GAAG;oBAC9B,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,IAAI,GAAG;gBAClC;gBACA,KAAK;YACT;QACJ;QACA,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG;QAC/B,IAAI,IACA,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;IACjD,QAAS,CAAC,MAAO;IACjB,OAAO,MAAM,IAAI,MAAM,GAAG,MAAM,IAAI,KAAK,GAAG;AAChD;AACA,uEAAuE;AACvE,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACzB,MAAM,IAAI;IACV,IAAI,IAAI,AAAC,IAAI,IAAK;IAClB,CAAC,CAAC,EAAE,IAAI;IACR,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM;AACtB;AACA,4EAA4E;AAC5E,IAAI,UAAU,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B,MAAM,IAAI;IACV,IAAI,IAAI,AAAC,IAAI,IAAK;IAClB,CAAC,CAAC,EAAE,IAAI;IACR,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM;IAClB,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM;AACtB;AACA,8CAA8C;AAC9C,IAAI,QAAQ,SAAU,CAAC,EAAE,EAAE;IACvB,iCAAiC;IACjC,IAAI,IAAI,EAAE;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,EAAE,EAAG;QAC/B,IAAI,CAAC,CAAC,EAAE,EACJ,EAAE,IAAI,CAAC;YAAE,GAAG;YAAG,GAAG,CAAC,CAAC,EAAE;QAAC;IAC/B;IACA,IAAI,IAAI,EAAE,MAAM;IAChB,IAAI,KAAK,EAAE,KAAK;IAChB,IAAI,CAAC,GACD,OAAO;QAAC;QAAI;KAAE;IAClB,IAAI,KAAK,GAAG;QACR,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG;QACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG;QACZ,OAAO;YAAC;YAAG;SAAE;IACjB;IACA,EAAE,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC;IAAE;IAC3C,6CAA6C;IAC7C,+DAA+D;IAC/D,EAAE,IAAI,CAAC;QAAE,GAAG,CAAC;QAAG,GAAG;IAAM;IACzB,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK;IAC7C,CAAC,CAAC,EAAE,GAAG;QAAE,GAAG,CAAC;QAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,mCAAmC;IACnC,oEAAoE;IACpE,6EAA6E;IAC7E,iCAAiC;IACjC,sEAAsE;IACtE,MAAO,MAAM,IAAI,EAAG;QAChB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,KAAK;QACtC,IAAI,CAAC,CAAC,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,KAAK;QAClD,CAAC,CAAC,KAAK,GAAG;YAAE,GAAG,CAAC;YAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;YAAE,GAAG;YAAG,GAAG;QAAE;IAChD;IACA,IAAI,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,QACV,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IACxB;IACA,eAAe;IACf,IAAI,KAAK,IAAI,IAAI,SAAS;IAC1B,mBAAmB;IACnB,IAAI,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,IAAI;IAC5B,IAAI,MAAM,IAAI;QACV,+BAA+B;QAC/B,4CAA4C;QAC5C,eAAe;QACf,IAAI,IAAI,GAAG,KAAK;QAChB,0BAA0B;QAC1B,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK;QAC/B,GAAG,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;QAAE;QACjE,MAAO,IAAI,GAAG,EAAE,EAAG;YACf,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAClB,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI;gBACf,MAAM,MAAM,CAAC,KAAM,MAAM,EAAE,CAAC,KAAK,AAAC;gBAClC,EAAE,CAAC,KAAK,GAAG;YACf,OAEI;QACR;QACA,QAAQ;QACR,MAAO,KAAK,EAAG;YACX,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAClB,IAAI,EAAE,CAAC,KAAK,GAAG,IACX,MAAM,KAAM,KAAK,EAAE,CAAC,KAAK,KAAK;iBAE9B,EAAE;QACV;QACA,MAAO,KAAK,KAAK,IAAI,EAAE,EAAG;YACtB,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAClB,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI;gBAChB,EAAE,EAAE,CAAC,KAAK;gBACV,EAAE;YACN;QACJ;QACA,MAAM;IACV;IACA,OAAO;QAAC,IAAI,GAAG;QAAK;KAAI;AAC5B;AACA,6CAA6C;AAC7C,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACtB,OAAO,EAAE,CAAC,IAAI,CAAC,IACT,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,MAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG;AACpB;AACA,0BAA0B;AAC1B,IAAI,KAAK,SAAU,CAAC;IAChB,IAAI,IAAI,EAAE,MAAM;IAChB,0CAA0C;IAC1C,MAAO,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;IAEnB,IAAI,KAAK,IAAI,IAAI,EAAE;IACnB,+BAA+B;IAC/B,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM;IAC/B,IAAI,IAAI,SAAU,CAAC;QAAI,EAAE,CAAC,MAAM,GAAG;IAAG;IACtC,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QACzB,IAAI,CAAC,CAAC,EAAE,IAAI,OAAO,KAAK,GACpB,EAAE;aACD;YACD,IAAI,CAAC,OAAO,MAAM,GAAG;gBACjB,MAAO,MAAM,KAAK,OAAO,IACrB,EAAE;gBACN,IAAI,MAAM,GAAG;oBACT,EAAE,MAAM,KAAK,AAAE,MAAM,MAAO,IAAK,QAAQ,AAAE,MAAM,KAAM,IAAK;oBAC5D,MAAM;gBACV;YACJ,OACK,IAAI,MAAM,GAAG;gBACd,EAAE,MAAM,EAAE;gBACV,MAAO,MAAM,GAAG,OAAO,EACnB,EAAE;gBACN,IAAI,MAAM,GACN,EAAE,AAAE,MAAM,KAAM,IAAK,OAAO,MAAM;YAC1C;YACA,MAAO,MACH,EAAE;YACN,MAAM;YACN,MAAM,CAAC,CAAC,EAAE;QACd;IACJ;IACA,OAAO;QAAC,GAAG,QAAQ,CAAC,GAAG;QAAM;KAAE;AACnC;AACA,yDAAyD;AACzD,IAAI,OAAO,SAAU,EAAE,EAAE,EAAE;IACvB,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,EAAE,EAC7B,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACtB,OAAO;AACX;AACA,uBAAuB;AACvB,0BAA0B;AAC1B,IAAI,QAAQ,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,wDAAwD;IACxD,IAAI,IAAI,IAAI,MAAM;IAClB,IAAI,IAAI,KAAK,MAAM;IACnB,GAAG,CAAC,EAAE,GAAG,IAAI;IACb,GAAG,CAAC,IAAI,EAAE,GAAG,MAAM;IACnB,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG;IACtB,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EACrB,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE;IAC3B,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI;AACzB;AACA,iBAAiB;AACjB,IAAI,OAAO,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACjE,MAAM,KAAK,KAAK;IAChB,EAAE,EAAE,CAAC,IAAI;IACT,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IAChD,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IAChD,IAAI,KAAK,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IAC3C,IAAI,KAAK,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IAC3C,IAAI,SAAS,IAAI,IAAI;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG;IACxB,IAAI,KAAK,MAAM,QAAQ,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;IACpD,IAAI,OAAO;IACX,MAAO,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;IAE3C,IAAI,OAAO,AAAC,KAAK,KAAM;IACvB,IAAI,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;IAC5C,IAAI,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,QAAQ,OAAO,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG;IACtI,IAAI,QAAQ,SAAS,QAAQ,OACzB,OAAO,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,KAAK;IAC/C,IAAI,IAAI,IAAI,IAAI;IAChB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK;IACzC,IAAI,QAAQ,OAAO;QACf,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK;QAC/D,IAAI,MAAM,KAAK,KAAK,MAAM;QAC1B,MAAM,KAAK,GAAG,MAAM;QACpB,MAAM,KAAK,IAAI,GAAG,MAAM;QACxB,MAAM,KAAK,IAAI,IAAI,OAAO;QAC1B,KAAK;QACL,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EACxB,MAAM,KAAK,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACtC,KAAK,IAAI;QACT,IAAI,OAAO;YAAC;YAAM;SAAK;QACvB,IAAK,IAAI,KAAK,GAAG,KAAK,GAAG,EAAE,GAAI;YAC3B,IAAI,OAAO,IAAI,CAAC,GAAG;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;gBAClC,IAAI,MAAM,IAAI,CAAC,EAAE,GAAG;gBACpB,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI;gBACtC,IAAI,MAAM,IACN,MAAM,KAAK,GAAG,AAAC,IAAI,CAAC,EAAE,KAAK,IAAK,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK;YAC/D;QACJ;IACJ,OACK;QACD,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;IACvC;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACzB,IAAI,IAAI,CAAC,EAAE,GAAG,KAAK;YACf,IAAI,MAAM,AAAC,IAAI,CAAC,EAAE,KAAK,KAAM;YAC7B,QAAQ,KAAK,GAAG,EAAE,CAAC,MAAM,IAAI,GAAG,KAAK,EAAE,CAAC,MAAM,IAAI;YAClD,IAAI,MAAM,GACN,MAAM,KAAK,GAAG,AAAC,IAAI,CAAC,EAAE,KAAK,KAAM,KAAK,KAAK,IAAI,CAAC,IAAI;YACxD,IAAI,MAAM,IAAI,CAAC,EAAE,GAAG;YACpB,QAAQ,KAAK,GAAG,EAAE,CAAC,IAAI,GAAG,KAAK,EAAE,CAAC,IAAI;YACtC,IAAI,MAAM,GACN,QAAQ,KAAK,GAAG,AAAC,IAAI,CAAC,EAAE,KAAK,IAAK,OAAO,KAAK,IAAI,CAAC,IAAI;QAC/D,OACK;YACD,QAAQ,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAClD;IACJ;IACA,QAAQ,KAAK,GAAG,EAAE,CAAC,IAAI;IACvB,OAAO,IAAI,EAAE,CAAC,IAAI;AACtB;AACA,uCAAuC;AACvC,IAAI,MAAM,WAAW,GAAG,IAAI,IAAI;IAAC;IAAO;IAAQ;IAAQ;IAAQ;IAAQ;IAAS;IAAS;IAAS;CAAQ;AAC3G,QAAQ;AACR,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG;AAC9B,4CAA4C;AAC5C,IAAI,OAAO,SAAU,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAC/C,IAAI,IAAI,IAAI,MAAM;IAClB,IAAI,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI;IACzD,8CAA8C;IAC9C,IAAI,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG;IACnC,IAAI,MAAM;IACV,IAAI,CAAC,OAAO,IAAI,GAAG;QACf,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK,MAAO;YAChC,MAAM;YACN,IAAI,IAAI,IAAI;YACZ,IAAI,IAAI,GAAG;gBACP,mBAAmB;gBACnB,MAAM,MAAM,GAAG,KAAK,IAAI,QAAQ,CAAC,GAAG;YACxC,OACK;gBACD,oBAAoB;gBACpB,CAAC,CAAC,EAAE,GAAG;gBACP,MAAM,MAAM,GAAG,KAAK,IAAI,QAAQ,CAAC,GAAG;YACxC;QACJ;IACJ,OACK;QACD,IAAI,MAAM,GAAG,CAAC,MAAM,EAAE;QACtB,IAAI,IAAI,QAAQ,IAAI,IAAI,MAAM;QAC9B,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI;QAC1B,gDAAgD;QAChD,IAAI,OAAO,IAAI,IAAI,QAAQ,OAAO,IAAI,IAAI,QAAQ;QAClD,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,IAAI,QAAQ,IAAI;QAC7C,IAAI,MAAM,SAAU,CAAC;YAAI,OAAO,CAAC,GAAG,CAAC,EAAE,GAAI,GAAG,CAAC,IAAI,EAAE,IAAI,QAAU,GAAG,CAAC,IAAI,EAAE,IAAI,KAAM,IAAI;QAAO;QAClG,4DAA4D;QAC5D,4BAA4B;QAC5B,IAAI,OAAO,IAAI,IAAI;QACnB,sCAAsC;QACtC,IAAI,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI;QACpC,iDAAiD;QACjD,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;QAClD,MAAO,IAAI,GAAG,EAAE,EAAG;YACf,aAAa;YACb,kDAAkD;YAClD,IAAI,KAAK,IAAI;YACb,wCAAwC;YACxC,IAAI,OAAO,IAAI,OAAO,QAAQ,IAAI,CAAC,GAAG;YACtC,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,GAAG,GAAG;YACX,iEAAiE;YACjE,yDAAyD;YACzD,IAAI,MAAM,GAAG;gBACT,kBAAkB;gBAClB,IAAI,MAAM,IAAI;gBACd,IAAI,CAAC,OAAO,QAAQ,KAAK,KAAK,KAAK,MAAM,KAAK;oBAC1C,MAAM,KAAK,KAAK,GAAG,GAAG,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;oBACxD,KAAK,OAAO,KAAK,GAAG,KAAK;oBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EACvB,EAAE,CAAC,EAAE,GAAG;oBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EACtB,EAAE,CAAC,EAAE,GAAG;gBAChB;gBACA,uBAAuB;gBACvB,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,MAAM,AAAC,OAAO,QAAS;gBACnD,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,MAAM;oBAC/B,IAAI,OAAO,KAAK,GAAG,CAAC,GAAG,OAAO;oBAC9B,IAAI,OAAO,KAAK,GAAG,CAAC,OAAO;oBAC3B,sBAAsB;oBACtB,+EAA+E;oBAC/E,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK;oBACvB,MAAO,OAAO,QAAQ,EAAE,QAAQ,QAAQ,MAAO;wBAC3C,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE;4BAChC,IAAI,KAAK;4BACT,MAAO,KAAK,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,EAAE;4BAEtD,IAAI,KAAK,GAAG;gCACR,IAAI,IAAI,IAAI;gCACZ,iEAAiE;gCACjE,IAAI,KAAK,MACL;gCACJ,mDAAmD;gCACnD,kDAAkD;gCAClD,wCAAwC;gCACxC,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK;gCAC7B,IAAI,KAAK;gCACT,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;oCAC1B,IAAI,KAAK,AAAC,IAAI,MAAM,IAAI,QAAS;oCACjC,IAAI,MAAM,IAAI,CAAC,GAAG;oCAClB,IAAI,KAAK,AAAC,KAAK,MAAM,QAAS;oCAC9B,IAAI,KAAK,IACL,KAAK,IAAI,QAAQ;gCACzB;4BACJ;wBACJ;wBACA,2BAA2B;wBAC3B,OAAO,OAAO,QAAQ,IAAI,CAAC,KAAK;wBAChC,OAAO,AAAC,OAAO,QAAQ,QAAS;oBACpC;gBACJ;gBACA,gDAAgD;gBAChD,IAAI,GAAG;oBACH,6CAA6C;oBAC7C,kEAAkE;oBAClE,IAAI,CAAC,KAAK,GAAG,YAAa,KAAK,CAAC,EAAE,IAAI,KAAM,KAAK,CAAC,EAAE;oBACpD,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG;oBAC1C,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;oBAC3B,EAAE,EAAE,CAAC,MAAM,IAAI;oBACf,EAAE,EAAE,CAAC,IAAI;oBACT,KAAK,IAAI;oBACT,EAAE;gBACN,OACK;oBACD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE;oBACnB,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChB;YACJ;QACJ;QACA,MAAM,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAC1D,6DAA6D;QAC7D,IAAI,CAAC,OAAO,MAAM,GACd,MAAM,MAAM,GAAG,MAAM,GAAG;IAChC;IACA,OAAO,IAAI,GAAG,GAAG,MAAM,KAAK,OAAO;AACvC;AACA,cAAc;AACd,IAAI,OAAO,WAAW,GAAG,AAAC;IACtB,IAAI,IAAI,IAAI,WAAW;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;QAC1B,IAAI,IAAI,GAAG,IAAI;QACf,MAAO,EAAE,EACL,IAAI,CAAC,AAAC,IAAI,KAAM,CAAC,SAAS,IAAK,MAAM;QACzC,CAAC,CAAC,EAAE,GAAG;IACX;IACA,OAAO;AACX;AACA,QAAQ;AACR,IAAI,MAAM;IACN,IAAI,IAAI,CAAC;IACT,OAAO;QACH,GAAG,SAAU,CAAC;YACV,kCAAkC;YAClC,IAAI,KAAK;YACT,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,EAAE,EAC5B,KAAK,IAAI,CAAC,AAAC,KAAK,MAAO,CAAC,CAAC,EAAE,CAAC,GAAI,OAAO;YAC3C,IAAI;QACR;QACA,GAAG;YAAc,OAAO,CAAC;QAAG;IAChC;AACJ;AACA,UAAU;AACV,IAAI,QAAQ;IACR,IAAI,IAAI,GAAG,IAAI;IACf,OAAO;QACH,GAAG,SAAU,CAAC;YACV,kCAAkC;YAClC,IAAI,IAAI,GAAG,IAAI;YACf,IAAI,IAAI,EAAE,MAAM;YAChB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAI;gBACrB,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM;gBAC3B,MAAO,IAAI,GAAG,EAAE,EACZ,KAAK,KAAK,CAAC,CAAC,EAAE;gBAClB,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACrE;YACA,IAAI,GAAG,IAAI;QACf;QACA,GAAG;YACC,KAAK,OAAO,KAAK;YACjB,OAAO,CAAC,IAAI,GAAG,KAAK,KAAK,AAAC,MAAM,KAAM,KAAK,CAAC,IAAI,GAAG,KAAK,IAAK,MAAM;QACvE;IACJ;AACJ;;AAEA,oBAAoB;AACpB,IAAI,OAAO,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACxC,OAAO,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,OAAQ,KAAK,IAAI,GAAG,EAAG,KAAK,MAAM,CAAC;AACzK;AACA,wBAAwB;AACxB,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;IACpB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EACV,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAK,IAAI,KAAK,EACV,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,OAAO;AACX;AACA,eAAe;AACf,6FAA6F;AAC7F,qHAAqH;AACrH,gIAAgI;AAChI,iHAAiH;AACjH,qGAAqG;AACrG,oDAAoD;AACpD,IAAI,OAAO,SAAU,EAAE,EAAE,KAAK,EAAE,EAAE;IAC9B,IAAI,KAAK;IACT,IAAI,KAAK,GAAG,QAAQ;IACpB,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,WAAW,CAAC,MAAM,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;IACpF,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,EAAE,EAAG;QAChC,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QACxB,IAAI,OAAO,KAAK,YAAY;YACxB,SAAS,MAAM,IAAI;YACnB,IAAI,OAAO,EAAE,QAAQ;YACrB,IAAI,EAAE,SAAS,EAAE;gBACb,qBAAqB;gBACrB,IAAI,KAAK,OAAO,CAAC,oBAAoB,CAAC,GAAG;oBACrC,IAAI,QAAQ,KAAK,OAAO,CAAC,KAAK,KAAK;oBACnC,SAAS,KAAK,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK;gBACjD,OACK;oBACD,SAAS;oBACT,IAAK,IAAI,KAAK,EAAE,SAAS,CACrB,SAAS,MAAM,IAAI,gBAAgB,IAAI,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,QAAQ;gBAC5E;YACJ,OAEI,SAAS;QACjB,OAEI,EAAE,CAAC,EAAE,GAAG;IAChB;IACA,OAAO;QAAC;QAAO;KAAG;AACtB;AACA,IAAI,KAAK,EAAE;AACX,aAAa;AACb,IAAI,OAAO,SAAU,CAAC;IAClB,IAAI,KAAK,EAAE;IACX,IAAK,IAAI,KAAK,EAAG;QACb,IAAI,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,CAAC,EAAE,YAAY,KAC7D,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM;IAC1D;IACA,OAAO;AACX;AACA,+BAA+B;AAC/B,IAAI,OAAO,SAAU,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;IAClC,IAAI;IACJ,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE;QACT,IAAI,QAAQ,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,MAAM,GAAG;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EACrB,KAAK,KAAK,GAAG,CAAC,EAAE,EAAE,OAAO,OAAO,QAAQ,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;QAC/D,EAAE,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,EAAE,OAAO;IACjC;IACA,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,4EAA4E,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK;AAC/I;AACA,wBAAwB;AACxB,IAAI,SAAS;IAAc,OAAO;QAAC;QAAI;QAAK;QAAK;QAAM;QAAM;QAAM;QAAI;QAAI;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAQ;QAAM;QAAK;QAAO;QAAa;QAAK;KAAI;AAAE;AAC/J,IAAI,QAAQ;IAAc,OAAO;QAAC;QAAI;QAAK;QAAK;QAAM;QAAM;QAAM;QAAO;QAAO;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAM;QAAO;QAAS;QAAO;QAAI;QAAI;QAAM;QAAO;QAAM;QAAM;QAAK;QAAM;QAAM;QAAa;KAAI;AAAE;AACpN,aAAa;AACb,IAAI,MAAM;IAAc,OAAO;QAAC;QAAK;QAAM;QAAQ;QAAK;KAAK;AAAE;AAC/D,eAAe;AACf,IAAI,OAAO;IAAc,OAAO;QAAC;QAAK;KAAI;AAAE;AAC5C,aAAa;AACb,IAAI,MAAM;IAAc,OAAO;QAAC;QAAK;QAAQ;KAAM;AAAE;AACrD,eAAe;AACf,IAAI,OAAO;IAAc,OAAO;QAAC;KAAI;AAAE;AACvC,WAAW;AACX,IAAI,MAAM,SAAU,GAAG;IAAI,OAAO,YAAY,KAAK;QAAC,IAAI,MAAM;KAAC;AAAG;AAClE,SAAS;AACT,IAAI,MAAM,SAAU,CAAC;IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI;AAAG;AAC/D,eAAe;AACf,IAAI,QAAQ,SAAU,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;IAC9C,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,SAAU,GAAG,EAAE,GAAG;QAC1C,EAAE,SAAS;QACX,GAAG,KAAK;IACZ;IACA,EAAE,WAAW,CAAC;QAAC;QAAK;KAAK,EAAE,KAAK,OAAO,GAAG;QAAC,IAAI,MAAM;KAAC,GAAG,EAAE;IAC3D,OAAO;QAAc,EAAE,SAAS;IAAI;AACxC;AACA,cAAc;AACd,IAAI,QAAQ,SAAU,IAAI;IACtB,KAAK,MAAM,GAAG,SAAU,GAAG,EAAE,KAAK;QAAI,OAAO,YAAY;YAAC;YAAK;SAAM,EAAE;YAAC,IAAI,MAAM;SAAC;IAAG;IACtF,OAAO,SAAU,EAAE;QAAI,OAAO,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE;IAAG;AACrE;AACA,sBAAsB;AACtB,IAAI,WAAW,SAAU,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;IAC9C,IAAI;IACJ,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,SAAU,GAAG,EAAE,GAAG;QAC1C,IAAI,KACA,EAAE,SAAS,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM;aACrC;YACD,IAAI,GAAG,CAAC,EAAE,EACN,EAAE,SAAS;YACf,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE;QAC9C;IACJ;IACA,EAAE,WAAW,CAAC;IACd,KAAK,IAAI,GAAG,SAAU,CAAC,EAAE,CAAC;QACtB,IAAI,GACA,MAAM;QACV,IAAI,CAAC,KAAK,MAAM,EACZ,MAAM;QACV,EAAE,WAAW,CAAC;YAAC;YAAG,IAAI;SAAE,EAAE;YAAC,EAAE,MAAM;SAAC;IACxC;IACA,KAAK,SAAS,GAAG;QAAc,EAAE,SAAS;IAAI;AAClD;AACA,eAAe;AACf,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,IAAI;AAAI;AAC1D,eAAe;AACf,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,IAAI,IAAM,CAAC,CAAC,IAAI,EAAE,IAAI,KAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAG,MAAM;AAAG;AACxG,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,GAAG,GAAG,KAAM,GAAG,GAAG,IAAI,KAAK;AAAa;AAC1E,cAAc;AACd,IAAI,SAAS,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IAC1B,MAAO,GAAG,EAAE,EACR,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO;AACzB;AACA,cAAc;AACd,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;IACpB,IAAI,KAAK,EAAE,QAAQ;IACnB,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,GAAG,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,cAAc;IACxG,IAAI,EAAE,KAAK,IAAI,GACX,OAAO,GAAG,GAAG,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,GAAG,MAAM;IAC9D,IAAI,IAAI;QACJ,CAAC,CAAC,EAAE,GAAG;QACP,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,EAAE,EAAE,EAC9B,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC;IAClC;AACJ;AACA,kDAAkD;AAClD,aAAa;AACb,IAAI,MAAM,SAAU,CAAC;IACjB,IAAI,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,GACrC,MAAM;IACV,IAAI,MAAM,CAAC,CAAC,EAAE;IACd,IAAI,KAAK;IACT,IAAI,MAAM,GACN,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI;IACjC,IAAK,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK;IAErE,OAAO,KAAK,CAAC,MAAM,CAAC;AACxB;AACA,cAAc;AACd,IAAI,MAAM,SAAU,CAAC;IACjB,IAAI,IAAI,EAAE,MAAM;IAChB,OAAO,CAAC,AAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,KAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAG,MAAM;AAChF;AACA,qBAAqB;AACrB,IAAI,OAAO,SAAU,CAAC;IAAI,OAAO,KAAK,CAAC,AAAC,EAAE,QAAQ,IAAK,EAAE,QAAQ,CAAC,MAAM,GAAG,KAAO,CAAC;AAAG;AACtF,cAAc;AACd,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;IACpB,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;IAChE,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,AAAC,MAAM,IAAK,CAAC,KAAM,KAAK,IAAI,KAAM,CAAC;AAC1D;AACA,aAAa;AACb,IAAI,MAAM,SAAU,CAAC;IACjB,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,AAAC,CAAC,CAAC,EAAE,KAAK,IAAK,KAAM,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,IAC9D,MAAM;IACV,IAAI,CAAC,CAAC,EAAE,GAAG,IACP,MAAM;AACd;AACA,SAAS,aAAa,IAAI,EAAE,EAAE;IAC1B,IAAI,CAAC,MAAM,OAAO,QAAQ,YACtB,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,CAAC,MAAM,GAAG;IACd,OAAO;AACX;AACA,mCAAmC;AACnC;;CAEC,GACD,IAAI,UAAyB;IACzB,SAAS,QAAQ,IAAI,EAAE,EAAE;QACrB,IAAI,CAAC,MAAM,OAAO,QAAQ,YACtB,KAAK,MAAM,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC;IACtB;IACA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAU,CAAC,EAAE,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI;IAC3C;IACA;;;;KAIC,GACD,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC3C,IAAI,IAAI,CAAC,CAAC,EACN,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,CAAC,OAAO,SAAS;IAC3B;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,eAA8B;IAC9B,SAAS,aAAa,IAAI,EAAE,EAAE;QAC1B,SAAS;YACL;YACA;gBAAc,OAAO;oBAAC;oBAAO;iBAAQ;YAAE;SAC1C,EAAE,IAAI,EAAE,aAAa,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,SAAU,EAAE;YACpD,IAAI,OAAO,IAAI,QAAQ,GAAG,IAAI;YAC9B,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,EAAE;IAClC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;KACH,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE;IAAI,GAAG,GAAG;AAC9E;AAOO,SAAS,YAAY,IAAI,EAAE,IAAI;IAClC,OAAO,KAAK,MAAM,QAAQ,CAAC,GAAG,GAAG;AACrC;AACA;;CAEC,GACD,IAAI,UAAyB;IACzB;;;KAGC,GACD,SAAS,QAAQ,EAAE;QACf,IAAI,CAAC,CAAC,GAAG,CAAC;QACV,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAU,CAAC;QAC7B,IAAI,IAAI,CAAC,CAAC,EACN,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM;QACrB,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM;QAC3B,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,GAAG;IACzC;IACA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAU,KAAK;QACjC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS;QAC7B,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM;QAC5D,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;IAC1D;IACA;;;;KAIC,GACD,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC3C,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;IAC1B;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,eAA8B;IAC9B;;;KAGC,GACD,SAAS,aAAa,EAAE;QACpB,IAAI,CAAC,MAAM,GAAG;QACd,SAAS;YACL;YACA;gBAAc,OAAO;oBAAC;oBAAO;iBAAQ;YAAE;SAC1C,EAAE,IAAI,EAAE,GAAG;YACR,IAAI,OAAO,IAAI;YACf,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,EAAE;IAClC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;KACH,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE;IAAK,GAAG,GAAG;AACnF;AAOO,SAAS,YAAY,IAAI,EAAE,GAAG;IACjC,OAAO,MAAM,MAAM;AACvB;AACA,2GAA2G;AAC3G;;CAEC,GACD,IAAI,OAAsB;IACtB,SAAS,KAAK,IAAI,EAAE,EAAE;QAClB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM;IAC7B;IACA;;;;KAIC,GACD,KAAK,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACxC,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IAC7C;IACA,KAAK,SAAS,CAAC,CAAC,GAAG,SAAU,CAAC,EAAE,CAAC;QAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM;QAClB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC;QAC3D,IAAI,IAAI,CAAC,CAAC,EACN,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG;QAC/B,IAAI,GACA,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,MAAM,CAAC,KAAK;IACrB;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,YAA2B;IAC3B,SAAS,UAAU,IAAI,EAAE,EAAE;QACvB,SAAS;YACL;YACA;YACA;gBAAc,OAAO;oBAAC;oBAAO;oBAAS;iBAAK;YAAE;SAChD,EAAE,IAAI,EAAE,aAAa,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,SAAU,EAAE;YACpD,IAAI,OAAO,IAAI,KAAK,GAAG,IAAI;YAC3B,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE;IAC/B,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;QACA;QACA;YAAc,OAAO;gBAAC;aAAS;QAAE;KACpC,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE;IAAI,GAAG,GAAG;AAC3E;AAOO,SAAS,SAAS,IAAI,EAAE,IAAI;IAC/B,IAAI,CAAC,MACD,OAAO,CAAC;IACZ,IAAI,IAAI,OAAO,IAAI,KAAK,MAAM;IAC9B,EAAE,CAAC,CAAC;IACJ,IAAI,IAAI,KAAK,MAAM,MAAM,KAAK,OAAO,IAAI,IAAI,EAAE,MAAM;IACrD,OAAO,IAAI,GAAG,OAAO,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;AACvE;AACA;;CAEC,GACD,IAAI,SAAwB;IACxB;;;KAGC,GACD,SAAS,OAAO,EAAE;QACd,IAAI,CAAC,CAAC,GAAG;QACT,QAAQ,IAAI,CAAC,IAAI,EAAE;IACvB;IACA;;;;KAIC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC1C,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;QAC/B,IAAI,IAAI,CAAC,CAAC,EAAE;YACR,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI;YAC1C,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,OACvB;YACJ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG;QAC1C;QACA,IAAI,OAAO;YACP,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,GAChB,MAAM;YACV,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjC;QACA,uDAAuD;QACvD,sDAAsD;QACtD,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;IACnC;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,cAA6B;IAC7B;;;KAGC,GACD,SAAS,YAAY,EAAE;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,SAAS;YACL;YACA;YACA;gBAAc,OAAO;oBAAC;oBAAO;oBAAS;iBAAO;YAAE;SAClD,EAAE,IAAI,EAAE,GAAG;YACR,IAAI,OAAO,IAAI;YACf,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,OAAO,IAAI,EAAE,IAAI,EAAE,EAAE;IACjC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;QACA;QACA;YAAc,OAAO;gBAAC;aAAW;QAAE;KACtC,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,WAAW,GAAG,IAAI,CAAC,EAAE;IAAI,GAAG,GAAG;AACjE;AAOO,SAAS,WAAW,IAAI,EAAE,GAAG;IAChC,OAAO,MAAM,KAAK,QAAQ,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,IAAI,GAAG,IAAI;AACjE;AACA;;CAEC,GACD,IAAI,OAAsB;IACtB,SAAS,KAAK,IAAI,EAAE,EAAE;QAClB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM;IAC7B;IACA;;;;KAIC,GACD,KAAK,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACxC,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IAC7C;IACA,KAAK,SAAS,CAAC,CAAC,GAAG,SAAU,CAAC,EAAE,CAAC;QAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC;QAChD,IAAI,IAAI,CAAC,CAAC,EACN,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG;QAC/B,IAAI,GACA,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,KAAK;IACrB;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,YAA2B;IAC3B,SAAS,UAAU,IAAI,EAAE,EAAE;QACvB,SAAS;YACL;YACA;YACA;gBAAc,OAAO;oBAAC;oBAAO;oBAAS;iBAAK;YAAE;SAChD,EAAE,IAAI,EAAE,aAAa,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,SAAU,EAAE;YACpD,IAAI,OAAO,IAAI,KAAK,GAAG,IAAI;YAC3B,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE;IAC/B,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;QACA;QACA;YAAc,OAAO;gBAAC;aAAS;QAAE;KACpC,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE;IAAI,GAAG,GAAG;AAC3E;AAOO,SAAS,SAAS,IAAI,EAAE,IAAI;IAC/B,IAAI,CAAC,MACD,OAAO,CAAC;IACZ,IAAI,IAAI;IACR,EAAE,CAAC,CAAC;IACJ,IAAI,IAAI,KAAK,MAAM,MAAM,GAAG;IAC5B,OAAO,IAAI,GAAG,OAAO,OAAO,GAAG,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK;AACzD;AACA;;CAEC,GACD,IAAI,SAAwB;IACxB;;;KAGC,GACD,SAAS,OAAO,EAAE;QACd,IAAI,CAAC,CAAC,GAAG;QACT,QAAQ,IAAI,CAAC,IAAI,EAAE;IACvB;IACA;;;;KAIC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC1C,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;QAC/B,IAAI,IAAI,CAAC,CAAC,EAAE;YACR,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,OACtB;YACJ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG;QAC1C;QACA,IAAI,OAAO;YACP,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,GAChB,MAAM;YACV,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjC;QACA,uDAAuD;QACvD,sDAAsD;QACtD,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;IACnC;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,cAA6B;IAC7B;;;KAGC,GACD,SAAS,YAAY,EAAE;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,SAAS;YACL;YACA;YACA;gBAAc,OAAO;oBAAC;oBAAO;oBAAS;iBAAO;YAAE;SAClD,EAAE,IAAI,EAAE,GAAG;YACR,IAAI,OAAO,IAAI;YACf,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,OAAO,IAAI,EAAE,IAAI,EAAE,EAAE;IACjC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;QACA;QACA;YAAc,OAAO;gBAAC;aAAW;QAAE;KACtC,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,WAAW,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE;IAAK,GAAG,GAAG;AAClF;AAOO,SAAS,WAAW,IAAI,EAAE,GAAG;IAChC,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG;AACpD;;;AAKA;;CAEC,GACD,IAAI,aAA4B;IAC5B;;;KAGC,GACD,SAAS,WAAW,EAAE;QAClB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;;KAIC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YACT,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;gBACzB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,MAAM;gBAC3C,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM;YAC7C,OAEI,IAAI,CAAC,CAAC,GAAG;YACb,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG;gBACnB,IAAI,UAAU,IAAI;gBAClB,IAAI,KAAK;oBAAc,QAAQ,MAAM,CAAC,KAAK,CAAC,SAAS;gBAAY;gBACjE,IAAI,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,IACxD,IAAI,IAAI,CAAC,CAAC,CAAC,MACX,AAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,IAAK,KAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,KAC9E,IAAI,IAAI,CAAC,CAAC,CAAC,MACX,IAAI,IAAI,CAAC,CAAC,CAAC;gBACrB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gBACpB,IAAI,CAAC,CAAC,GAAG;YACb;QACJ,OAEI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;IAC3B;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,kBAAiC;IACjC;;;GAGD,GACC,SAAS,gBAAgB,EAAE;QACvB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;;KAIC,GACD,gBAAgB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACnD,WAAW,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IAChD;IACA,OAAO;AACX;;AAEO,SAAS,WAAW,IAAI,EAAE,IAAI,EAAE,EAAE;IACrC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,AAAC,IAAI,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,IAChD,OAAO,MAAM,MAAM,MACnB,AAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,AAAC,IAAI,CAAC,EAAE,IAAI,IAAK,KAAM,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,KACtE,QAAQ,MAAM,MAAM,MACpB,OAAO,MAAM,MAAM;AACjC;AAOO,SAAS,eAAe,IAAI,EAAE,GAAG;IACpC,OAAO,AAAC,IAAI,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,IAChD,WAAW,MAAM,OACjB,AAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,AAAC,IAAI,CAAC,EAAE,IAAI,IAAK,KAAM,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,KACtE,YAAY,MAAM,OAClB,WAAW,MAAM;AAC/B;AACA,gCAAgC;AAChC,IAAI,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B,IAAK,IAAI,KAAK,EAAG;QACb,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,IAAI,IAAI;QACxB,IAAI,eAAe,IACf,CAAC,CAAC,EAAE,GAAG;YAAC;YAAK;SAAE;aACd,IAAI,MAAM,OAAO,CAAC,MACnB,CAAC,CAAC,EAAE,GAAG;YAAC,GAAG,CAAC,EAAE;YAAE,IAAI,GAAG,GAAG,CAAC,EAAE;SAAE;aAE/B,KAAK,KAAK,IAAI,KAAK,GAAG;IAC9B;AACJ;AACA,eAAe;AACf,IAAI,KAAK,OAAO,eAAe,eAAe,WAAW,GAAG,IAAI;AAChE,eAAe;AACf,IAAI,KAAK,OAAO,eAAe,eAAe,WAAW,GAAG,IAAI;AAChE,sBAAsB;AACtB,IAAI,MAAM;AACV,IAAI;IACA,GAAG,MAAM,CAAC,IAAI;QAAE,QAAQ;IAAK;IAC7B,MAAM;AACV,EACA,OAAO,GAAG,CAAE;AACZ,cAAc;AACd,IAAI,QAAQ,SAAU,CAAC;IACnB,IAAK,IAAI,IAAI,IAAI,IAAI,IAAK;QACtB,IAAI,IAAI,CAAC,CAAC,IAAI;QACd,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;QACzC,IAAI,IAAI,KAAK,EAAE,MAAM,EACjB,OAAO;YAAC;YAAG,IAAI,GAAG,IAAI;SAAG;QAC7B,IAAI,CAAC,IACD,KAAK,OAAO,YAAY,CAAC;aACxB,IAAI,MAAM,GAAG;YACd,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,IAAK,CAAC,CAAC,IAAI,GAAG,EAAG,IAAI,OAC9E,KAAK,OAAO,YAAY,CAAC,QAAS,KAAK,IAAK,QAAS,IAAI;QACjE,OACK,IAAI,KAAK,GACV,KAAK,OAAO,YAAY,CAAC,CAAC,IAAI,EAAE,KAAK,IAAK,CAAC,CAAC,IAAI,GAAG;aAEnD,KAAK,OAAO,YAAY,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,IAAK,CAAC,CAAC,IAAI,GAAG;IACjF;AACJ;AACA;;CAEC,GACD,IAAI,aAA4B;IAC5B;;;KAGC,GACD,SAAS,WAAW,EAAE;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,KACA,IAAI,CAAC,CAAC,GAAG,IAAI;aAEb,IAAI,CAAC,CAAC,GAAG;IACjB;IACA;;;;KAIC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,QAAQ,CAAC,CAAC;QACV,IAAI,IAAI,CAAC,CAAC,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO;gBAAE,QAAQ;YAAK,IAAI;YACpD,IAAI,OAAO;gBACP,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,EACtB,MAAM;gBACV,IAAI,CAAC,CAAC,GAAG;YACb;YACA;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,CAAC,EACP,MAAM;QACV,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,MAAM;QAC7C,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;QACd,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM;QAC5B,IAAI,KAAK,MAAM,MAAM,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;QAC3C,IAAI,OAAO;YACP,IAAI,GAAG,MAAM,EACT,MAAM;YACV,IAAI,CAAC,CAAC,GAAG;QACb,OAEI,IAAI,CAAC,CAAC,GAAG;QACb,IAAI,CAAC,MAAM,CAAC,IAAI;IACpB;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,aAA4B;IAC5B;;;KAGC,GACD,SAAS,WAAW,EAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;;KAIC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,IAAI,CAAC,CAAC,EACN,MAAM;QACV,IAAI,CAAC,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,CAAC,GAAG,SAAS;IAClD;IACA,OAAO;AACX;;AASO,SAAS,QAAQ,GAAG,EAAE,MAAM;IAC/B,IAAI,QAAQ;QACR,IAAI,OAAO,IAAI,GAAG,IAAI,MAAM;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAC9B,IAAI,CAAC,EAAE,GAAG,IAAI,UAAU,CAAC;QAC7B,OAAO;IACX;IACA,IAAI,IACA,OAAO,GAAG,MAAM,CAAC;IACrB,IAAI,IAAI,IAAI,MAAM;IAClB,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,CAAC;IAC7C,IAAI,KAAK;IACT,IAAI,IAAI,SAAU,CAAC;QAAI,EAAE,CAAC,KAAK,GAAG;IAAG;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,IAAI,KAAK,IAAI,GAAG,MAAM,EAAE;YACpB,IAAI,IAAI,IAAI,GAAG,KAAK,IAAI,CAAC,AAAC,IAAI,KAAM,CAAC;YACrC,EAAE,GAAG,CAAC;YACN,KAAK;QACT;QACA,IAAI,IAAI,IAAI,UAAU,CAAC;QACvB,IAAI,IAAI,OAAO,QACX,EAAE;aACD,IAAI,IAAI,MACT,EAAE,MAAO,KAAK,IAAK,EAAE,MAAO,IAAI;aAC/B,IAAI,IAAI,SAAS,IAAI,OACtB,IAAI,QAAQ,CAAC,IAAI,QAAQ,EAAE,IAAK,IAAI,UAAU,CAAC,EAAE,KAAK,MAClD,EAAE,MAAO,KAAK,KAAM,EAAE,MAAO,AAAC,KAAK,KAAM,KAAM,EAAE,MAAO,AAAC,KAAK,IAAK,KAAM,EAAE,MAAO,IAAI;aAE1F,EAAE,MAAO,KAAK,KAAM,EAAE,MAAO,AAAC,KAAK,IAAK,KAAM,EAAE,MAAO,IAAI;IACnE;IACA,OAAO,IAAI,IAAI,GAAG;AACtB;AAQO,SAAS,UAAU,GAAG,EAAE,MAAM;IACjC,IAAI,QAAQ;QACR,IAAI,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,KAAK,MACjC,KAAK,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,IAAI;QAC7D,OAAO;IACX,OACK,IAAI,IACL,OAAO,GAAG,MAAM,CAAC;SAChB;QACD,IAAI,KAAK,MAAM,MAAM,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;QAC7C,IAAI,IAAI,MAAM,EACV,MAAM;QACV,OAAO;IACX;AACJ;;AAEA,mBAAmB;AACnB,IAAI,MAAM,SAAU,CAAC;IAAI,OAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAG;AACzE,wBAAwB;AACxB,IAAI,OAAO,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,IAAI,KAAK,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,IAAI;AAAK;AAC5E,kBAAkB;AAClB,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACtB,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,KAAK,UAAU,EAAE,QAAQ,CAAC,IAAI,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,IAAI;IACrI,IAAI,KAAK,KAAK,MAAM,aAAa,KAAK,GAAG,MAAM;QAAC;QAAI,GAAG,GAAG,IAAI;QAAK,GAAG,GAAG,IAAI;KAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IACtH,OAAO;QAAC,GAAG,GAAG,IAAI;QAAK;QAAI;QAAI;QAAI,KAAK,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,IAAI;QAAK;KAAI;AAC/E;AACA,yBAAyB;AACzB,IAAI,OAAO,SAAU,CAAC,EAAE,CAAC;IACrB,MAAO,GAAG,GAAG,MAAM,GAAG,KAAK,IAAI,GAAG,GAAG,IAAI;IAEzC,OAAO;QAAC,GAAG,GAAG,IAAI;QAAK,GAAG,GAAG,IAAI;QAAI,GAAG,GAAG,IAAI;KAAI;AACvD;AACA,qBAAqB;AACrB,IAAI,OAAO,SAAU,EAAE;IACnB,IAAI,KAAK;IACT,IAAI,IAAI;QACJ,IAAK,IAAI,KAAK,GAAI;YACd,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;YACpB,IAAI,IAAI,OACJ,MAAM;YACV,MAAM,IAAI;QACd;IACJ;IACA,OAAO;AACX;AACA,mBAAmB;AACnB,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;IACzC,IAAI,KAAK,GAAG,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,MAAM,GAAG,MAAM;IACvD,IAAI,MAAM,KAAK;IACf,OAAO,GAAG,GAAG,MAAM,OAAO,YAAY,YAAY,KAAK;IACvD,IAAI,MAAM,MACN,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,EAAE,EAAE;IAC9B,CAAC,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,gCAAgC;IACnD,CAAC,CAAC,IAAI,GAAG,AAAC,EAAE,IAAI,IAAI,IAAK,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK;IACzD,CAAC,CAAC,IAAI,GAAG,EAAE,WAAW,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,EAAE,WAAW,IAAI;IACxD,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,OAAO,KAAK,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI,GAAG,WAAW,KAAK;IAClF,IAAI,IAAI,KAAK,IAAI,KACb,MAAM;IACV,OAAO,GAAG,GAAG,AAAC,KAAK,KAAO,AAAC,GAAG,QAAQ,KAAK,KAAM,KAAO,GAAG,OAAO,MAAM,KAAO,GAAG,QAAQ,MAAM,KAAO,GAAG,UAAU,MAAM,IAAM,GAAG,UAAU,OAAO,IAAK,KAAK;IAC9J,IAAI,KAAK,MAAM;QACX,OAAO,GAAG,GAAG,EAAE,GAAG;QAClB,OAAO,GAAG,IAAI,GAAG;QACjB,OAAO,GAAG,IAAI,GAAG,EAAE,IAAI;IAC3B;IACA,OAAO,GAAG,IAAI,IAAI;IAClB,OAAO,GAAG,IAAI,IAAI,MAAM,KAAK;IAC7B,IAAI,MAAM,MAAM;QACZ,OAAO,GAAG,GAAG;QACb,OAAO,GAAG,IAAI,GAAG,EAAE,KAAK;QACxB,OAAO,GAAG,IAAI,IAAI,KAAK,KAAK;IAChC;IACA,EAAE,GAAG,CAAC,IAAI;IACV,KAAK;IACL,IAAI,KAAK;QACL,IAAK,IAAI,KAAK,GAAI;YACd,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,IAAI,MAAM;YAC/B,OAAO,GAAG,GAAG,CAAC;YACd,OAAO,GAAG,IAAI,GAAG;YACjB,EAAE,GAAG,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI;QAChC;IACJ;IACA,IAAI,KACA,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK;IACvB,OAAO;AACX;AACA,8CAA8C;AAC9C,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7B,OAAO,GAAG,GAAG,YAAY,YAAY;IACrC,OAAO,GAAG,IAAI,GAAG;IACjB,OAAO,GAAG,IAAI,IAAI;IAClB,OAAO,GAAG,IAAI,IAAI;IAClB,OAAO,GAAG,IAAI,IAAI;AACtB;AACA;;CAEC,GACD,IAAI,iBAAgC;IAChC;;;KAGC,GACD,SAAS,eAAe,QAAQ;QAC5B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,WAAW,GAAG;IACvB;IACA;;;;;;;KAOC,GACD,eAAe,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,KAAK;QACrD,IAAI,CAAC,MAAM,CAAC,MAAM,OAAO;IAC7B;IACA;;;;;;KAMC,GACD,eAAe,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,IAAI,IAAI,MAAM,MAAM;QACzB,IAAI,OACA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,OAAO,SAAS;IACjC;IACA,OAAO;AACX;;AAEA,wEAAwE;AACxE;;;CAGC,GACD,IAAI,aAA4B;IAC5B;;;;KAIC,GACD,SAAS,WAAW,QAAQ,EAAE,IAAI;QAC9B,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,MACD,OAAO,CAAC;QACZ,eAAe,IAAI,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,MAAM,SAAU,GAAG,EAAE,KAAK;YAC3C,QAAQ,MAAM,CAAC,MAAM,KAAK;QAC9B;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,KAAK;IAC9B;IACA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,KAAK;QACjD,IAAI;YACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;QACvB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM;QACzB;IACJ;IACA;;;;KAIC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,eAAe,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IACpD;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,kBAAiC;IACjC;;;;KAIC,GACD,SAAS,gBAAgB,QAAQ,EAAE,IAAI;QACnC,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,MACD,OAAO,CAAC;QACZ,eAAe,IAAI,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,CAAC,GAAG,IAAI,aAAa,MAAM,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;YACrD,QAAQ,MAAM,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,KAAK;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS;IACrC;IACA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,KAAK;QACtD,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;IACvB;IACA;;;;KAIC,GACD,gBAAgB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACnD,eAAe,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IACpD;IACA,OAAO;AACX;;AAEA,4BAA4B;AAC5B;;CAEC,GACD,IAAI,MAAqB;IACrB;;;;KAIC,GACD,SAAS,IAAI,EAAE;QACX,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,CAAC,GAAG,EAAE;QACX,IAAI,CAAC,CAAC,GAAG;IACb;IACA;;;KAGC,GACD,IAAI,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI;QAC9B,IAAI,UAAU,IAAI;QAClB,IAAI,IAAI,CAAC,CAAC,GAAG,GACT,MAAM;QACV,IAAI,IAAI,QAAQ,KAAK,QAAQ,GAAG,KAAK,EAAE,MAAM;QAC7C,IAAI,MAAM,KAAK,OAAO,EAAE,IAAI,OAAO,QAAQ;QAC3C,IAAI,IAAI,MAAM,KAAK,QAAQ,CAAC,MAAM,IAAK,KAAM,IAAI,MAAM,IAAI,EAAE,MAAM;QACnE,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI;QACjC,IAAI,KAAK,OACL,MAAM;QACV,IAAI,SAAS,IAAI,GAAG;QACpB,IAAI,QAAQ,GAAG,MAAM,GAAG;QACxB,IAAI,OAAO;YAAC;SAAO;QACnB,IAAI,OAAO;YACP,IAAK,IAAI,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,MAAM,EAAE,KAAM;gBACtD,IAAI,MAAM,MAAM,CAAC,GAAG;gBACpB,QAAQ,MAAM,CAAC,MAAM,KAAK;YAC9B;YACA,OAAO,EAAE;QACb;QACA,IAAI,KAAK,IAAI,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM;QACvB,IAAI,KAAK,IAAI,MAAM;YACf,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;gBACC,IAAI,KAAK,SAAS,EACd,KAAK,SAAS;YACtB;YACA,GAAG;gBACC;gBACA,IAAI,IAAI;oBACJ,IAAI,MAAM,QAAQ,CAAC,CAAC,MAAM,EAAE;oBAC5B,IAAI,KACA,IAAI,CAAC;yBAEL,QAAQ,CAAC,GAAG;gBACpB;gBACA,KAAK;YACT;QACJ;QACA,IAAI,KAAK;QACT,KAAK,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;YACnC,IAAI,KAAK;gBACL,QAAQ,MAAM,CAAC,KAAK,KAAK;gBACzB,QAAQ,SAAS;YACrB,OACK;gBACD,MAAM,IAAI,MAAM;gBAChB,KAAK,IAAI,CAAC;gBACV,IAAI,OAAO;oBACP,IAAI,KAAK,IAAI,GAAG;oBAChB,OAAO,IAAI,GAAG;oBACd,OAAO,IAAI,GAAG,KAAK,GAAG;oBACtB,OAAO,IAAI,GAAG;oBACd,OAAO,IAAI,IAAI,KAAK,IAAI;oBACxB,KAAK,IAAI,CAAC;oBACV,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,KAAK,IAAI;oBACtE,IAAI,IACA,GAAG,CAAC;oBACR,KAAK;gBACT,OACK,IAAI,IACL;YACR;QACJ;QACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAChB;IACA;;;;KAIC,GACD,IAAI,SAAS,CAAC,GAAG,GAAG;QAChB,IAAI,UAAU,IAAI;QAClB,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG;YACZ,IAAI,IAAI,CAAC,CAAC,GAAG,GACT,MAAM;YACV,MAAM;QACV;QACA,IAAI,IAAI,CAAC,CAAC,EACN,IAAI,CAAC,CAAC;aAEN,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACR,GAAG;gBACC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,GACf;gBACJ,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG;gBACrB,QAAQ,CAAC;YACb;YACA,GAAG,YAAc;QACrB;QACJ,IAAI,CAAC,CAAC,GAAG;IACb;IACA,IAAI,SAAS,CAAC,CAAC,GAAG;QACd,IAAI,KAAK,GAAG,IAAI,GAAG,KAAK;QACxB,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YAChD,IAAI,IAAI,EAAE,CAAC,GAAG;YACd,MAAM,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;QACjE;QACA,IAAI,MAAM,IAAI,GAAG,KAAK;QACtB,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YAChD,IAAI,IAAI,EAAE,CAAC,GAAG;YACd,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;YACrC,MAAM,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC;QAC5E;QACA,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI;QAChC,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK;QACvB,IAAI,CAAC,CAAC,GAAG;IACb;IACA;;;KAGC,GACD,IAAI,SAAS,CAAC,SAAS,GAAG;QACtB,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YAChD,IAAI,IAAI,EAAE,CAAC,GAAG;YACd,EAAE,CAAC;QACP;QACA,IAAI,CAAC,CAAC,GAAG;IACb;IACA,OAAO;AACX;;AAEO,SAAS,IAAI,IAAI,EAAE,IAAI,EAAE,EAAE;IAC9B,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,IAAI,IAAI,CAAC;IACT,KAAK,MAAM,IAAI,GAAG;IAClB,IAAI,IAAI,OAAO,IAAI,CAAC;IACpB,IAAI,MAAM,EAAE,MAAM,EAAE,IAAI,GAAG,MAAM;IACjC,IAAI,OAAO,KAAK,QAAQ,IAAI,MAAM;IAClC,IAAI,OAAO,EAAE;IACb,IAAI,OAAO;QACP,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,IAAI,CAAC,EAAE;IACf;IACA,IAAI,MAAM;QACN,IAAI,MAAM,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,MAAM,MAAM;QAChD,MAAM;QACN,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EAAG;YAC3B,IAAI,IAAI,KAAK,CAAC,EAAE;YAChB,IAAI;gBACA,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM;gBAClB,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;gBAC3B,IAAI,OAAO,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE,KAAK;gBACzC,IAAI,MAAM,MAAM;gBAChB,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE;gBACb,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,MAAM;YAChG,EACA,OAAO,GAAG;gBACN,OAAO,GAAG,GAAG;YACjB;QACJ;QACA,IAAI,KAAK,GAAG,MAAM,MAAM,EAAE,KAAK;QAC/B,GAAG,MAAM;IACb;IACA,IAAI,CAAC,KACD;IACJ,IAAI,UAAU,SAAU,CAAC;QACrB,IAAI,KAAK,CAAC,CAAC,EAAE;QACb,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QACvC,IAAI,IAAI,OAAO,OAAO,KAAK,MAAM;QACjC,EAAE,CAAC,CAAC;QACJ,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE,MAAM;QACjC,IAAI,MAAM,EAAE,OAAO,EAAE,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,EAAE,MAAM;QAChE,IAAI,MAAM,KAAK,EAAE,KAAK;QACtB,IAAI,cAAc,EAAE,KAAK,IAAI,IAAI,IAAI;QACrC,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;YACpB,IAAI,GAAG;gBACH;gBACA,GAAG,GAAG;YACV,OACK;gBACD,IAAI,IAAI,EAAE,MAAM;gBAChB,KAAK,CAAC,EAAE,GAAG,IAAI,GAAG;oBACd,MAAM;oBACN,KAAK,EAAE,CAAC;oBACR,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,GAAG,KAAK,GAAG,MAAM,IAAK,KAAM,IAAI,MAAM,IAAI;oBAC1C,aAAa;gBACjB;gBACA,KAAK,KAAK,IAAI,MAAM;gBACpB,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;gBACxC,IAAI,CAAC,EAAE,KACH;YACR;QACJ;QACA,IAAI,IAAI,OACJ,IAAI,qBAAqB;QAC7B,IAAI,CAAC,aACD,IAAI,MAAM;aACT,IAAI,OAAO,QAAQ;YACpB,IAAI;gBACA,IAAI,MAAM,YAAY,MAAM;YAChC,EACA,OAAO,GAAG;gBACN,IAAI,GAAG;YACX;QACJ,OAEI,KAAK,IAAI,CAAC,QAAQ,MAAM,GAAG;IACnC;IACA,yCAAyC;IACzC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EAAG;QAC3B,QAAQ;IACZ;IACA,OAAO;AACX;AAQO,SAAS,QAAQ,IAAI,EAAE,IAAI;IAC9B,IAAI,CAAC,MACD,OAAO,CAAC;IACZ,IAAI,IAAI,CAAC;IACT,IAAI,QAAQ,EAAE;IACd,KAAK,MAAM,IAAI,GAAG;IAClB,IAAI,IAAI;IACR,IAAI,MAAM;IACV,IAAK,IAAI,MAAM,EAAG;QACd,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QACvC,IAAI,cAAc,EAAE,KAAK,IAAI,IAAI,IAAI;QACrC,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE,MAAM;QACjC,IAAI,MAAM,EAAE,OAAO,EAAE,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,EAAE,MAAM;QAChE,IAAI,MAAM,KAAK,EAAE,KAAK;QACtB,IAAI,IAAI,OACJ,MAAM;QACV,IAAI,IAAI,cAAc,YAAY,MAAM,KAAK,MAAM,IAAI,EAAE,MAAM;QAC/D,IAAI,IAAI;QACR,EAAE,CAAC,CAAC;QACJ,MAAM,IAAI,CAAC,IAAI,GAAG;YACd,MAAM,KAAK,MAAM;YACjB,KAAK,EAAE,CAAC;YACR,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG,KAAK,GAAG,MAAM,IAAK,KAAM,IAAI,MAAM,IAAI;YAC1C,GAAG;YACH,aAAa;QACjB;QACA,KAAK,KAAK,IAAI,MAAM;QACpB,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;IAC5C;IACA,IAAI,MAAM,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,MAAM,MAAM;IAChD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACnC,IAAI,IAAI,KAAK,CAAC,EAAE;QAChB,IAAI,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM;QACrC,IAAI,OAAO,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE,KAAK;QACzC,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG;QACnB,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;IAC1F;IACA,IAAI,KAAK,GAAG,MAAM,MAAM,EAAE,KAAK;IAC/B,OAAO;AACX;AACA;;CAEC,GACD,IAAI,mBAAkC;IAClC,SAAS,oBACT;IACA,iBAAiB,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,KAAK;QACnD,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM;IAC5B;IACA,iBAAiB,WAAW,GAAG;IAC/B,OAAO;AACX;;AAEA;;;CAGC,GACD,IAAI,eAA8B;IAC9B;;KAEC,GACD,SAAS;QACL,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,SAAU,GAAG,EAAE,KAAK;YACrC,QAAQ,MAAM,CAAC,MAAM,KAAK;QAC9B;IACJ;IACA,aAAa,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,KAAK;QAC/C,IAAI;YACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM;QACtB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM;QACzB;IACJ;IACA,aAAa,WAAW,GAAG;IAC3B,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,oBAAmC;IACnC;;KAEC,GACD,SAAS,kBAAkB,CAAC,EAAE,EAAE;QAC5B,IAAI,UAAU,IAAI;QAClB,IAAI,KAAK,QAAQ;YACb,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,SAAU,GAAG,EAAE,KAAK;gBACrC,QAAQ,MAAM,CAAC,MAAM,KAAK;YAC9B;QACJ,OACK;YACD,IAAI,CAAC,CAAC,GAAG,IAAI,aAAa,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;gBAC/C,QAAQ,MAAM,CAAC,KAAK,KAAK;YAC7B;YACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS;QACrC;IACJ;IACA,kBAAkB,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,KAAK;QACpD,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,EAChB,OAAO,IAAI,MAAM;QACrB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM;IACtB;IACA,kBAAkB,WAAW,GAAG;IAChC,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,QAAuB;IACvB;;;KAGC,GACD,SAAS,MAAM,EAAE;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,CAAC,GAAG,EAAE;QACX,IAAI,CAAC,CAAC,GAAG;YACL,GAAG;QACP;QACA,IAAI,CAAC,CAAC,GAAG;IACb;IACA;;;;KAIC,GACD,MAAM,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACzC,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,CAAC,EACP,MAAM;QACV,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG;YACZ,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM;YACvC,IAAI,QAAQ,MAAM,QAAQ,CAAC,GAAG;YAC9B,IAAI,CAAC,CAAC,IAAI;YACV,IAAI,IAAI,CAAC,CAAC,EACN,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAE1B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;YACnB,QAAQ,MAAM,QAAQ,CAAC;YACvB,IAAI,MAAM,MAAM,EACZ,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;QAChC,OACK;YACD,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK;YAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EACd,MAAM;iBACL,IAAI,CAAC,MAAM,MAAM,EAClB,MAAM,IAAI,CAAC,CAAC;iBACX;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,MAAM;gBACzC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM;YACjD;YACA,IAAI,IAAI,IAAI,MAAM,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,IAAI,CAAC,CAAC;YACnD,IAAI,UAAU;gBACV,IAAI;gBACJ,IAAI,MAAM,GAAG,KAAK;gBAClB,IAAI,OAAO,WAAW;oBAClB,IAAI,GAAG,KAAK;oBACZ,OAAO,CAAC,GAAG;oBACX,OAAO,CAAC,GAAG;oBACX,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI;oBACrH,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI;wBACvB,IAAI,SAAS,EAAE;wBACf,OAAO,CAAC,CAAC,OAAO,CAAC;wBACjB,IAAI;wBACJ,IAAI,OAAO,GAAG,KAAK,IAAI,KAAK,OAAO,GAAG,KAAK,IAAI;wBAC/C,IAAI,OAAO,UAAU,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,KAAK,MAAM,CAAC;wBAC3D,IAAI,QAAQ,YAAY;4BACpB,KAAK,KAAK;gCAAC,CAAC;6BAAE,GAAG,KAAK,KAAK,IAAI,OAAO,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;wBAC7D,OACK,IAAI,IACL,OAAO,CAAC;wBACZ,KAAK;wBACL,OAAO,CAAC,GAAG;wBACX,IAAI;wBACJ,IAAI,SAAS;4BACT,MAAM;4BACN,aAAa;4BACb,OAAO;gCACH,IAAI,CAAC,OAAO,MAAM,EACd,MAAM;gCACV,IAAI,CAAC,MACD,OAAO,MAAM,CAAC,MAAM,IAAI;qCACvB;oCACD,IAAI,MAAM,QAAQ,CAAC,CAAC,MAAM;oCAC1B,IAAI,CAAC,KACD,MAAM,8BAA8B;oCACxC,MAAM,OAAO,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,MAAM,MAAM;oCACrD,IAAI,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;wCAAI,OAAO,MAAM,CAAC,KAAK,KAAK;oCAAQ;oCAC1E,IAAK,IAAI,KAAK,GAAG,SAAS,QAAQ,KAAK,OAAO,MAAM,EAAE,KAAM;wCACxD,IAAI,MAAM,MAAM,CAAC,GAAG;wCACpB,IAAI,IAAI,CAAC,KAAK;oCAClB;oCACA,IAAI,QAAQ,CAAC,CAAC,EAAE,IAAI,UAAU,QAAQ,CAAC,EACnC,QAAQ,CAAC,GAAG;yCAEZ,IAAI,IAAI,CAAC,IAAI;gCACrB;4BACJ;4BACA,WAAW;gCACP,IAAI,OAAO,IAAI,SAAS,EACpB,IAAI,SAAS;4BACrB;wBACJ;wBACA,IAAI,QAAQ,GACR,OAAO,IAAI,GAAG,MAAM,OAAO,YAAY,GAAG;wBAC9C,OAAO,MAAM,CAAC;oBAClB;oBACA,OAAO;gBACX,OACK,IAAI,IAAI;oBACT,IAAI,OAAO,WAAW;wBAClB,KAAK,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG;wBAClD,OAAO;oBACX,OACK,IAAI,OAAO,WAAW;wBACvB,KAAK,KAAK,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG;wBAC/B,OAAO;oBACX;gBACJ;YACJ;YACA,IAAI,SAAS,IAAI;YACjB,MAAO,IAAI,IAAI,GAAG,EAAE,EAAG;gBACnB,IAAI,UAAU;gBACd,IAAI,YAAY,SACZ;YACR;YACA,IAAI,CAAC,CAAC,GAAG;YACT,IAAI,KAAK,GAAG;gBACR,IAAI,MAAM,IAAI,IAAI,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,KAAK,OAAO,aAAa,CAAC,KAAK,IAAI,QAAQ,CAAC,GAAG;gBAClH,IAAI,KACA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;qBAEhB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC;YAC/B;YACA,IAAI,IAAI,GACJ,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YACtC,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,CAAC;QAC1B;QACA,IAAI,OAAO;YACP,IAAI,IAAI,CAAC,CAAC,EACN,MAAM;YACV,IAAI,CAAC,CAAC,GAAG;QACb;IACJ;IACA;;;;KAIC,GACD,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,OAAO;QACxC,IAAI,CAAC,CAAC,CAAC,QAAQ,WAAW,CAAC,GAAG;IAClC;IACA,OAAO;AACX;;AAQO,SAAS,MAAM,IAAI,EAAE,EAAE;IAC1B,IAAI,OAAO,MAAM,YACb,MAAM;IACV,IAAI,OAAO,EAAE;IACb,IAAI,OAAO;QACP,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,IAAI,CAAC,EAAE;IACf;IACA,IAAI,QAAQ,CAAC;IACb,IAAI,IAAI,KAAK,MAAM,GAAG;IACtB,MAAO,GAAG,MAAM,MAAM,WAAW,EAAE,EAAG;QAClC,IAAI,CAAC,KAAK,KAAK,MAAM,GAAG,IAAI,OAAO;YAC/B,GAAG,oBAAoB;YACvB;QACJ;IACJ;;IAEA,IAAI,MAAM,GAAG,MAAM,IAAI;IACvB,IAAI,CAAC,KACD,GAAG,MAAM,CAAC;IACd,IAAI,IAAI;IACR,IAAI,IAAI,GAAG,MAAM,IAAI;IACrB,IAAI,IAAI,KAAK;IACb,IAAI,GAAG;QACH,IAAI,GAAG,MAAM,IAAI;QACjB,IAAI,GAAG,MAAM,MAAM,WAAW;YAC1B,GAAG,oBAAoB;YACvB;QACJ;QACA,IAAI,MAAM,GAAG,MAAM,IAAI;QACvB,IAAI,GAAG,MAAM,IAAI;IACrB;IACA,IAAI,UAAU,SAAU,CAAC;QACrB,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM;QAClH,IAAI;QACJ,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;YACpB,IAAI,GAAG;gBACH;gBACA,GAAG,GAAG;YACV,OACK;gBACD,KAAK,CAAC,GAAG,GAAG;gBACZ,IAAI,CAAC,EAAE,KACH,GAAG,MAAM;YACjB;QACJ;QACA,IAAI,CAAC,KACD,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI;aAC1B,IAAI,OAAO,GAAG;YACf,IAAI,OAAO,KAAK,QAAQ,CAAC,GAAG,IAAI;YAChC,IAAI,KAAK,QAAQ;gBACb,IAAI;oBACA,IAAI,MAAM,YAAY,MAAM,IAAI,GAAG;gBACvC,EACA,OAAO,GAAG;oBACN,IAAI,GAAG;gBACX;YACJ,OAEI,KAAK,IAAI,CAAC,QAAQ,MAAM;gBAAE,MAAM;YAAG,GAAG;QAC9C,OAEI,IAAI,8BAA8B,KAAK;IAC/C;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,QAAQ;IACZ;IACA,OAAO;AACX;AAOO,SAAS,UAAU,IAAI;IAC1B,IAAI,QAAQ,CAAC;IACb,IAAI,IAAI,KAAK,MAAM,GAAG;IACtB,MAAO,GAAG,MAAM,MAAM,WAAW,EAAE,EAAG;QAClC,IAAI,CAAC,KAAK,KAAK,MAAM,GAAG,IAAI,OACxB,MAAM;IACd;;IAEA,IAAI,IAAI,GAAG,MAAM,IAAI;IACrB,IAAI,CAAC,GACD,OAAO,CAAC;IACZ,IAAI,IAAI,GAAG,MAAM,IAAI;IACrB,IAAI,IAAI,KAAK;IACb,IAAI,GAAG;QACH,IAAI,GAAG,MAAM,IAAI;QACjB,IAAI,GAAG,MAAM,MAAM,WACf,MAAM;QACV,IAAI,GAAG,MAAM,IAAI;QACjB,IAAI,GAAG,MAAM,IAAI;IACrB;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM;QAClH,IAAI;QACJ,IAAI,CAAC,KACD,KAAK,CAAC,GAAG,GAAG,IAAI,MAAM,GAAG,IAAI;aAC5B,IAAI,OAAO,GACZ,KAAK,CAAC,GAAG,GAAG,YAAY,KAAK,QAAQ,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;aAEzD,MAAM,8BAA8B;IAC5C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js"], "sourcesContent": ["/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\nimport { RGBAFormat, LinearFilter, ClampToEdgeWrapping, Scene, OrthographicCamera, HalfFloatType, FloatType, Mesh, PlaneGeometry, WebGLRenderTarget, UVMapping, WebGLRenderer, DataTexture, LinearSRGBColorSpace, ShaderMaterial, Texture, IntType, ShortType, ByteType, UnsignedIntType, UnsignedByteType, MeshBasicMaterial } from 'three';\n\nconst getBufferForType = (type, width, height) => {\n    let out;\n    switch (type) {\n        case UnsignedByteType:\n            out = new Uint8ClampedArray(width * height * 4);\n            break;\n        case HalfFloatType:\n            out = new Uint16Array(width * height * 4);\n            break;\n        case UnsignedIntType:\n            out = new Uint32Array(width * height * 4);\n            break;\n        case ByteType:\n            out = new Int8Array(width * height * 4);\n            break;\n        case ShortType:\n            out = new Int16Array(width * height * 4);\n            break;\n        case IntType:\n            out = new Int32Array(width * height * 4);\n            break;\n        case FloatType:\n            out = new Float32Array(width * height * 4);\n            break;\n        default:\n            throw new Error('Unsupported data type');\n    }\n    return out;\n};\nlet _canReadPixelsResult;\n/**\n * Test if this browser implementation can correctly read pixels from the specified\n * Render target type.\n *\n * Runs only once\n *\n * @param type\n * @param renderer\n * @param camera\n * @param renderTargetOptions\n * @returns\n */\nconst canReadPixels = (type, renderer, camera, renderTargetOptions) => {\n    if (_canReadPixelsResult !== undefined)\n        return _canReadPixelsResult;\n    const testRT = new WebGLRenderTarget(1, 1, renderTargetOptions);\n    renderer.setRenderTarget(testRT);\n    const mesh = new Mesh(new PlaneGeometry(), new MeshBasicMaterial({ color: 0xffffff }));\n    renderer.render(mesh, camera);\n    renderer.setRenderTarget(null);\n    const out = getBufferForType(type, testRT.width, testRT.height);\n    renderer.readRenderTargetPixels(testRT, 0, 0, testRT.width, testRT.height, out);\n    testRT.dispose();\n    mesh.geometry.dispose();\n    mesh.material.dispose();\n    _canReadPixelsResult = out[0] !== 0;\n    return _canReadPixelsResult;\n};\n/**\n * Utility class used for rendering a texture with a material\n *\n * @category Core\n * @group Core\n */\nclass QuadRenderer {\n    /**\n     * Constructs a new QuadRenderer\n     *\n     * @param options Parameters for this QuadRenderer\n     */\n    constructor(options) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;\n        this._rendererIsDisposable = false;\n        this._supportsReadPixels = true;\n        /**\n         * Renders the input texture using the specified material\n         */\n        this.render = () => {\n            this._renderer.setRenderTarget(this._renderTarget);\n            try {\n                this._renderer.render(this._scene, this._camera);\n            }\n            catch (e) {\n                this._renderer.setRenderTarget(null);\n                throw e;\n            }\n            this._renderer.setRenderTarget(null);\n        };\n        this._width = options.width;\n        this._height = options.height;\n        this._type = options.type;\n        this._colorSpace = options.colorSpace;\n        const rtOptions = {\n            // fixed options\n            format: RGBAFormat,\n            depthBuffer: false,\n            stencilBuffer: false,\n            // user options\n            type: this._type, // set in class property\n            colorSpace: this._colorSpace, // set in class property\n            anisotropy: ((_a = options.renderTargetOptions) === null || _a === void 0 ? void 0 : _a.anisotropy) !== undefined ? (_b = options.renderTargetOptions) === null || _b === void 0 ? void 0 : _b.anisotropy : 1,\n            generateMipmaps: ((_c = options.renderTargetOptions) === null || _c === void 0 ? void 0 : _c.generateMipmaps) !== undefined ? (_d = options.renderTargetOptions) === null || _d === void 0 ? void 0 : _d.generateMipmaps : false,\n            magFilter: ((_e = options.renderTargetOptions) === null || _e === void 0 ? void 0 : _e.magFilter) !== undefined ? (_f = options.renderTargetOptions) === null || _f === void 0 ? void 0 : _f.magFilter : LinearFilter,\n            minFilter: ((_g = options.renderTargetOptions) === null || _g === void 0 ? void 0 : _g.minFilter) !== undefined ? (_h = options.renderTargetOptions) === null || _h === void 0 ? void 0 : _h.minFilter : LinearFilter,\n            samples: ((_j = options.renderTargetOptions) === null || _j === void 0 ? void 0 : _j.samples) !== undefined ? (_k = options.renderTargetOptions) === null || _k === void 0 ? void 0 : _k.samples : undefined,\n            wrapS: ((_l = options.renderTargetOptions) === null || _l === void 0 ? void 0 : _l.wrapS) !== undefined ? (_m = options.renderTargetOptions) === null || _m === void 0 ? void 0 : _m.wrapS : ClampToEdgeWrapping,\n            wrapT: ((_o = options.renderTargetOptions) === null || _o === void 0 ? void 0 : _o.wrapT) !== undefined ? (_p = options.renderTargetOptions) === null || _p === void 0 ? void 0 : _p.wrapT : ClampToEdgeWrapping\n        };\n        this._material = options.material;\n        if (options.renderer) {\n            this._renderer = options.renderer;\n        }\n        else {\n            this._renderer = QuadRenderer.instantiateRenderer();\n            this._rendererIsDisposable = true;\n        }\n        this._scene = new Scene();\n        this._camera = new OrthographicCamera();\n        this._camera.position.set(0, 0, 10);\n        this._camera.left = -0.5;\n        this._camera.right = 0.5;\n        this._camera.top = 0.5;\n        this._camera.bottom = -0.5;\n        this._camera.updateProjectionMatrix();\n        if (!canReadPixels(this._type, this._renderer, this._camera, rtOptions)) {\n            let alternativeType;\n            switch (this._type) {\n                case HalfFloatType:\n                    alternativeType = this._renderer.extensions.has('EXT_color_buffer_float') ? FloatType : undefined;\n                    break;\n            }\n            if (alternativeType !== undefined) {\n                console.warn(`This browser does not support reading pixels from ${this._type} RenderTargets, switching to ${FloatType}`);\n                this._type = alternativeType;\n            }\n            else {\n                this._supportsReadPixels = false;\n                console.warn('This browser dos not support toArray or toDataTexture, calls to those methods will result in an error thrown');\n            }\n        }\n        this._quad = new Mesh(new PlaneGeometry(), this._material);\n        this._quad.geometry.computeBoundingBox();\n        this._scene.add(this._quad);\n        this._renderTarget = new WebGLRenderTarget(this.width, this.height, rtOptions);\n        this._renderTarget.texture.mapping = ((_q = options.renderTargetOptions) === null || _q === void 0 ? void 0 : _q.mapping) !== undefined ? (_r = options.renderTargetOptions) === null || _r === void 0 ? void 0 : _r.mapping : UVMapping;\n    }\n    /**\n     * Instantiates a temporary renderer\n     *\n     * @returns\n     */\n    static instantiateRenderer() {\n        const renderer = new WebGLRenderer();\n        renderer.setSize(128, 128);\n        // renderer.outputColorSpace = SRGBColorSpace\n        // renderer.toneMapping = LinearToneMapping\n        // renderer.debug.checkShaderErrors = false\n        // this._rendererIsDisposable = true\n        return renderer;\n    }\n    /**\n     * Obtains a Buffer containing the rendered texture.\n     *\n     * @throws Error if the browser cannot read pixels from this RenderTarget type.\n     * @returns a TypedArray containing RGBA values from this renderer\n     */\n    toArray() {\n        if (!this._supportsReadPixels)\n            throw new Error('Can\\'t read pixels in this browser');\n        const out = getBufferForType(this._type, this._width, this._height);\n        this._renderer.readRenderTargetPixels(this._renderTarget, 0, 0, this._width, this._height, out);\n        return out;\n    }\n    /**\n     * Performs a readPixel operation in the renderTarget\n     * and returns a DataTexture containing the read data\n     *\n     * @param options options\n     * @returns\n     */\n    toDataTexture(options) {\n        const returnValue = new DataTexture(\n        // fixed values\n        this.toArray(), this.width, this.height, RGBAFormat, this._type, \n        // user values\n        (options === null || options === void 0 ? void 0 : options.mapping) || UVMapping, (options === null || options === void 0 ? void 0 : options.wrapS) || ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.wrapT) || ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.magFilter) || LinearFilter, (options === null || options === void 0 ? void 0 : options.minFilter) || LinearFilter, (options === null || options === void 0 ? void 0 : options.anisotropy) || 1, \n        // fixed value\n        LinearSRGBColorSpace);\n        // set this afterwards, we can't set it in constructor\n        returnValue.generateMipmaps = (options === null || options === void 0 ? void 0 : options.generateMipmaps) !== undefined ? options === null || options === void 0 ? void 0 : options.generateMipmaps : false;\n        return returnValue;\n    }\n    /**\n     * If using a disposable renderer, it will dispose it.\n     */\n    disposeOnDemandRenderer() {\n        this._renderer.setRenderTarget(null);\n        if (this._rendererIsDisposable) {\n            this._renderer.dispose();\n            this._renderer.forceContextLoss();\n        }\n    }\n    /**\n     * Will dispose of **all** assets used by this renderer.\n     *\n     *\n     * @param disposeRenderTarget will dispose of the renderTarget which will not be usable later\n     * set this to true if you passed the `renderTarget.texture` to a `PMREMGenerator`\n     * or are otherwise done with it.\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const mesh = new Mesh(geometry, new MeshBasicMaterial({ map: result.renderTarget.texture }) )\n     * // DO NOT dispose the renderTarget here,\n     * // it is used directly in the material\n     * result.dispose()\n     * ```\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const pmremGenerator = new PMREMGenerator( renderer );\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const envMap = pmremGenerator.fromEquirectangular(result.renderTarget.texture)\n     * const mesh = new Mesh(geometry, new MeshStandardMaterial({ envMap }) )\n     * // renderTarget can be disposed here\n     * // because it was used to generate a PMREM texture\n     * result.dispose(true)\n     * ```\n     */\n    dispose(disposeRenderTarget) {\n        this.disposeOnDemandRenderer();\n        if (disposeRenderTarget) {\n            this.renderTarget.dispose();\n        }\n        // dispose shader material texture uniforms\n        if (this.material instanceof ShaderMaterial) {\n            Object.values(this.material.uniforms).forEach(v => {\n                if (v.value instanceof Texture)\n                    v.value.dispose();\n            });\n        }\n        // dispose other material properties\n        Object.values(this.material).forEach(value => {\n            if (value instanceof Texture)\n                value.dispose();\n        });\n        this.material.dispose();\n        this._quad.geometry.dispose();\n    }\n    /**\n     * Width of the texture\n     */\n    get width() { return this._width; }\n    set width(value) {\n        this._width = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * Height of the texture\n     */\n    get height() { return this._height; }\n    set height(value) {\n        this._height = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * The renderer used\n     */\n    get renderer() { return this._renderer; }\n    /**\n     * The `WebGLRenderTarget` used.\n     */\n    get renderTarget() { return this._renderTarget; }\n    set renderTarget(value) {\n        this._renderTarget = value;\n        this._width = value.width;\n        this._height = value.height;\n        // this._type = value.texture.type\n    }\n    /**\n     * The `Material` used.\n     */\n    get material() { return this._material; }\n    /**\n     *\n     */\n    get type() { return this._type; }\n    get colorSpace() { return this._colorSpace; }\n}\n\nexport { QuadRenderer as Q };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;;AAEA,MAAM,mBAAmB,CAAC,MAAM,OAAO;IACnC,IAAI;IACJ,OAAQ;QACJ,KAAK,+IAAA,CAAA,mBAAgB;YACjB,MAAM,IAAI,kBAAkB,QAAQ,SAAS;YAC7C;QACJ,KAAK,+IAAA,CAAA,gBAAa;YACd,MAAM,IAAI,YAAY,QAAQ,SAAS;YACvC;QACJ,KAAK,+IAAA,CAAA,kBAAe;YAChB,MAAM,IAAI,YAAY,QAAQ,SAAS;YACvC;QACJ,KAAK,+IAAA,CAAA,WAAQ;YACT,MAAM,IAAI,UAAU,QAAQ,SAAS;YACrC;QACJ,KAAK,+IAAA,CAAA,YAAS;YACV,MAAM,IAAI,WAAW,QAAQ,SAAS;YACtC;QACJ,KAAK,+IAAA,CAAA,UAAO;YACR,MAAM,IAAI,WAAW,QAAQ,SAAS;YACtC;QACJ,KAAK,+IAAA,CAAA,YAAS;YACV,MAAM,IAAI,aAAa,QAAQ,SAAS;YACxC;QACJ;YACI,MAAM,IAAI,MAAM;IACxB;IACA,OAAO;AACX;AACA,IAAI;AACJ;;;;;;;;;;;CAWC,GACD,MAAM,gBAAgB,CAAC,MAAM,UAAU,QAAQ;IAC3C,IAAI,yBAAyB,WACzB,OAAO;IACX,MAAM,SAAS,IAAI,+IAAA,CAAA,oBAAiB,CAAC,GAAG,GAAG;IAC3C,SAAS,eAAe,CAAC;IACzB,MAAM,OAAO,IAAI,+IAAA,CAAA,OAAI,CAAC,IAAI,+IAAA,CAAA,gBAAa,IAAI,IAAI,+IAAA,CAAA,oBAAiB,CAAC;QAAE,OAAO;IAAS;IACnF,SAAS,MAAM,CAAC,MAAM;IACtB,SAAS,eAAe,CAAC;IACzB,MAAM,MAAM,iBAAiB,MAAM,OAAO,KAAK,EAAE,OAAO,MAAM;IAC9D,SAAS,sBAAsB,CAAC,QAAQ,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM,EAAE;IAC3E,OAAO,OAAO;IACd,KAAK,QAAQ,CAAC,OAAO;IACrB,KAAK,QAAQ,CAAC,OAAO;IACrB,uBAAuB,GAAG,CAAC,EAAE,KAAK;IAClC,OAAO;AACX;AACA;;;;;CAKC,GACD,MAAM;IACF;;;;KAIC,GACD,YAAY,OAAO,CAAE;QACjB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAChE,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,CAAC,mBAAmB,GAAG;QAC3B;;SAEC,GACD,IAAI,CAAC,MAAM,GAAG;YACV,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa;YACjD,IAAI;gBACA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;YACnD,EACA,OAAO,GAAG;gBACN,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;gBAC/B,MAAM;YACV;YACA,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QACnC;QACA,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;QAC3B,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;QAC7B,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI;QACzB,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU;QACrC,MAAM,YAAY;YACd,gBAAgB;YAChB,QAAQ,+IAAA,CAAA,aAAU;YAClB,aAAa;YACb,eAAe;YACf,eAAe;YACf,MAAM,IAAI,CAAC,KAAK;YAChB,YAAY,IAAI,CAAC,WAAW;YAC5B,YAAY,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG;YAC5M,iBAAiB,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,GAAG;YAC3N,WAAW,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG,+IAAA,CAAA,eAAY;YACrN,WAAW,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG,+IAAA,CAAA,eAAY;YACrN,SAAS,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG;YACnM,OAAO,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,+IAAA,CAAA,sBAAmB;YAChN,OAAO,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,+IAAA,CAAA,sBAAmB;QACpN;QACA,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;QACjC,IAAI,QAAQ,QAAQ,EAAE;YAClB,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;QACrC,OACK;YACD,IAAI,CAAC,SAAS,GAAG,aAAa,mBAAmB;YACjD,IAAI,CAAC,qBAAqB,GAAG;QACjC;QACA,IAAI,CAAC,MAAM,GAAG,IAAI,+IAAA,CAAA,QAAK;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,+IAAA,CAAA,qBAAkB;QACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG;QAChC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;QACrB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACnB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,sBAAsB;QACnC,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY;YACrE,IAAI;YACJ,OAAQ,IAAI,CAAC,KAAK;gBACd,KAAK,+IAAA,CAAA,gBAAa;oBACd,kBAAkB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,4BAA4B,+IAAA,CAAA,YAAS,GAAG;oBACxF;YACR;YACA,IAAI,oBAAoB,WAAW;gBAC/B,QAAQ,IAAI,CAAC,CAAC,kDAAkD,EAAE,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,+IAAA,CAAA,YAAS,EAAE;gBACvH,IAAI,CAAC,KAAK,GAAG;YACjB,OACK;gBACD,IAAI,CAAC,mBAAmB,GAAG;gBAC3B,QAAQ,IAAI,CAAC;YACjB;QACJ;QACA,IAAI,CAAC,KAAK,GAAG,IAAI,+IAAA,CAAA,OAAI,CAAC,IAAI,+IAAA,CAAA,gBAAa,IAAI,IAAI,CAAC,SAAS;QACzD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,+IAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;QACpE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG,+IAAA,CAAA,YAAS;IAC5O;IACA;;;;KAIC,GACD,OAAO,sBAAsB;QACzB,MAAM,WAAW,IAAI,iKAAA,CAAA,gBAAa;QAClC,SAAS,OAAO,CAAC,KAAK;QACtB,6CAA6C;QAC7C,2CAA2C;QAC3C,2CAA2C;QAC3C,oCAAoC;QACpC,OAAO;IACX;IACA;;;;;KAKC,GACD,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,mBAAmB,EACzB,MAAM,IAAI,MAAM;QACpB,MAAM,MAAM,iBAAiB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;QAClE,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE;QAC3F,OAAO;IACX;IACA;;;;;;KAMC,GACD,cAAc,OAAO,EAAE;QACnB,MAAM,cAAc,IAAI,+IAAA,CAAA,cAAW,CACnC,eAAe;QACf,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,+IAAA,CAAA,aAAU,EAAE,IAAI,CAAC,KAAK,EAC/D,cAAc;QACd,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,OAAO,KAAK,+IAAA,CAAA,YAAS,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,+IAAA,CAAA,sBAAmB,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,+IAAA,CAAA,sBAAmB,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,KAAK,+IAAA,CAAA,eAAY,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,KAAK,+IAAA,CAAA,eAAY,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,KAAK,GAC9f,cAAc;QACd,+IAAA,CAAA,uBAAoB;QACpB,sDAAsD;QACtD,YAAY,eAAe,GAAG,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,eAAe,MAAM,YAAY,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,eAAe,GAAG;QACtM,OAAO;IACX;IACA;;KAEC,GACD,0BAA0B;QACtB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QAC/B,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,SAAS,CAAC,OAAO;YACtB,IAAI,CAAC,SAAS,CAAC,gBAAgB;QACnC;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BC,GACD,QAAQ,mBAAmB,EAAE;QACzB,IAAI,CAAC,uBAAuB;QAC5B,IAAI,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC,OAAO;QAC7B;QACA,2CAA2C;QAC3C,IAAI,IAAI,CAAC,QAAQ,YAAY,+IAAA,CAAA,iBAAc,EAAE;YACzC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;gBAC1C,IAAI,EAAE,KAAK,YAAY,+IAAA,CAAA,UAAO,EAC1B,EAAE,KAAK,CAAC,OAAO;YACvB;QACJ;QACA,oCAAoC;QACpC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;YACjC,IAAI,iBAAiB,+IAAA,CAAA,UAAO,EACxB,MAAM,OAAO;QACrB;QACA,IAAI,CAAC,QAAQ,CAAC,OAAO;QACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO;IAC/B;IACA;;KAEC,GACD,IAAI,QAAQ;QAAE,OAAO,IAAI,CAAC,MAAM;IAAE;IAClC,IAAI,MAAM,KAAK,EAAE;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;IACxD;IACA;;KAEC,GACD,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,OAAO;IAAE;IACpC,IAAI,OAAO,KAAK,EAAE;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;IACxD;IACA;;KAEC,GACD,IAAI,WAAW;QAAE,OAAO,IAAI,CAAC,SAAS;IAAE;IACxC;;KAEC,GACD,IAAI,eAAe;QAAE,OAAO,IAAI,CAAC,aAAa;IAAE;IAChD,IAAI,aAAa,KAAK,EAAE;QACpB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK;QACzB,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM;IAC3B,kCAAkC;IACtC;IACA;;KAEC,GACD,IAAI,WAAW;QAAE,OAAO,IAAI,CAAC,SAAS;IAAE;IACxC;;KAEC,GACD,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,KAAK;IAAE;IAChC,IAAI,aAAa;QAAE,OAAO,IAAI,CAAC,WAAW;IAAE;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40monogrid/gainmap-js/dist/decode.js"], "sourcesContent": ["/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\nimport { Q as QuadRenderer } from './QuadRenderer-DuOPRGA4.js';\nimport { ShaderMaterial, Vector3, NoBlending, SRGBColorSpace, LinearSRGBColorSpace, HalfFloatType, Loader, LoadingManager, Texture, UVMapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, FileLoader } from 'three';\n\nconst vertexShader = /* glsl */ `\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n`;\nconst fragmentShader = /* glsl */ `\n// min half float value\n#define HALF_FLOAT_MIN vec3( -65504, -65504, -65504 )\n// max half float value\n#define HALF_FLOAT_MAX vec3( 65504, 65504, 65504 )\n\nuniform sampler2D sdr;\nuniform sampler2D gainMap;\nuniform vec3 gamma;\nuniform vec3 offsetHdr;\nuniform vec3 offsetSdr;\nuniform vec3 gainMapMin;\nuniform vec3 gainMapMax;\nuniform float weightFactor;\n\nvarying vec2 vUv;\n\nvoid main() {\n  vec3 rgb = texture2D( sdr, vUv ).rgb;\n  vec3 recovery = texture2D( gainMap, vUv ).rgb;\n  vec3 logRecovery = pow( recovery, gamma );\n  vec3 logBoost = gainMapMin * ( 1.0 - logRecovery ) + gainMapMax * logRecovery;\n  vec3 hdrColor = (rgb + offsetSdr) * exp2( logBoost * weightFactor ) - offsetHdr;\n  vec3 clampedHdrColor = max( HALF_FLOAT_MIN, min( HALF_FLOAT_MAX, hdrColor ));\n  gl_FragColor = vec4( clampedHdrColor , 1.0 );\n}\n`;\n/**\n * A Material which is able to decode the Gainmap into a full HDR Representation\n *\n * @category Materials\n * @group Materials\n */\nclass GainMapDecoderMaterial extends ShaderMaterial {\n    /**\n     *\n     * @param params\n     */\n    constructor({ gamma, offsetHdr, offsetSdr, gainMapMin, gainMapMax, maxDisplayBoost, hdrCapacityMin, hdrCapacityMax, sdr, gainMap }) {\n        super({\n            name: 'GainMapDecoderMaterial',\n            vertexShader,\n            fragmentShader,\n            uniforms: {\n                sdr: { value: sdr },\n                gainMap: { value: gainMap },\n                gamma: { value: new Vector3(1.0 / gamma[0], 1.0 / gamma[1], 1.0 / gamma[2]) },\n                offsetHdr: { value: new Vector3().fromArray(offsetHdr) },\n                offsetSdr: { value: new Vector3().fromArray(offsetSdr) },\n                gainMapMin: { value: new Vector3().fromArray(gainMapMin) },\n                gainMapMax: { value: new Vector3().fromArray(gainMapMax) },\n                weightFactor: {\n                    value: (Math.log2(maxDisplayBoost) - hdrCapacityMin) / (hdrCapacityMax - hdrCapacityMin)\n                }\n            },\n            blending: NoBlending,\n            depthTest: false,\n            depthWrite: false\n        });\n        this._maxDisplayBoost = maxDisplayBoost;\n        this._hdrCapacityMin = hdrCapacityMin;\n        this._hdrCapacityMax = hdrCapacityMax;\n        this.needsUpdate = true;\n        this.uniformsNeedUpdate = true;\n    }\n    get sdr() { return this.uniforms.sdr.value; }\n    set sdr(value) { this.uniforms.sdr.value = value; }\n    get gainMap() { return this.uniforms.gainMap.value; }\n    set gainMap(value) { this.uniforms.gainMap.value = value; }\n    /**\n     * @see {@link GainMapMetadata.offsetHdr}\n     */\n    get offsetHdr() { return this.uniforms.offsetHdr.value.toArray(); }\n    set offsetHdr(value) { this.uniforms.offsetHdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.offsetSdr}\n     */\n    get offsetSdr() { return this.uniforms.offsetSdr.value.toArray(); }\n    set offsetSdr(value) { this.uniforms.offsetSdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMin}\n     */\n    get gainMapMin() { return this.uniforms.gainMapMin.value.toArray(); }\n    set gainMapMin(value) { this.uniforms.gainMapMin.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMax}\n     */\n    get gainMapMax() { return this.uniforms.gainMapMax.value.toArray(); }\n    set gainMapMax(value) { this.uniforms.gainMapMax.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gamma}\n     */\n    get gamma() {\n        const g = this.uniforms.gamma.value;\n        return [1 / g.x, 1 / g.y, 1 / g.z];\n    }\n    set gamma(value) {\n        const g = this.uniforms.gamma.value;\n        g.x = 1.0 / value[0];\n        g.y = 1.0 / value[1];\n        g.z = 1.0 / value[2];\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMin() { return this._hdrCapacityMin; }\n    set hdrCapacityMin(value) {\n        this._hdrCapacityMin = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMax() { return this._hdrCapacityMax; }\n    set hdrCapacityMax(value) {\n        this._hdrCapacityMax = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainmapDecodingParameters.maxDisplayBoost}\n     * @remarks Non Logarithmic space\n     */\n    get maxDisplayBoost() { return this._maxDisplayBoost; }\n    set maxDisplayBoost(value) {\n        this._maxDisplayBoost = Math.max(1, Math.min(65504, value));\n        this.calculateWeight();\n    }\n    calculateWeight() {\n        const val = (Math.log2(this._maxDisplayBoost) - this._hdrCapacityMin) / (this._hdrCapacityMax - this._hdrCapacityMin);\n        this.uniforms.weightFactor.value = Math.max(0, Math.min(1, val));\n    }\n}\n\n/**\n * Decodes a gain map using a WebGLRenderTarget\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @example\n * import { decode } from '@monogrid/gainmap-js'\n * import {\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   TextureLoader,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const textureLoader = new TextureLoader()\n *\n * // load SDR Representation\n * const sdr = await textureLoader.loadAsync('sdr.jpg')\n * // load Gain map recovery image\n * const gainMap = await textureLoader.loadAsync('gainmap.jpg')\n * // load metadata\n * const metadata = await (await fetch('metadata.json')).json()\n *\n * const result = await decode({\n *   sdr,\n *   gainMap,\n *   // this allows to use `result.renderTarget.texture` directly\n *   renderer,\n *   // this will restore the full HDR range\n *   maxDisplayBoost: Math.pow(2, metadata.hdrCapacityMax),\n *   ...metadata\n * })\n *\n * const scene = new Scene()\n * // `result` can be used to populate a Texture\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n * @param params\n * @returns\n * @throws {Error} if the WebGLRenderer fails to render the gain map\n */\nconst decode = (params) => {\n    const { sdr, gainMap, renderer } = params;\n    if (sdr.colorSpace !== SRGBColorSpace) {\n        console.warn('SDR Colorspace needs to be *SRGBColorSpace*, setting it automatically');\n        sdr.colorSpace = SRGBColorSpace;\n    }\n    sdr.needsUpdate = true;\n    if (gainMap.colorSpace !== LinearSRGBColorSpace) {\n        console.warn('Gainmap Colorspace needs to be *LinearSRGBColorSpace*, setting it automatically');\n        gainMap.colorSpace = LinearSRGBColorSpace;\n    }\n    gainMap.needsUpdate = true;\n    const material = new GainMapDecoderMaterial({\n        ...params,\n        sdr,\n        gainMap\n    });\n    const quadRenderer = new QuadRenderer({\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        width: sdr.image.width,\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        height: sdr.image.height,\n        type: HalfFloatType,\n        colorSpace: LinearSRGBColorSpace,\n        material,\n        renderer,\n        renderTargetOptions: params.renderTargetOptions\n    });\n    try {\n        quadRenderer.render();\n    }\n    catch (e) {\n        quadRenderer.disposeOnDemandRenderer();\n        throw e;\n    }\n    return quadRenderer;\n};\n\nclass GainMapNotFoundError extends Error {\n}\n\nclass XMPMetadataNotFoundError extends Error {\n}\n\nconst getXMLValue = (xml, tag, defaultValue) => {\n    // Check for attribute format first: tag=\"value\"\n    const attributeMatch = new RegExp(`${tag}=\"([^\"]*)\"`, 'i').exec(xml);\n    if (attributeMatch)\n        return attributeMatch[1];\n    // Check for tag format: <tag>value</tag> or <tag><rdf:li>value</rdf:li>...</tag>\n    const tagMatch = new RegExp(`<${tag}[^>]*>([\\\\s\\\\S]*?)</${tag}>`, 'i').exec(xml);\n    if (tagMatch) {\n        // Check if it contains rdf:li elements\n        const liValues = tagMatch[1].match(/<rdf:li>([^<]*)<\\/rdf:li>/g);\n        if (liValues && liValues.length === 3) {\n            return liValues.map(v => v.replace(/<\\/?rdf:li>/g, ''));\n        }\n        return tagMatch[1].trim();\n    }\n    if (defaultValue !== undefined)\n        return defaultValue;\n    throw new Error(`Can't find ${tag} in gainmap metadata`);\n};\nconst extractXMP = (input) => {\n    let str;\n    // support node test environment\n    if (typeof TextDecoder !== 'undefined')\n        str = new TextDecoder().decode(input);\n    else\n        str = input.toString();\n    let start = str.indexOf('<x:xmpmeta');\n    while (start !== -1) {\n        const end = str.indexOf('x:xmpmeta>', start);\n        const xmpBlock = str.slice(start, end + 10);\n        try {\n            const gainMapMin = getXMLValue(xmpBlock, 'hdrgm:GainMapMin', '0');\n            const gainMapMax = getXMLValue(xmpBlock, 'hdrgm:GainMapMax');\n            const gamma = getXMLValue(xmpBlock, 'hdrgm:Gamma', '1');\n            const offsetSDR = getXMLValue(xmpBlock, 'hdrgm:OffsetSDR', '0.015625');\n            const offsetHDR = getXMLValue(xmpBlock, 'hdrgm:OffsetHDR', '0.015625');\n            // These are always attributes, so we can use a simpler regex\n            const hdrCapacityMinMatch = /hdrgm:HDRCapacityMin=\"([^\"]*)\"/.exec(xmpBlock);\n            const hdrCapacityMin = hdrCapacityMinMatch ? hdrCapacityMinMatch[1] : '0';\n            const hdrCapacityMaxMatch = /hdrgm:HDRCapacityMax=\"([^\"]*)\"/.exec(xmpBlock);\n            if (!hdrCapacityMaxMatch)\n                throw new Error('Incomplete gainmap metadata');\n            const hdrCapacityMax = hdrCapacityMaxMatch[1];\n            return {\n                gainMapMin: Array.isArray(gainMapMin) ? gainMapMin.map(v => parseFloat(v)) : [parseFloat(gainMapMin), parseFloat(gainMapMin), parseFloat(gainMapMin)],\n                gainMapMax: Array.isArray(gainMapMax) ? gainMapMax.map(v => parseFloat(v)) : [parseFloat(gainMapMax), parseFloat(gainMapMax), parseFloat(gainMapMax)],\n                gamma: Array.isArray(gamma) ? gamma.map(v => parseFloat(v)) : [parseFloat(gamma), parseFloat(gamma), parseFloat(gamma)],\n                offsetSdr: Array.isArray(offsetSDR) ? offsetSDR.map(v => parseFloat(v)) : [parseFloat(offsetSDR), parseFloat(offsetSDR), parseFloat(offsetSDR)],\n                offsetHdr: Array.isArray(offsetHDR) ? offsetHDR.map(v => parseFloat(v)) : [parseFloat(offsetHDR), parseFloat(offsetHDR), parseFloat(offsetHDR)],\n                hdrCapacityMin: parseFloat(hdrCapacityMin),\n                hdrCapacityMax: parseFloat(hdrCapacityMax)\n            };\n        }\n        catch (e) {\n            // Continue searching for another xmpmeta block if this one fails\n        }\n        start = str.indexOf('<x:xmpmeta', end);\n    }\n};\n\n/**\n * MPF Extractor (Multi Picture Format Extractor)\n * By Henrik S Nilsson 2019\n *\n * Extracts images stored in images based on the MPF format (found here: https://www.cipa.jp/e/std/std-sec.html\n * under \"CIPA DC-007-Translation-2021 Multi-Picture Format\"\n *\n * Overly commented, and without intention of being complete or production ready.\n * Created to extract depth maps from iPhone images, and to learn about image metadata.\n * Kudos to: Phil Harvey (exiftool), Jaume Sanchez (android-lens-blur-depth-extractor)\n */\nclass MPFExtractor {\n    constructor(options) {\n        this.options = {\n            debug: options && options.debug !== undefined ? options.debug : false,\n            extractFII: options && options.extractFII !== undefined ? options.extractFII : true,\n            extractNonFII: options && options.extractNonFII !== undefined ? options.extractNonFII : true\n        };\n    }\n    extract(imageArrayBuffer) {\n        return new Promise((resolve, reject) => {\n            const debug = this.options.debug;\n            const dataView = new DataView(imageArrayBuffer.buffer);\n            // If you're executing this line on a big endian machine, it'll be reversed.\n            // bigEnd further down though, refers to the endianness of the image itself.\n            if (dataView.getUint16(0) !== 0xffd8) {\n                reject(new Error('Not a valid jpeg'));\n                return;\n            }\n            const length = dataView.byteLength;\n            let offset = 2;\n            let loops = 0;\n            let marker; // APP# marker\n            while (offset < length) {\n                if (++loops > 250) {\n                    reject(new Error(`Found no marker after ${loops} loops 😵`));\n                    return;\n                }\n                if (dataView.getUint8(offset) !== 0xff) {\n                    reject(new Error(`Not a valid marker at offset 0x${offset.toString(16)}, found: 0x${dataView.getUint8(offset).toString(16)}`));\n                    return;\n                }\n                marker = dataView.getUint8(offset + 1);\n                if (debug)\n                    console.log(`Marker: ${marker.toString(16)}`);\n                if (marker === 0xe2) {\n                    if (debug)\n                        console.log('Found APP2 marker (0xffe2)');\n                    // Works for iPhone 8 Plus, X, and XSMax. Or any photos of MPF format.\n                    // Great way to visualize image information in html is using Exiftool. E.g.:\n                    // ./exiftool.exe -htmldump -wantTrailer photo.jpg > photo.html\n                    const formatPt = offset + 4;\n                    /*\n                     *  Structure of the MP Format Identifier\n                     *\n                     *  Offset Addr.  | Code (Hex)  | Description\n                     *  +00             ff            Marker Prefix      <-- offset\n                     *  +01             e2            APP2\n                     *  +02             #n            APP2 Field Length\n                     *  +03             #n            APP2 Field Length\n                     *  +04             4d            'M'                <-- formatPt\n                     *  +05             50            'P'\n                     *  +06             46            'F'\n                     *  +07             00            NULL\n                     *                                                   <-- tiffOffset\n                     */\n                    if (dataView.getUint32(formatPt) === 0x4d504600) {\n                        // Found MPF tag, so we start dig out sub images\n                        const tiffOffset = formatPt + 4;\n                        let bigEnd; // Endianness from TIFF header\n                        // Test for TIFF validity and endianness\n                        // 0x4949 and 0x4D4D ('II' and 'MM') marks Little Endian and Big Endian\n                        if (dataView.getUint16(tiffOffset) === 0x4949) {\n                            bigEnd = false;\n                        }\n                        else if (dataView.getUint16(tiffOffset) === 0x4d4d) {\n                            bigEnd = true;\n                        }\n                        else {\n                            reject(new Error('No valid endianness marker found in TIFF header'));\n                            return;\n                        }\n                        if (dataView.getUint16(tiffOffset + 2, !bigEnd) !== 0x002a) {\n                            reject(new Error('Not valid TIFF data! (no 0x002A marker)'));\n                            return;\n                        }\n                        // 32 bit number stating the offset from the start of the 8 Byte MP Header\n                        // to MP Index IFD Least possible value is thus 8 (means 0 offset)\n                        const firstIFDOffset = dataView.getUint32(tiffOffset + 4, !bigEnd);\n                        if (firstIFDOffset < 0x00000008) {\n                            reject(new Error('Not valid TIFF data! (First offset less than 8)'));\n                            return;\n                        }\n                        // Move ahead to MP Index IFD\n                        // Assume we're at the first IFD, so firstIFDOffset points to\n                        // MP Index IFD and not MP Attributes IFD. (If we try extract from a sub image,\n                        // we fail silently here due to this assumption)\n                        // Count (2 Byte) | MP Index Fields a.k.a. MP Entries (count * 12 Byte) | Offset of Next IFD (4 Byte)\n                        const dirStart = tiffOffset + firstIFDOffset; // Start of IFD (Image File Directory)\n                        const count = dataView.getUint16(dirStart, !bigEnd); // Count of MPEntries (2 Byte)\n                        // Extract info from MPEntries (starting after Count)\n                        const entriesStart = dirStart + 2;\n                        let numberOfImages = 0;\n                        for (let i = entriesStart; i < entriesStart + 12 * count; i += 12) {\n                            // Each entry is 12 Bytes long\n                            // Check MP Index IFD tags, here we only take tag 0xb001 = Number of images\n                            if (dataView.getUint16(i, !bigEnd) === 0xb001) {\n                                // stored in Last 4 bytes of its 12 Byte entry.\n                                numberOfImages = dataView.getUint32(i + 8, !bigEnd);\n                            }\n                        }\n                        const nextIFDOffsetLen = 4; // 4 Byte offset field that appears after MP Index IFD tags\n                        const MPImageListValPt = dirStart + 2 + count * 12 + nextIFDOffsetLen;\n                        const images = [];\n                        for (let i = MPImageListValPt; i < MPImageListValPt + numberOfImages * 16; i += 16) {\n                            const image = {\n                                MPType: dataView.getUint32(i, !bigEnd),\n                                size: dataView.getUint32(i + 4, !bigEnd),\n                                // This offset is specified relative to the address of the MP Endian\n                                // field in the MP Header, unless the image is a First Individual Image,\n                                // in which case the value of the offset shall be NULL (0x00000000).\n                                dataOffset: dataView.getUint32(i + 8, !bigEnd),\n                                dependantImages: dataView.getUint32(i + 12, !bigEnd),\n                                start: -1,\n                                end: -1,\n                                isFII: false\n                            };\n                            if (!image.dataOffset) {\n                                // dataOffset is 0x00000000 for First Individual Image\n                                image.start = 0;\n                                image.isFII = true;\n                            }\n                            else {\n                                image.start = tiffOffset + image.dataOffset;\n                                image.isFII = false;\n                            }\n                            image.end = image.start + image.size;\n                            images.push(image);\n                        }\n                        if (this.options.extractNonFII && images.length) {\n                            const bufferBlob = new Blob([dataView]);\n                            const imgs = [];\n                            for (const image of images) {\n                                if (image.isFII && !this.options.extractFII) {\n                                    continue; // Skip FII\n                                }\n                                const imageBlob = bufferBlob.slice(image.start, image.end + 1, 'image/jpeg');\n                                // we don't need this\n                                // const imageUrl = URL.createObjectURL(imageBlob)\n                                // image.img = document.createElement('img')\n                                // image.img.src = imageUrl\n                                imgs.push(imageBlob);\n                            }\n                            resolve(imgs);\n                        }\n                    }\n                }\n                offset += 2 + dataView.getUint16(offset + 2);\n            }\n        });\n    }\n}\n\n/**\n * Extracts XMP Metadata and the gain map recovery image\n * from a single JPEG file.\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @param jpegFile an `Uint8Array` containing and encoded JPEG file\n * @returns an sdr `Uint8Array` compressed in JPEG, a gainMap `Uint8Array` compressed in JPEG and the XMP parsed XMP metadata\n * @throws Error if XMP Metadata is not found\n * @throws Error if Gain map image is not found\n * @example\n * import { FileLoader } from 'three'\n * import { extractGainmapFromJPEG } from '@monogrid/gainmap-js'\n *\n * const jpegFile = await new FileLoader()\n *  .setResponseType('arraybuffer')\n *  .loadAsync('image.jpg')\n *\n * const { sdr, gainMap, metadata } = extractGainmapFromJPEG(jpegFile)\n */\nconst extractGainmapFromJPEG = async (jpegFile) => {\n    const metadata = extractXMP(jpegFile);\n    if (!metadata)\n        throw new XMPMetadataNotFoundError('Gain map XMP metadata not found');\n    const mpfExtractor = new MPFExtractor({ extractFII: true, extractNonFII: true });\n    const images = await mpfExtractor.extract(jpegFile);\n    if (images.length !== 2)\n        throw new GainMapNotFoundError('Gain map recovery image not found');\n    return {\n        sdr: new Uint8Array(await images[0].arrayBuffer()),\n        gainMap: new Uint8Array(await images[1].arrayBuffer()),\n        metadata\n    };\n};\n\n/**\n * private function, async get image from blob\n *\n * @param blob\n * @returns\n */\nconst getHTMLImageFromBlob = (blob) => {\n    return new Promise((resolve, reject) => {\n        const img = document.createElement('img');\n        img.onload = () => { resolve(img); };\n        img.onerror = (e) => { reject(e); };\n        img.src = URL.createObjectURL(blob);\n    });\n};\n\nclass LoaderBase extends Loader {\n    /**\n     *\n     * @param renderer\n     * @param manager\n     */\n    constructor(renderer, manager) {\n        super(manager);\n        if (renderer)\n            this._renderer = renderer;\n        this._internalLoadingManager = new LoadingManager();\n    }\n    /**\n     * Specify the renderer to use when rendering the gain map\n     *\n     * @param renderer\n     * @returns\n     */\n    setRenderer(renderer) {\n        this._renderer = renderer;\n        return this;\n    }\n    /**\n     * Specify the renderTarget options to use when rendering the gain map\n     *\n     * @param options\n     * @returns\n     */\n    setRenderTargetOptions(options) {\n        this._renderTargetOptions = options;\n        return this;\n    }\n    /**\n     * @private\n     * @returns\n     */\n    prepareQuadRenderer() {\n        if (!this._renderer)\n            console.warn('WARNING: An existing WebGL Renderer was not passed to this Loader constructor or in setRenderer, the result of this Loader will need to be converted to a Data Texture with toDataTexture() before you can use it in your renderer.');\n        // temporary values\n        const material = new GainMapDecoderMaterial({\n            gainMapMax: [1, 1, 1],\n            gainMapMin: [0, 0, 0],\n            gamma: [1, 1, 1],\n            offsetHdr: [1, 1, 1],\n            offsetSdr: [1, 1, 1],\n            hdrCapacityMax: 1,\n            hdrCapacityMin: 0,\n            maxDisplayBoost: 1,\n            gainMap: new Texture(),\n            sdr: new Texture()\n        });\n        return new QuadRenderer({\n            width: 16,\n            height: 16,\n            type: HalfFloatType,\n            colorSpace: LinearSRGBColorSpace,\n            material,\n            renderer: this._renderer,\n            renderTargetOptions: this._renderTargetOptions\n        });\n    }\n    /**\n   * @private\n   * @param quadRenderer\n   * @param metadata\n   * @param sdrBuffer\n   * @param gainMapBuffer\n   */\n    async render(quadRenderer, metadata, sdrBuffer, gainMapBuffer) {\n        // this is optional, will render a black gain-map if not present\n        const gainMapBlob = gainMapBuffer ? new Blob([gainMapBuffer], { type: 'image/jpeg' }) : undefined;\n        const sdrBlob = new Blob([sdrBuffer], { type: 'image/jpeg' });\n        let sdrImage;\n        let gainMapImage;\n        let needsFlip = false;\n        if (typeof createImageBitmap === 'undefined') {\n            const res = await Promise.all([\n                gainMapBlob ? getHTMLImageFromBlob(gainMapBlob) : Promise.resolve(undefined),\n                getHTMLImageFromBlob(sdrBlob)\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n            needsFlip = true;\n        }\n        else {\n            const res = await Promise.all([\n                gainMapBlob ? createImageBitmap(gainMapBlob, { imageOrientation: 'flipY' }) : Promise.resolve(undefined),\n                createImageBitmap(sdrBlob, { imageOrientation: 'flipY' })\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n        }\n        const gainMap = new Texture(gainMapImage || new ImageData(2, 2), UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, 1, LinearSRGBColorSpace);\n        gainMap.flipY = needsFlip;\n        gainMap.needsUpdate = true;\n        const sdr = new Texture(sdrImage, UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, 1, SRGBColorSpace);\n        sdr.flipY = needsFlip;\n        sdr.needsUpdate = true;\n        quadRenderer.width = sdrImage.width;\n        quadRenderer.height = sdrImage.height;\n        quadRenderer.material.gainMap = gainMap;\n        quadRenderer.material.sdr = sdr;\n        quadRenderer.material.gainMapMin = metadata.gainMapMin;\n        quadRenderer.material.gainMapMax = metadata.gainMapMax;\n        quadRenderer.material.offsetHdr = metadata.offsetHdr;\n        quadRenderer.material.offsetSdr = metadata.offsetSdr;\n        quadRenderer.material.gamma = metadata.gamma;\n        quadRenderer.material.hdrCapacityMin = metadata.hdrCapacityMin;\n        quadRenderer.material.hdrCapacityMax = metadata.hdrCapacityMax;\n        quadRenderer.material.maxDisplayBoost = Math.pow(2, metadata.hdrCapacityMax);\n        quadRenderer.material.needsUpdate = true;\n        quadRenderer.render();\n    }\n}\n\n/**\n * A Three.js Loader for the gain map format.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { GainMapLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new GainMapLoader(renderer)\n *\n * const result = await loader.loadAsync(['sdr.jpeg', 'gainmap.jpeg', 'metadata.json'])\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass GainMapLoader extends LoaderBase {\n    /**\n     * Loads a gainmap using separate data\n     * * sdr image\n     * * gain map image\n     * * metadata json\n     *\n     * useful for webp gain maps\n     *\n     * @param urls An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load([sdrUrl, gainMapUrl, metadataUrl], onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        let sdr;\n        let gainMap;\n        let metadata;\n        const loadCheck = async () => {\n            if (sdr && gainMap && metadata) {\n                // solves #16\n                try {\n                    await this.render(quadRenderer, metadata, sdr, gainMap);\n                }\n                catch (error) {\n                    this.manager.itemError(sdrUrl);\n                    this.manager.itemError(gainMapUrl);\n                    this.manager.itemError(metadataUrl);\n                    if (typeof onError === 'function')\n                        onError(error);\n                    quadRenderer.disposeOnDemandRenderer();\n                    return;\n                }\n                if (typeof onLoad === 'function')\n                    onLoad(quadRenderer);\n                this.manager.itemEnd(sdrUrl);\n                this.manager.itemEnd(gainMapUrl);\n                this.manager.itemEnd(metadataUrl);\n                quadRenderer.disposeOnDemandRenderer();\n            }\n        };\n        let sdrLengthComputable = true;\n        let sdrTotal = 0;\n        let sdrLoaded = 0;\n        let gainMapLengthComputable = true;\n        let gainMapTotal = 0;\n        let gainMapLoaded = 0;\n        let metadataLengthComputable = true;\n        let metadataTotal = 0;\n        let metadataLoaded = 0;\n        const progressHandler = () => {\n            if (typeof onProgress === 'function') {\n                const total = sdrTotal + gainMapTotal + metadataTotal;\n                const loaded = sdrLoaded + gainMapLoaded + metadataLoaded;\n                const lengthComputable = sdrLengthComputable && gainMapLengthComputable && metadataLengthComputable;\n                onProgress(new ProgressEvent('progress', { lengthComputable, loaded, total }));\n            }\n        };\n        this.manager.itemStart(sdrUrl);\n        this.manager.itemStart(gainMapUrl);\n        this.manager.itemStart(metadataUrl);\n        const sdrLoader = new FileLoader(this._internalLoadingManager);\n        sdrLoader.setResponseType('arraybuffer');\n        sdrLoader.setRequestHeader(this.requestHeader);\n        sdrLoader.setPath(this.path);\n        sdrLoader.setWithCredentials(this.withCredentials);\n        sdrLoader.load(sdrUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid sdr buffer');\n            sdr = buffer;\n            await loadCheck();\n        }, (e) => {\n            sdrLengthComputable = e.lengthComputable;\n            sdrLoaded = e.loaded;\n            sdrTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(sdrUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const gainMapLoader = new FileLoader(this._internalLoadingManager);\n        gainMapLoader.setResponseType('arraybuffer');\n        gainMapLoader.setRequestHeader(this.requestHeader);\n        gainMapLoader.setPath(this.path);\n        gainMapLoader.setWithCredentials(this.withCredentials);\n        gainMapLoader.load(gainMapUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid gainmap buffer');\n            gainMap = buffer;\n            await loadCheck();\n        }, (e) => {\n            gainMapLengthComputable = e.lengthComputable;\n            gainMapLoaded = e.loaded;\n            gainMapTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(gainMapUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const metadataLoader = new FileLoader(this._internalLoadingManager);\n        // metadataLoader.setResponseType('json')\n        metadataLoader.setRequestHeader(this.requestHeader);\n        metadataLoader.setPath(this.path);\n        metadataLoader.setWithCredentials(this.withCredentials);\n        metadataLoader.load(metadataUrl, async (json) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof json !== 'string')\n                throw new Error('Invalid metadata string');\n            // TODO: implement check on JSON file and remove this eslint disable\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            metadata = JSON.parse(json);\n            await loadCheck();\n        }, (e) => {\n            metadataLengthComputable = e.lengthComputable;\n            metadataLoaded = e.loaded;\n            metadataTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(metadataUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\n/**\n * A Three.js Loader for a JPEG with embedded gainmap metadata.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { HDRJPGLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new HDRJPGLoader(renderer)\n *\n * const result = await loader.loadAsync('gainmap.jpeg')\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass HDRJPGLoader extends LoaderBase {\n    /**\n     * Loads a JPEG containing gain map metadata\n     * Renders a normal SDR image if gainmap data is not found\n     *\n     * @param url An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load(url, onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        const loader = new FileLoader(this._internalLoadingManager);\n        loader.setResponseType('arraybuffer');\n        loader.setRequestHeader(this.requestHeader);\n        loader.setPath(this.path);\n        loader.setWithCredentials(this.withCredentials);\n        this.manager.itemStart(url);\n        loader.load(url, async (jpeg) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof jpeg === 'string')\n                throw new Error('Invalid buffer, received [string], was expecting [ArrayBuffer]');\n            const jpegBuffer = new Uint8Array(jpeg);\n            let sdrJPEG;\n            let gainMapJPEG;\n            let metadata;\n            try {\n                const extractionResult = await extractGainmapFromJPEG(jpegBuffer);\n                // gain map is successfully reconstructed\n                sdrJPEG = extractionResult.sdr;\n                gainMapJPEG = extractionResult.gainMap;\n                metadata = extractionResult.metadata;\n            }\n            catch (e) {\n                // render the SDR version if this is not a gainmap\n                if (e instanceof XMPMetadataNotFoundError || e instanceof GainMapNotFoundError) {\n                    console.warn(`Failure to reconstruct an HDR image from ${url}: Gain map metadata not found in the file, HDRJPGLoader will render the SDR jpeg`);\n                    metadata = {\n                        gainMapMin: [0, 0, 0],\n                        gainMapMax: [1, 1, 1],\n                        gamma: [1, 1, 1],\n                        hdrCapacityMin: 0,\n                        hdrCapacityMax: 1,\n                        offsetHdr: [0, 0, 0],\n                        offsetSdr: [0, 0, 0]\n                    };\n                    sdrJPEG = jpegBuffer;\n                }\n                else {\n                    throw e;\n                }\n            }\n            // solves #16\n            try {\n                await this.render(quadRenderer, metadata, sdrJPEG, gainMapJPEG);\n            }\n            catch (error) {\n                this.manager.itemError(url);\n                if (typeof onError === 'function')\n                    onError(error);\n                quadRenderer.disposeOnDemandRenderer();\n                return;\n            }\n            if (typeof onLoad === 'function')\n                onLoad(quadRenderer);\n            this.manager.itemEnd(url);\n            quadRenderer.disposeOnDemandRenderer();\n        }, onProgress, (error) => {\n            this.manager.itemError(url);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\nexport { GainMapDecoderMaterial, GainMapLoader, HDRJPGLoader, HDRJPGLoader as JPEGRLoader, MPFExtractor, QuadRenderer, decode, extractGainmapFromJPEG, extractXMP };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;AACA;;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC;;;;;;;AAOjC,CAAC;AACD,MAAM,iBAAiB,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BnC,CAAC;AACD;;;;;CAKC,GACD,MAAM,+BAA+B,+IAAA,CAAA,iBAAc;IAC/C;;;KAGC,GACD,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,EAAE,OAAO,EAAE,CAAE;QAChI,KAAK,CAAC;YACF,MAAM;YACN;YACA;YACA,UAAU;gBACN,KAAK;oBAAE,OAAO;gBAAI;gBAClB,SAAS;oBAAE,OAAO;gBAAQ;gBAC1B,OAAO;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,CAAC,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,EAAE;gBAAE;gBAC5E,WAAW;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;gBAAW;gBACvD,WAAW;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;gBAAW;gBACvD,YAAY;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;gBAAY;gBACzD,YAAY;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;gBAAY;gBACzD,cAAc;oBACV,OAAO,CAAC,KAAK,IAAI,CAAC,mBAAmB,cAAc,IAAI,CAAC,iBAAiB,cAAc;gBAC3F;YACJ;YACA,UAAU,+IAAA,CAAA,aAAU;YACpB,WAAW;YACX,YAAY;QAChB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IACA,IAAI,MAAM;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK;IAAE;IAC5C,IAAI,IAAI,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAG;IAAO;IAClD,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;IAAE;IACpD,IAAI,QAAQ,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG;IAAO;IAC1D;;KAEC,GACD,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO;IAAI;IAClE,IAAI,UAAU,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;IAAQ;IACvE;;KAEC,GACD,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO;IAAI;IAClE,IAAI,UAAU,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;IAAQ;IACvE;;KAEC,GACD,IAAI,aAAa;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO;IAAI;IACpE,IAAI,WAAW,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC;IAAQ;IACzE;;KAEC,GACD,IAAI,aAAa;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO;IAAI;IACpE,IAAI,WAAW,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC;IAAQ;IACzE;;KAEC,GACD,IAAI,QAAQ;QACR,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK;QACnC,OAAO;YAAC,IAAI,EAAE,CAAC;YAAE,IAAI,EAAE,CAAC;YAAE,IAAI,EAAE,CAAC;SAAC;IACtC;IACA,IAAI,MAAM,KAAK,EAAE;QACb,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK;QACnC,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE;QACpB,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE;QACpB,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE;IACxB;IACA;;;KAGC,GACD,IAAI,iBAAiB;QAAE,OAAO,IAAI,CAAC,eAAe;IAAE;IACpD,IAAI,eAAe,KAAK,EAAE;QACtB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,eAAe;IACxB;IACA;;;KAGC,GACD,IAAI,iBAAiB;QAAE,OAAO,IAAI,CAAC,eAAe;IAAE;IACpD,IAAI,eAAe,KAAK,EAAE;QACtB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,eAAe;IACxB;IACA;;;KAGC,GACD,IAAI,kBAAkB;QAAE,OAAO,IAAI,CAAC,gBAAgB;IAAE;IACtD,IAAI,gBAAgB,KAAK,EAAE;QACvB,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;QACpD,IAAI,CAAC,eAAe;IACxB;IACA,kBAAkB;QACd,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe;QACpH,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;IAC/D;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsDC,GACD,MAAM,SAAS,CAAC;IACZ,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;IACnC,IAAI,IAAI,UAAU,KAAK,+IAAA,CAAA,iBAAc,EAAE;QACnC,QAAQ,IAAI,CAAC;QACb,IAAI,UAAU,GAAG,+IAAA,CAAA,iBAAc;IACnC;IACA,IAAI,WAAW,GAAG;IAClB,IAAI,QAAQ,UAAU,KAAK,+IAAA,CAAA,uBAAoB,EAAE;QAC7C,QAAQ,IAAI,CAAC;QACb,QAAQ,UAAU,GAAG,+IAAA,CAAA,uBAAoB;IAC7C;IACA,QAAQ,WAAW,GAAG;IACtB,MAAM,WAAW,IAAI,uBAAuB;QACxC,GAAG,MAAM;QACT;QACA;IACJ;IACA,MAAM,eAAe,IAAI,+KAAA,CAAA,IAAY,CAAC;QAClC,6EAA6E;QAC7E,+GAA+G;QAC/G,OAAO,IAAI,KAAK,CAAC,KAAK;QACtB,6EAA6E;QAC7E,+GAA+G;QAC/G,QAAQ,IAAI,KAAK,CAAC,MAAM;QACxB,MAAM,+IAAA,CAAA,gBAAa;QACnB,YAAY,+IAAA,CAAA,uBAAoB;QAChC;QACA;QACA,qBAAqB,OAAO,mBAAmB;IACnD;IACA,IAAI;QACA,aAAa,MAAM;IACvB,EACA,OAAO,GAAG;QACN,aAAa,uBAAuB;QACpC,MAAM;IACV;IACA,OAAO;AACX;AAEA,MAAM,6BAA6B;AACnC;AAEA,MAAM,iCAAiC;AACvC;AAEA,MAAM,cAAc,CAAC,KAAK,KAAK;IAC3B,gDAAgD;IAChD,MAAM,iBAAiB,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC;IAChE,IAAI,gBACA,OAAO,cAAc,CAAC,EAAE;IAC5B,iFAAiF;IACjF,MAAM,WAAW,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,oBAAoB,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC;IAC5E,IAAI,UAAU;QACV,uCAAuC;QACvC,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;QACnC,IAAI,YAAY,SAAS,MAAM,KAAK,GAAG;YACnC,OAAO,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,gBAAgB;QACvD;QACA,OAAO,QAAQ,CAAC,EAAE,CAAC,IAAI;IAC3B;IACA,IAAI,iBAAiB,WACjB,OAAO;IACX,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,oBAAoB,CAAC;AAC3D;AACA,MAAM,aAAa,CAAC;IAChB,IAAI;IACJ,gCAAgC;IAChC,IAAI,OAAO,gBAAgB,aACvB,MAAM,IAAI,cAAc,MAAM,CAAC;SAE/B,MAAM,MAAM,QAAQ;IACxB,IAAI,QAAQ,IAAI,OAAO,CAAC;IACxB,MAAO,UAAU,CAAC,EAAG;QACjB,MAAM,MAAM,IAAI,OAAO,CAAC,cAAc;QACtC,MAAM,WAAW,IAAI,KAAK,CAAC,OAAO,MAAM;QACxC,IAAI;YACA,MAAM,aAAa,YAAY,UAAU,oBAAoB;YAC7D,MAAM,aAAa,YAAY,UAAU;YACzC,MAAM,QAAQ,YAAY,UAAU,eAAe;YACnD,MAAM,YAAY,YAAY,UAAU,mBAAmB;YAC3D,MAAM,YAAY,YAAY,UAAU,mBAAmB;YAC3D,6DAA6D;YAC7D,MAAM,sBAAsB,iCAAiC,IAAI,CAAC;YAClE,MAAM,iBAAiB,sBAAsB,mBAAmB,CAAC,EAAE,GAAG;YACtE,MAAM,sBAAsB,iCAAiC,IAAI,CAAC;YAClE,IAAI,CAAC,qBACD,MAAM,IAAI,MAAM;YACpB,MAAM,iBAAiB,mBAAmB,CAAC,EAAE;YAC7C,OAAO;gBACH,YAAY,MAAM,OAAO,CAAC,cAAc,WAAW,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAa,WAAW;oBAAa,WAAW;iBAAY;gBACrJ,YAAY,MAAM,OAAO,CAAC,cAAc,WAAW,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAa,WAAW;oBAAa,WAAW;iBAAY;gBACrJ,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAQ,WAAW;oBAAQ,WAAW;iBAAO;gBACvH,WAAW,MAAM,OAAO,CAAC,aAAa,UAAU,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAY,WAAW;oBAAY,WAAW;iBAAW;gBAC/I,WAAW,MAAM,OAAO,CAAC,aAAa,UAAU,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAY,WAAW;oBAAY,WAAW;iBAAW;gBAC/I,gBAAgB,WAAW;gBAC3B,gBAAgB,WAAW;YAC/B;QACJ,EACA,OAAO,GAAG;QACN,iEAAiE;QACrE;QACA,QAAQ,IAAI,OAAO,CAAC,cAAc;IACtC;AACJ;AAEA;;;;;;;;;;CAUC,GACD,MAAM;IACF,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,WAAW,QAAQ,KAAK,KAAK,YAAY,QAAQ,KAAK,GAAG;YAChE,YAAY,WAAW,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,GAAG;YAC/E,eAAe,WAAW,QAAQ,aAAa,KAAK,YAAY,QAAQ,aAAa,GAAG;QAC5F;IACJ;IACA,QAAQ,gBAAgB,EAAE;QACtB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK;YAChC,MAAM,WAAW,IAAI,SAAS,iBAAiB,MAAM;YACrD,4EAA4E;YAC5E,4EAA4E;YAC5E,IAAI,SAAS,SAAS,CAAC,OAAO,QAAQ;gBAClC,OAAO,IAAI,MAAM;gBACjB;YACJ;YACA,MAAM,SAAS,SAAS,UAAU;YAClC,IAAI,SAAS;YACb,IAAI,QAAQ;YACZ,IAAI,QAAQ,cAAc;YAC1B,MAAO,SAAS,OAAQ;gBACpB,IAAI,EAAE,QAAQ,KAAK;oBACf,OAAO,IAAI,MAAM,CAAC,sBAAsB,EAAE,MAAM,SAAS,CAAC;oBAC1D;gBACJ;gBACA,IAAI,SAAS,QAAQ,CAAC,YAAY,MAAM;oBACpC,OAAO,IAAI,MAAM,CAAC,+BAA+B,EAAE,OAAO,QAAQ,CAAC,IAAI,WAAW,EAAE,SAAS,QAAQ,CAAC,QAAQ,QAAQ,CAAC,KAAK;oBAC5H;gBACJ;gBACA,SAAS,SAAS,QAAQ,CAAC,SAAS;gBACpC,IAAI,OACA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,QAAQ,CAAC,KAAK;gBAChD,IAAI,WAAW,MAAM;oBACjB,IAAI,OACA,QAAQ,GAAG,CAAC;oBAChB,sEAAsE;oBACtE,4EAA4E;oBAC5E,+DAA+D;oBAC/D,MAAM,WAAW,SAAS;oBAC1B;;;;;;;;;;;;;qBAaC,GACD,IAAI,SAAS,SAAS,CAAC,cAAc,YAAY;wBAC7C,gDAAgD;wBAChD,MAAM,aAAa,WAAW;wBAC9B,IAAI,QAAQ,8BAA8B;wBAC1C,wCAAwC;wBACxC,uEAAuE;wBACvE,IAAI,SAAS,SAAS,CAAC,gBAAgB,QAAQ;4BAC3C,SAAS;wBACb,OACK,IAAI,SAAS,SAAS,CAAC,gBAAgB,QAAQ;4BAChD,SAAS;wBACb,OACK;4BACD,OAAO,IAAI,MAAM;4BACjB;wBACJ;wBACA,IAAI,SAAS,SAAS,CAAC,aAAa,GAAG,CAAC,YAAY,QAAQ;4BACxD,OAAO,IAAI,MAAM;4BACjB;wBACJ;wBACA,0EAA0E;wBAC1E,kEAAkE;wBAClE,MAAM,iBAAiB,SAAS,SAAS,CAAC,aAAa,GAAG,CAAC;wBAC3D,IAAI,iBAAiB,YAAY;4BAC7B,OAAO,IAAI,MAAM;4BACjB;wBACJ;wBACA,6BAA6B;wBAC7B,6DAA6D;wBAC7D,+EAA+E;wBAC/E,gDAAgD;wBAChD,qGAAqG;wBACrG,MAAM,WAAW,aAAa,gBAAgB,sCAAsC;wBACpF,MAAM,QAAQ,SAAS,SAAS,CAAC,UAAU,CAAC,SAAS,8BAA8B;wBACnF,qDAAqD;wBACrD,MAAM,eAAe,WAAW;wBAChC,IAAI,iBAAiB;wBACrB,IAAK,IAAI,IAAI,cAAc,IAAI,eAAe,KAAK,OAAO,KAAK,GAAI;4BAC/D,8BAA8B;4BAC9B,2EAA2E;4BAC3E,IAAI,SAAS,SAAS,CAAC,GAAG,CAAC,YAAY,QAAQ;gCAC3C,+CAA+C;gCAC/C,iBAAiB,SAAS,SAAS,CAAC,IAAI,GAAG,CAAC;4BAChD;wBACJ;wBACA,MAAM,mBAAmB,GAAG,2DAA2D;wBACvF,MAAM,mBAAmB,WAAW,IAAI,QAAQ,KAAK;wBACrD,MAAM,SAAS,EAAE;wBACjB,IAAK,IAAI,IAAI,kBAAkB,IAAI,mBAAmB,iBAAiB,IAAI,KAAK,GAAI;4BAChF,MAAM,QAAQ;gCACV,QAAQ,SAAS,SAAS,CAAC,GAAG,CAAC;gCAC/B,MAAM,SAAS,SAAS,CAAC,IAAI,GAAG,CAAC;gCACjC,oEAAoE;gCACpE,wEAAwE;gCACxE,oEAAoE;gCACpE,YAAY,SAAS,SAAS,CAAC,IAAI,GAAG,CAAC;gCACvC,iBAAiB,SAAS,SAAS,CAAC,IAAI,IAAI,CAAC;gCAC7C,OAAO,CAAC;gCACR,KAAK,CAAC;gCACN,OAAO;4BACX;4BACA,IAAI,CAAC,MAAM,UAAU,EAAE;gCACnB,sDAAsD;gCACtD,MAAM,KAAK,GAAG;gCACd,MAAM,KAAK,GAAG;4BAClB,OACK;gCACD,MAAM,KAAK,GAAG,aAAa,MAAM,UAAU;gCAC3C,MAAM,KAAK,GAAG;4BAClB;4BACA,MAAM,GAAG,GAAG,MAAM,KAAK,GAAG,MAAM,IAAI;4BACpC,OAAO,IAAI,CAAC;wBAChB;wBACA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,OAAO,MAAM,EAAE;4BAC7C,MAAM,aAAa,IAAI,KAAK;gCAAC;6BAAS;4BACtC,MAAM,OAAO,EAAE;4BACf,KAAK,MAAM,SAAS,OAAQ;gCACxB,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;oCACzC,UAAU,WAAW;gCACzB;gCACA,MAAM,YAAY,WAAW,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG,GAAG,GAAG;gCAC/D,qBAAqB;gCACrB,kDAAkD;gCAClD,4CAA4C;gCAC5C,2BAA2B;gCAC3B,KAAK,IAAI,CAAC;4BACd;4BACA,QAAQ;wBACZ;oBACJ;gBACJ;gBACA,UAAU,IAAI,SAAS,SAAS,CAAC,SAAS;YAC9C;QACJ;IACJ;AACJ;AAEA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,yBAAyB,OAAO;IAClC,MAAM,WAAW,WAAW;IAC5B,IAAI,CAAC,UACD,MAAM,IAAI,yBAAyB;IACvC,MAAM,eAAe,IAAI,aAAa;QAAE,YAAY;QAAM,eAAe;IAAK;IAC9E,MAAM,SAAS,MAAM,aAAa,OAAO,CAAC;IAC1C,IAAI,OAAO,MAAM,KAAK,GAClB,MAAM,IAAI,qBAAqB;IACnC,OAAO;QACH,KAAK,IAAI,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,WAAW;QAC/C,SAAS,IAAI,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,WAAW;QACnD;IACJ;AACJ;AAEA;;;;;CAKC,GACD,MAAM,uBAAuB,CAAC;IAC1B,OAAO,IAAI,QAAQ,CAAC,SAAS;QACzB,MAAM,MAAM,SAAS,aAAa,CAAC;QACnC,IAAI,MAAM,GAAG;YAAQ,QAAQ;QAAM;QACnC,IAAI,OAAO,GAAG,CAAC;YAAQ,OAAO;QAAI;QAClC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAClC;AACJ;AAEA,MAAM,mBAAmB,+IAAA,CAAA,SAAM;IAC3B;;;;KAIC,GACD,YAAY,QAAQ,EAAE,OAAO,CAAE;QAC3B,KAAK,CAAC;QACN,IAAI,UACA,IAAI,CAAC,SAAS,GAAG;QACrB,IAAI,CAAC,uBAAuB,GAAG,IAAI,+IAAA,CAAA,iBAAc;IACrD;IACA;;;;;KAKC,GACD,YAAY,QAAQ,EAAE;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI;IACf;IACA;;;;;KAKC,GACD,uBAAuB,OAAO,EAAE;QAC5B,IAAI,CAAC,oBAAoB,GAAG;QAC5B,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,sBAAsB;QAClB,IAAI,CAAC,IAAI,CAAC,SAAS,EACf,QAAQ,IAAI,CAAC;QACjB,mBAAmB;QACnB,MAAM,WAAW,IAAI,uBAAuB;YACxC,YAAY;gBAAC;gBAAG;gBAAG;aAAE;YACrB,YAAY;gBAAC;gBAAG;gBAAG;aAAE;YACrB,OAAO;gBAAC;gBAAG;gBAAG;aAAE;YAChB,WAAW;gBAAC;gBAAG;gBAAG;aAAE;YACpB,WAAW;gBAAC;gBAAG;gBAAG;aAAE;YACpB,gBAAgB;YAChB,gBAAgB;YAChB,iBAAiB;YACjB,SAAS,IAAI,+IAAA,CAAA,UAAO;YACpB,KAAK,IAAI,+IAAA,CAAA,UAAO;QACpB;QACA,OAAO,IAAI,+KAAA,CAAA,IAAY,CAAC;YACpB,OAAO;YACP,QAAQ;YACR,MAAM,+IAAA,CAAA,gBAAa;YACnB,YAAY,+IAAA,CAAA,uBAAoB;YAChC;YACA,UAAU,IAAI,CAAC,SAAS;YACxB,qBAAqB,IAAI,CAAC,oBAAoB;QAClD;IACJ;IACA;;;;;;GAMD,GACC,MAAM,OAAO,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE;QAC3D,gEAAgE;QAChE,MAAM,cAAc,gBAAgB,IAAI,KAAK;YAAC;SAAc,EAAE;YAAE,MAAM;QAAa,KAAK;QACxF,MAAM,UAAU,IAAI,KAAK;YAAC;SAAU,EAAE;YAAE,MAAM;QAAa;QAC3D,IAAI;QACJ,IAAI;QACJ,IAAI,YAAY;QAChB,IAAI,OAAO,sBAAsB,aAAa;YAC1C,MAAM,MAAM,MAAM,QAAQ,GAAG,CAAC;gBAC1B,cAAc,qBAAqB,eAAe,QAAQ,OAAO,CAAC;gBAClE,qBAAqB;aACxB;YACD,eAAe,GAAG,CAAC,EAAE;YACrB,WAAW,GAAG,CAAC,EAAE;YACjB,YAAY;QAChB,OACK;YACD,MAAM,MAAM,MAAM,QAAQ,GAAG,CAAC;gBAC1B,cAAc,kBAAkB,aAAa;oBAAE,kBAAkB;gBAAQ,KAAK,QAAQ,OAAO,CAAC;gBAC9F,kBAAkB,SAAS;oBAAE,kBAAkB;gBAAQ;aAC1D;YACD,eAAe,GAAG,CAAC,EAAE;YACrB,WAAW,GAAG,CAAC,EAAE;QACrB;QACA,MAAM,UAAU,IAAI,+IAAA,CAAA,UAAO,CAAC,gBAAgB,IAAI,UAAU,GAAG,IAAI,+IAAA,CAAA,YAAS,EAAE,+IAAA,CAAA,sBAAmB,EAAE,+IAAA,CAAA,sBAAmB,EAAE,+IAAA,CAAA,eAAY,EAAE,+IAAA,CAAA,2BAAwB,EAAE,+IAAA,CAAA,aAAU,EAAE,+IAAA,CAAA,mBAAgB,EAAE,GAAG,+IAAA,CAAA,uBAAoB;QACnN,QAAQ,KAAK,GAAG;QAChB,QAAQ,WAAW,GAAG;QACtB,MAAM,MAAM,IAAI,+IAAA,CAAA,UAAO,CAAC,UAAU,+IAAA,CAAA,YAAS,EAAE,+IAAA,CAAA,sBAAmB,EAAE,+IAAA,CAAA,sBAAmB,EAAE,+IAAA,CAAA,eAAY,EAAE,+IAAA,CAAA,2BAAwB,EAAE,+IAAA,CAAA,aAAU,EAAE,+IAAA,CAAA,mBAAgB,EAAE,GAAG,+IAAA,CAAA,iBAAc;QAC9K,IAAI,KAAK,GAAG;QACZ,IAAI,WAAW,GAAG;QAClB,aAAa,KAAK,GAAG,SAAS,KAAK;QACnC,aAAa,MAAM,GAAG,SAAS,MAAM;QACrC,aAAa,QAAQ,CAAC,OAAO,GAAG;QAChC,aAAa,QAAQ,CAAC,GAAG,GAAG;QAC5B,aAAa,QAAQ,CAAC,UAAU,GAAG,SAAS,UAAU;QACtD,aAAa,QAAQ,CAAC,UAAU,GAAG,SAAS,UAAU;QACtD,aAAa,QAAQ,CAAC,SAAS,GAAG,SAAS,SAAS;QACpD,aAAa,QAAQ,CAAC,SAAS,GAAG,SAAS,SAAS;QACpD,aAAa,QAAQ,CAAC,KAAK,GAAG,SAAS,KAAK;QAC5C,aAAa,QAAQ,CAAC,cAAc,GAAG,SAAS,cAAc;QAC9D,aAAa,QAAQ,CAAC,cAAc,GAAG,SAAS,cAAc;QAC9D,aAAa,QAAQ,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,cAAc;QAC3E,aAAa,QAAQ,CAAC,WAAW,GAAG;QACpC,aAAa,MAAM;IACvB;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CC,GACD,MAAM,sBAAsB;IACxB;;;;;;;;;;;;;KAaC,GACD,KAAK,CAAC,QAAQ,YAAY,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE;QACjE,MAAM,eAAe,IAAI,CAAC,mBAAmB;QAC7C,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,YAAY;YACd,IAAI,OAAO,WAAW,UAAU;gBAC5B,aAAa;gBACb,IAAI;oBACA,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,UAAU,KAAK;gBACnD,EACA,OAAO,OAAO;oBACV,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;oBACvB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;oBACvB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;oBACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;oBACZ,aAAa,uBAAuB;oBACpC;gBACJ;gBACA,IAAI,OAAO,WAAW,YAClB,OAAO;gBACX,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACrB,aAAa,uBAAuB;YACxC;QACJ;QACA,IAAI,sBAAsB;QAC1B,IAAI,WAAW;QACf,IAAI,YAAY;QAChB,IAAI,0BAA0B;QAC9B,IAAI,eAAe;QACnB,IAAI,gBAAgB;QACpB,IAAI,2BAA2B;QAC/B,IAAI,gBAAgB;QACpB,IAAI,iBAAiB;QACrB,MAAM,kBAAkB;YACpB,IAAI,OAAO,eAAe,YAAY;gBAClC,MAAM,QAAQ,WAAW,eAAe;gBACxC,MAAM,SAAS,YAAY,gBAAgB;gBAC3C,MAAM,mBAAmB,uBAAuB,2BAA2B;gBAC3E,WAAW,IAAI,cAAc,YAAY;oBAAE;oBAAkB;oBAAQ;gBAAM;YAC/E;QACJ;QACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,MAAM,YAAY,IAAI,+IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;QAC7D,UAAU,eAAe,CAAC;QAC1B,UAAU,gBAAgB,CAAC,IAAI,CAAC,aAAa;QAC7C,UAAU,OAAO,CAAC,IAAI,CAAC,IAAI;QAC3B,UAAU,kBAAkB,CAAC,IAAI,CAAC,eAAe;QACjD,UAAU,IAAI,CAAC,QAAQ,OAAO;YAC1B;;YAEA,GACA,IAAI,OAAO,WAAW,UAClB,MAAM,IAAI,MAAM;YACpB,MAAM;YACN,MAAM;QACV,GAAG,CAAC;YACA,sBAAsB,EAAE,gBAAgB;YACxC,YAAY,EAAE,MAAM;YACpB,WAAW,EAAE,KAAK;YAClB;QACJ,GAAG,CAAC;YACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;QAChB;QACA,MAAM,gBAAgB,IAAI,+IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;QACjE,cAAc,eAAe,CAAC;QAC9B,cAAc,gBAAgB,CAAC,IAAI,CAAC,aAAa;QACjD,cAAc,OAAO,CAAC,IAAI,CAAC,IAAI;QAC/B,cAAc,kBAAkB,CAAC,IAAI,CAAC,eAAe;QACrD,cAAc,IAAI,CAAC,YAAY,OAAO;YAClC;;YAEA,GACA,IAAI,OAAO,WAAW,UAClB,MAAM,IAAI,MAAM;YACpB,UAAU;YACV,MAAM;QACV,GAAG,CAAC;YACA,0BAA0B,EAAE,gBAAgB;YAC5C,gBAAgB,EAAE,MAAM;YACxB,eAAe,EAAE,KAAK;YACtB;QACJ,GAAG,CAAC;YACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;QAChB;QACA,MAAM,iBAAiB,IAAI,+IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;QAClE,yCAAyC;QACzC,eAAe,gBAAgB,CAAC,IAAI,CAAC,aAAa;QAClD,eAAe,OAAO,CAAC,IAAI,CAAC,IAAI;QAChC,eAAe,kBAAkB,CAAC,IAAI,CAAC,eAAe;QACtD,eAAe,IAAI,CAAC,aAAa,OAAO;YACpC;;YAEA,GACA,IAAI,OAAO,SAAS,UAChB,MAAM,IAAI,MAAM;YACpB,oEAAoE;YACpE,mEAAmE;YACnE,WAAW,KAAK,KAAK,CAAC;YACtB,MAAM;QACV,GAAG,CAAC;YACA,2BAA2B,EAAE,gBAAgB;YAC7C,iBAAiB,EAAE,MAAM;YACzB,gBAAgB,EAAE,KAAK;YACvB;QACJ,GAAG,CAAC;YACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;QAChB;QACA,OAAO;IACX;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CC,GACD,MAAM,qBAAqB;IACvB;;;;;;;;;KASC,GACD,KAAK,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE;QACnC,MAAM,eAAe,IAAI,CAAC,mBAAmB;QAC7C,MAAM,SAAS,IAAI,+IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;QAC1D,OAAO,eAAe,CAAC;QACvB,OAAO,gBAAgB,CAAC,IAAI,CAAC,aAAa;QAC1C,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI;QACxB,OAAO,kBAAkB,CAAC,IAAI,CAAC,eAAe;QAC9C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,OAAO,IAAI,CAAC,KAAK,OAAO;YACpB;;YAEA,GACA,IAAI,OAAO,SAAS,UAChB,MAAM,IAAI,MAAM;YACpB,MAAM,aAAa,IAAI,WAAW;YAClC,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;gBACA,MAAM,mBAAmB,MAAM,uBAAuB;gBACtD,yCAAyC;gBACzC,UAAU,iBAAiB,GAAG;gBAC9B,cAAc,iBAAiB,OAAO;gBACtC,WAAW,iBAAiB,QAAQ;YACxC,EACA,OAAO,GAAG;gBACN,kDAAkD;gBAClD,IAAI,aAAa,4BAA4B,aAAa,sBAAsB;oBAC5E,QAAQ,IAAI,CAAC,CAAC,yCAAyC,EAAE,IAAI,gFAAgF,CAAC;oBAC9I,WAAW;wBACP,YAAY;4BAAC;4BAAG;4BAAG;yBAAE;wBACrB,YAAY;4BAAC;4BAAG;4BAAG;yBAAE;wBACrB,OAAO;4BAAC;4BAAG;4BAAG;yBAAE;wBAChB,gBAAgB;wBAChB,gBAAgB;wBAChB,WAAW;4BAAC;4BAAG;4BAAG;yBAAE;wBACpB,WAAW;4BAAC;4BAAG;4BAAG;yBAAE;oBACxB;oBACA,UAAU;gBACd,OACK;oBACD,MAAM;gBACV;YACJ;YACA,aAAa;YACb,IAAI;gBACA,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,UAAU,SAAS;YACvD,EACA,OAAO,OAAO;gBACV,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;gBACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;gBACZ,aAAa,uBAAuB;gBACpC;YACJ;YACA,IAAI,OAAO,WAAW,YAClB,OAAO;YACX,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YACrB,aAAa,uBAAuB;QACxC,GAAG,YAAY,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;QAChB;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/stats.js/build/stats.min.js"], "sourcesContent": ["// stats.js - http://github.com/mrdoob/stats.js\n(function(f,e){\"object\"===typeof exports&&\"undefined\"!==typeof module?module.exports=e():\"function\"===typeof define&&define.amd?define(e):f.Stats=e()})(this,function(){var f=function(){function e(a){c.appendChild(a.dom);return a}function u(a){for(var d=0;d<c.children.length;d++)c.children[d].style.display=d===a?\"block\":\"none\";l=a}var l=0,c=document.createElement(\"div\");c.style.cssText=\"position:fixed;top:0;left:0;cursor:pointer;opacity:0.9;z-index:10000\";c.addEventListener(\"click\",function(a){a.preventDefault();\nu(++l%c.children.length)},!1);var k=(performance||Date).now(),g=k,a=0,r=e(new f.Panel(\"FPS\",\"#0ff\",\"#002\")),h=e(new f.Panel(\"MS\",\"#0f0\",\"#020\"));if(self.performance&&self.performance.memory)var t=e(new f.Panel(\"MB\",\"#f08\",\"#201\"));u(0);return{REVISION:16,dom:c,addPanel:e,showPanel:u,begin:function(){k=(performance||Date).now()},end:function(){a++;var c=(performance||Date).now();h.update(c-k,200);if(c>g+1E3&&(r.update(1E3*a/(c-g),100),g=c,a=0,t)){var d=performance.memory;t.update(d.usedJSHeapSize/\n1048576,d.jsHeapSizeLimit/1048576)}return c},update:function(){k=this.end()},domElement:c,setMode:u}};f.Panel=function(e,f,l){var c=Infinity,k=0,g=Math.round,a=g(window.devicePixelRatio||1),r=80*a,h=48*a,t=3*a,v=2*a,d=3*a,m=15*a,n=74*a,p=30*a,q=document.createElement(\"canvas\");q.width=r;q.height=h;q.style.cssText=\"width:80px;height:48px\";var b=q.getContext(\"2d\");b.font=\"bold \"+9*a+\"px Helvetica,Arial,sans-serif\";b.textBaseline=\"top\";b.fillStyle=l;b.fillRect(0,0,r,h);b.fillStyle=f;b.fillText(e,t,v);\nb.fillRect(d,m,n,p);b.fillStyle=l;b.globalAlpha=.9;b.fillRect(d,m,n,p);return{dom:q,update:function(h,w){c=Math.min(c,h);k=Math.max(k,h);b.fillStyle=l;b.globalAlpha=1;b.fillRect(0,0,r,m);b.fillStyle=f;b.fillText(g(h)+\" \"+e+\" (\"+g(c)+\"-\"+g(k)+\")\",t,v);b.drawImage(q,d+a,m,n-a,p,d,m,n-a,p);b.fillRect(d+n-a,m,a,p);b.fillStyle=l;b.globalAlpha=.9;b.fillRect(d+n-a,m,a,g((1-h/w)*p))}}};return f});\n"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAuD,OAAO,OAAO,GAAC;AAAgE,CAAC,EAAE,IAAI,EAAC;IAAW,IAAI,IAAE;QAAW,SAAS,EAAE,CAAC;YAAE,EAAE,WAAW,CAAC,EAAE,GAAG;YAAE,OAAO;QAAC;QAAC,SAAS,EAAE,CAAC;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,QAAQ,CAAC,MAAM,EAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,GAAC,MAAI,IAAE,UAAQ;YAAO,IAAE;QAAC;QAAC,IAAI,IAAE,GAAE,IAAE,SAAS,aAAa,CAAC;QAAO,EAAE,KAAK,CAAC,OAAO,GAAC;QAAuE,EAAE,gBAAgB,CAAC,SAAQ,SAAS,CAAC;YAAE,EAAE,cAAc;YAClgB,EAAE,EAAE,IAAE,EAAE,QAAQ,CAAC,MAAM;QAAC,GAAE,CAAC;QAAG,IAAI,IAAE,CAAC,eAAa,IAAI,EAAE,GAAG,IAAG,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,IAAI,EAAE,KAAK,CAAC,OAAM,QAAO,UAAS,IAAE,EAAE,IAAI,EAAE,KAAK,CAAC,MAAK,QAAO;QAAS,IAAG,KAAK,WAAW,IAAE,KAAK,WAAW,CAAC,MAAM,EAAC,IAAI,IAAE,EAAE,IAAI,EAAE,KAAK,CAAC,MAAK,QAAO;QAAS,EAAE;QAAG,OAAM;YAAC,UAAS;YAAG,KAAI;YAAE,UAAS;YAAE,WAAU;YAAE,OAAM;gBAAW,IAAE,CAAC,eAAa,IAAI,EAAE,GAAG;YAAE;YAAE,KAAI;gBAAW;gBAAI,IAAI,IAAE,CAAC,eAAa,IAAI,EAAE,GAAG;gBAAG,EAAE,MAAM,CAAC,IAAE,GAAE;gBAAK,IAAG,IAAE,IAAE,OAAK,CAAC,EAAE,MAAM,CAAC,MAAI,IAAE,CAAC,IAAE,CAAC,GAAE,MAAK,IAAE,GAAE,IAAE,GAAE,CAAC,GAAE;oBAAC,IAAI,IAAE,YAAY,MAAM;oBAAC,EAAE,MAAM,CAAC,EAAE,cAAc,GACpf,SAAQ,EAAE,eAAe,GAAC;gBAAQ;gBAAC,OAAO;YAAC;YAAE,QAAO;gBAAW,IAAE,IAAI,CAAC,GAAG;YAAE;YAAE,YAAW;YAAE,SAAQ;QAAC;IAAC;IAAE,EAAE,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,UAAS,IAAE,GAAE,IAAE,KAAK,KAAK,EAAC,IAAE,EAAE,OAAO,gBAAgB,IAAE,IAAG,IAAE,KAAG,GAAE,IAAE,KAAG,GAAE,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,KAAG,GAAE,IAAE,KAAG,GAAE,IAAE,KAAG,GAAE,IAAE,SAAS,aAAa,CAAC;QAAU,EAAE,KAAK,GAAC;QAAE,EAAE,MAAM,GAAC;QAAE,EAAE,KAAK,CAAC,OAAO,GAAC;QAAyB,IAAI,IAAE,EAAE,UAAU,CAAC;QAAM,EAAE,IAAI,GAAC,UAAQ,IAAE,IAAE;QAAgC,EAAE,YAAY,GAAC;QAAM,EAAE,SAAS,GAAC;QAAE,EAAE,QAAQ,CAAC,GAAE,GAAE,GAAE;QAAG,EAAE,SAAS,GAAC;QAAE,EAAE,QAAQ,CAAC,GAAE,GAAE;QACpf,EAAE,QAAQ,CAAC,GAAE,GAAE,GAAE;QAAG,EAAE,SAAS,GAAC;QAAE,EAAE,WAAW,GAAC;QAAG,EAAE,QAAQ,CAAC,GAAE,GAAE,GAAE;QAAG,OAAM;YAAC,KAAI;YAAE,QAAO,SAAS,CAAC,EAAC,CAAC;gBAAE,IAAE,KAAK,GAAG,CAAC,GAAE;gBAAG,IAAE,KAAK,GAAG,CAAC,GAAE;gBAAG,EAAE,SAAS,GAAC;gBAAE,EAAE,WAAW,GAAC;gBAAE,EAAE,QAAQ,CAAC,GAAE,GAAE,GAAE;gBAAG,EAAE,SAAS,GAAC;gBAAE,EAAE,QAAQ,CAAC,EAAE,KAAG,MAAI,IAAE,OAAK,EAAE,KAAG,MAAI,EAAE,KAAG,KAAI,GAAE;gBAAG,EAAE,SAAS,CAAC,GAAE,IAAE,GAAE,GAAE,IAAE,GAAE,GAAE,GAAE,GAAE,IAAE,GAAE;gBAAG,EAAE,QAAQ,CAAC,IAAE,IAAE,GAAE,GAAE,GAAE;gBAAG,EAAE,SAAS,GAAC;gBAAE,EAAE,WAAW,GAAC;gBAAG,EAAE,QAAQ,CAAC,IAAE,IAAE,GAAE,GAAE,GAAE,EAAE,CAAC,IAAE,IAAE,CAAC,IAAE;YAAG;QAAC;IAAC;IAAE,OAAO;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/troika-worker-utils/dist/troika-worker-utils.esm.js"], "sourcesContent": ["/**\n * Main content for the worker that handles the loading and execution of\n * modules within it.\n */\nfunction workerBootstrap() {\n  var modules = Object.create(null);\n\n  // Handle messages for registering a module\n  function registerModule(ref, callback) {\n    var id = ref.id;\n    var name = ref.name;\n    var dependencies = ref.dependencies; if ( dependencies === void 0 ) dependencies = [];\n    var init = ref.init; if ( init === void 0 ) init = function(){};\n    var getTransferables = ref.getTransferables; if ( getTransferables === void 0 ) getTransferables = null;\n\n    // Only register once\n    if (modules[id]) { return }\n\n    try {\n      // If any dependencies are modules, ensure they're registered and grab their value\n      dependencies = dependencies.map(function (dep) {\n        if (dep && dep.isWorkerModule) {\n          registerModule(dep, function (depResult) {\n            if (depResult instanceof Error) { throw depResult }\n          });\n          dep = modules[dep.id].value;\n        }\n        return dep\n      });\n\n      // Rehydrate functions\n      init = rehydrate((\"<\" + name + \">.init\"), init);\n      if (getTransferables) {\n        getTransferables = rehydrate((\"<\" + name + \">.getTransferables\"), getTransferables);\n      }\n\n      // Initialize the module and store its value\n      var value = null;\n      if (typeof init === 'function') {\n        value = init.apply(void 0, dependencies);\n      } else {\n        console.error('worker module init function failed to rehydrate');\n      }\n      modules[id] = {\n        id: id,\n        value: value,\n        getTransferables: getTransferables\n      };\n      callback(value);\n    } catch(err) {\n      if (!(err && err.noLog)) {\n        console.error(err);\n      }\n      callback(err);\n    }\n  }\n\n  // Handle messages for calling a registered module's result function\n  function callModule(ref, callback) {\n    var ref$1;\n\n    var id = ref.id;\n    var args = ref.args;\n    if (!modules[id] || typeof modules[id].value !== 'function') {\n      callback(new Error((\"Worker module \" + id + \": not found or its 'init' did not return a function\")));\n    }\n    try {\n      var result = (ref$1 = modules[id]).value.apply(ref$1, args);\n      if (result && typeof result.then === 'function') {\n        result.then(handleResult, function (rej) { return callback(rej instanceof Error ? rej : new Error('' + rej)); });\n      } else {\n        handleResult(result);\n      }\n    } catch(err) {\n      callback(err);\n    }\n    function handleResult(result) {\n      try {\n        var tx = modules[id].getTransferables && modules[id].getTransferables(result);\n        if (!tx || !Array.isArray(tx) || !tx.length) {\n          tx = undefined; //postMessage is very picky about not passing null or empty transferables\n        }\n        callback(result, tx);\n      } catch(err) {\n        console.error(err);\n        callback(err);\n      }\n    }\n  }\n\n  function rehydrate(name, str) {\n    var result = void 0;\n    self.troikaDefine = function (r) { return result = r; };\n    var url = URL.createObjectURL(\n      new Blob(\n        [(\"/** \" + (name.replace(/\\*/g, '')) + \" **/\\n\\ntroikaDefine(\\n\" + str + \"\\n)\")],\n        {type: 'application/javascript'}\n      )\n    );\n    try {\n      importScripts(url);\n    } catch(err) {\n      console.error(err);\n    }\n    URL.revokeObjectURL(url);\n    delete self.troikaDefine;\n    return result\n  }\n\n  // Handler for all messages within the worker\n  self.addEventListener('message', function (e) {\n    var ref = e.data;\n    var messageId = ref.messageId;\n    var action = ref.action;\n    var data = ref.data;\n    try {\n      // Module registration\n      if (action === 'registerModule') {\n        registerModule(data, function (result) {\n          if (result instanceof Error) {\n            postMessage({\n              messageId: messageId,\n              success: false,\n              error: result.message\n            });\n          } else {\n            postMessage({\n              messageId: messageId,\n              success: true,\n              result: {isCallable: typeof result === 'function'}\n            });\n          }\n        });\n      }\n      // Invocation\n      if (action === 'callModule') {\n        callModule(data, function (result, transferables) {\n          if (result instanceof Error) {\n            postMessage({\n              messageId: messageId,\n              success: false,\n              error: result.message\n            });\n          } else {\n            postMessage({\n              messageId: messageId,\n              success: true,\n              result: result\n            }, transferables || undefined);\n          }\n        });\n      }\n    } catch(err) {\n      postMessage({\n        messageId: messageId,\n        success: false,\n        error: err.stack\n      });\n    }\n  });\n}\n\n/**\n * Fallback for `defineWorkerModule` that behaves identically but runs in the main\n * thread, for when the execution environment doesn't support web workers or they\n * are disallowed due to e.g. CSP security restrictions.\n */\nfunction defineMainThreadModule(options) {\n  var moduleFunc = function() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    return moduleFunc._getInitResult().then(function (initResult) {\n      if (typeof initResult === 'function') {\n        return initResult.apply(void 0, args)\n      } else {\n        throw new Error('Worker module function was called but `init` did not return a callable function')\n      }\n    })\n  };\n  moduleFunc._getInitResult = function() {\n    // We can ignore getTransferables in main thread. TODO workerId?\n    var dependencies = options.dependencies;\n    var init = options.init;\n\n    // Resolve dependencies\n    dependencies = Array.isArray(dependencies) ? dependencies.map(function (dep) {\n      if (dep) {\n        // If it's a worker module, use its main thread impl\n        dep = dep.onMainThread || dep;\n        // If it's a main thread worker module, use its init return value\n        if (dep._getInitResult) {\n          dep = dep._getInitResult();\n        }\n      }\n      return dep\n    }) : [];\n\n    // Invoke init with the resolved dependencies\n    var initPromise = Promise.all(dependencies).then(function (deps) {\n      return init.apply(null, deps)\n    });\n\n    // Cache the resolved promise for subsequent calls\n    moduleFunc._getInitResult = function () { return initPromise; };\n\n    return initPromise\n  };\n  return moduleFunc\n}\n\nvar supportsWorkers = function () {\n  var supported = false;\n\n  // Only attempt worker initialization in browsers; elsewhere it would just be\n  // noise e.g. loading into a Node environment for SSR.\n  if (typeof window !== 'undefined' && typeof window.document !== 'undefined') {\n    try {\n      // TODO additional checks for things like importScripts within the worker?\n      //  Would need to be an async check.\n      var worker = new Worker(\n        URL.createObjectURL(new Blob([''], { type: 'application/javascript' }))\n      );\n      worker.terminate();\n      supported = true;\n    } catch (err) {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') ; else {\n        console.log(\n          (\"Troika createWorkerModule: web workers not allowed; falling back to main thread execution. Cause: [\" + (err.message) + \"]\")\n        );\n      }\n    }\n  }\n\n  // Cached result\n  supportsWorkers = function () { return supported; };\n  return supported\n};\n\nvar _workerModuleId = 0;\nvar _messageId = 0;\nvar _allowInitAsString = false;\nvar workers = Object.create(null);\nvar registeredModules = Object.create(null); //workerId -> Set<unregisterFn>\nvar openRequests = Object.create(null);\n\n\n/**\n * Define a module of code that will be executed with a web worker. This provides a simple\n * interface for moving chunks of logic off the main thread, and managing their dependencies\n * among one another.\n *\n * @param {object} options\n * @param {function} options.init\n * @param {array} [options.dependencies]\n * @param {function} [options.getTransferables]\n * @param {string} [options.name]\n * @param {string} [options.workerId]\n * @return {function(...[*]): {then}}\n */\nfunction defineWorkerModule(options) {\n  if ((!options || typeof options.init !== 'function') && !_allowInitAsString) {\n    throw new Error('requires `options.init` function')\n  }\n  var dependencies = options.dependencies;\n  var init = options.init;\n  var getTransferables = options.getTransferables;\n  var workerId = options.workerId;\n\n  var onMainThread = defineMainThreadModule(options);\n\n  if (workerId == null) {\n    workerId = '#default';\n  }\n  var id = \"workerModule\" + (++_workerModuleId);\n  var name = options.name || id;\n  var registrationPromise = null;\n\n  dependencies = dependencies && dependencies.map(function (dep) {\n    // Wrap raw functions as worker modules with no dependencies\n    if (typeof dep === 'function' && !dep.workerModuleData) {\n      _allowInitAsString = true;\n      dep = defineWorkerModule({\n        workerId: workerId,\n        name: (\"<\" + name + \"> function dependency: \" + (dep.name)),\n        init: (\"function(){return (\\n\" + (stringifyFunction(dep)) + \"\\n)}\")\n      });\n      _allowInitAsString = false;\n    }\n    // Grab postable data for worker modules\n    if (dep && dep.workerModuleData) {\n      dep = dep.workerModuleData;\n    }\n    return dep\n  });\n\n  function moduleFunc() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    if (!supportsWorkers()) {\n      return onMainThread.apply(void 0, args)\n    }\n\n    // Register this module if needed\n    if (!registrationPromise) {\n      registrationPromise = callWorker(workerId,'registerModule', moduleFunc.workerModuleData);\n      var unregister = function () {\n        registrationPromise = null;\n        registeredModules[workerId].delete(unregister);\n      }\n      ;(registeredModules[workerId] || (registeredModules[workerId] = new Set())).add(unregister);\n    }\n\n    // Invoke the module, returning a promise\n    return registrationPromise.then(function (ref) {\n      var isCallable = ref.isCallable;\n\n      if (isCallable) {\n        return callWorker(workerId,'callModule', {id: id, args: args})\n      } else {\n        throw new Error('Worker module function was called but `init` did not return a callable function')\n      }\n    })\n  }\n  moduleFunc.workerModuleData = {\n    isWorkerModule: true,\n    id: id,\n    name: name,\n    dependencies: dependencies,\n    init: stringifyFunction(init),\n    getTransferables: getTransferables && stringifyFunction(getTransferables)\n  };\n\n  moduleFunc.onMainThread = onMainThread;\n\n  return moduleFunc\n}\n\n/**\n * Terminate an active Worker by a workerId that was passed to defineWorkerModule.\n * This only terminates the Worker itself; the worker module will remain available\n * and if you call it again its Worker will be respawned.\n * @param {string} workerId\n */\nfunction terminateWorker(workerId) {\n  // Unregister all modules that were registered in that worker\n  if (registeredModules[workerId]) {\n    registeredModules[workerId].forEach(function (unregister) {\n      unregister();\n    });\n  }\n  // Terminate the Worker object\n  if (workers[workerId]) {\n    workers[workerId].terminate();\n    delete workers[workerId];\n  }\n}\n\n/**\n * Stringifies a function into a form that can be deserialized in the worker\n * @param fn\n */\nfunction stringifyFunction(fn) {\n  var str = fn.toString();\n  // If it was defined in object method/property format, it needs to be modified\n  if (!/^function/.test(str) && /^\\w+\\s*\\(/.test(str)) {\n    str = 'function ' + str;\n  }\n  return str\n}\n\n\nfunction getWorker(workerId) {\n  var worker = workers[workerId];\n  if (!worker) {\n    // Bootstrap the worker's content\n    var bootstrap = stringifyFunction(workerBootstrap);\n\n    // Create the worker from the bootstrap function content\n    worker = workers[workerId] = new Worker(\n      URL.createObjectURL(\n        new Blob(\n          [(\"/** Worker Module Bootstrap: \" + (workerId.replace(/\\*/g, '')) + \" **/\\n\\n;(\" + bootstrap + \")()\")],\n          {type: 'application/javascript'}\n        )\n      )\n    );\n\n    // Single handler for response messages from the worker\n    worker.onmessage = function (e) {\n      var response = e.data;\n      var msgId = response.messageId;\n      var callback = openRequests[msgId];\n      if (!callback) {\n        throw new Error('WorkerModule response with empty or unknown messageId')\n      }\n      delete openRequests[msgId];\n      callback(response);\n    };\n  }\n  return worker\n}\n\n// Issue a call to the worker with a callback to handle the response\nfunction callWorker(workerId, action, data) {\n  return new Promise(function (resolve, reject) {\n    var messageId = ++_messageId;\n    openRequests[messageId] = function (response) {\n      if (response.success) {\n        resolve(response.result);\n      } else {\n        reject(new Error((\"Error in worker \" + action + \" call: \" + (response.error))));\n      }\n    };\n    getWorker(workerId).postMessage({\n      messageId: messageId,\n      action: action,\n      data: data\n    });\n  })\n}\n\nexport { defineWorkerModule, stringifyFunction, terminateWorker };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AACD,SAAS;IACP,IAAI,UAAU,OAAO,MAAM,CAAC;IAE5B,2CAA2C;IAC3C,SAAS,eAAe,GAAG,EAAE,QAAQ;QACnC,IAAI,KAAK,IAAI,EAAE;QACf,IAAI,OAAO,IAAI,IAAI;QACnB,IAAI,eAAe,IAAI,YAAY;QAAE,IAAK,iBAAiB,KAAK,GAAI,eAAe,EAAE;QACrF,IAAI,OAAO,IAAI,IAAI;QAAE,IAAK,SAAS,KAAK,GAAI,OAAO,YAAW;QAC9D,IAAI,mBAAmB,IAAI,gBAAgB;QAAE,IAAK,qBAAqB,KAAK,GAAI,mBAAmB;QAEnG,qBAAqB;QACrB,IAAI,OAAO,CAAC,GAAG,EAAE;YAAE;QAAO;QAE1B,IAAI;YACF,kFAAkF;YAClF,eAAe,aAAa,GAAG,CAAC,SAAU,GAAG;gBAC3C,IAAI,OAAO,IAAI,cAAc,EAAE;oBAC7B,eAAe,KAAK,SAAU,SAAS;wBACrC,IAAI,qBAAqB,OAAO;4BAAE,MAAM;wBAAU;oBACpD;oBACA,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK;gBAC7B;gBACA,OAAO;YACT;YAEA,sBAAsB;YACtB,OAAO,UAAW,MAAM,OAAO,UAAW;YAC1C,IAAI,kBAAkB;gBACpB,mBAAmB,UAAW,MAAM,OAAO,sBAAuB;YACpE;YAEA,4CAA4C;YAC5C,IAAI,QAAQ;YACZ,IAAI,OAAO,SAAS,YAAY;gBAC9B,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG;YAC7B,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;YACA,OAAO,CAAC,GAAG,GAAG;gBACZ,IAAI;gBACJ,OAAO;gBACP,kBAAkB;YACpB;YACA,SAAS;QACX,EAAE,OAAM,KAAK;YACX,IAAI,CAAC,CAAC,OAAO,IAAI,KAAK,GAAG;gBACvB,QAAQ,KAAK,CAAC;YAChB;YACA,SAAS;QACX;IACF;IAEA,oEAAoE;IACpE,SAAS,WAAW,GAAG,EAAE,QAAQ;QAC/B,IAAI;QAEJ,IAAI,KAAK,IAAI,EAAE;QACf,IAAI,OAAO,IAAI,IAAI;QACnB,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,YAAY;YAC3D,SAAS,IAAI,MAAO,mBAAmB,KAAK;QAC9C;QACA,IAAI;YACF,IAAI,SAAS,CAAC,QAAQ,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO;YACtD,IAAI,UAAU,OAAO,OAAO,IAAI,KAAK,YAAY;gBAC/C,OAAO,IAAI,CAAC,cAAc,SAAU,GAAG;oBAAI,OAAO,SAAS,eAAe,QAAQ,MAAM,IAAI,MAAM,KAAK;gBAAO;YAChH,OAAO;gBACL,aAAa;YACf;QACF,EAAE,OAAM,KAAK;YACX,SAAS;QACX;QACA,SAAS,aAAa,MAAM;YAC1B,IAAI;gBACF,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBACtE,IAAI,CAAC,MAAM,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,EAAE;oBAC3C,KAAK,WAAW,yEAAyE;gBAC3F;gBACA,SAAS,QAAQ;YACnB,EAAE,OAAM,KAAK;gBACX,QAAQ,KAAK,CAAC;gBACd,SAAS;YACX;QACF;IACF;IAEA,SAAS,UAAU,IAAI,EAAE,GAAG;QAC1B,IAAI,SAAS,KAAK;QAClB,KAAK,YAAY,GAAG,SAAU,CAAC;YAAI,OAAO,SAAS;QAAG;QACtD,IAAI,MAAM,IAAI,eAAe,CAC3B,IAAI,KACF;YAAE,SAAU,KAAK,OAAO,CAAC,OAAO,MAAO,4BAA4B,MAAM;SAAO,EAChF;YAAC,MAAM;QAAwB;QAGnC,IAAI;YACF,cAAc;QAChB,EAAE,OAAM,KAAK;YACX,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI,eAAe,CAAC;QACpB,OAAO,KAAK,YAAY;QACxB,OAAO;IACT;IAEA,6CAA6C;IAC7C,KAAK,gBAAgB,CAAC,WAAW,SAAU,CAAC;QAC1C,IAAI,MAAM,EAAE,IAAI;QAChB,IAAI,YAAY,IAAI,SAAS;QAC7B,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,OAAO,IAAI,IAAI;QACnB,IAAI;YACF,sBAAsB;YACtB,IAAI,WAAW,kBAAkB;gBAC/B,eAAe,MAAM,SAAU,MAAM;oBACnC,IAAI,kBAAkB,OAAO;wBAC3B,YAAY;4BACV,WAAW;4BACX,SAAS;4BACT,OAAO,OAAO,OAAO;wBACvB;oBACF,OAAO;wBACL,YAAY;4BACV,WAAW;4BACX,SAAS;4BACT,QAAQ;gCAAC,YAAY,OAAO,WAAW;4BAAU;wBACnD;oBACF;gBACF;YACF;YACA,aAAa;YACb,IAAI,WAAW,cAAc;gBAC3B,WAAW,MAAM,SAAU,MAAM,EAAE,aAAa;oBAC9C,IAAI,kBAAkB,OAAO;wBAC3B,YAAY;4BACV,WAAW;4BACX,SAAS;4BACT,OAAO,OAAO,OAAO;wBACvB;oBACF,OAAO;wBACL,YAAY;4BACV,WAAW;4BACX,SAAS;4BACT,QAAQ;wBACV,GAAG,iBAAiB;oBACtB;gBACF;YACF;QACF,EAAE,OAAM,KAAK;YACX,YAAY;gBACV,WAAW;gBACX,SAAS;gBACT,OAAO,IAAI,KAAK;YAClB;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,OAAO;IACrC,IAAI,aAAa;QACf,IAAI,OAAO,EAAE,EAAE,MAAM,UAAU,MAAM;QACrC,MAAQ,MAAQ,IAAI,CAAE,IAAK,GAAG,SAAS,CAAE,IAAK;QAE9C,OAAO,WAAW,cAAc,GAAG,IAAI,CAAC,SAAU,UAAU;YAC1D,IAAI,OAAO,eAAe,YAAY;gBACpC,OAAO,WAAW,KAAK,CAAC,KAAK,GAAG;YAClC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IACA,WAAW,cAAc,GAAG;QAC1B,gEAAgE;QAChE,IAAI,eAAe,QAAQ,YAAY;QACvC,IAAI,OAAO,QAAQ,IAAI;QAEvB,uBAAuB;QACvB,eAAe,MAAM,OAAO,CAAC,gBAAgB,aAAa,GAAG,CAAC,SAAU,GAAG;YACzE,IAAI,KAAK;gBACP,oDAAoD;gBACpD,MAAM,IAAI,YAAY,IAAI;gBAC1B,iEAAiE;gBACjE,IAAI,IAAI,cAAc,EAAE;oBACtB,MAAM,IAAI,cAAc;gBAC1B;YACF;YACA,OAAO;QACT,KAAK,EAAE;QAEP,6CAA6C;QAC7C,IAAI,cAAc,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC,SAAU,IAAI;YAC7D,OAAO,KAAK,KAAK,CAAC,MAAM;QAC1B;QAEA,kDAAkD;QAClD,WAAW,cAAc,GAAG;YAAc,OAAO;QAAa;QAE9D,OAAO;IACT;IACA,OAAO;AACT;AAEA,IAAI,kBAAkB;IACpB,IAAI,YAAY;IAEhB,6EAA6E;IAC7E,sDAAsD;IACtD,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,aAAa;QAC3E,IAAI;YACF,0EAA0E;YAC1E,oCAAoC;YACpC,IAAI,SAAS,IAAI,OACf,IAAI,eAAe,CAAC,IAAI,KAAK;gBAAC;aAAG,EAAE;gBAAE,MAAM;YAAyB;YAEtE,OAAO,SAAS;YAChB,YAAY;QACd,EAAE,OAAO,KAAK;YACZ;iBAA8E;gBAC5E,QAAQ,GAAG,CACR,wGAAyG,IAAI,OAAO,GAAI;YAE7H;QACF;IACF;IAEA,gBAAgB;IAChB,kBAAkB;QAAc,OAAO;IAAW;IAClD,OAAO;AACT;AAEA,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI,UAAU,OAAO,MAAM,CAAC;AAC5B,IAAI,oBAAoB,OAAO,MAAM,CAAC,OAAO,+BAA+B;AAC5E,IAAI,eAAe,OAAO,MAAM,CAAC;AAGjC;;;;;;;;;;;;CAYC,GACD,SAAS,mBAAmB,OAAO;IACjC,IAAI,CAAC,CAAC,WAAW,OAAO,QAAQ,IAAI,KAAK,UAAU,KAAK,CAAC,oBAAoB;QAC3E,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,eAAe,QAAQ,YAAY;IACvC,IAAI,OAAO,QAAQ,IAAI;IACvB,IAAI,mBAAmB,QAAQ,gBAAgB;IAC/C,IAAI,WAAW,QAAQ,QAAQ;IAE/B,IAAI,eAAe,uBAAuB;IAE1C,IAAI,YAAY,MAAM;QACpB,WAAW;IACb;IACA,IAAI,KAAK,iBAAkB,EAAE;IAC7B,IAAI,OAAO,QAAQ,IAAI,IAAI;IAC3B,IAAI,sBAAsB;IAE1B,eAAe,gBAAgB,aAAa,GAAG,CAAC,SAAU,GAAG;QAC3D,4DAA4D;QAC5D,IAAI,OAAO,QAAQ,cAAc,CAAC,IAAI,gBAAgB,EAAE;YACtD,qBAAqB;YACrB,MAAM,mBAAmB;gBACvB,UAAU;gBACV,MAAO,MAAM,OAAO,4BAA6B,IAAI,IAAI;gBACzD,MAAO,0BAA2B,kBAAkB,OAAQ;YAC9D;YACA,qBAAqB;QACvB;QACA,wCAAwC;QACxC,IAAI,OAAO,IAAI,gBAAgB,EAAE;YAC/B,MAAM,IAAI,gBAAgB;QAC5B;QACA,OAAO;IACT;IAEA,SAAS;QACP,IAAI,OAAO,EAAE,EAAE,MAAM,UAAU,MAAM;QACrC,MAAQ,MAAQ,IAAI,CAAE,IAAK,GAAG,SAAS,CAAE,IAAK;QAE9C,IAAI,CAAC,mBAAmB;YACtB,OAAO,aAAa,KAAK,CAAC,KAAK,GAAG;QACpC;QAEA,iCAAiC;QACjC,IAAI,CAAC,qBAAqB;YACxB,sBAAsB,WAAW,UAAS,kBAAkB,WAAW,gBAAgB;YACvF,IAAI,aAAa;gBACf,sBAAsB;gBACtB,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC;YACrC;YACC,CAAC,iBAAiB,CAAC,SAAS,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC;QAClF;QAEA,yCAAyC;QACzC,OAAO,oBAAoB,IAAI,CAAC,SAAU,GAAG;YAC3C,IAAI,aAAa,IAAI,UAAU;YAE/B,IAAI,YAAY;gBACd,OAAO,WAAW,UAAS,cAAc;oBAAC,IAAI;oBAAI,MAAM;gBAAI;YAC9D,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IACA,WAAW,gBAAgB,GAAG;QAC5B,gBAAgB;QAChB,IAAI;QACJ,MAAM;QACN,cAAc;QACd,MAAM,kBAAkB;QACxB,kBAAkB,oBAAoB,kBAAkB;IAC1D;IAEA,WAAW,YAAY,GAAG;IAE1B,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,gBAAgB,QAAQ;IAC/B,6DAA6D;IAC7D,IAAI,iBAAiB,CAAC,SAAS,EAAE;QAC/B,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,SAAU,UAAU;YACtD;QACF;IACF;IACA,8BAA8B;IAC9B,IAAI,OAAO,CAAC,SAAS,EAAE;QACrB,OAAO,CAAC,SAAS,CAAC,SAAS;QAC3B,OAAO,OAAO,CAAC,SAAS;IAC1B;AACF;AAEA;;;CAGC,GACD,SAAS,kBAAkB,EAAE;IAC3B,IAAI,MAAM,GAAG,QAAQ;IACrB,8EAA8E;IAC9E,IAAI,CAAC,YAAY,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,MAAM;QACnD,MAAM,cAAc;IACtB;IACA,OAAO;AACT;AAGA,SAAS,UAAU,QAAQ;IACzB,IAAI,SAAS,OAAO,CAAC,SAAS;IAC9B,IAAI,CAAC,QAAQ;QACX,iCAAiC;QACjC,IAAI,YAAY,kBAAkB;QAElC,wDAAwD;QACxD,SAAS,OAAO,CAAC,SAAS,GAAG,IAAI,OAC/B,IAAI,eAAe,CACjB,IAAI,KACF;YAAE,kCAAmC,SAAS,OAAO,CAAC,OAAO,MAAO,eAAe,YAAY;SAAO,EACtG;YAAC,MAAM;QAAwB;QAKrC,uDAAuD;QACvD,OAAO,SAAS,GAAG,SAAU,CAAC;YAC5B,IAAI,WAAW,EAAE,IAAI;YACrB,IAAI,QAAQ,SAAS,SAAS;YAC9B,IAAI,WAAW,YAAY,CAAC,MAAM;YAClC,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,YAAY,CAAC,MAAM;YAC1B,SAAS;QACX;IACF;IACA,OAAO;AACT;AAEA,oEAAoE;AACpE,SAAS,WAAW,QAAQ,EAAE,MAAM,EAAE,IAAI;IACxC,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QAC1C,IAAI,YAAY,EAAE;QAClB,YAAY,CAAC,UAAU,GAAG,SAAU,QAAQ;YAC1C,IAAI,SAAS,OAAO,EAAE;gBACpB,QAAQ,SAAS,MAAM;YACzB,OAAO;gBACL,OAAO,IAAI,MAAO,qBAAqB,SAAS,YAAa,SAAS,KAAK;YAC7E;QACF;QACA,UAAU,UAAU,WAAW,CAAC;YAC9B,WAAW;YACX,QAAQ;YACR,MAAM;QACR;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/webgl-sdf-generator/dist/webgl-sdf-generator.mjs"], "sourcesContent": ["function SDFGenerator() {\nvar exports = (function (exports) {\n\n  /**\n   * Find the point on a quadratic bezier curve at t where t is in the range [0, 1]\n   */\n  function pointOnQuadraticBezier (x0, y0, x1, y1, x2, y2, t, pointOut) {\n    var t2 = 1 - t;\n    pointOut.x = t2 * t2 * x0 + 2 * t2 * t * x1 + t * t * x2;\n    pointOut.y = t2 * t2 * y0 + 2 * t2 * t * y1 + t * t * y2;\n  }\n\n  /**\n   * Find the point on a cubic bezier curve at t where t is in the range [0, 1]\n   */\n  function pointOnCubicBezier (x0, y0, x1, y1, x2, y2, x3, y3, t, pointOut) {\n    var t2 = 1 - t;\n    pointOut.x = t2 * t2 * t2 * x0 + 3 * t2 * t2 * t * x1 + 3 * t2 * t * t * x2 + t * t * t * x3;\n    pointOut.y = t2 * t2 * t2 * y0 + 3 * t2 * t2 * t * y1 + 3 * t2 * t * t * y2 + t * t * t * y3;\n  }\n\n  /**\n   * Parse a path string into its constituent line/curve commands, invoking a callback for each.\n   * @param {string} pathString - An SVG-like path string to parse; should only contain commands: M/L/Q/C/Z\n   * @param {function(\n   *   command: 'L'|'Q'|'C',\n   *   startX: number,\n   *   startY: number,\n   *   endX: number,\n   *   endY: number,\n   *   ctrl1X?: number,\n   *   ctrl1Y?: number,\n   *   ctrl2X?: number,\n   *   ctrl2Y?: number\n   * )} commandCallback - A callback function that will be called once for each parsed path command, passing the\n   *                      command identifier (only L/Q/C commands) and its numeric arguments.\n   */\n  function forEachPathCommand(pathString, commandCallback) {\n    var segmentRE = /([MLQCZ])([^MLQCZ]*)/g;\n    var match, firstX, firstY, prevX, prevY;\n    while ((match = segmentRE.exec(pathString))) {\n      var args = match[2]\n        .replace(/^\\s*|\\s*$/g, '')\n        .split(/[,\\s]+/)\n        .map(function (v) { return parseFloat(v); });\n      switch (match[1]) {\n        case 'M':\n          prevX = firstX = args[0];\n          prevY = firstY = args[1];\n          break\n        case 'L':\n          if (args[0] !== prevX || args[1] !== prevY) { // yup, some fonts have zero-length line commands\n            commandCallback('L', prevX, prevY, (prevX = args[0]), (prevY = args[1]));\n          }\n          break\n        case 'Q': {\n          commandCallback('Q', prevX, prevY, (prevX = args[2]), (prevY = args[3]), args[0], args[1]);\n          break\n        }\n        case 'C': {\n          commandCallback('C', prevX, prevY, (prevX = args[4]), (prevY = args[5]), args[0], args[1], args[2], args[3]);\n          break\n        }\n        case 'Z':\n          if (prevX !== firstX || prevY !== firstY) {\n            commandCallback('L', prevX, prevY, firstX, firstY);\n          }\n          break\n      }\n    }\n  }\n\n  /**\n   * Convert a path string to a series of straight line segments\n   * @param {string} pathString - An SVG-like path string to parse; should only contain commands: M/L/Q/C/Z\n   * @param {function(x1:number, y1:number, x2:number, y2:number)} segmentCallback - A callback\n   *        function that will be called once for every line segment\n   * @param {number} [curvePoints] - How many straight line segments to use when approximating a\n   *        bezier curve in the path. Defaults to 16.\n   */\n  function pathToLineSegments (pathString, segmentCallback, curvePoints) {\n    if ( curvePoints === void 0 ) curvePoints = 16;\n\n    var tempPoint = { x: 0, y: 0 };\n    forEachPathCommand(pathString, function (command, startX, startY, endX, endY, ctrl1X, ctrl1Y, ctrl2X, ctrl2Y) {\n      switch (command) {\n        case 'L':\n          segmentCallback(startX, startY, endX, endY);\n          break\n        case 'Q': {\n          var prevCurveX = startX;\n          var prevCurveY = startY;\n          for (var i = 1; i < curvePoints; i++) {\n            pointOnQuadraticBezier(\n              startX, startY,\n              ctrl1X, ctrl1Y,\n              endX, endY,\n              i / (curvePoints - 1),\n              tempPoint\n            );\n            segmentCallback(prevCurveX, prevCurveY, tempPoint.x, tempPoint.y);\n            prevCurveX = tempPoint.x;\n            prevCurveY = tempPoint.y;\n          }\n          break\n        }\n        case 'C': {\n          var prevCurveX$1 = startX;\n          var prevCurveY$1 = startY;\n          for (var i$1 = 1; i$1 < curvePoints; i$1++) {\n            pointOnCubicBezier(\n              startX, startY,\n              ctrl1X, ctrl1Y,\n              ctrl2X, ctrl2Y,\n              endX, endY,\n              i$1 / (curvePoints - 1),\n              tempPoint\n            );\n            segmentCallback(prevCurveX$1, prevCurveY$1, tempPoint.x, tempPoint.y);\n            prevCurveX$1 = tempPoint.x;\n            prevCurveY$1 = tempPoint.y;\n          }\n          break\n        }\n      }\n    });\n  }\n\n  var viewportQuadVertex = \"precision highp float;attribute vec2 aUV;varying vec2 vUV;void main(){vUV=aUV;gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}\";\n\n  var copyTexFragment = \"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){gl_FragColor=texture2D(tex,vUV);}\";\n\n  var cache = new WeakMap();\n\n  var glContextParams = {\n    premultipliedAlpha: false,\n    preserveDrawingBuffer: true,\n    antialias: false,\n    depth: false,\n  };\n\n  /**\n   * This is a little helper library for WebGL. It assists with state management for a GL context.\n   * It's pretty tightly wrapped to the needs of this package, not very general-purpose.\n   *\n   * @param { WebGLRenderingContext | HTMLCanvasElement | OffscreenCanvas } glOrCanvas - the GL context to wrap\n   * @param { ({gl, getExtension, withProgram, withTexture, withTextureFramebuffer, handleContextLoss}) => void } callback\n   */\n  function withWebGLContext (glOrCanvas, callback) {\n    var gl = glOrCanvas.getContext ? glOrCanvas.getContext('webgl', glContextParams) : glOrCanvas;\n    var wrapper = cache.get(gl);\n    if (!wrapper) {\n      var isWebGL2 = typeof WebGL2RenderingContext !== 'undefined' && gl instanceof WebGL2RenderingContext;\n      var extensions = {};\n      var programs = {};\n      var textures = {};\n      var textureUnit = -1;\n      var framebufferStack = [];\n\n      gl.canvas.addEventListener('webglcontextlost', function (e) {\n        handleContextLoss();\n        e.preventDefault();\n      }, false);\n\n      function getExtension (name) {\n        var ext = extensions[name];\n        if (!ext) {\n          ext = extensions[name] = gl.getExtension(name);\n          if (!ext) {\n            throw new Error((name + \" not supported\"))\n          }\n        }\n        return ext\n      }\n\n      function compileShader (src, type) {\n        var shader = gl.createShader(type);\n        gl.shaderSource(shader, src);\n        gl.compileShader(shader);\n        // const status = gl.getShaderParameter(shader, gl.COMPILE_STATUS)\n        // if (!status && !gl.isContextLost()) {\n        //   throw new Error(gl.getShaderInfoLog(shader).trim())\n        // }\n        return shader\n      }\n\n      function withProgram (name, vert, frag, func) {\n        if (!programs[name]) {\n          var attributes = {};\n          var uniforms = {};\n          var program = gl.createProgram();\n          gl.attachShader(program, compileShader(vert, gl.VERTEX_SHADER));\n          gl.attachShader(program, compileShader(frag, gl.FRAGMENT_SHADER));\n          gl.linkProgram(program);\n\n          programs[name] = {\n            program: program,\n            transaction: function transaction (func) {\n              gl.useProgram(program);\n              func({\n                setUniform: function setUniform (type, name) {\n                  var values = [], len = arguments.length - 2;\n                  while ( len-- > 0 ) values[ len ] = arguments[ len + 2 ];\n\n                  var uniformLoc = uniforms[name] || (uniforms[name] = gl.getUniformLocation(program, name));\n                  gl[(\"uniform\" + type)].apply(gl, [ uniformLoc ].concat( values ));\n                },\n\n                setAttribute: function setAttribute (name, size, usage, instancingDivisor, data) {\n                  var attr = attributes[name];\n                  if (!attr) {\n                    attr = attributes[name] = {\n                      buf: gl.createBuffer(), // TODO should we destroy our buffers?\n                      loc: gl.getAttribLocation(program, name),\n                      data: null\n                    };\n                  }\n                  gl.bindBuffer(gl.ARRAY_BUFFER, attr.buf);\n                  gl.vertexAttribPointer(attr.loc, size, gl.FLOAT, false, 0, 0);\n                  gl.enableVertexAttribArray(attr.loc);\n                  if (isWebGL2) {\n                    gl.vertexAttribDivisor(attr.loc, instancingDivisor);\n                  } else {\n                    getExtension('ANGLE_instanced_arrays').vertexAttribDivisorANGLE(attr.loc, instancingDivisor);\n                  }\n                  if (data !== attr.data) {\n                    gl.bufferData(gl.ARRAY_BUFFER, data, usage);\n                    attr.data = data;\n                  }\n                }\n              });\n            }\n          };\n        }\n\n        programs[name].transaction(func);\n      }\n\n      function withTexture (name, func) {\n        textureUnit++;\n        try {\n          gl.activeTexture(gl.TEXTURE0 + textureUnit);\n          var texture = textures[name];\n          if (!texture) {\n            texture = textures[name] = gl.createTexture();\n            gl.bindTexture(gl.TEXTURE_2D, texture);\n            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\n            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\n          }\n          gl.bindTexture(gl.TEXTURE_2D, texture);\n          func(texture, textureUnit);\n        } finally {\n          textureUnit--;\n        }\n      }\n\n      function withTextureFramebuffer (texture, textureUnit, func) {\n        var framebuffer = gl.createFramebuffer();\n        framebufferStack.push(framebuffer);\n        gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n        gl.activeTexture(gl.TEXTURE0 + textureUnit);\n        gl.bindTexture(gl.TEXTURE_2D, texture);\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\n        try {\n          func(framebuffer);\n        } finally {\n          gl.deleteFramebuffer(framebuffer);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebufferStack[--framebufferStack.length - 1] || null);\n        }\n      }\n\n      function handleContextLoss () {\n        extensions = {};\n        programs = {};\n        textures = {};\n        textureUnit = -1;\n        framebufferStack.length = 0;\n      }\n\n      cache.set(gl, wrapper = {\n        gl: gl,\n        isWebGL2: isWebGL2,\n        getExtension: getExtension,\n        withProgram: withProgram,\n        withTexture: withTexture,\n        withTextureFramebuffer: withTextureFramebuffer,\n        handleContextLoss: handleContextLoss,\n      });\n    }\n    callback(wrapper);\n  }\n\n\n  function renderImageData(glOrCanvas, imageData, x, y, width, height, channels, framebuffer) {\n    if ( channels === void 0 ) channels = 15;\n    if ( framebuffer === void 0 ) framebuffer = null;\n\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var withProgram = ref.withProgram;\n      var withTexture = ref.withTexture;\n\n      withTexture('copy', function (tex, texUnit) {\n        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.UNSIGNED_BYTE, imageData);\n        withProgram('copy', viewportQuadVertex, copyTexFragment, function (ref) {\n          var setUniform = ref.setUniform;\n          var setAttribute = ref.setAttribute;\n\n          setAttribute('aUV', 2, gl.STATIC_DRAW, 0, new Float32Array([0, 0, 2, 0, 0, 2]));\n          setUniform('1i', 'image', texUnit);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer || null);\n          gl.disable(gl.BLEND);\n          gl.colorMask(channels & 8, channels & 4, channels & 2, channels & 1);\n          gl.viewport(x, y, width, height);\n          gl.scissor(x, y, width, height);\n          gl.drawArrays(gl.TRIANGLES, 0, 3);\n        });\n      });\n    });\n  }\n\n  /**\n   * Resizing a canvas clears its contents; this utility copies the previous contents over.\n   * @param canvas\n   * @param newWidth\n   * @param newHeight\n   */\n  function resizeWebGLCanvasWithoutClearing(canvas, newWidth, newHeight) {\n    var width = canvas.width;\n    var height = canvas.height;\n    withWebGLContext(canvas, function (ref) {\n      var gl = ref.gl;\n\n      var data = new Uint8Array(width * height * 4);\n      gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, data);\n      canvas.width = newWidth;\n      canvas.height = newHeight;\n      renderImageData(gl, data, 0, 0, width, height);\n    });\n  }\n\n  var webglUtils = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    withWebGLContext: withWebGLContext,\n    renderImageData: renderImageData,\n    resizeWebGLCanvasWithoutClearing: resizeWebGLCanvasWithoutClearing\n  });\n\n  function generate$2 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n\n    var textureData = new Uint8Array(sdfWidth * sdfHeight);\n\n    var viewBoxWidth = viewBox[2] - viewBox[0];\n    var viewBoxHeight = viewBox[3] - viewBox[1];\n\n    // Decompose all paths into straight line segments and add them to an index\n    var segments = [];\n    pathToLineSegments(path, function (x1, y1, x2, y2) {\n      segments.push({\n        x1: x1, y1: y1, x2: x2, y2: y2,\n        minX: Math.min(x1, x2),\n        minY: Math.min(y1, y2),\n        maxX: Math.max(x1, x2),\n        maxY: Math.max(y1, y2)\n      });\n    });\n\n    // Sort segments by maxX, this will let us short-circuit some loops below\n    segments.sort(function (a, b) { return a.maxX - b.maxX; });\n\n    // For each target SDF texel, find the distance from its center to its nearest line segment,\n    // map that distance to an alpha value, and write that alpha to the texel\n    for (var sdfX = 0; sdfX < sdfWidth; sdfX++) {\n      for (var sdfY = 0; sdfY < sdfHeight; sdfY++) {\n        var signedDist = findNearestSignedDistance(\n          viewBox[0] + viewBoxWidth * (sdfX + 0.5) / sdfWidth,\n          viewBox[1] + viewBoxHeight * (sdfY + 0.5) / sdfHeight\n        );\n\n        // Use an exponential scale to ensure the texels very near the glyph path have adequate\n        // precision, while allowing the distance field to cover the entire texture, given that\n        // there are only 8 bits available. Formula visualized: https://www.desmos.com/calculator/uiaq5aqiam\n        var alpha = Math.pow((1 - Math.abs(signedDist) / maxDistance), sdfExponent) / 2;\n        if (signedDist < 0) {\n          alpha = 1 - alpha;\n        }\n\n        alpha = Math.max(0, Math.min(255, Math.round(alpha * 255))); //clamp\n        textureData[sdfY * sdfWidth + sdfX] = alpha;\n      }\n    }\n\n    return textureData\n\n    /**\n     * For a given x/y, search the index for the closest line segment and return\n     * its signed distance. Negative = inside, positive = outside, zero = on edge\n     * @param x\n     * @param y\n     * @returns {number}\n     */\n    function findNearestSignedDistance (x, y) {\n      var closestDistSq = Infinity;\n      var closestDist = Infinity;\n\n      for (var i = segments.length; i--;) {\n        var seg = segments[i];\n        if (seg.maxX + closestDist <= x) { break } //sorting by maxX means no more can be closer, so we can short-circuit\n        if (x + closestDist > seg.minX && y - closestDist < seg.maxY && y + closestDist > seg.minY) {\n          var distSq = absSquareDistanceToLineSegment(x, y, seg.x1, seg.y1, seg.x2, seg.y2);\n          if (distSq < closestDistSq) {\n            closestDistSq = distSq;\n            closestDist = Math.sqrt(closestDistSq);\n          }\n        }\n      }\n\n      // Flip to negative distance if inside the poly\n      if (isPointInPoly(x, y)) {\n        closestDist = -closestDist;\n      }\n      return closestDist\n    }\n\n    /**\n     * Determine whether the given point lies inside or outside the glyph. Uses a simple\n     * winding-number ray casting algorithm using a ray pointing east from the point.\n     */\n    function isPointInPoly (x, y) {\n      var winding = 0;\n      for (var i = segments.length; i--;) {\n        var seg = segments[i];\n        if (seg.maxX <= x) { break } //sorting by maxX means no more can cross, so we can short-circuit\n        var intersects = ((seg.y1 > y) !== (seg.y2 > y)) && (x < (seg.x2 - seg.x1) * (y - seg.y1) / (seg.y2 - seg.y1) + seg.x1);\n        if (intersects) {\n          winding += seg.y1 < seg.y2 ? 1 : -1;\n        }\n      }\n      return winding !== 0\n    }\n  }\n\n  function generateIntoCanvas$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    generateIntoFramebuffer$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, null, x, y, channel);\n  }\n\n  function generateIntoFramebuffer$1 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas, framebuffer, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    var data = generate$2(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent);\n    // Expand single-channel data to rbga\n    var rgbaData = new Uint8Array(data.length * 4);\n    for (var i = 0; i < data.length; i++) {\n      rgbaData[i * 4 + channel] = data[i];\n    }\n    renderImageData(glOrCanvas, rgbaData, x, y, sdfWidth, sdfHeight, 1 << (3 - channel), framebuffer);\n  }\n\n  /**\n   * Find the absolute distance from a point to a line segment at closest approach\n   */\n  function absSquareDistanceToLineSegment (x, y, lineX0, lineY0, lineX1, lineY1) {\n    var ldx = lineX1 - lineX0;\n    var ldy = lineY1 - lineY0;\n    var lengthSq = ldx * ldx + ldy * ldy;\n    var t = lengthSq ? Math.max(0, Math.min(1, ((x - lineX0) * ldx + (y - lineY0) * ldy) / lengthSq)) : 0;\n    var dx = x - (lineX0 + t * ldx);\n    var dy = y - (lineY0 + t * ldy);\n    return dx * dx + dy * dy\n  }\n\n  var javascript = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    generate: generate$2,\n    generateIntoCanvas: generateIntoCanvas$2,\n    generateIntoFramebuffer: generateIntoFramebuffer$1\n  });\n\n  var mainVertex = \"precision highp float;uniform vec4 uGlyphBounds;attribute vec2 aUV;attribute vec4 aLineSegment;varying vec4 vLineSegment;varying vec2 vGlyphXY;void main(){vLineSegment=aLineSegment;vGlyphXY=mix(uGlyphBounds.xy,uGlyphBounds.zw,aUV);gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}\";\n\n  var mainFragment = \"precision highp float;uniform vec4 uGlyphBounds;uniform float uMaxDistance;uniform float uExponent;varying vec4 vLineSegment;varying vec2 vGlyphXY;float absDistToSegment(vec2 point,vec2 lineA,vec2 lineB){vec2 lineDir=lineB-lineA;float lenSq=dot(lineDir,lineDir);float t=lenSq==0.0 ? 0.0 : clamp(dot(point-lineA,lineDir)/lenSq,0.0,1.0);vec2 linePt=lineA+t*lineDir;return distance(point,linePt);}void main(){vec4 seg=vLineSegment;vec2 p=vGlyphXY;float dist=absDistToSegment(p,seg.xy,seg.zw);float val=pow(1.0-clamp(dist/uMaxDistance,0.0,1.0),uExponent)*0.5;bool crossing=(seg.y>p.y!=seg.w>p.y)&&(p.x<(seg.z-seg.x)*(p.y-seg.y)/(seg.w-seg.y)+seg.x);bool crossingUp=crossing&&vLineSegment.y<vLineSegment.w;gl_FragColor=vec4(crossingUp ? 1.0/255.0 : 0.0,crossing&&!crossingUp ? 1.0/255.0 : 0.0,0.0,val);}\";\n\n  var postFragment = \"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){vec4 color=texture2D(tex,vUV);bool inside=color.r!=color.g;float val=inside ? 1.0-color.a : color.a;gl_FragColor=vec4(val);}\";\n\n  // Single triangle covering viewport\n  var viewportUVs = new Float32Array([0, 0, 2, 0, 0, 2]);\n\n  var implicitContext = null;\n  var isTestingSupport = false;\n  var NULL_OBJECT = {};\n  var supportByCanvas = new WeakMap(); // canvas -> bool\n\n  function validateSupport (glOrCanvas) {\n    if (!isTestingSupport && !isSupported(glOrCanvas)) {\n      throw new Error('WebGL generation not supported')\n    }\n  }\n\n  function generate$1 (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( glOrCanvas === void 0 ) glOrCanvas = null;\n\n    if (!glOrCanvas) {\n      glOrCanvas = implicitContext;\n      if (!glOrCanvas) {\n        var canvas = typeof OffscreenCanvas === 'function'\n          ? new OffscreenCanvas(1, 1)\n          : typeof document !== 'undefined'\n            ? document.createElement('canvas')\n            : null;\n        if (!canvas) {\n          throw new Error('OffscreenCanvas or DOM canvas not supported')\n        }\n        glOrCanvas = implicitContext = canvas.getContext('webgl', { depth: false });\n      }\n    }\n\n    validateSupport(glOrCanvas);\n\n    var rgbaData = new Uint8Array(sdfWidth * sdfHeight * 4); //not Uint8ClampedArray, cuz Safari\n\n    // Render into a background texture framebuffer\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var withTexture = ref.withTexture;\n      var withTextureFramebuffer = ref.withTextureFramebuffer;\n\n      withTexture('readable', function (texture, textureUnit) {\n        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, sdfWidth, sdfHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);\n\n        withTextureFramebuffer(texture, textureUnit, function (framebuffer) {\n          generateIntoFramebuffer(\n            sdfWidth,\n            sdfHeight,\n            path,\n            viewBox,\n            maxDistance,\n            sdfExponent,\n            gl,\n            framebuffer,\n            0,\n            0,\n            0 // red channel\n          );\n          gl.readPixels(0, 0, sdfWidth, sdfHeight, gl.RGBA, gl.UNSIGNED_BYTE, rgbaData);\n        });\n      });\n    });\n\n    // Throw away all but the red channel\n    var data = new Uint8Array(sdfWidth * sdfHeight);\n    for (var i = 0, j = 0; i < rgbaData.length; i += 4) {\n      data[j++] = rgbaData[i];\n    }\n\n    return data\n  }\n\n  function generateIntoCanvas$1(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    generateIntoFramebuffer(sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, canvas, null, x, y, channel);\n  }\n\n  function generateIntoFramebuffer (sdfWidth, sdfHeight, path, viewBox, maxDistance, sdfExponent, glOrCanvas, framebuffer, x, y, channel) {\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    // Verify support\n    validateSupport(glOrCanvas);\n\n    // Compute path segments\n    var lineSegmentCoords = [];\n    pathToLineSegments(path, function (x1, y1, x2, y2) {\n      lineSegmentCoords.push(x1, y1, x2, y2);\n    });\n    lineSegmentCoords = new Float32Array(lineSegmentCoords);\n\n    withWebGLContext(glOrCanvas, function (ref) {\n      var gl = ref.gl;\n      var isWebGL2 = ref.isWebGL2;\n      var getExtension = ref.getExtension;\n      var withProgram = ref.withProgram;\n      var withTexture = ref.withTexture;\n      var withTextureFramebuffer = ref.withTextureFramebuffer;\n      var handleContextLoss = ref.handleContextLoss;\n\n      withTexture('rawDistances', function (intermediateTexture, intermediateTextureUnit) {\n        if (sdfWidth !== intermediateTexture._lastWidth || sdfHeight !== intermediateTexture._lastHeight) {\n          gl.texImage2D(\n            gl.TEXTURE_2D, 0, gl.RGBA,\n            intermediateTexture._lastWidth = sdfWidth,\n            intermediateTexture._lastHeight = sdfHeight,\n            0, gl.RGBA, gl.UNSIGNED_BYTE, null\n          );\n        }\n\n        // Unsigned distance pass\n        withProgram('main', mainVertex, mainFragment, function (ref) {\n          var setAttribute = ref.setAttribute;\n          var setUniform = ref.setUniform;\n\n          // Init extensions\n          var instancingExtension = !isWebGL2 && getExtension('ANGLE_instanced_arrays');\n          var blendMinMaxExtension = !isWebGL2 && getExtension('EXT_blend_minmax');\n\n          // Init/update attributes\n          setAttribute('aUV', 2, gl.STATIC_DRAW, 0, viewportUVs);\n          setAttribute('aLineSegment', 4, gl.DYNAMIC_DRAW, 1, lineSegmentCoords);\n\n          // Init/update uniforms\n          setUniform.apply(void 0, [ '4f', 'uGlyphBounds' ].concat( viewBox ));\n          setUniform('1f', 'uMaxDistance', maxDistance);\n          setUniform('1f', 'uExponent', sdfExponent);\n\n          // Render initial unsigned distance / winding number info to a texture\n          withTextureFramebuffer(intermediateTexture, intermediateTextureUnit, function (framebuffer) {\n            gl.enable(gl.BLEND);\n            gl.colorMask(true, true, true, true);\n            gl.viewport(0, 0, sdfWidth, sdfHeight);\n            gl.scissor(0, 0, sdfWidth, sdfHeight);\n            gl.blendFunc(gl.ONE, gl.ONE);\n            // Red+Green channels are incremented (FUNC_ADD) for segment-ray crossings to give a \"winding number\".\n            // Alpha holds the closest (MAX) unsigned distance.\n            gl.blendEquationSeparate(gl.FUNC_ADD, isWebGL2 ? gl.MAX : blendMinMaxExtension.MAX_EXT);\n            gl.clear(gl.COLOR_BUFFER_BIT);\n            if (isWebGL2) {\n              gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, lineSegmentCoords.length / 4);\n            } else {\n              instancingExtension.drawArraysInstancedANGLE(gl.TRIANGLES, 0, 3, lineSegmentCoords.length / 4);\n            }\n            // Debug\n            // const debug = new Uint8Array(sdfWidth * sdfHeight * 4)\n            // gl.readPixels(0, 0, sdfWidth, sdfHeight, gl.RGBA, gl.UNSIGNED_BYTE, debug)\n            // console.log('intermediate texture data: ', debug)\n          });\n        });\n\n        // Use the data stored in the texture to apply inside/outside and write to the output framebuffer rect+channel.\n        withProgram('post', viewportQuadVertex, postFragment, function (program) {\n          program.setAttribute('aUV', 2, gl.STATIC_DRAW, 0, viewportUVs);\n          program.setUniform('1i', 'tex', intermediateTextureUnit);\n          gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);\n          gl.disable(gl.BLEND);\n          gl.colorMask(channel === 0, channel === 1, channel === 2, channel === 3);\n          gl.viewport(x, y, sdfWidth, sdfHeight);\n          gl.scissor(x, y, sdfWidth, sdfHeight);\n          gl.drawArrays(gl.TRIANGLES, 0, 3);\n        });\n      });\n\n      // Handle context loss occurring during any of the above calls\n      if (gl.isContextLost()) {\n        handleContextLoss();\n        throw new Error('webgl context lost')\n      }\n    });\n  }\n\n  function isSupported (glOrCanvas) {\n    var key = (!glOrCanvas || glOrCanvas === implicitContext) ? NULL_OBJECT : (glOrCanvas.canvas || glOrCanvas);\n    var supported = supportByCanvas.get(key);\n    if (supported === undefined) {\n      isTestingSupport = true;\n      var failReason = null;\n      try {\n        // Since we can't detect all failure modes up front, let's just do a trial run of a\n        // simple path and compare what we get back to the correct expected result. This will\n        // also serve to prime the shader compilation.\n        var expectedResult = [\n          97, 106, 97, 61,\n          99, 137, 118, 80,\n          80, 118, 137, 99,\n          61, 97, 106, 97\n        ];\n        var testResult = generate$1(\n          4,\n          4,\n          'M8,8L16,8L24,24L16,24Z',\n          [0, 0, 32, 32],\n          24,\n          1,\n          glOrCanvas\n        );\n        supported = testResult && expectedResult.length === testResult.length &&\n          testResult.every(function (val, i) { return val === expectedResult[i]; });\n        if (!supported) {\n          failReason = 'bad trial run results';\n          console.info(expectedResult, testResult);\n        }\n      } catch (err) {\n        // TODO if it threw due to webgl context loss, should we maybe leave isSupported as null and try again later?\n        supported = false;\n        failReason = err.message;\n      }\n      if (failReason) {\n        console.warn('WebGL SDF generation not supported:', failReason);\n      }\n      isTestingSupport = false;\n      supportByCanvas.set(key, supported);\n    }\n    return supported\n  }\n\n  var webgl = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    generate: generate$1,\n    generateIntoCanvas: generateIntoCanvas$1,\n    generateIntoFramebuffer: generateIntoFramebuffer,\n    isSupported: isSupported\n  });\n\n  /**\n   * Generate an SDF texture image for a 2D path.\n   *\n   * @param {number} sdfWidth - width of the SDF output image in pixels.\n   * @param {number} sdfHeight - height of the SDF output image in pixels.\n   * @param {string} path - an SVG-like path string describing the glyph; should only contain commands: M/L/Q/C/Z.\n   * @param {number[]} viewBox - [minX, minY, maxX, maxY] in font units aligning with the texture's edges.\n   * @param {number} maxDistance - the maximum distance from the glyph path in font units that will be encoded; defaults\n   *        to half the maximum viewBox dimension.\n   * @param {number} [sdfExponent] - specifies an exponent for encoding the SDF's distance values; higher exponents\n   *        will give greater precision nearer the glyph's path.\n   * @return {Uint8Array}\n   */\n  function generate(\n    sdfWidth,\n    sdfHeight,\n    path,\n    viewBox,\n    maxDistance,\n    sdfExponent\n  ) {\n    if ( maxDistance === void 0 ) maxDistance = Math.max(viewBox[2] - viewBox[0], viewBox[3] - viewBox[1]) / 2;\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n\n    try {\n      return generate$1.apply(webgl, arguments)\n    } catch(e) {\n      console.info('WebGL SDF generation failed, falling back to JS', e);\n      return generate$2.apply(javascript, arguments)\n    }\n  }\n\n  /**\n   * Generate an SDF texture image for a 2D path, inserting the result into a WebGL `canvas` at a given x/y position\n   * and color channel. This is generally much faster than calling `generate` because it does not require reading pixels\n   * back from the GPU->CPU -- the `canvas` can be used directly as a WebGL texture image, so it all stays on the GPU.\n   *\n   * @param {number} sdfWidth - width of the SDF output image in pixels.\n   * @param {number} sdfHeight - height of the SDF output image in pixels.\n   * @param {string} path - an SVG-like path string describing the glyph; should only contain commands: M/L/Q/C/Z.\n   * @param {number[]} viewBox - [minX, minY, maxX, maxY] in font units aligning with the texture's edges.\n   * @param {number} maxDistance - the maximum distance from the glyph path in font units that will be encoded; defaults\n   *        to half the maximum viewBox dimension.\n   * @param {number} [sdfExponent] - specifies an exponent for encoding the SDF's distance values; higher exponents\n   *        will give greater precision nearer the glyph's path.\n   * @param {HTMLCanvasElement|OffscreenCanvas} canvas - a WebGL-enabled canvas into which the SDF will be rendered.\n   *        Only the relevant rect/channel will be modified, the rest will be preserved. To avoid unpredictable results\n   *        due to shared GL context state, this canvas should be dedicated to use by this library alone.\n   * @param {number} x - the x position at which to render the SDF.\n   * @param {number} y - the y position at which to render the SDF.\n   * @param {number} channel - the color channel index (0-4) into which the SDF will be rendered.\n   * @return {Uint8Array}\n   */\n  function generateIntoCanvas(\n    sdfWidth,\n    sdfHeight,\n    path,\n    viewBox,\n    maxDistance,\n    sdfExponent,\n    canvas,\n    x,\n    y,\n    channel\n  ) {\n    if ( maxDistance === void 0 ) maxDistance = Math.max(viewBox[2] - viewBox[0], viewBox[3] - viewBox[1]) / 2;\n    if ( sdfExponent === void 0 ) sdfExponent = 1;\n    if ( x === void 0 ) x = 0;\n    if ( y === void 0 ) y = 0;\n    if ( channel === void 0 ) channel = 0;\n\n    try {\n      return generateIntoCanvas$1.apply(webgl, arguments)\n    } catch(e) {\n      console.info('WebGL SDF generation failed, falling back to JS', e);\n      return generateIntoCanvas$2.apply(javascript, arguments)\n    }\n  }\n\n  exports.forEachPathCommand = forEachPathCommand;\n  exports.generate = generate;\n  exports.generateIntoCanvas = generateIntoCanvas;\n  exports.javascript = javascript;\n  exports.pathToLineSegments = pathToLineSegments;\n  exports.webgl = webgl;\n  exports.webglUtils = webglUtils;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n}({}));\nreturn exports\n}\n\nexport { SDFGenerator as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACT,IAAI,UAAW,SAAU,OAAO;QAE9B;;GAEC,GACD,SAAS,uBAAwB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ;YAClE,IAAI,KAAK,IAAI;YACb,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;YACtD,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;QACxD;QAEA;;GAEC,GACD,SAAS,mBAAoB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ;YACtE,IAAI,KAAK,IAAI;YACb,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;YAC1F,SAAS,CAAC,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;QAC5F;QAEA;;;;;;;;;;;;;;;GAeC,GACD,SAAS,mBAAmB,UAAU,EAAE,eAAe;YACrD,IAAI,YAAY;YAChB,IAAI,OAAO,QAAQ,QAAQ,OAAO;YAClC,MAAQ,QAAQ,UAAU,IAAI,CAAC,YAAc;gBAC3C,IAAI,OAAO,KAAK,CAAC,EAAE,CAChB,OAAO,CAAC,cAAc,IACtB,KAAK,CAAC,UACN,GAAG,CAAC,SAAU,CAAC;oBAAI,OAAO,WAAW;gBAAI;gBAC5C,OAAQ,KAAK,CAAC,EAAE;oBACd,KAAK;wBACH,QAAQ,SAAS,IAAI,CAAC,EAAE;wBACxB,QAAQ,SAAS,IAAI,CAAC,EAAE;wBACxB;oBACF,KAAK;wBACH,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,CAAC,EAAE,KAAK,OAAO;4BAC1C,gBAAgB,KAAK,OAAO,OAAQ,QAAQ,IAAI,CAAC,EAAE,EAAI,QAAQ,IAAI,CAAC,EAAE;wBACxE;wBACA;oBACF,KAAK;wBAAK;4BACR,gBAAgB,KAAK,OAAO,OAAQ,QAAQ,IAAI,CAAC,EAAE,EAAI,QAAQ,IAAI,CAAC,EAAE,EAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;4BACzF;wBACF;oBACA,KAAK;wBAAK;4BACR,gBAAgB,KAAK,OAAO,OAAQ,QAAQ,IAAI,CAAC,EAAE,EAAI,QAAQ,IAAI,CAAC,EAAE,EAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;4BAC3G;wBACF;oBACA,KAAK;wBACH,IAAI,UAAU,UAAU,UAAU,QAAQ;4BACxC,gBAAgB,KAAK,OAAO,OAAO,QAAQ;wBAC7C;wBACA;gBACJ;YACF;QACF;QAEA;;;;;;;GAOC,GACD,SAAS,mBAAoB,UAAU,EAAE,eAAe,EAAE,WAAW;YACnE,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAE5C,IAAI,YAAY;gBAAE,GAAG;gBAAG,GAAG;YAAE;YAC7B,mBAAmB,YAAY,SAAU,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;gBAC1G,OAAQ;oBACN,KAAK;wBACH,gBAAgB,QAAQ,QAAQ,MAAM;wBACtC;oBACF,KAAK;wBAAK;4BACR,IAAI,aAAa;4BACjB,IAAI,aAAa;4BACjB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gCACpC,uBACE,QAAQ,QACR,QAAQ,QACR,MAAM,MACN,IAAI,CAAC,cAAc,CAAC,GACpB;gCAEF,gBAAgB,YAAY,YAAY,UAAU,CAAC,EAAE,UAAU,CAAC;gCAChE,aAAa,UAAU,CAAC;gCACxB,aAAa,UAAU,CAAC;4BAC1B;4BACA;wBACF;oBACA,KAAK;wBAAK;4BACR,IAAI,eAAe;4BACnB,IAAI,eAAe;4BACnB,IAAK,IAAI,MAAM,GAAG,MAAM,aAAa,MAAO;gCAC1C,mBACE,QAAQ,QACR,QAAQ,QACR,QAAQ,QACR,MAAM,MACN,MAAM,CAAC,cAAc,CAAC,GACtB;gCAEF,gBAAgB,cAAc,cAAc,UAAU,CAAC,EAAE,UAAU,CAAC;gCACpE,eAAe,UAAU,CAAC;gCAC1B,eAAe,UAAU,CAAC;4BAC5B;4BACA;wBACF;gBACF;YACF;QACF;QAEA,IAAI,qBAAqB;QAEzB,IAAI,kBAAkB;QAEtB,IAAI,QAAQ,IAAI;QAEhB,IAAI,kBAAkB;YACpB,oBAAoB;YACpB,uBAAuB;YACvB,WAAW;YACX,OAAO;QACT;QAEA;;;;;;GAMC,GACD,SAAS,iBAAkB,UAAU,EAAE,QAAQ;YAC7C,IAAI,KAAK,WAAW,UAAU,GAAG,WAAW,UAAU,CAAC,SAAS,mBAAmB;YACnF,IAAI,UAAU,MAAM,GAAG,CAAC;YACxB,IAAI,CAAC,SAAS;gBACZ,IAAI,WAAW,OAAO,2BAA2B,eAAe,cAAc;gBAC9E,IAAI,aAAa,CAAC;gBAClB,IAAI,WAAW,CAAC;gBAChB,IAAI,WAAW,CAAC;gBAChB,IAAI,cAAc,CAAC;gBACnB,IAAI,mBAAmB,EAAE;gBAEzB,GAAG,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,SAAU,CAAC;oBACxD;oBACA,EAAE,cAAc;gBAClB,GAAG;gBAEH,SAAS,aAAc,IAAI;oBACzB,IAAI,MAAM,UAAU,CAAC,KAAK;oBAC1B,IAAI,CAAC,KAAK;wBACR,MAAM,UAAU,CAAC,KAAK,GAAG,GAAG,YAAY,CAAC;wBACzC,IAAI,CAAC,KAAK;4BACR,MAAM,IAAI,MAAO,OAAO;wBAC1B;oBACF;oBACA,OAAO;gBACT;gBAEA,SAAS,cAAe,GAAG,EAAE,IAAI;oBAC/B,IAAI,SAAS,GAAG,YAAY,CAAC;oBAC7B,GAAG,YAAY,CAAC,QAAQ;oBACxB,GAAG,aAAa,CAAC;oBACjB,kEAAkE;oBAClE,wCAAwC;oBACxC,wDAAwD;oBACxD,IAAI;oBACJ,OAAO;gBACT;gBAEA,SAAS,YAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;oBAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;wBACnB,IAAI,aAAa,CAAC;wBAClB,IAAI,WAAW,CAAC;wBAChB,IAAI,UAAU,GAAG,aAAa;wBAC9B,GAAG,YAAY,CAAC,SAAS,cAAc,MAAM,GAAG,aAAa;wBAC7D,GAAG,YAAY,CAAC,SAAS,cAAc,MAAM,GAAG,eAAe;wBAC/D,GAAG,WAAW,CAAC;wBAEf,QAAQ,CAAC,KAAK,GAAG;4BACf,SAAS;4BACT,aAAa,SAAS,YAAa,IAAI;gCACrC,GAAG,UAAU,CAAC;gCACd,KAAK;oCACH,YAAY,SAAS,WAAY,IAAI,EAAE,IAAI;wCACzC,IAAI,SAAS,EAAE,EAAE,MAAM,UAAU,MAAM,GAAG;wCAC1C,MAAQ,QAAQ,EAAI,MAAM,CAAE,IAAK,GAAG,SAAS,CAAE,MAAM,EAAG;wCAExD,IAAI,aAAa,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,GAAG,kBAAkB,CAAC,SAAS,KAAK;wCACzF,EAAE,CAAE,YAAY,KAAM,CAAC,KAAK,CAAC,IAAI;4CAAE;yCAAY,CAAC,MAAM,CAAE;oCAC1D;oCAEA,cAAc,SAAS,aAAc,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI;wCAC7E,IAAI,OAAO,UAAU,CAAC,KAAK;wCAC3B,IAAI,CAAC,MAAM;4CACT,OAAO,UAAU,CAAC,KAAK,GAAG;gDACxB,KAAK,GAAG,YAAY;gDACpB,KAAK,GAAG,iBAAiB,CAAC,SAAS;gDACnC,MAAM;4CACR;wCACF;wCACA,GAAG,UAAU,CAAC,GAAG,YAAY,EAAE,KAAK,GAAG;wCACvC,GAAG,mBAAmB,CAAC,KAAK,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE,OAAO,GAAG;wCAC3D,GAAG,uBAAuB,CAAC,KAAK,GAAG;wCACnC,IAAI,UAAU;4CACZ,GAAG,mBAAmB,CAAC,KAAK,GAAG,EAAE;wCACnC,OAAO;4CACL,aAAa,0BAA0B,wBAAwB,CAAC,KAAK,GAAG,EAAE;wCAC5E;wCACA,IAAI,SAAS,KAAK,IAAI,EAAE;4CACtB,GAAG,UAAU,CAAC,GAAG,YAAY,EAAE,MAAM;4CACrC,KAAK,IAAI,GAAG;wCACd;oCACF;gCACF;4BACF;wBACF;oBACF;oBAEA,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;gBAC7B;gBAEA,SAAS,YAAa,IAAI,EAAE,IAAI;oBAC9B;oBACA,IAAI;wBACF,GAAG,aAAa,CAAC,GAAG,QAAQ,GAAG;wBAC/B,IAAI,UAAU,QAAQ,CAAC,KAAK;wBAC5B,IAAI,CAAC,SAAS;4BACZ,UAAU,QAAQ,CAAC,KAAK,GAAG,GAAG,aAAa;4BAC3C,GAAG,WAAW,CAAC,GAAG,UAAU,EAAE;4BAC9B,GAAG,aAAa,CAAC,GAAG,UAAU,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO;4BACjE,GAAG,aAAa,CAAC,GAAG,UAAU,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO;wBACnE;wBACA,GAAG,WAAW,CAAC,GAAG,UAAU,EAAE;wBAC9B,KAAK,SAAS;oBAChB,SAAU;wBACR;oBACF;gBACF;gBAEA,SAAS,uBAAwB,OAAO,EAAE,WAAW,EAAE,IAAI;oBACzD,IAAI,cAAc,GAAG,iBAAiB;oBACtC,iBAAiB,IAAI,CAAC;oBACtB,GAAG,eAAe,CAAC,GAAG,WAAW,EAAE;oBACnC,GAAG,aAAa,CAAC,GAAG,QAAQ,GAAG;oBAC/B,GAAG,WAAW,CAAC,GAAG,UAAU,EAAE;oBAC9B,GAAG,oBAAoB,CAAC,GAAG,WAAW,EAAE,GAAG,iBAAiB,EAAE,GAAG,UAAU,EAAE,SAAS;oBACtF,IAAI;wBACF,KAAK;oBACP,SAAU;wBACR,GAAG,iBAAiB,CAAC;wBACrB,GAAG,eAAe,CAAC,GAAG,WAAW,EAAE,gBAAgB,CAAC,EAAE,iBAAiB,MAAM,GAAG,EAAE,IAAI;oBACxF;gBACF;gBAEA,SAAS;oBACP,aAAa,CAAC;oBACd,WAAW,CAAC;oBACZ,WAAW,CAAC;oBACZ,cAAc,CAAC;oBACf,iBAAiB,MAAM,GAAG;gBAC5B;gBAEA,MAAM,GAAG,CAAC,IAAI,UAAU;oBACtB,IAAI;oBACJ,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,aAAa;oBACb,wBAAwB;oBACxB,mBAAmB;gBACrB;YACF;YACA,SAAS;QACX;QAGA,SAAS,gBAAgB,UAAU,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW;YACxF,IAAK,aAAa,KAAK,GAAI,WAAW;YACtC,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAE5C,iBAAiB,YAAY,SAAU,GAAG;gBACxC,IAAI,KAAK,IAAI,EAAE;gBACf,IAAI,cAAc,IAAI,WAAW;gBACjC,IAAI,cAAc,IAAI,WAAW;gBAEjC,YAAY,QAAQ,SAAU,GAAG,EAAE,OAAO;oBACxC,GAAG,UAAU,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,IAAI,EAAE,OAAO,QAAQ,GAAG,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;oBACtF,YAAY,QAAQ,oBAAoB,iBAAiB,SAAU,GAAG;wBACpE,IAAI,aAAa,IAAI,UAAU;wBAC/B,IAAI,eAAe,IAAI,YAAY;wBAEnC,aAAa,OAAO,GAAG,GAAG,WAAW,EAAE,GAAG,IAAI,aAAa;4BAAC;4BAAG;4BAAG;4BAAG;4BAAG;4BAAG;yBAAE;wBAC7E,WAAW,MAAM,SAAS;wBAC1B,GAAG,eAAe,CAAC,GAAG,WAAW,EAAE,eAAe;wBAClD,GAAG,OAAO,CAAC,GAAG,KAAK;wBACnB,GAAG,SAAS,CAAC,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW;wBAClE,GAAG,QAAQ,CAAC,GAAG,GAAG,OAAO;wBACzB,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO;wBACxB,GAAG,UAAU,CAAC,GAAG,SAAS,EAAE,GAAG;oBACjC;gBACF;YACF;QACF;QAEA;;;;;GAKC,GACD,SAAS,iCAAiC,MAAM,EAAE,QAAQ,EAAE,SAAS;YACnE,IAAI,QAAQ,OAAO,KAAK;YACxB,IAAI,SAAS,OAAO,MAAM;YAC1B,iBAAiB,QAAQ,SAAU,GAAG;gBACpC,IAAI,KAAK,IAAI,EAAE;gBAEf,IAAI,OAAO,IAAI,WAAW,QAAQ,SAAS;gBAC3C,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,QAAQ,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;gBAC9D,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAChB,gBAAgB,IAAI,MAAM,GAAG,GAAG,OAAO;YACzC;QACF;QAEA,IAAI,aAAa,WAAW,GAAE,OAAO,MAAM,CAAC;YAC1C,WAAW;YACX,kBAAkB;YAClB,iBAAiB;YACjB,kCAAkC;QACpC;QAEA,SAAS,WAAY,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW;YAC/E,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAE5C,IAAI,cAAc,IAAI,WAAW,WAAW;YAE5C,IAAI,eAAe,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;YAC1C,IAAI,gBAAgB,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;YAE3C,2EAA2E;YAC3E,IAAI,WAAW,EAAE;YACjB,mBAAmB,MAAM,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC/C,SAAS,IAAI,CAAC;oBACZ,IAAI;oBAAI,IAAI;oBAAI,IAAI;oBAAI,IAAI;oBAC5B,MAAM,KAAK,GAAG,CAAC,IAAI;oBACnB,MAAM,KAAK,GAAG,CAAC,IAAI;oBACnB,MAAM,KAAK,GAAG,CAAC,IAAI;oBACnB,MAAM,KAAK,GAAG,CAAC,IAAI;gBACrB;YACF;YAEA,yEAAyE;YACzE,SAAS,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,EAAE,IAAI,GAAG,EAAE,IAAI;YAAE;YAExD,4FAA4F;YAC5F,yEAAyE;YACzE,IAAK,IAAI,OAAO,GAAG,OAAO,UAAU,OAAQ;gBAC1C,IAAK,IAAI,OAAO,GAAG,OAAO,WAAW,OAAQ;oBAC3C,IAAI,aAAa,0BACf,OAAO,CAAC,EAAE,GAAG,eAAe,CAAC,OAAO,GAAG,IAAI,UAC3C,OAAO,CAAC,EAAE,GAAG,gBAAgB,CAAC,OAAO,GAAG,IAAI;oBAG9C,uFAAuF;oBACvF,uFAAuF;oBACvF,oGAAoG;oBACpG,IAAI,QAAQ,KAAK,GAAG,CAAE,IAAI,KAAK,GAAG,CAAC,cAAc,aAAc,eAAe;oBAC9E,IAAI,aAAa,GAAG;wBAClB,QAAQ,IAAI;oBACd;oBAEA,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,QAAQ,QAAQ,OAAO;oBACpE,WAAW,CAAC,OAAO,WAAW,KAAK,GAAG;gBACxC;YACF;YAEA,OAAO;;YAEP;;;;;;KAMC,GACD,SAAS,0BAA2B,CAAC,EAAE,CAAC;gBACtC,IAAI,gBAAgB;gBACpB,IAAI,cAAc;gBAElB,IAAK,IAAI,IAAI,SAAS,MAAM,EAAE,KAAM;oBAClC,IAAI,MAAM,QAAQ,CAAC,EAAE;oBACrB,IAAI,IAAI,IAAI,GAAG,eAAe,GAAG;wBAAE;oBAAM,EAAE,sEAAsE;oBACjH,IAAI,IAAI,cAAc,IAAI,IAAI,IAAI,IAAI,cAAc,IAAI,IAAI,IAAI,IAAI,cAAc,IAAI,IAAI,EAAE;wBAC1F,IAAI,SAAS,+BAA+B,GAAG,GAAG,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE;wBAChF,IAAI,SAAS,eAAe;4BAC1B,gBAAgB;4BAChB,cAAc,KAAK,IAAI,CAAC;wBAC1B;oBACF;gBACF;gBAEA,+CAA+C;gBAC/C,IAAI,cAAc,GAAG,IAAI;oBACvB,cAAc,CAAC;gBACjB;gBACA,OAAO;YACT;YAEA;;;KAGC,GACD,SAAS,cAAe,CAAC,EAAE,CAAC;gBAC1B,IAAI,UAAU;gBACd,IAAK,IAAI,IAAI,SAAS,MAAM,EAAE,KAAM;oBAClC,IAAI,MAAM,QAAQ,CAAC,EAAE;oBACrB,IAAI,IAAI,IAAI,IAAI,GAAG;wBAAE;oBAAM,EAAE,kEAAkE;oBAC/F,IAAI,aAAa,AAAE,IAAI,EAAE,GAAG,MAAQ,IAAI,EAAE,GAAG,KAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,IAAI,EAAE;oBACtH,IAAI,YAAY;wBACd,WAAW,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;oBACpC;gBACF;gBACA,OAAO,YAAY;YACrB;QACF;QAEA,SAAS,qBAAqB,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;YAC/G,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,0BAA0B,UAAU,WAAW,MAAM,SAAS,aAAa,aAAa,QAAQ,MAAM,GAAG,GAAG;QAC9G;QAEA,SAAS,0BAA2B,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;YACtI,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,IAAI,OAAO,WAAW,UAAU,WAAW,MAAM,SAAS,aAAa;YACvE,qCAAqC;YACrC,IAAI,WAAW,IAAI,WAAW,KAAK,MAAM,GAAG;YAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,QAAQ,CAAC,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,EAAE;YACrC;YACA,gBAAgB,YAAY,UAAU,GAAG,GAAG,UAAU,WAAW,KAAM,IAAI,SAAU;QACvF;QAEA;;GAEC,GACD,SAAS,+BAAgC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;YAC3E,IAAI,MAAM,SAAS;YACnB,IAAI,MAAM,SAAS;YACnB,IAAI,WAAW,MAAM,MAAM,MAAM;YACjC,IAAI,IAAI,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,aAAa;YACpG,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,GAAG;YAC9B,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,GAAG;YAC9B,OAAO,KAAK,KAAK,KAAK;QACxB;QAEA,IAAI,aAAa,WAAW,GAAE,OAAO,MAAM,CAAC;YAC1C,WAAW;YACX,UAAU;YACV,oBAAoB;YACpB,yBAAyB;QAC3B;QAEA,IAAI,aAAa;QAEjB,IAAI,eAAe;QAEnB,IAAI,eAAe;QAEnB,oCAAoC;QACpC,IAAI,cAAc,IAAI,aAAa;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAErD,IAAI,kBAAkB;QACtB,IAAI,mBAAmB;QACvB,IAAI,cAAc,CAAC;QACnB,IAAI,kBAAkB,IAAI,WAAW,iBAAiB;QAEtD,SAAS,gBAAiB,UAAU;YAClC,IAAI,CAAC,oBAAoB,CAAC,YAAY,aAAa;gBACjD,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,SAAS,WAAY,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU;YAC3F,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,eAAe,KAAK,GAAI,aAAa;YAE1C,IAAI,CAAC,YAAY;gBACf,aAAa;gBACb,IAAI,CAAC,YAAY;oBACf,IAAI,SAAS,OAAO,oBAAoB,aACpC,IAAI,gBAAgB,GAAG,KACvB,OAAO,aAAa,cAClB,SAAS,aAAa,CAAC,YACvB;oBACN,IAAI,CAAC,QAAQ;wBACX,MAAM,IAAI,MAAM;oBAClB;oBACA,aAAa,kBAAkB,OAAO,UAAU,CAAC,SAAS;wBAAE,OAAO;oBAAM;gBAC3E;YACF;YAEA,gBAAgB;YAEhB,IAAI,WAAW,IAAI,WAAW,WAAW,YAAY,IAAI,mCAAmC;YAE5F,+CAA+C;YAC/C,iBAAiB,YAAY,SAAU,GAAG;gBACxC,IAAI,KAAK,IAAI,EAAE;gBACf,IAAI,cAAc,IAAI,WAAW;gBACjC,IAAI,yBAAyB,IAAI,sBAAsB;gBAEvD,YAAY,YAAY,SAAU,OAAO,EAAE,WAAW;oBACpD,GAAG,UAAU,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,IAAI,EAAE,UAAU,WAAW,GAAG,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;oBAE5F,uBAAuB,SAAS,aAAa,SAAU,WAAW;wBAChE,wBACE,UACA,WACA,MACA,SACA,aACA,aACA,IACA,aACA,GACA,GACA,EAAE,cAAc;;wBAElB,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,WAAW,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;oBACtE;gBACF;YACF;YAEA,qCAAqC;YACrC,IAAI,OAAO,IAAI,WAAW,WAAW;YACrC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;gBAClD,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,EAAE;YACzB;YAEA,OAAO;QACT;QAEA,SAAS,qBAAqB,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;YAC/G,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,wBAAwB,UAAU,WAAW,MAAM,SAAS,aAAa,aAAa,QAAQ,MAAM,GAAG,GAAG;QAC5G;QAEA,SAAS,wBAAyB,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;YACpI,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,iBAAiB;YACjB,gBAAgB;YAEhB,wBAAwB;YACxB,IAAI,oBAAoB,EAAE;YAC1B,mBAAmB,MAAM,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC/C,kBAAkB,IAAI,CAAC,IAAI,IAAI,IAAI;YACrC;YACA,oBAAoB,IAAI,aAAa;YAErC,iBAAiB,YAAY,SAAU,GAAG;gBACxC,IAAI,KAAK,IAAI,EAAE;gBACf,IAAI,WAAW,IAAI,QAAQ;gBAC3B,IAAI,eAAe,IAAI,YAAY;gBACnC,IAAI,cAAc,IAAI,WAAW;gBACjC,IAAI,cAAc,IAAI,WAAW;gBACjC,IAAI,yBAAyB,IAAI,sBAAsB;gBACvD,IAAI,oBAAoB,IAAI,iBAAiB;gBAE7C,YAAY,gBAAgB,SAAU,mBAAmB,EAAE,uBAAuB;oBAChF,IAAI,aAAa,oBAAoB,UAAU,IAAI,cAAc,oBAAoB,WAAW,EAAE;wBAChG,GAAG,UAAU,CACX,GAAG,UAAU,EAAE,GAAG,GAAG,IAAI,EACzB,oBAAoB,UAAU,GAAG,UACjC,oBAAoB,WAAW,GAAG,WAClC,GAAG,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;oBAElC;oBAEA,yBAAyB;oBACzB,YAAY,QAAQ,YAAY,cAAc,SAAU,GAAG;wBACzD,IAAI,eAAe,IAAI,YAAY;wBACnC,IAAI,aAAa,IAAI,UAAU;wBAE/B,kBAAkB;wBAClB,IAAI,sBAAsB,CAAC,YAAY,aAAa;wBACpD,IAAI,uBAAuB,CAAC,YAAY,aAAa;wBAErD,yBAAyB;wBACzB,aAAa,OAAO,GAAG,GAAG,WAAW,EAAE,GAAG;wBAC1C,aAAa,gBAAgB,GAAG,GAAG,YAAY,EAAE,GAAG;wBAEpD,uBAAuB;wBACvB,WAAW,KAAK,CAAC,KAAK,GAAG;4BAAE;4BAAM;yBAAgB,CAAC,MAAM,CAAE;wBAC1D,WAAW,MAAM,gBAAgB;wBACjC,WAAW,MAAM,aAAa;wBAE9B,sEAAsE;wBACtE,uBAAuB,qBAAqB,yBAAyB,SAAU,WAAW;4BACxF,GAAG,MAAM,CAAC,GAAG,KAAK;4BAClB,GAAG,SAAS,CAAC,MAAM,MAAM,MAAM;4BAC/B,GAAG,QAAQ,CAAC,GAAG,GAAG,UAAU;4BAC5B,GAAG,OAAO,CAAC,GAAG,GAAG,UAAU;4BAC3B,GAAG,SAAS,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG;4BAC3B,sGAAsG;4BACtG,mDAAmD;4BACnD,GAAG,qBAAqB,CAAC,GAAG,QAAQ,EAAE,WAAW,GAAG,GAAG,GAAG,qBAAqB,OAAO;4BACtF,GAAG,KAAK,CAAC,GAAG,gBAAgB;4BAC5B,IAAI,UAAU;gCACZ,GAAG,mBAAmB,CAAC,GAAG,SAAS,EAAE,GAAG,GAAG,kBAAkB,MAAM,GAAG;4BACxE,OAAO;gCACL,oBAAoB,wBAAwB,CAAC,GAAG,SAAS,EAAE,GAAG,GAAG,kBAAkB,MAAM,GAAG;4BAC9F;wBACA,QAAQ;wBACR,yDAAyD;wBACzD,6EAA6E;wBAC7E,oDAAoD;wBACtD;oBACF;oBAEA,+GAA+G;oBAC/G,YAAY,QAAQ,oBAAoB,cAAc,SAAU,OAAO;wBACrE,QAAQ,YAAY,CAAC,OAAO,GAAG,GAAG,WAAW,EAAE,GAAG;wBAClD,QAAQ,UAAU,CAAC,MAAM,OAAO;wBAChC,GAAG,eAAe,CAAC,GAAG,WAAW,EAAE;wBACnC,GAAG,OAAO,CAAC,GAAG,KAAK;wBACnB,GAAG,SAAS,CAAC,YAAY,GAAG,YAAY,GAAG,YAAY,GAAG,YAAY;wBACtE,GAAG,QAAQ,CAAC,GAAG,GAAG,UAAU;wBAC5B,GAAG,OAAO,CAAC,GAAG,GAAG,UAAU;wBAC3B,GAAG,UAAU,CAAC,GAAG,SAAS,EAAE,GAAG;oBACjC;gBACF;gBAEA,8DAA8D;gBAC9D,IAAI,GAAG,aAAa,IAAI;oBACtB;oBACA,MAAM,IAAI,MAAM;gBAClB;YACF;QACF;QAEA,SAAS,YAAa,UAAU;YAC9B,IAAI,MAAM,AAAC,CAAC,cAAc,eAAe,kBAAmB,cAAe,WAAW,MAAM,IAAI;YAChG,IAAI,YAAY,gBAAgB,GAAG,CAAC;YACpC,IAAI,cAAc,WAAW;gBAC3B,mBAAmB;gBACnB,IAAI,aAAa;gBACjB,IAAI;oBACF,mFAAmF;oBACnF,qFAAqF;oBACrF,8CAA8C;oBAC9C,IAAI,iBAAiB;wBACnB;wBAAI;wBAAK;wBAAI;wBACb;wBAAI;wBAAK;wBAAK;wBACd;wBAAI;wBAAK;wBAAK;wBACd;wBAAI;wBAAI;wBAAK;qBACd;oBACD,IAAI,aAAa,WACf,GACA,GACA,0BACA;wBAAC;wBAAG;wBAAG;wBAAI;qBAAG,EACd,IACA,GACA;oBAEF,YAAY,cAAc,eAAe,MAAM,KAAK,WAAW,MAAM,IACnE,WAAW,KAAK,CAAC,SAAU,GAAG,EAAE,CAAC;wBAAI,OAAO,QAAQ,cAAc,CAAC,EAAE;oBAAE;oBACzE,IAAI,CAAC,WAAW;wBACd,aAAa;wBACb,QAAQ,IAAI,CAAC,gBAAgB;oBAC/B;gBACF,EAAE,OAAO,KAAK;oBACZ,6GAA6G;oBAC7G,YAAY;oBACZ,aAAa,IAAI,OAAO;gBAC1B;gBACA,IAAI,YAAY;oBACd,QAAQ,IAAI,CAAC,uCAAuC;gBACtD;gBACA,mBAAmB;gBACnB,gBAAgB,GAAG,CAAC,KAAK;YAC3B;YACA,OAAO;QACT;QAEA,IAAI,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;YACrC,WAAW;YACX,UAAU;YACV,oBAAoB;YACpB,yBAAyB;YACzB,aAAa;QACf;QAEA;;;;;;;;;;;;GAYC,GACD,SAAS,SACP,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,OAAO,EACP,WAAW,EACX,WAAW;YAEX,IAAK,gBAAgB,KAAK,GAAI,cAAc,KAAK,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI;YACzG,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAE5C,IAAI;gBACF,OAAO,WAAW,KAAK,CAAC,OAAO;YACjC,EAAE,OAAM,GAAG;gBACT,QAAQ,IAAI,CAAC,mDAAmD;gBAChE,OAAO,WAAW,KAAK,CAAC,YAAY;YACtC;QACF;QAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,SAAS,mBACP,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,OAAO,EACP,WAAW,EACX,WAAW,EACX,MAAM,EACN,CAAC,EACD,CAAC,EACD,OAAO;YAEP,IAAK,gBAAgB,KAAK,GAAI,cAAc,KAAK,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI;YACzG,IAAK,gBAAgB,KAAK,GAAI,cAAc;YAC5C,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,MAAM,KAAK,GAAI,IAAI;YACxB,IAAK,YAAY,KAAK,GAAI,UAAU;YAEpC,IAAI;gBACF,OAAO,qBAAqB,KAAK,CAAC,OAAO;YAC3C,EAAE,OAAM,GAAG;gBACT,QAAQ,IAAI,CAAC,mDAAmD;gBAChE,OAAO,qBAAqB,KAAK,CAAC,YAAY;YAChD;QACF;QAEA,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,QAAQ,GAAG;QACnB,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,UAAU,GAAG;QACrB,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,KAAK,GAAG;QAChB,QAAQ,UAAU,GAAG;QAErB,OAAO,cAAc,CAAC,SAAS,cAAc;YAAE,OAAO;QAAK;QAE3D,OAAO;IAET,EAAE,CAAC;IACH,OAAO;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6732, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/bidi-js/dist/bidi.mjs"], "sourcesContent": ["function bidiFactory() {\nvar bidi = (function (exports) {\n\n  // Bidi character types data, auto generated\n  var DATA = {\n    \"R\": \"13k,1a,2,3,3,2+1j,ch+16,a+1,5+2,2+n,5,a,4,6+16,4+3,h+1b,4mo,179q,2+9,2+11,2i9+7y,2+68,4,3+4,5+13,4+3,2+4k,3+29,8+cf,1t+7z,w+17,3+3m,1t+3z,16o1+5r,8+30,8+mc,29+1r,29+4v,75+73\",\n    \"EN\": \"1c+9,3d+1,6,187+9,513,4+5,7+9,sf+j,175h+9,qw+q,161f+1d,4xt+a,25i+9\",\n    \"ES\": \"17,2,6dp+1,f+1,av,16vr,mx+1,4o,2\",\n    \"ET\": \"z+2,3h+3,b+1,ym,3e+1,2o,p4+1,8,6u,7c,g6,1wc,1n9+4,30+1b,2n,6d,qhx+1,h0m,a+1,49+2,63+1,4+1,6bb+3,12jj\",\n    \"AN\": \"16o+5,2j+9,2+1,35,ed,1ff2+9,87+u\",\n    \"CS\": \"18,2+1,b,2u,12k,55v,l,17v0,2,3,53,2+1,b\",\n    \"B\": \"a,3,f+2,2v,690\",\n    \"S\": \"9,2,k\",\n    \"WS\": \"c,k,4f4,1vk+a,u,1j,335\",\n    \"ON\": \"x+1,4+4,h+5,r+5,r+3,z,5+3,2+1,2+1,5,2+2,3+4,o,w,ci+1,8+d,3+d,6+8,2+g,39+1,9,6+1,2,33,b8,3+1,3c+1,7+1,5r,b,7h+3,sa+5,2,3i+6,jg+3,ur+9,2v,ij+1,9g+9,7+a,8m,4+1,49+x,14u,2+2,c+2,e+2,e+2,e+1,i+n,e+e,2+p,u+2,e+2,36+1,2+3,2+1,b,2+2,6+5,2,2,2,h+1,5+4,6+3,3+f,16+2,5+3l,3+81,1y+p,2+40,q+a,m+13,2r+ch,2+9e,75+hf,3+v,2+2w,6e+5,f+6,75+2a,1a+p,2+2g,d+5x,r+b,6+3,4+o,g,6+1,6+2,2k+1,4,2j,5h+z,1m+1,1e+f,t+2,1f+e,d+3,4o+3,2s+1,w,535+1r,h3l+1i,93+2,2s,b+1,3l+x,2v,4g+3,21+3,kz+1,g5v+1,5a,j+9,n+v,2,3,2+8,2+1,3+2,2,3,46+1,4+4,h+5,r+5,r+a,3h+2,4+6,b+4,78,1r+24,4+c,4,1hb,ey+6,103+j,16j+c,1ux+7,5+g,fsh,jdq+1t,4,57+2e,p1,1m,1m,1m,1m,4kt+1,7j+17,5+2r,d+e,3+e,2+e,2+10,m+4,w,1n+5,1q,4z+5,4b+rb,9+c,4+c,4+37,d+2g,8+b,l+b,5+1j,9+9,7+13,9+t,3+1,27+3c,2+29,2+3q,d+d,3+4,4+2,6+6,a+o,8+6,a+2,e+6,16+42,2+1i\",\n    \"BN\": \"0+8,6+d,2s+5,2+p,e,4m9,1kt+2,2b+5,5+5,17q9+v,7k,6p+8,6+1,119d+3,440+7,96s+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+75,6p+2rz,1ben+1,1ekf+1,1ekf+1\",\n    \"NSM\": \"lc+33,7o+6,7c+18,2,2+1,2+1,2,21+a,1d+k,h,2u+6,3+5,3+1,2+3,10,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,g+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+g,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,k1+w,2db+2,3y,2p+v,ff+3,30+1,n9x+3,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,r2,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+5,3+1,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2d+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,f0c+4,1o+6,t5,1s+3,2a,f5l+1,43t+2,i+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,gzhy+6n\",\n    \"AL\": \"16w,3,2,e+1b,z+2,2+2s,g+1,8+1,b+m,2+t,s+2i,c+e,4h+f,1d+1e,1bwe+dp,3+3z,x+c,2+1,35+3y,2rm+z,5+7,b+5,dt+l,c+u,17nl+27,1t+27,4x+6n,3+d\",\n    \"LRO\": \"6ct\",\n    \"RLO\": \"6cu\",\n    \"LRE\": \"6cq\",\n    \"RLE\": \"6cr\",\n    \"PDF\": \"6cs\",\n    \"LRI\": \"6ee\",\n    \"RLI\": \"6ef\",\n    \"FSI\": \"6eg\",\n    \"PDI\": \"6eh\"\n  };\n\n  var TYPES = {};\n  var TYPES_TO_NAMES = {};\n  TYPES.L = 1; //L is the default\n  TYPES_TO_NAMES[1] = 'L';\n  Object.keys(DATA).forEach(function (type, i) {\n    TYPES[type] = 1 << (i + 1);\n    TYPES_TO_NAMES[TYPES[type]] = type;\n  });\n  Object.freeze(TYPES);\n\n  var ISOLATE_INIT_TYPES = TYPES.LRI | TYPES.RLI | TYPES.FSI;\n  var STRONG_TYPES = TYPES.L | TYPES.R | TYPES.AL;\n  var NEUTRAL_ISOLATE_TYPES = TYPES.B | TYPES.S | TYPES.WS | TYPES.ON | TYPES.FSI | TYPES.LRI | TYPES.RLI | TYPES.PDI;\n  var BN_LIKE_TYPES = TYPES.BN | TYPES.RLE | TYPES.LRE | TYPES.RLO | TYPES.LRO | TYPES.PDF;\n  var TRAILING_TYPES = TYPES.S | TYPES.WS | TYPES.B | ISOLATE_INIT_TYPES | TYPES.PDI | BN_LIKE_TYPES;\n\n  var map = null;\n\n  function parseData () {\n    if (!map) {\n      //const start = performance.now()\n      map = new Map();\n      var loop = function ( type ) {\n        if (DATA.hasOwnProperty(type)) {\n          var lastCode = 0;\n          DATA[type].split(',').forEach(function (range) {\n            var ref = range.split('+');\n            var skip = ref[0];\n            var step = ref[1];\n            skip = parseInt(skip, 36);\n            step = step ? parseInt(step, 36) : 0;\n            map.set(lastCode += skip, TYPES[type]);\n            for (var i = 0; i < step; i++) {\n              map.set(++lastCode, TYPES[type]);\n            }\n          });\n        }\n      };\n\n      for (var type in DATA) loop( type );\n      //console.log(`char types parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  /**\n   * @param {string} char\n   * @return {number}\n   */\n  function getBidiCharType (char) {\n    parseData();\n    return map.get(char.codePointAt(0)) || TYPES.L\n  }\n\n  function getBidiCharTypeName(char) {\n    return TYPES_TO_NAMES[getBidiCharType(char)]\n  }\n\n  // Bidi bracket pairs data, auto generated\n  var data$1 = {\n    \"pairs\": \"14>1,1e>2,u>2,2wt>1,1>1,1ge>1,1wp>1,1j>1,f>1,hm>1,1>1,u>1,u6>1,1>1,+5,28>1,w>1,1>1,+3,b8>1,1>1,+3,1>3,-1>-1,3>1,1>1,+2,1s>1,1>1,x>1,th>1,1>1,+2,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,4q>1,1e>2,u>2,2>1,+1\",\n    \"canonical\": \"6f1>-6dx,6dy>-6dx,6ec>-6ed,6ee>-6ed,6ww>2jj,-2ji>2jj,14r4>-1e7l,1e7m>-1e7l,1e7m>-1e5c,1e5d>-1e5b,1e5c>-14qx,14qy>-14qx,14vn>-1ecg,1ech>-1ecg,1edu>-1ecg,1eci>-1ecg,1eda>-1ecg,1eci>-1ecg,1eci>-168q,168r>-168q,168s>-14ye,14yf>-14ye\"\n  };\n\n  /**\n   * Parses an string that holds encoded codepoint mappings, e.g. for bracket pairs or\n   * mirroring characters, as encoded by scripts/generateBidiData.js. Returns an object\n   * holding the `map`, and optionally a `reverseMap` if `includeReverse:true`.\n   * @param {string} encodedString\n   * @param {boolean} includeReverse - true if you want reverseMap in the output\n   * @return {{map: Map<number, number>, reverseMap?: Map<number, number>}}\n   */\n  function parseCharacterMap (encodedString, includeReverse) {\n    var radix = 36;\n    var lastCode = 0;\n    var map = new Map();\n    var reverseMap = includeReverse && new Map();\n    var prevPair;\n    encodedString.split(',').forEach(function visit(entry) {\n      if (entry.indexOf('+') !== -1) {\n        for (var i = +entry; i--;) {\n          visit(prevPair);\n        }\n      } else {\n        prevPair = entry;\n        var ref = entry.split('>');\n        var a = ref[0];\n        var b = ref[1];\n        a = String.fromCodePoint(lastCode += parseInt(a, radix));\n        b = String.fromCodePoint(lastCode += parseInt(b, radix));\n        map.set(a, b);\n        includeReverse && reverseMap.set(b, a);\n      }\n    });\n    return { map: map, reverseMap: reverseMap }\n  }\n\n  var openToClose, closeToOpen, canonical;\n\n  function parse$1 () {\n    if (!openToClose) {\n      //const start = performance.now()\n      var ref = parseCharacterMap(data$1.pairs, true);\n      var map = ref.map;\n      var reverseMap = ref.reverseMap;\n      openToClose = map;\n      closeToOpen = reverseMap;\n      canonical = parseCharacterMap(data$1.canonical, false).map;\n      //console.log(`brackets parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  function openingToClosingBracket (char) {\n    parse$1();\n    return openToClose.get(char) || null\n  }\n\n  function closingToOpeningBracket (char) {\n    parse$1();\n    return closeToOpen.get(char) || null\n  }\n\n  function getCanonicalBracket (char) {\n    parse$1();\n    return canonical.get(char) || null\n  }\n\n  // Local type aliases\n  var TYPE_L = TYPES.L;\n  var TYPE_R = TYPES.R;\n  var TYPE_EN = TYPES.EN;\n  var TYPE_ES = TYPES.ES;\n  var TYPE_ET = TYPES.ET;\n  var TYPE_AN = TYPES.AN;\n  var TYPE_CS = TYPES.CS;\n  var TYPE_B = TYPES.B;\n  var TYPE_S = TYPES.S;\n  var TYPE_ON = TYPES.ON;\n  var TYPE_BN = TYPES.BN;\n  var TYPE_NSM = TYPES.NSM;\n  var TYPE_AL = TYPES.AL;\n  var TYPE_LRO = TYPES.LRO;\n  var TYPE_RLO = TYPES.RLO;\n  var TYPE_LRE = TYPES.LRE;\n  var TYPE_RLE = TYPES.RLE;\n  var TYPE_PDF = TYPES.PDF;\n  var TYPE_LRI = TYPES.LRI;\n  var TYPE_RLI = TYPES.RLI;\n  var TYPE_FSI = TYPES.FSI;\n  var TYPE_PDI = TYPES.PDI;\n\n  /**\n   * @typedef {object} GetEmbeddingLevelsResult\n   * @property {{start, end, level}[]} paragraphs\n   * @property {Uint8Array} levels\n   */\n\n  /**\n   * This function applies the Bidirectional Algorithm to a string, returning the resolved embedding levels\n   * in a single Uint8Array plus a list of objects holding each paragraph's start and end indices and resolved\n   * base embedding level.\n   *\n   * @param {string} string - The input string\n   * @param {\"ltr\"|\"rtl\"|\"auto\"} [baseDirection] - Use \"ltr\" or \"rtl\" to force a base paragraph direction,\n   *        otherwise a direction will be chosen automatically from each paragraph's contents.\n   * @return {GetEmbeddingLevelsResult}\n   */\n  function getEmbeddingLevels (string, baseDirection) {\n    var MAX_DEPTH = 125;\n\n    // Start by mapping all characters to their unicode type, as a bitmask integer\n    var charTypes = new Uint32Array(string.length);\n    for (var i = 0; i < string.length; i++) {\n      charTypes[i] = getBidiCharType(string[i]);\n    }\n\n    var charTypeCounts = new Map(); //will be cleared at start of each paragraph\n    function changeCharType(i, type) {\n      var oldType = charTypes[i];\n      charTypes[i] = type;\n      charTypeCounts.set(oldType, charTypeCounts.get(oldType) - 1);\n      if (oldType & NEUTRAL_ISOLATE_TYPES) {\n        charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) - 1);\n      }\n      charTypeCounts.set(type, (charTypeCounts.get(type) || 0) + 1);\n      if (type & NEUTRAL_ISOLATE_TYPES) {\n        charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) || 0) + 1);\n      }\n    }\n\n    var embedLevels = new Uint8Array(string.length);\n    var isolationPairs = new Map(); //init->pdi and pdi->init\n\n    // === 3.3.1 The Paragraph Level ===\n    // 3.3.1 P1: Split the text into paragraphs\n    var paragraphs = []; // [{start, end, level}, ...]\n    var paragraph = null;\n    for (var i$1 = 0; i$1 < string.length; i$1++) {\n      if (!paragraph) {\n        paragraphs.push(paragraph = {\n          start: i$1,\n          end: string.length - 1,\n          // 3.3.1 P2-P3: Determine the paragraph level\n          level: baseDirection === 'rtl' ? 1 : baseDirection === 'ltr' ? 0 : determineAutoEmbedLevel(i$1, false)\n        });\n      }\n      if (charTypes[i$1] & TYPE_B) {\n        paragraph.end = i$1;\n        paragraph = null;\n      }\n    }\n\n    var FORMATTING_TYPES = TYPE_RLE | TYPE_LRE | TYPE_RLO | TYPE_LRO | ISOLATE_INIT_TYPES | TYPE_PDI | TYPE_PDF | TYPE_B;\n    var nextEven = function (n) { return n + ((n & 1) ? 1 : 2); };\n    var nextOdd = function (n) { return n + ((n & 1) ? 2 : 1); };\n\n    // Everything from here on will operate per paragraph.\n    for (var paraIdx = 0; paraIdx < paragraphs.length; paraIdx++) {\n      paragraph = paragraphs[paraIdx];\n      var statusStack = [{\n        _level: paragraph.level,\n        _override: 0, //0=neutral, 1=L, 2=R\n        _isolate: 0 //bool\n      }];\n      var stackTop = (void 0);\n      var overflowIsolateCount = 0;\n      var overflowEmbeddingCount = 0;\n      var validIsolateCount = 0;\n      charTypeCounts.clear();\n\n      // === 3.3.2 Explicit Levels and Directions ===\n      for (var i$2 = paragraph.start; i$2 <= paragraph.end; i$2++) {\n        var charType = charTypes[i$2];\n        stackTop = statusStack[statusStack.length - 1];\n\n        // Set initial counts\n        charTypeCounts.set(charType, (charTypeCounts.get(charType) || 0) + 1);\n        if (charType & NEUTRAL_ISOLATE_TYPES) {\n          charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) || 0) + 1);\n        }\n\n        // Explicit Embeddings: 3.3.2 X2 - X3\n        if (charType & FORMATTING_TYPES) { //prefilter all formatters\n          if (charType & (TYPE_RLE | TYPE_LRE)) {\n            embedLevels[i$2] = stackTop._level; // 5.2\n            var level = (charType === TYPE_RLE ? nextOdd : nextEven)(stackTop._level);\n            if (level <= MAX_DEPTH && !overflowIsolateCount && !overflowEmbeddingCount) {\n              statusStack.push({\n                _level: level,\n                _override: 0,\n                _isolate: 0\n              });\n            } else if (!overflowIsolateCount) {\n              overflowEmbeddingCount++;\n            }\n          }\n\n          // Explicit Overrides: 3.3.2 X4 - X5\n          else if (charType & (TYPE_RLO | TYPE_LRO)) {\n            embedLevels[i$2] = stackTop._level; // 5.2\n            var level$1 = (charType === TYPE_RLO ? nextOdd : nextEven)(stackTop._level);\n            if (level$1 <= MAX_DEPTH && !overflowIsolateCount && !overflowEmbeddingCount) {\n              statusStack.push({\n                _level: level$1,\n                _override: (charType & TYPE_RLO) ? TYPE_R : TYPE_L,\n                _isolate: 0\n              });\n            } else if (!overflowIsolateCount) {\n              overflowEmbeddingCount++;\n            }\n          }\n\n          // Isolates: 3.3.2 X5a - X5c\n          else if (charType & ISOLATE_INIT_TYPES) {\n            // X5c - FSI becomes either RLI or LRI\n            if (charType & TYPE_FSI) {\n              charType = determineAutoEmbedLevel(i$2 + 1, true) === 1 ? TYPE_RLI : TYPE_LRI;\n            }\n\n            embedLevels[i$2] = stackTop._level;\n            if (stackTop._override) {\n              changeCharType(i$2, stackTop._override);\n            }\n            var level$2 = (charType === TYPE_RLI ? nextOdd : nextEven)(stackTop._level);\n            if (level$2 <= MAX_DEPTH && overflowIsolateCount === 0 && overflowEmbeddingCount === 0) {\n              validIsolateCount++;\n              statusStack.push({\n                _level: level$2,\n                _override: 0,\n                _isolate: 1,\n                _isolInitIndex: i$2\n              });\n            } else {\n              overflowIsolateCount++;\n            }\n          }\n\n          // Terminating Isolates: 3.3.2 X6a\n          else if (charType & TYPE_PDI) {\n            if (overflowIsolateCount > 0) {\n              overflowIsolateCount--;\n            } else if (validIsolateCount > 0) {\n              overflowEmbeddingCount = 0;\n              while (!statusStack[statusStack.length - 1]._isolate) {\n                statusStack.pop();\n              }\n              // Add to isolation pairs bidirectional mapping:\n              var isolInitIndex = statusStack[statusStack.length - 1]._isolInitIndex;\n              if (isolInitIndex != null) {\n                isolationPairs.set(isolInitIndex, i$2);\n                isolationPairs.set(i$2, isolInitIndex);\n              }\n              statusStack.pop();\n              validIsolateCount--;\n            }\n            stackTop = statusStack[statusStack.length - 1];\n            embedLevels[i$2] = stackTop._level;\n            if (stackTop._override) {\n              changeCharType(i$2, stackTop._override);\n            }\n          }\n\n\n          // Terminating Embeddings and Overrides: 3.3.2 X7\n          else if (charType & TYPE_PDF) {\n            if (overflowIsolateCount === 0) {\n              if (overflowEmbeddingCount > 0) {\n                overflowEmbeddingCount--;\n              } else if (!stackTop._isolate && statusStack.length > 1) {\n                statusStack.pop();\n                stackTop = statusStack[statusStack.length - 1];\n              }\n            }\n            embedLevels[i$2] = stackTop._level; // 5.2\n          }\n\n          // End of Paragraph: 3.3.2 X8\n          else if (charType & TYPE_B) {\n            embedLevels[i$2] = paragraph.level;\n          }\n        }\n\n        // Non-formatting characters: 3.3.2 X6\n        else {\n          embedLevels[i$2] = stackTop._level;\n          // NOTE: This exclusion of BN seems to go against what section 5.2 says, but is required for test passage\n          if (stackTop._override && charType !== TYPE_BN) {\n            changeCharType(i$2, stackTop._override);\n          }\n        }\n      }\n\n      // === 3.3.3 Preparations for Implicit Processing ===\n\n      // Remove all RLE, LRE, RLO, LRO, PDF, and BN characters: 3.3.3 X9\n      // Note: Due to section 5.2, we won't remove them, but we'll use the BN_LIKE_TYPES bitset to\n      // easily ignore them all from here on out.\n\n      // 3.3.3 X10\n      // Compute the set of isolating run sequences as specified by BD13\n      var levelRuns = [];\n      var currentRun = null;\n      for (var i$3 = paragraph.start; i$3 <= paragraph.end; i$3++) {\n        var charType$1 = charTypes[i$3];\n        if (!(charType$1 & BN_LIKE_TYPES)) {\n          var lvl = embedLevels[i$3];\n          var isIsolInit = charType$1 & ISOLATE_INIT_TYPES;\n          var isPDI = charType$1 === TYPE_PDI;\n          if (currentRun && lvl === currentRun._level) {\n            currentRun._end = i$3;\n            currentRun._endsWithIsolInit = isIsolInit;\n          } else {\n            levelRuns.push(currentRun = {\n              _start: i$3,\n              _end: i$3,\n              _level: lvl,\n              _startsWithPDI: isPDI,\n              _endsWithIsolInit: isIsolInit\n            });\n          }\n        }\n      }\n      var isolatingRunSeqs = []; // [{seqIndices: [], sosType: L|R, eosType: L|R}]\n      for (var runIdx = 0; runIdx < levelRuns.length; runIdx++) {\n        var run = levelRuns[runIdx];\n        if (!run._startsWithPDI || (run._startsWithPDI && !isolationPairs.has(run._start))) {\n          var seqRuns = [currentRun = run];\n          for (var pdiIndex = (void 0); currentRun && currentRun._endsWithIsolInit && (pdiIndex = isolationPairs.get(currentRun._end)) != null;) {\n            for (var i$4 = runIdx + 1; i$4 < levelRuns.length; i$4++) {\n              if (levelRuns[i$4]._start === pdiIndex) {\n                seqRuns.push(currentRun = levelRuns[i$4]);\n                break\n              }\n            }\n          }\n          // build flat list of indices across all runs:\n          var seqIndices = [];\n          for (var i$5 = 0; i$5 < seqRuns.length; i$5++) {\n            var run$1 = seqRuns[i$5];\n            for (var j = run$1._start; j <= run$1._end; j++) {\n              seqIndices.push(j);\n            }\n          }\n          // determine the sos/eos types:\n          var firstLevel = embedLevels[seqIndices[0]];\n          var prevLevel = paragraph.level;\n          for (var i$6 = seqIndices[0] - 1; i$6 >= 0; i$6--) {\n            if (!(charTypes[i$6] & BN_LIKE_TYPES)) { //5.2\n              prevLevel = embedLevels[i$6];\n              break\n            }\n          }\n          var lastIndex = seqIndices[seqIndices.length - 1];\n          var lastLevel = embedLevels[lastIndex];\n          var nextLevel = paragraph.level;\n          if (!(charTypes[lastIndex] & ISOLATE_INIT_TYPES)) {\n            for (var i$7 = lastIndex + 1; i$7 <= paragraph.end; i$7++) {\n              if (!(charTypes[i$7] & BN_LIKE_TYPES)) { //5.2\n                nextLevel = embedLevels[i$7];\n                break\n              }\n            }\n          }\n          isolatingRunSeqs.push({\n            _seqIndices: seqIndices,\n            _sosType: Math.max(prevLevel, firstLevel) % 2 ? TYPE_R : TYPE_L,\n            _eosType: Math.max(nextLevel, lastLevel) % 2 ? TYPE_R : TYPE_L\n          });\n        }\n      }\n\n      // The next steps are done per isolating run sequence\n      for (var seqIdx = 0; seqIdx < isolatingRunSeqs.length; seqIdx++) {\n        var ref = isolatingRunSeqs[seqIdx];\n        var seqIndices$1 = ref._seqIndices;\n        var sosType = ref._sosType;\n        var eosType = ref._eosType;\n        /**\n         * All the level runs in an isolating run sequence have the same embedding level.\n         * \n         * DO NOT change any `embedLevels[i]` within the current scope.\n         */\n        var embedDirection = ((embedLevels[seqIndices$1[0]]) & 1) ? TYPE_R : TYPE_L;\n\n        // === 3.3.4 Resolving Weak Types ===\n\n        // W1 + 5.2. Search backward from each NSM to the first character in the isolating run sequence whose\n        // bidirectional type is not BN, and set the NSM to ON if it is an isolate initiator or PDI, and to its\n        // type otherwise. If the NSM is the first non-BN character, change the NSM to the type of sos.\n        if (charTypeCounts.get(TYPE_NSM)) {\n          for (var si = 0; si < seqIndices$1.length; si++) {\n            var i$8 = seqIndices$1[si];\n            if (charTypes[i$8] & TYPE_NSM) {\n              var prevType = sosType;\n              for (var sj = si - 1; sj >= 0; sj--) {\n                if (!(charTypes[seqIndices$1[sj]] & BN_LIKE_TYPES)) { //5.2 scan back to first non-BN\n                  prevType = charTypes[seqIndices$1[sj]];\n                  break\n                }\n              }\n              changeCharType(i$8, (prevType & (ISOLATE_INIT_TYPES | TYPE_PDI)) ? TYPE_ON : prevType);\n            }\n          }\n        }\n\n        // W2. Search backward from each instance of a European number until the first strong type (R, L, AL, or sos)\n        // is found. If an AL is found, change the type of the European number to Arabic number.\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$1 = 0; si$1 < seqIndices$1.length; si$1++) {\n            var i$9 = seqIndices$1[si$1];\n            if (charTypes[i$9] & TYPE_EN) {\n              for (var sj$1 = si$1 - 1; sj$1 >= -1; sj$1--) {\n                var prevCharType = sj$1 === -1 ? sosType : charTypes[seqIndices$1[sj$1]];\n                if (prevCharType & STRONG_TYPES) {\n                  if (prevCharType === TYPE_AL) {\n                    changeCharType(i$9, TYPE_AN);\n                  }\n                  break\n                }\n              }\n            }\n          }\n        }\n\n        // W3. Change all ALs to R\n        if (charTypeCounts.get(TYPE_AL)) {\n          for (var si$2 = 0; si$2 < seqIndices$1.length; si$2++) {\n            var i$10 = seqIndices$1[si$2];\n            if (charTypes[i$10] & TYPE_AL) {\n              changeCharType(i$10, TYPE_R);\n            }\n          }\n        }\n\n        // W4. A single European separator between two European numbers changes to a European number. A single common\n        // separator between two numbers of the same type changes to that type.\n        if (charTypeCounts.get(TYPE_ES) || charTypeCounts.get(TYPE_CS)) {\n          for (var si$3 = 1; si$3 < seqIndices$1.length - 1; si$3++) {\n            var i$11 = seqIndices$1[si$3];\n            if (charTypes[i$11] & (TYPE_ES | TYPE_CS)) {\n              var prevType$1 = 0, nextType = 0;\n              for (var sj$2 = si$3 - 1; sj$2 >= 0; sj$2--) {\n                prevType$1 = charTypes[seqIndices$1[sj$2]];\n                if (!(prevType$1 & BN_LIKE_TYPES)) { //5.2\n                  break\n                }\n              }\n              for (var sj$3 = si$3 + 1; sj$3 < seqIndices$1.length; sj$3++) {\n                nextType = charTypes[seqIndices$1[sj$3]];\n                if (!(nextType & BN_LIKE_TYPES)) { //5.2\n                  break\n                }\n              }\n              if (prevType$1 === nextType && (charTypes[i$11] === TYPE_ES ? prevType$1 === TYPE_EN : (prevType$1 & (TYPE_EN | TYPE_AN)))) {\n                changeCharType(i$11, prevType$1);\n              }\n            }\n          }\n        }\n\n        // W5. A sequence of European terminators adjacent to European numbers changes to all European numbers.\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$4 = 0; si$4 < seqIndices$1.length; si$4++) {\n            var i$12 = seqIndices$1[si$4];\n            if (charTypes[i$12] & TYPE_EN) {\n              for (var sj$4 = si$4 - 1; sj$4 >= 0 && (charTypes[seqIndices$1[sj$4]] & (TYPE_ET | BN_LIKE_TYPES)); sj$4--) {\n                changeCharType(seqIndices$1[sj$4], TYPE_EN);\n              }\n              for (si$4++; si$4 < seqIndices$1.length && (charTypes[seqIndices$1[si$4]] & (TYPE_ET | BN_LIKE_TYPES | TYPE_EN)); si$4++) {\n                if (charTypes[seqIndices$1[si$4]] !== TYPE_EN) {\n                  changeCharType(seqIndices$1[si$4], TYPE_EN);\n                }\n              }\n            }\n          }\n        }\n\n        // W6. Otherwise, separators and terminators change to Other Neutral.\n        if (charTypeCounts.get(TYPE_ET) || charTypeCounts.get(TYPE_ES) || charTypeCounts.get(TYPE_CS)) {\n          for (var si$5 = 0; si$5 < seqIndices$1.length; si$5++) {\n            var i$13 = seqIndices$1[si$5];\n            if (charTypes[i$13] & (TYPE_ET | TYPE_ES | TYPE_CS)) {\n              changeCharType(i$13, TYPE_ON);\n              // 5.2 transform adjacent BNs too:\n              for (var sj$5 = si$5 - 1; sj$5 >= 0 && (charTypes[seqIndices$1[sj$5]] & BN_LIKE_TYPES); sj$5--) {\n                changeCharType(seqIndices$1[sj$5], TYPE_ON);\n              }\n              for (var sj$6 = si$5 + 1; sj$6 < seqIndices$1.length && (charTypes[seqIndices$1[sj$6]] & BN_LIKE_TYPES); sj$6++) {\n                changeCharType(seqIndices$1[sj$6], TYPE_ON);\n              }\n            }\n          }\n        }\n\n        // W7. Search backward from each instance of a European number until the first strong type (R, L, or sos)\n        // is found. If an L is found, then change the type of the European number to L.\n        // NOTE: implemented in single forward pass for efficiency\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$6 = 0, prevStrongType = sosType; si$6 < seqIndices$1.length; si$6++) {\n            var i$14 = seqIndices$1[si$6];\n            var type = charTypes[i$14];\n            if (type & TYPE_EN) {\n              if (prevStrongType === TYPE_L) {\n                changeCharType(i$14, TYPE_L);\n              }\n            } else if (type & STRONG_TYPES) {\n              prevStrongType = type;\n            }\n          }\n        }\n\n        // === 3.3.5 Resolving Neutral and Isolate Formatting Types ===\n\n        if (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES)) {\n          // N0. Process bracket pairs in an isolating run sequence sequentially in the logical order of the text\n          // positions of the opening paired brackets using the logic given below. Within this scope, bidirectional\n          // types EN and AN are treated as R.\n          var R_TYPES_FOR_N_STEPS = (TYPE_R | TYPE_EN | TYPE_AN);\n          var STRONG_TYPES_FOR_N_STEPS = R_TYPES_FOR_N_STEPS | TYPE_L;\n\n          // * Identify the bracket pairs in the current isolating run sequence according to BD16.\n          var bracketPairs = [];\n          {\n            var openerStack = [];\n            for (var si$7 = 0; si$7 < seqIndices$1.length; si$7++) {\n              // NOTE: for any potential bracket character we also test that it still carries a NI\n              // type, as that may have been changed earlier. This doesn't seem to be explicitly\n              // called out in the spec, but is required for passage of certain tests.\n              if (charTypes[seqIndices$1[si$7]] & NEUTRAL_ISOLATE_TYPES) {\n                var char = string[seqIndices$1[si$7]];\n                var oppositeBracket = (void 0);\n                // Opening bracket\n                if (openingToClosingBracket(char) !== null) {\n                  if (openerStack.length < 63) {\n                    openerStack.push({ char: char, seqIndex: si$7 });\n                  } else {\n                    break\n                  }\n                }\n                // Closing bracket\n                else if ((oppositeBracket = closingToOpeningBracket(char)) !== null) {\n                  for (var stackIdx = openerStack.length - 1; stackIdx >= 0; stackIdx--) {\n                    var stackChar = openerStack[stackIdx].char;\n                    if (stackChar === oppositeBracket ||\n                      stackChar === closingToOpeningBracket(getCanonicalBracket(char)) ||\n                      openingToClosingBracket(getCanonicalBracket(stackChar)) === char\n                    ) {\n                      bracketPairs.push([openerStack[stackIdx].seqIndex, si$7]);\n                      openerStack.length = stackIdx; //pop the matching bracket and all following\n                      break\n                    }\n                  }\n                }\n              }\n            }\n            bracketPairs.sort(function (a, b) { return a[0] - b[0]; });\n          }\n          // * For each bracket-pair element in the list of pairs of text positions\n          for (var pairIdx = 0; pairIdx < bracketPairs.length; pairIdx++) {\n            var ref$1 = bracketPairs[pairIdx];\n            var openSeqIdx = ref$1[0];\n            var closeSeqIdx = ref$1[1];\n            // a. Inspect the bidirectional types of the characters enclosed within the bracket pair.\n            // b. If any strong type (either L or R) matching the embedding direction is found, set the type for both\n            // brackets in the pair to match the embedding direction.\n            var foundStrongType = false;\n            var useStrongType = 0;\n            for (var si$8 = openSeqIdx + 1; si$8 < closeSeqIdx; si$8++) {\n              var i$15 = seqIndices$1[si$8];\n              if (charTypes[i$15] & STRONG_TYPES_FOR_N_STEPS) {\n                foundStrongType = true;\n                var lr = (charTypes[i$15] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                if (lr === embedDirection) {\n                  useStrongType = lr;\n                  break\n                }\n              }\n            }\n            // c. Otherwise, if there is a strong type it must be opposite the embedding direction. Therefore, test\n            // for an established context with a preceding strong type by checking backwards before the opening paired\n            // bracket until the first strong type (L, R, or sos) is found.\n            //    1. If the preceding strong type is also opposite the embedding direction, context is established, so\n            //    set the type for both brackets in the pair to that direction.\n            //    2. Otherwise set the type for both brackets in the pair to the embedding direction.\n            if (foundStrongType && !useStrongType) {\n              useStrongType = sosType;\n              for (var si$9 = openSeqIdx - 1; si$9 >= 0; si$9--) {\n                var i$16 = seqIndices$1[si$9];\n                if (charTypes[i$16] & STRONG_TYPES_FOR_N_STEPS) {\n                  var lr$1 = (charTypes[i$16] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  if (lr$1 !== embedDirection) {\n                    useStrongType = lr$1;\n                  } else {\n                    useStrongType = embedDirection;\n                  }\n                  break\n                }\n              }\n            }\n            if (useStrongType) {\n              charTypes[seqIndices$1[openSeqIdx]] = charTypes[seqIndices$1[closeSeqIdx]] = useStrongType;\n              // * Any number of characters that had original bidirectional character type NSM prior to the application\n              // of W1 that immediately follow a paired bracket which changed to L or R under N0 should change to match\n              // the type of their preceding bracket.\n              if (useStrongType !== embedDirection) {\n                for (var si$10 = openSeqIdx + 1; si$10 < seqIndices$1.length; si$10++) {\n                  if (!(charTypes[seqIndices$1[si$10]] & BN_LIKE_TYPES)) {\n                    if (getBidiCharType(string[seqIndices$1[si$10]]) & TYPE_NSM) {\n                      charTypes[seqIndices$1[si$10]] = useStrongType;\n                    }\n                    break\n                  }\n                }\n              }\n              if (useStrongType !== embedDirection) {\n                for (var si$11 = closeSeqIdx + 1; si$11 < seqIndices$1.length; si$11++) {\n                  if (!(charTypes[seqIndices$1[si$11]] & BN_LIKE_TYPES)) {\n                    if (getBidiCharType(string[seqIndices$1[si$11]]) & TYPE_NSM) {\n                      charTypes[seqIndices$1[si$11]] = useStrongType;\n                    }\n                    break\n                  }\n                }\n              }\n            }\n          }\n\n          // N1. A sequence of NIs takes the direction of the surrounding strong text if the text on both sides has the\n          // same direction.\n          // N2. Any remaining NIs take the embedding direction.\n          for (var si$12 = 0; si$12 < seqIndices$1.length; si$12++) {\n            if (charTypes[seqIndices$1[si$12]] & NEUTRAL_ISOLATE_TYPES) {\n              var niRunStart = si$12, niRunEnd = si$12;\n              var prevType$2 = sosType; //si === 0 ? sosType : (charTypes[seqIndices[si - 1]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L\n              for (var si2 = si$12 - 1; si2 >= 0; si2--) {\n                if (charTypes[seqIndices$1[si2]] & BN_LIKE_TYPES) {\n                  niRunStart = si2; //5.2 treat BNs adjacent to NIs as NIs\n                } else {\n                  prevType$2 = (charTypes[seqIndices$1[si2]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  break\n                }\n              }\n              var nextType$1 = eosType;\n              for (var si2$1 = si$12 + 1; si2$1 < seqIndices$1.length; si2$1++) {\n                if (charTypes[seqIndices$1[si2$1]] & (NEUTRAL_ISOLATE_TYPES | BN_LIKE_TYPES)) {\n                  niRunEnd = si2$1;\n                } else {\n                  nextType$1 = (charTypes[seqIndices$1[si2$1]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  break\n                }\n              }\n              for (var sj$7 = niRunStart; sj$7 <= niRunEnd; sj$7++) {\n                charTypes[seqIndices$1[sj$7]] = prevType$2 === nextType$1 ? prevType$2 : embedDirection;\n              }\n              si$12 = niRunEnd;\n            }\n          }\n        }\n      }\n\n      // === 3.3.6 Resolving Implicit Levels ===\n\n      for (var i$17 = paragraph.start; i$17 <= paragraph.end; i$17++) {\n        var level$3 = embedLevels[i$17];\n        var type$1 = charTypes[i$17];\n        // I2. For all characters with an odd (right-to-left) embedding level, those of type L, EN or AN go up one level.\n        if (level$3 & 1) {\n          if (type$1 & (TYPE_L | TYPE_EN | TYPE_AN)) {\n            embedLevels[i$17]++;\n          }\n        }\n          // I1. For all characters with an even (left-to-right) embedding level, those of type R go up one level\n        // and those of type AN or EN go up two levels.\n        else {\n          if (type$1 & TYPE_R) {\n            embedLevels[i$17]++;\n          } else if (type$1 & (TYPE_AN | TYPE_EN)) {\n            embedLevels[i$17] += 2;\n          }\n        }\n\n        // 5.2: Resolve any LRE, RLE, LRO, RLO, PDF, or BN to the level of the preceding character if there is one,\n        // and otherwise to the base level.\n        if (type$1 & BN_LIKE_TYPES) {\n          embedLevels[i$17] = i$17 === 0 ? paragraph.level : embedLevels[i$17 - 1];\n        }\n\n        // 3.4 L1.1-4: Reset the embedding level of segment/paragraph separators, and any sequence of whitespace or\n        // isolate formatting characters preceding them or the end of the paragraph, to the paragraph level.\n        // NOTE: this will also need to be applied to each individual line ending after line wrapping occurs.\n        if (i$17 === paragraph.end || getBidiCharType(string[i$17]) & (TYPE_S | TYPE_B)) {\n          for (var j$1 = i$17; j$1 >= 0 && (getBidiCharType(string[j$1]) & TRAILING_TYPES); j$1--) {\n            embedLevels[j$1] = paragraph.level;\n          }\n        }\n      }\n    }\n\n    // DONE! The resolved levels can then be used, after line wrapping, to flip runs of characters\n    // according to section 3.4 Reordering Resolved Levels\n    return {\n      levels: embedLevels,\n      paragraphs: paragraphs\n    }\n\n    function determineAutoEmbedLevel (start, isFSI) {\n      // 3.3.1 P2 - P3\n      for (var i = start; i < string.length; i++) {\n        var charType = charTypes[i];\n        if (charType & (TYPE_R | TYPE_AL)) {\n          return 1\n        }\n        if ((charType & (TYPE_B | TYPE_L)) || (isFSI && charType === TYPE_PDI)) {\n          return 0\n        }\n        if (charType & ISOLATE_INIT_TYPES) {\n          var pdi = indexOfMatchingPDI(i);\n          i = pdi === -1 ? string.length : pdi;\n        }\n      }\n      return 0\n    }\n\n    function indexOfMatchingPDI (isolateStart) {\n      // 3.1.2 BD9\n      var isolationLevel = 1;\n      for (var i = isolateStart + 1; i < string.length; i++) {\n        var charType = charTypes[i];\n        if (charType & TYPE_B) {\n          break\n        }\n        if (charType & TYPE_PDI) {\n          if (--isolationLevel === 0) {\n            return i\n          }\n        } else if (charType & ISOLATE_INIT_TYPES) {\n          isolationLevel++;\n        }\n      }\n      return -1\n    }\n  }\n\n  // Bidi mirrored chars data, auto generated\n  var data = \"14>1,j>2,t>2,u>2,1a>g,2v3>1,1>1,1ge>1,1wd>1,b>1,1j>1,f>1,ai>3,-2>3,+1,8>1k0,-1jq>1y7,-1y6>1hf,-1he>1h6,-1h5>1ha,-1h8>1qi,-1pu>1,6>3u,-3s>7,6>1,1>1,f>1,1>1,+2,3>1,1>1,+13,4>1,1>1,6>1eo,-1ee>1,3>1mg,-1me>1mk,-1mj>1mi,-1mg>1mi,-1md>1,1>1,+2,1>10k,-103>1,1>1,4>1,5>1,1>1,+10,3>1,1>8,-7>8,+1,-6>7,+1,a>1,1>1,u>1,u6>1,1>1,+5,26>1,1>1,2>1,2>2,8>1,7>1,4>1,1>1,+5,b8>1,1>1,+3,1>3,-2>1,2>1,1>1,+2,c>1,3>1,1>1,+2,h>1,3>1,a>1,1>1,2>1,3>1,1>1,d>1,f>1,3>1,1a>1,1>1,6>1,7>1,13>1,k>1,1>1,+19,4>1,1>1,+2,2>1,1>1,+18,m>1,a>1,1>1,lk>1,1>1,4>1,2>1,f>1,3>1,1>1,+3,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,6>1,4j>1,j>2,t>2,u>2,2>1,+1\";\n\n  var mirrorMap;\n\n  function parse () {\n    if (!mirrorMap) {\n      //const start = performance.now()\n      var ref = parseCharacterMap(data, true);\n      var map = ref.map;\n      var reverseMap = ref.reverseMap;\n      // Combine both maps into one\n      reverseMap.forEach(function (value, key) {\n        map.set(key, value);\n      });\n      mirrorMap = map;\n      //console.log(`mirrored chars parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  function getMirroredCharacter (char) {\n    parse();\n    return mirrorMap.get(char) || null\n  }\n\n  /**\n   * Given a string and its resolved embedding levels, build a map of indices to replacement chars\n   * for any characters in right-to-left segments that have defined mirrored characters.\n   * @param string\n   * @param embeddingLevels\n   * @param [start]\n   * @param [end]\n   * @return {Map<number, string>}\n   */\n  function getMirroredCharactersMap(string, embeddingLevels, start, end) {\n    var strLen = string.length;\n    start = Math.max(0, start == null ? 0 : +start);\n    end = Math.min(strLen - 1, end == null ? strLen - 1 : +end);\n\n    var map = new Map();\n    for (var i = start; i <= end; i++) {\n      if (embeddingLevels[i] & 1) { //only odd (rtl) levels\n        var mirror = getMirroredCharacter(string[i]);\n        if (mirror !== null) {\n          map.set(i, mirror);\n        }\n      }\n    }\n    return map\n  }\n\n  /**\n   * Given a start and end denoting a single line within a string, and a set of precalculated\n   * bidi embedding levels, produce a list of segments whose ordering should be flipped, in sequence.\n   * @param {string} string - the full input string\n   * @param {GetEmbeddingLevelsResult} embeddingLevelsResult - the result object from getEmbeddingLevels\n   * @param {number} [start] - first character in a subset of the full string\n   * @param {number} [end] - last character in a subset of the full string\n   * @return {number[][]} - the list of start/end segments that should be flipped, in order.\n   */\n  function getReorderSegments(string, embeddingLevelsResult, start, end) {\n    var strLen = string.length;\n    start = Math.max(0, start == null ? 0 : +start);\n    end = Math.min(strLen - 1, end == null ? strLen - 1 : +end);\n\n    var segments = [];\n    embeddingLevelsResult.paragraphs.forEach(function (paragraph) {\n      var lineStart = Math.max(start, paragraph.start);\n      var lineEnd = Math.min(end, paragraph.end);\n      if (lineStart < lineEnd) {\n        // Local slice for mutation\n        var lineLevels = embeddingLevelsResult.levels.slice(lineStart, lineEnd + 1);\n\n        // 3.4 L1.4: Reset any sequence of whitespace characters and/or isolate formatting characters at the\n        // end of the line to the paragraph level.\n        for (var i = lineEnd; i >= lineStart && (getBidiCharType(string[i]) & TRAILING_TYPES); i--) {\n          lineLevels[i] = paragraph.level;\n        }\n\n        // L2. From the highest level found in the text to the lowest odd level on each line, including intermediate levels\n        // not actually present in the text, reverse any contiguous sequence of characters that are at that level or higher.\n        var maxLevel = paragraph.level;\n        var minOddLevel = Infinity;\n        for (var i$1 = 0; i$1 < lineLevels.length; i$1++) {\n          var level = lineLevels[i$1];\n          if (level > maxLevel) { maxLevel = level; }\n          if (level < minOddLevel) { minOddLevel = level | 1; }\n        }\n        for (var lvl = maxLevel; lvl >= minOddLevel; lvl--) {\n          for (var i$2 = 0; i$2 < lineLevels.length; i$2++) {\n            if (lineLevels[i$2] >= lvl) {\n              var segStart = i$2;\n              while (i$2 + 1 < lineLevels.length && lineLevels[i$2 + 1] >= lvl) {\n                i$2++;\n              }\n              if (i$2 > segStart) {\n                segments.push([segStart + lineStart, i$2 + lineStart]);\n              }\n            }\n          }\n        }\n      }\n    });\n    return segments\n  }\n\n  /**\n   * @param {string} string\n   * @param {GetEmbeddingLevelsResult} embedLevelsResult\n   * @param {number} [start]\n   * @param {number} [end]\n   * @return {string} the new string with bidi segments reordered\n   */\n  function getReorderedString(string, embedLevelsResult, start, end) {\n    var indices = getReorderedIndices(string, embedLevelsResult, start, end);\n    var chars = [].concat( string );\n    indices.forEach(function (charIndex, i) {\n      chars[i] = (\n        (embedLevelsResult.levels[charIndex] & 1) ? getMirroredCharacter(string[charIndex]) : null\n      ) || string[charIndex];\n    });\n    return chars.join('')\n  }\n\n  /**\n   * @param {string} string\n   * @param {GetEmbeddingLevelsResult} embedLevelsResult\n   * @param {number} [start]\n   * @param {number} [end]\n   * @return {number[]} an array with character indices in their new bidi order\n   */\n  function getReorderedIndices(string, embedLevelsResult, start, end) {\n    var segments = getReorderSegments(string, embedLevelsResult, start, end);\n    // Fill an array with indices\n    var indices = [];\n    for (var i = 0; i < string.length; i++) {\n      indices[i] = i;\n    }\n    // Reverse each segment in order\n    segments.forEach(function (ref) {\n      var start = ref[0];\n      var end = ref[1];\n\n      var slice = indices.slice(start, end + 1);\n      for (var i = slice.length; i--;) {\n        indices[end - i] = slice[i];\n      }\n    });\n    return indices\n  }\n\n  exports.closingToOpeningBracket = closingToOpeningBracket;\n  exports.getBidiCharType = getBidiCharType;\n  exports.getBidiCharTypeName = getBidiCharTypeName;\n  exports.getCanonicalBracket = getCanonicalBracket;\n  exports.getEmbeddingLevels = getEmbeddingLevels;\n  exports.getMirroredCharacter = getMirroredCharacter;\n  exports.getMirroredCharactersMap = getMirroredCharactersMap;\n  exports.getReorderSegments = getReorderSegments;\n  exports.getReorderedIndices = getReorderedIndices;\n  exports.getReorderedString = getReorderedString;\n  exports.openingToClosingBracket = openingToClosingBracket;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n}({}));\nreturn bidi}\n\nexport default bidiFactory;\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACT,IAAI,OAAQ,SAAU,OAAO;QAE3B,4CAA4C;QAC5C,IAAI,OAAO;YACT,KAAK;YACL,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,KAAK;YACL,KAAK;YACL,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QAEA,IAAI,QAAQ,CAAC;QACb,IAAI,iBAAiB,CAAC;QACtB,MAAM,CAAC,GAAG,GAAG,kBAAkB;QAC/B,cAAc,CAAC,EAAE,GAAG;QACpB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,SAAU,IAAI,EAAE,CAAC;YACzC,KAAK,CAAC,KAAK,GAAG,KAAM,IAAI;YACxB,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG;QAChC;QACA,OAAO,MAAM,CAAC;QAEd,IAAI,qBAAqB,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;QAC1D,IAAI,eAAe,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE;QAC/C,IAAI,wBAAwB,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;QACnH,IAAI,gBAAgB,MAAM,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;QACxF,IAAI,iBAAiB,MAAM,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,qBAAqB,MAAM,GAAG,GAAG;QAErF,IAAI,MAAM;QAEV,SAAS;YACP,IAAI,CAAC,KAAK;gBACR,iCAAiC;gBACjC,MAAM,IAAI;gBACV,IAAI,OAAO,SAAW,IAAI;oBACxB,IAAI,KAAK,cAAc,CAAC,OAAO;wBAC7B,IAAI,WAAW;wBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,SAAU,KAAK;4BAC3C,IAAI,MAAM,MAAM,KAAK,CAAC;4BACtB,IAAI,OAAO,GAAG,CAAC,EAAE;4BACjB,IAAI,OAAO,GAAG,CAAC,EAAE;4BACjB,OAAO,SAAS,MAAM;4BACtB,OAAO,OAAO,SAAS,MAAM,MAAM;4BACnC,IAAI,GAAG,CAAC,YAAY,MAAM,KAAK,CAAC,KAAK;4BACrC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gCAC7B,IAAI,GAAG,CAAC,EAAE,UAAU,KAAK,CAAC,KAAK;4BACjC;wBACF;oBACF;gBACF;gBAEA,IAAK,IAAI,QAAQ,KAAM,KAAM;YAC7B,oEAAoE;YACtE;QACF;QAEA;;;GAGC,GACD,SAAS,gBAAiB,IAAI;YAC5B;YACA,OAAO,IAAI,GAAG,CAAC,KAAK,WAAW,CAAC,OAAO,MAAM,CAAC;QAChD;QAEA,SAAS,oBAAoB,IAAI;YAC/B,OAAO,cAAc,CAAC,gBAAgB,MAAM;QAC9C;QAEA,0CAA0C;QAC1C,IAAI,SAAS;YACX,SAAS;YACT,aAAa;QACf;QAEA;;;;;;;GAOC,GACD,SAAS,kBAAmB,aAAa,EAAE,cAAc;YACvD,IAAI,QAAQ;YACZ,IAAI,WAAW;YACf,IAAI,MAAM,IAAI;YACd,IAAI,aAAa,kBAAkB,IAAI;YACvC,IAAI;YACJ,cAAc,KAAK,CAAC,KAAK,OAAO,CAAC,SAAS,MAAM,KAAK;gBACnD,IAAI,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;oBAC7B,IAAK,IAAI,IAAI,CAAC,OAAO,KAAM;wBACzB,MAAM;oBACR;gBACF,OAAO;oBACL,WAAW;oBACX,IAAI,MAAM,MAAM,KAAK,CAAC;oBACtB,IAAI,IAAI,GAAG,CAAC,EAAE;oBACd,IAAI,IAAI,GAAG,CAAC,EAAE;oBACd,IAAI,OAAO,aAAa,CAAC,YAAY,SAAS,GAAG;oBACjD,IAAI,OAAO,aAAa,CAAC,YAAY,SAAS,GAAG;oBACjD,IAAI,GAAG,CAAC,GAAG;oBACX,kBAAkB,WAAW,GAAG,CAAC,GAAG;gBACtC;YACF;YACA,OAAO;gBAAE,KAAK;gBAAK,YAAY;YAAW;QAC5C;QAEA,IAAI,aAAa,aAAa;QAE9B,SAAS;YACP,IAAI,CAAC,aAAa;gBAChB,iCAAiC;gBACjC,IAAI,MAAM,kBAAkB,OAAO,KAAK,EAAE;gBAC1C,IAAI,MAAM,IAAI,GAAG;gBACjB,IAAI,aAAa,IAAI,UAAU;gBAC/B,cAAc;gBACd,cAAc;gBACd,YAAY,kBAAkB,OAAO,SAAS,EAAE,OAAO,GAAG;YAC1D,kEAAkE;YACpE;QACF;QAEA,SAAS,wBAAyB,IAAI;YACpC;YACA,OAAO,YAAY,GAAG,CAAC,SAAS;QAClC;QAEA,SAAS,wBAAyB,IAAI;YACpC;YACA,OAAO,YAAY,GAAG,CAAC,SAAS;QAClC;QAEA,SAAS,oBAAqB,IAAI;YAChC;YACA,OAAO,UAAU,GAAG,CAAC,SAAS;QAChC;QAEA,qBAAqB;QACrB,IAAI,SAAS,MAAM,CAAC;QACpB,IAAI,SAAS,MAAM,CAAC;QACpB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC;QACpB,IAAI,SAAS,MAAM,CAAC;QACpB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,UAAU,MAAM,EAAE;QACtB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QACxB,IAAI,WAAW,MAAM,GAAG;QAExB;;;;GAIC,GAED;;;;;;;;;GASC,GACD,SAAS,mBAAoB,MAAM,EAAE,aAAa;YAChD,IAAI,YAAY;YAEhB,8EAA8E;YAC9E,IAAI,YAAY,IAAI,YAAY,OAAO,MAAM;YAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,SAAS,CAAC,EAAE,GAAG,gBAAgB,MAAM,CAAC,EAAE;YAC1C;YAEA,IAAI,iBAAiB,IAAI,OAAO,4CAA4C;YAC5E,SAAS,eAAe,CAAC,EAAE,IAAI;gBAC7B,IAAI,UAAU,SAAS,CAAC,EAAE;gBAC1B,SAAS,CAAC,EAAE,GAAG;gBACf,eAAe,GAAG,CAAC,SAAS,eAAe,GAAG,CAAC,WAAW;gBAC1D,IAAI,UAAU,uBAAuB;oBACnC,eAAe,GAAG,CAAC,uBAAuB,eAAe,GAAG,CAAC,yBAAyB;gBACxF;gBACA,eAAe,GAAG,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,SAAS,CAAC,IAAI;gBAC3D,IAAI,OAAO,uBAAuB;oBAChC,eAAe,GAAG,CAAC,uBAAuB,CAAC,eAAe,GAAG,CAAC,0BAA0B,CAAC,IAAI;gBAC/F;YACF;YAEA,IAAI,cAAc,IAAI,WAAW,OAAO,MAAM;YAC9C,IAAI,iBAAiB,IAAI,OAAO,yBAAyB;YAEzD,oCAAoC;YACpC,2CAA2C;YAC3C,IAAI,aAAa,EAAE,EAAE,6BAA6B;YAClD,IAAI,YAAY;YAChB,IAAK,IAAI,MAAM,GAAG,MAAM,OAAO,MAAM,EAAE,MAAO;gBAC5C,IAAI,CAAC,WAAW;oBACd,WAAW,IAAI,CAAC,YAAY;wBAC1B,OAAO;wBACP,KAAK,OAAO,MAAM,GAAG;wBACrB,6CAA6C;wBAC7C,OAAO,kBAAkB,QAAQ,IAAI,kBAAkB,QAAQ,IAAI,wBAAwB,KAAK;oBAClG;gBACF;gBACA,IAAI,SAAS,CAAC,IAAI,GAAG,QAAQ;oBAC3B,UAAU,GAAG,GAAG;oBAChB,YAAY;gBACd;YACF;YAEA,IAAI,mBAAmB,WAAW,WAAW,WAAW,WAAW,qBAAqB,WAAW,WAAW;YAC9G,IAAI,WAAW,SAAU,CAAC;gBAAI,OAAO,IAAI,CAAC,AAAC,IAAI,IAAK,IAAI,CAAC;YAAG;YAC5D,IAAI,UAAU,SAAU,CAAC;gBAAI,OAAO,IAAI,CAAC,AAAC,IAAI,IAAK,IAAI,CAAC;YAAG;YAE3D,sDAAsD;YACtD,IAAK,IAAI,UAAU,GAAG,UAAU,WAAW,MAAM,EAAE,UAAW;gBAC5D,YAAY,UAAU,CAAC,QAAQ;gBAC/B,IAAI,cAAc;oBAAC;wBACjB,QAAQ,UAAU,KAAK;wBACvB,WAAW;wBACX,UAAU,EAAE,MAAM;oBACpB;iBAAE;gBACF,IAAI,WAAY,KAAK;gBACrB,IAAI,uBAAuB;gBAC3B,IAAI,yBAAyB;gBAC7B,IAAI,oBAAoB;gBACxB,eAAe,KAAK;gBAEpB,+CAA+C;gBAC/C,IAAK,IAAI,MAAM,UAAU,KAAK,EAAE,OAAO,UAAU,GAAG,EAAE,MAAO;oBAC3D,IAAI,WAAW,SAAS,CAAC,IAAI;oBAC7B,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;oBAE9C,qBAAqB;oBACrB,eAAe,GAAG,CAAC,UAAU,CAAC,eAAe,GAAG,CAAC,aAAa,CAAC,IAAI;oBACnE,IAAI,WAAW,uBAAuB;wBACpC,eAAe,GAAG,CAAC,uBAAuB,CAAC,eAAe,GAAG,CAAC,0BAA0B,CAAC,IAAI;oBAC/F;oBAEA,qCAAqC;oBACrC,IAAI,WAAW,kBAAkB;wBAC/B,IAAI,WAAW,CAAC,WAAW,QAAQ,GAAG;4BACpC,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE,MAAM;4BAC1C,IAAI,QAAQ,CAAC,aAAa,WAAW,UAAU,QAAQ,EAAE,SAAS,MAAM;4BACxE,IAAI,SAAS,aAAa,CAAC,wBAAwB,CAAC,wBAAwB;gCAC1E,YAAY,IAAI,CAAC;oCACf,QAAQ;oCACR,WAAW;oCACX,UAAU;gCACZ;4BACF,OAAO,IAAI,CAAC,sBAAsB;gCAChC;4BACF;wBACF,OAGK,IAAI,WAAW,CAAC,WAAW,QAAQ,GAAG;4BACzC,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE,MAAM;4BAC1C,IAAI,UAAU,CAAC,aAAa,WAAW,UAAU,QAAQ,EAAE,SAAS,MAAM;4BAC1E,IAAI,WAAW,aAAa,CAAC,wBAAwB,CAAC,wBAAwB;gCAC5E,YAAY,IAAI,CAAC;oCACf,QAAQ;oCACR,WAAW,AAAC,WAAW,WAAY,SAAS;oCAC5C,UAAU;gCACZ;4BACF,OAAO,IAAI,CAAC,sBAAsB;gCAChC;4BACF;wBACF,OAGK,IAAI,WAAW,oBAAoB;4BACtC,sCAAsC;4BACtC,IAAI,WAAW,UAAU;gCACvB,WAAW,wBAAwB,MAAM,GAAG,UAAU,IAAI,WAAW;4BACvE;4BAEA,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM;4BAClC,IAAI,SAAS,SAAS,EAAE;gCACtB,eAAe,KAAK,SAAS,SAAS;4BACxC;4BACA,IAAI,UAAU,CAAC,aAAa,WAAW,UAAU,QAAQ,EAAE,SAAS,MAAM;4BAC1E,IAAI,WAAW,aAAa,yBAAyB,KAAK,2BAA2B,GAAG;gCACtF;gCACA,YAAY,IAAI,CAAC;oCACf,QAAQ;oCACR,WAAW;oCACX,UAAU;oCACV,gBAAgB;gCAClB;4BACF,OAAO;gCACL;4BACF;wBACF,OAGK,IAAI,WAAW,UAAU;4BAC5B,IAAI,uBAAuB,GAAG;gCAC5B;4BACF,OAAO,IAAI,oBAAoB,GAAG;gCAChC,yBAAyB;gCACzB,MAAO,CAAC,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAE;oCACpD,YAAY,GAAG;gCACjB;gCACA,gDAAgD;gCAChD,IAAI,gBAAgB,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,cAAc;gCACtE,IAAI,iBAAiB,MAAM;oCACzB,eAAe,GAAG,CAAC,eAAe;oCAClC,eAAe,GAAG,CAAC,KAAK;gCAC1B;gCACA,YAAY,GAAG;gCACf;4BACF;4BACA,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;4BAC9C,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM;4BAClC,IAAI,SAAS,SAAS,EAAE;gCACtB,eAAe,KAAK,SAAS,SAAS;4BACxC;wBACF,OAIK,IAAI,WAAW,UAAU;4BAC5B,IAAI,yBAAyB,GAAG;gCAC9B,IAAI,yBAAyB,GAAG;oCAC9B;gCACF,OAAO,IAAI,CAAC,SAAS,QAAQ,IAAI,YAAY,MAAM,GAAG,GAAG;oCACvD,YAAY,GAAG;oCACf,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;gCAChD;4BACF;4BACA,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE,MAAM;wBAC5C,OAGK,IAAI,WAAW,QAAQ;4BAC1B,WAAW,CAAC,IAAI,GAAG,UAAU,KAAK;wBACpC;oBACF,OAGK;wBACH,WAAW,CAAC,IAAI,GAAG,SAAS,MAAM;wBAClC,yGAAyG;wBACzG,IAAI,SAAS,SAAS,IAAI,aAAa,SAAS;4BAC9C,eAAe,KAAK,SAAS,SAAS;wBACxC;oBACF;gBACF;gBAEA,qDAAqD;gBAErD,kEAAkE;gBAClE,4FAA4F;gBAC5F,2CAA2C;gBAE3C,YAAY;gBACZ,kEAAkE;gBAClE,IAAI,YAAY,EAAE;gBAClB,IAAI,aAAa;gBACjB,IAAK,IAAI,MAAM,UAAU,KAAK,EAAE,OAAO,UAAU,GAAG,EAAE,MAAO;oBAC3D,IAAI,aAAa,SAAS,CAAC,IAAI;oBAC/B,IAAI,CAAC,CAAC,aAAa,aAAa,GAAG;wBACjC,IAAI,MAAM,WAAW,CAAC,IAAI;wBAC1B,IAAI,aAAa,aAAa;wBAC9B,IAAI,QAAQ,eAAe;wBAC3B,IAAI,cAAc,QAAQ,WAAW,MAAM,EAAE;4BAC3C,WAAW,IAAI,GAAG;4BAClB,WAAW,iBAAiB,GAAG;wBACjC,OAAO;4BACL,UAAU,IAAI,CAAC,aAAa;gCAC1B,QAAQ;gCACR,MAAM;gCACN,QAAQ;gCACR,gBAAgB;gCAChB,mBAAmB;4BACrB;wBACF;oBACF;gBACF;gBACA,IAAI,mBAAmB,EAAE,EAAE,iDAAiD;gBAC5E,IAAK,IAAI,SAAS,GAAG,SAAS,UAAU,MAAM,EAAE,SAAU;oBACxD,IAAI,MAAM,SAAS,CAAC,OAAO;oBAC3B,IAAI,CAAC,IAAI,cAAc,IAAK,IAAI,cAAc,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,MAAM,GAAI;wBAClF,IAAI,UAAU;4BAAC,aAAa;yBAAI;wBAChC,IAAK,IAAI,WAAY,KAAK,GAAI,cAAc,WAAW,iBAAiB,IAAI,CAAC,WAAW,eAAe,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,MAAO;4BACrI,IAAK,IAAI,MAAM,SAAS,GAAG,MAAM,UAAU,MAAM,EAAE,MAAO;gCACxD,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU;oCACtC,QAAQ,IAAI,CAAC,aAAa,SAAS,CAAC,IAAI;oCACxC;gCACF;4BACF;wBACF;wBACA,8CAA8C;wBAC9C,IAAI,aAAa,EAAE;wBACnB,IAAK,IAAI,MAAM,GAAG,MAAM,QAAQ,MAAM,EAAE,MAAO;4BAC7C,IAAI,QAAQ,OAAO,CAAC,IAAI;4BACxB,IAAK,IAAI,IAAI,MAAM,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,IAAK;gCAC/C,WAAW,IAAI,CAAC;4BAClB;wBACF;wBACA,+BAA+B;wBAC/B,IAAI,aAAa,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;wBAC3C,IAAI,YAAY,UAAU,KAAK;wBAC/B,IAAK,IAAI,MAAM,UAAU,CAAC,EAAE,GAAG,GAAG,OAAO,GAAG,MAAO;4BACjD,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,GAAG,aAAa,GAAG;gCACrC,YAAY,WAAW,CAAC,IAAI;gCAC5B;4BACF;wBACF;wBACA,IAAI,YAAY,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;wBACjD,IAAI,YAAY,WAAW,CAAC,UAAU;wBACtC,IAAI,YAAY,UAAU,KAAK;wBAC/B,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,GAAG,kBAAkB,GAAG;4BAChD,IAAK,IAAI,MAAM,YAAY,GAAG,OAAO,UAAU,GAAG,EAAE,MAAO;gCACzD,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,GAAG,aAAa,GAAG;oCACrC,YAAY,WAAW,CAAC,IAAI;oCAC5B;gCACF;4BACF;wBACF;wBACA,iBAAiB,IAAI,CAAC;4BACpB,aAAa;4BACb,UAAU,KAAK,GAAG,CAAC,WAAW,cAAc,IAAI,SAAS;4BACzD,UAAU,KAAK,GAAG,CAAC,WAAW,aAAa,IAAI,SAAS;wBAC1D;oBACF;gBACF;gBAEA,qDAAqD;gBACrD,IAAK,IAAI,SAAS,GAAG,SAAS,iBAAiB,MAAM,EAAE,SAAU;oBAC/D,IAAI,MAAM,gBAAgB,CAAC,OAAO;oBAClC,IAAI,eAAe,IAAI,WAAW;oBAClC,IAAI,UAAU,IAAI,QAAQ;oBAC1B,IAAI,UAAU,IAAI,QAAQ;oBAC1B;;;;SAIC,GACD,IAAI,iBAAiB,AAAC,AAAC,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC,GAAI,IAAK,SAAS;oBAErE,qCAAqC;oBAErC,qGAAqG;oBACrG,uGAAuG;oBACvG,+FAA+F;oBAC/F,IAAI,eAAe,GAAG,CAAC,WAAW;wBAChC,IAAK,IAAI,KAAK,GAAG,KAAK,aAAa,MAAM,EAAE,KAAM;4BAC/C,IAAI,MAAM,YAAY,CAAC,GAAG;4BAC1B,IAAI,SAAS,CAAC,IAAI,GAAG,UAAU;gCAC7B,IAAI,WAAW;gCACf,IAAK,IAAI,KAAK,KAAK,GAAG,MAAM,GAAG,KAAM;oCACnC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,aAAa,GAAG;wCAClD,WAAW,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC;wCACtC;oCACF;gCACF;gCACA,eAAe,KAAK,AAAC,WAAW,CAAC,qBAAqB,QAAQ,IAAK,UAAU;4BAC/E;wBACF;oBACF;oBAEA,6GAA6G;oBAC7G,wFAAwF;oBACxF,IAAI,eAAe,GAAG,CAAC,UAAU;wBAC/B,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;4BACrD,IAAI,MAAM,YAAY,CAAC,KAAK;4BAC5B,IAAI,SAAS,CAAC,IAAI,GAAG,SAAS;gCAC5B,IAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,CAAC,GAAG,OAAQ;oCAC5C,IAAI,eAAe,SAAS,CAAC,IAAI,UAAU,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC;oCACxE,IAAI,eAAe,cAAc;wCAC/B,IAAI,iBAAiB,SAAS;4CAC5B,eAAe,KAAK;wCACtB;wCACA;oCACF;gCACF;4BACF;wBACF;oBACF;oBAEA,0BAA0B;oBAC1B,IAAI,eAAe,GAAG,CAAC,UAAU;wBAC/B,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;4BACrD,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS;gCAC7B,eAAe,MAAM;4BACvB;wBACF;oBACF;oBAEA,6GAA6G;oBAC7G,uEAAuE;oBACvE,IAAI,eAAe,GAAG,CAAC,YAAY,eAAe,GAAG,CAAC,UAAU;wBAC9D,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,GAAG,GAAG,OAAQ;4BACzD,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,UAAU,OAAO,GAAG;gCACzC,IAAI,aAAa,GAAG,WAAW;gCAC/B,IAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,GAAG,OAAQ;oCAC3C,aAAa,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC;oCAC1C,IAAI,CAAC,CAAC,aAAa,aAAa,GAAG;wCACjC;oCACF;gCACF;gCACA,IAAK,IAAI,OAAO,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;oCAC5D,WAAW,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC;oCACxC,IAAI,CAAC,CAAC,WAAW,aAAa,GAAG;wCAC/B;oCACF;gCACF;gCACA,IAAI,eAAe,YAAY,CAAC,SAAS,CAAC,KAAK,KAAK,UAAU,eAAe,UAAW,aAAa,CAAC,UAAU,OAAO,CAAE,GAAG;oCAC1H,eAAe,MAAM;gCACvB;4BACF;wBACF;oBACF;oBAEA,uGAAuG;oBACvG,IAAI,eAAe,GAAG,CAAC,UAAU;wBAC/B,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;4BACrD,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS;gCAC7B,IAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,KAAM,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,aAAa,GAAI,OAAQ;oCAC1G,eAAe,YAAY,CAAC,KAAK,EAAE;gCACrC;gCACA,IAAK,QAAQ,OAAO,aAAa,MAAM,IAAK,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,gBAAgB,OAAO,GAAI,OAAQ;oCACxH,IAAI,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS;wCAC7C,eAAe,YAAY,CAAC,KAAK,EAAE;oCACrC;gCACF;4BACF;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,IAAI,eAAe,GAAG,CAAC,YAAY,eAAe,GAAG,CAAC,YAAY,eAAe,GAAG,CAAC,UAAU;wBAC7F,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;4BACrD,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,UAAU,UAAU,OAAO,GAAG;gCACnD,eAAe,MAAM;gCACrB,kCAAkC;gCAClC,IAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,KAAM,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,eAAgB,OAAQ;oCAC9F,eAAe,YAAY,CAAC,KAAK,EAAE;gCACrC;gCACA,IAAK,IAAI,OAAO,OAAO,GAAG,OAAO,aAAa,MAAM,IAAK,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,eAAgB,OAAQ;oCAC/G,eAAe,YAAY,CAAC,KAAK,EAAE;gCACrC;4BACF;wBACF;oBACF;oBAEA,yGAAyG;oBACzG,gFAAgF;oBAChF,0DAA0D;oBAC1D,IAAI,eAAe,GAAG,CAAC,UAAU;wBAC/B,IAAK,IAAI,OAAO,GAAG,iBAAiB,SAAS,OAAO,aAAa,MAAM,EAAE,OAAQ;4BAC/E,IAAI,OAAO,YAAY,CAAC,KAAK;4BAC7B,IAAI,OAAO,SAAS,CAAC,KAAK;4BAC1B,IAAI,OAAO,SAAS;gCAClB,IAAI,mBAAmB,QAAQ;oCAC7B,eAAe,MAAM;gCACvB;4BACF,OAAO,IAAI,OAAO,cAAc;gCAC9B,iBAAiB;4BACnB;wBACF;oBACF;oBAEA,+DAA+D;oBAE/D,IAAI,eAAe,GAAG,CAAC,wBAAwB;wBAC7C,uGAAuG;wBACvG,yGAAyG;wBACzG,oCAAoC;wBACpC,IAAI,sBAAuB,SAAS,UAAU;wBAC9C,IAAI,2BAA2B,sBAAsB;wBAErD,wFAAwF;wBACxF,IAAI,eAAe,EAAE;wBACrB;4BACE,IAAI,cAAc,EAAE;4BACpB,IAAK,IAAI,OAAO,GAAG,OAAO,aAAa,MAAM,EAAE,OAAQ;gCACrD,oFAAoF;gCACpF,kFAAkF;gCAClF,wEAAwE;gCACxE,IAAI,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,uBAAuB;oCACzD,IAAI,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;oCACrC,IAAI,kBAAmB,KAAK;oCAC5B,kBAAkB;oCAClB,IAAI,wBAAwB,UAAU,MAAM;wCAC1C,IAAI,YAAY,MAAM,GAAG,IAAI;4CAC3B,YAAY,IAAI,CAAC;gDAAE,MAAM;gDAAM,UAAU;4CAAK;wCAChD,OAAO;4CACL;wCACF;oCACF,OAEK,IAAI,CAAC,kBAAkB,wBAAwB,KAAK,MAAM,MAAM;wCACnE,IAAK,IAAI,WAAW,YAAY,MAAM,GAAG,GAAG,YAAY,GAAG,WAAY;4CACrE,IAAI,YAAY,WAAW,CAAC,SAAS,CAAC,IAAI;4CAC1C,IAAI,cAAc,mBAChB,cAAc,wBAAwB,oBAAoB,UAC1D,wBAAwB,oBAAoB,gBAAgB,MAC5D;gDACA,aAAa,IAAI,CAAC;oDAAC,WAAW,CAAC,SAAS,CAAC,QAAQ;oDAAE;iDAAK;gDACxD,YAAY,MAAM,GAAG,UAAU,4CAA4C;gDAC3E;4CACF;wCACF;oCACF;gCACF;4BACF;4BACA,aAAa,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;gCAAI,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;4BAAE;wBAC1D;wBACA,yEAAyE;wBACzE,IAAK,IAAI,UAAU,GAAG,UAAU,aAAa,MAAM,EAAE,UAAW;4BAC9D,IAAI,QAAQ,YAAY,CAAC,QAAQ;4BACjC,IAAI,aAAa,KAAK,CAAC,EAAE;4BACzB,IAAI,cAAc,KAAK,CAAC,EAAE;4BAC1B,yFAAyF;4BACzF,yGAAyG;4BACzG,yDAAyD;4BACzD,IAAI,kBAAkB;4BACtB,IAAI,gBAAgB;4BACpB,IAAK,IAAI,OAAO,aAAa,GAAG,OAAO,aAAa,OAAQ;gCAC1D,IAAI,OAAO,YAAY,CAAC,KAAK;gCAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,0BAA0B;oCAC9C,kBAAkB;oCAClB,IAAI,KAAK,AAAC,SAAS,CAAC,KAAK,GAAG,sBAAuB,SAAS;oCAC5D,IAAI,OAAO,gBAAgB;wCACzB,gBAAgB;wCAChB;oCACF;gCACF;4BACF;4BACA,uGAAuG;4BACvG,0GAA0G;4BAC1G,+DAA+D;4BAC/D,0GAA0G;4BAC1G,mEAAmE;4BACnE,yFAAyF;4BACzF,IAAI,mBAAmB,CAAC,eAAe;gCACrC,gBAAgB;gCAChB,IAAK,IAAI,OAAO,aAAa,GAAG,QAAQ,GAAG,OAAQ;oCACjD,IAAI,OAAO,YAAY,CAAC,KAAK;oCAC7B,IAAI,SAAS,CAAC,KAAK,GAAG,0BAA0B;wCAC9C,IAAI,OAAO,AAAC,SAAS,CAAC,KAAK,GAAG,sBAAuB,SAAS;wCAC9D,IAAI,SAAS,gBAAgB;4CAC3B,gBAAgB;wCAClB,OAAO;4CACL,gBAAgB;wCAClB;wCACA;oCACF;gCACF;4BACF;4BACA,IAAI,eAAe;gCACjB,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG;gCAC7E,yGAAyG;gCACzG,yGAAyG;gCACzG,uCAAuC;gCACvC,IAAI,kBAAkB,gBAAgB;oCACpC,IAAK,IAAI,QAAQ,aAAa,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;wCACrE,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,aAAa,GAAG;4CACrD,IAAI,gBAAgB,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,UAAU;gDAC3D,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG;4CACnC;4CACA;wCACF;oCACF;gCACF;gCACA,IAAI,kBAAkB,gBAAgB;oCACpC,IAAK,IAAI,QAAQ,cAAc,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;wCACtE,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,aAAa,GAAG;4CACrD,IAAI,gBAAgB,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,UAAU;gDAC3D,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG;4CACnC;4CACA;wCACF;oCACF;gCACF;4BACF;wBACF;wBAEA,6GAA6G;wBAC7G,kBAAkB;wBAClB,sDAAsD;wBACtD,IAAK,IAAI,QAAQ,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;4BACxD,IAAI,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,uBAAuB;gCAC1D,IAAI,aAAa,OAAO,WAAW;gCACnC,IAAI,aAAa,SAAS,8FAA8F;gCACxH,IAAK,IAAI,MAAM,QAAQ,GAAG,OAAO,GAAG,MAAO;oCACzC,IAAI,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,eAAe;wCAChD,aAAa,KAAK,sCAAsC;oCAC1D,OAAO;wCACL,aAAa,AAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,sBAAuB,SAAS;wCAC7E;oCACF;gCACF;gCACA,IAAI,aAAa;gCACjB,IAAK,IAAI,QAAQ,QAAQ,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;oCAChE,IAAI,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,aAAa,GAAG;wCAC5E,WAAW;oCACb,OAAO;wCACL,aAAa,AAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,sBAAuB,SAAS;wCAC/E;oCACF;gCACF;gCACA,IAAK,IAAI,OAAO,YAAY,QAAQ,UAAU,OAAQ;oCACpD,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,eAAe,aAAa,aAAa;gCAC3E;gCACA,QAAQ;4BACV;wBACF;oBACF;gBACF;gBAEA,0CAA0C;gBAE1C,IAAK,IAAI,OAAO,UAAU,KAAK,EAAE,QAAQ,UAAU,GAAG,EAAE,OAAQ;oBAC9D,IAAI,UAAU,WAAW,CAAC,KAAK;oBAC/B,IAAI,SAAS,SAAS,CAAC,KAAK;oBAC5B,iHAAiH;oBACjH,IAAI,UAAU,GAAG;wBACf,IAAI,SAAS,CAAC,SAAS,UAAU,OAAO,GAAG;4BACzC,WAAW,CAAC,KAAK;wBACnB;oBACF,OAGK;wBACH,IAAI,SAAS,QAAQ;4BACnB,WAAW,CAAC,KAAK;wBACnB,OAAO,IAAI,SAAS,CAAC,UAAU,OAAO,GAAG;4BACvC,WAAW,CAAC,KAAK,IAAI;wBACvB;oBACF;oBAEA,2GAA2G;oBAC3G,mCAAmC;oBACnC,IAAI,SAAS,eAAe;wBAC1B,WAAW,CAAC,KAAK,GAAG,SAAS,IAAI,UAAU,KAAK,GAAG,WAAW,CAAC,OAAO,EAAE;oBAC1E;oBAEA,2GAA2G;oBAC3G,oGAAoG;oBACpG,qGAAqG;oBACrG,IAAI,SAAS,UAAU,GAAG,IAAI,gBAAgB,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG;wBAC/E,IAAK,IAAI,MAAM,MAAM,OAAO,KAAM,gBAAgB,MAAM,CAAC,IAAI,IAAI,gBAAiB,MAAO;4BACvF,WAAW,CAAC,IAAI,GAAG,UAAU,KAAK;wBACpC;oBACF;gBACF;YACF;YAEA,8FAA8F;YAC9F,sDAAsD;YACtD,OAAO;gBACL,QAAQ;gBACR,YAAY;YACd;;YAEA,SAAS,wBAAyB,KAAK,EAAE,KAAK;gBAC5C,gBAAgB;gBAChB,IAAK,IAAI,IAAI,OAAO,IAAI,OAAO,MAAM,EAAE,IAAK;oBAC1C,IAAI,WAAW,SAAS,CAAC,EAAE;oBAC3B,IAAI,WAAW,CAAC,SAAS,OAAO,GAAG;wBACjC,OAAO;oBACT;oBACA,IAAI,AAAC,WAAW,CAAC,SAAS,MAAM,KAAO,SAAS,aAAa,UAAW;wBACtE,OAAO;oBACT;oBACA,IAAI,WAAW,oBAAoB;wBACjC,IAAI,MAAM,mBAAmB;wBAC7B,IAAI,QAAQ,CAAC,IAAI,OAAO,MAAM,GAAG;oBACnC;gBACF;gBACA,OAAO;YACT;YAEA,SAAS,mBAAoB,YAAY;gBACvC,YAAY;gBACZ,IAAI,iBAAiB;gBACrB,IAAK,IAAI,IAAI,eAAe,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBACrD,IAAI,WAAW,SAAS,CAAC,EAAE;oBAC3B,IAAI,WAAW,QAAQ;wBACrB;oBACF;oBACA,IAAI,WAAW,UAAU;wBACvB,IAAI,EAAE,mBAAmB,GAAG;4BAC1B,OAAO;wBACT;oBACF,OAAO,IAAI,WAAW,oBAAoB;wBACxC;oBACF;gBACF;gBACA,OAAO,CAAC;YACV;QACF;QAEA,2CAA2C;QAC3C,IAAI,OAAO;QAEX,IAAI;QAEJ,SAAS;YACP,IAAI,CAAC,WAAW;gBACd,iCAAiC;gBACjC,IAAI,MAAM,kBAAkB,MAAM;gBAClC,IAAI,MAAM,IAAI,GAAG;gBACjB,IAAI,aAAa,IAAI,UAAU;gBAC/B,6BAA6B;gBAC7B,WAAW,OAAO,CAAC,SAAU,KAAK,EAAE,GAAG;oBACrC,IAAI,GAAG,CAAC,KAAK;gBACf;gBACA,YAAY;YACZ,wEAAwE;YAC1E;QACF;QAEA,SAAS,qBAAsB,IAAI;YACjC;YACA,OAAO,UAAU,GAAG,CAAC,SAAS;QAChC;QAEA;;;;;;;;GAQC,GACD,SAAS,yBAAyB,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG;YACnE,IAAI,SAAS,OAAO,MAAM;YAC1B,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO,IAAI,CAAC;YACzC,MAAM,KAAK,GAAG,CAAC,SAAS,GAAG,OAAO,OAAO,SAAS,IAAI,CAAC;YAEvD,IAAI,MAAM,IAAI;YACd,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,IAAI,eAAe,CAAC,EAAE,GAAG,GAAG;oBAC1B,IAAI,SAAS,qBAAqB,MAAM,CAAC,EAAE;oBAC3C,IAAI,WAAW,MAAM;wBACnB,IAAI,GAAG,CAAC,GAAG;oBACb;gBACF;YACF;YACA,OAAO;QACT;QAEA;;;;;;;;GAQC,GACD,SAAS,mBAAmB,MAAM,EAAE,qBAAqB,EAAE,KAAK,EAAE,GAAG;YACnE,IAAI,SAAS,OAAO,MAAM;YAC1B,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO,IAAI,CAAC;YACzC,MAAM,KAAK,GAAG,CAAC,SAAS,GAAG,OAAO,OAAO,SAAS,IAAI,CAAC;YAEvD,IAAI,WAAW,EAAE;YACjB,sBAAsB,UAAU,CAAC,OAAO,CAAC,SAAU,SAAS;gBAC1D,IAAI,YAAY,KAAK,GAAG,CAAC,OAAO,UAAU,KAAK;gBAC/C,IAAI,UAAU,KAAK,GAAG,CAAC,KAAK,UAAU,GAAG;gBACzC,IAAI,YAAY,SAAS;oBACvB,2BAA2B;oBAC3B,IAAI,aAAa,sBAAsB,MAAM,CAAC,KAAK,CAAC,WAAW,UAAU;oBAEzE,oGAAoG;oBACpG,0CAA0C;oBAC1C,IAAK,IAAI,IAAI,SAAS,KAAK,aAAc,gBAAgB,MAAM,CAAC,EAAE,IAAI,gBAAiB,IAAK;wBAC1F,UAAU,CAAC,EAAE,GAAG,UAAU,KAAK;oBACjC;oBAEA,mHAAmH;oBACnH,oHAAoH;oBACpH,IAAI,WAAW,UAAU,KAAK;oBAC9B,IAAI,cAAc;oBAClB,IAAK,IAAI,MAAM,GAAG,MAAM,WAAW,MAAM,EAAE,MAAO;wBAChD,IAAI,QAAQ,UAAU,CAAC,IAAI;wBAC3B,IAAI,QAAQ,UAAU;4BAAE,WAAW;wBAAO;wBAC1C,IAAI,QAAQ,aAAa;4BAAE,cAAc,QAAQ;wBAAG;oBACtD;oBACA,IAAK,IAAI,MAAM,UAAU,OAAO,aAAa,MAAO;wBAClD,IAAK,IAAI,MAAM,GAAG,MAAM,WAAW,MAAM,EAAE,MAAO;4BAChD,IAAI,UAAU,CAAC,IAAI,IAAI,KAAK;gCAC1B,IAAI,WAAW;gCACf,MAAO,MAAM,IAAI,WAAW,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,IAAK;oCAChE;gCACF;gCACA,IAAI,MAAM,UAAU;oCAClB,SAAS,IAAI,CAAC;wCAAC,WAAW;wCAAW,MAAM;qCAAU;gCACvD;4BACF;wBACF;oBACF;gBACF;YACF;YACA,OAAO;QACT;QAEA;;;;;;GAMC,GACD,SAAS,mBAAmB,MAAM,EAAE,iBAAiB,EAAE,KAAK,EAAE,GAAG;YAC/D,IAAI,UAAU,oBAAoB,QAAQ,mBAAmB,OAAO;YACpE,IAAI,QAAQ,EAAE,CAAC,MAAM,CAAE;YACvB,QAAQ,OAAO,CAAC,SAAU,SAAS,EAAE,CAAC;gBACpC,KAAK,CAAC,EAAE,GAAG,CACT,AAAC,kBAAkB,MAAM,CAAC,UAAU,GAAG,IAAK,qBAAqB,MAAM,CAAC,UAAU,IAAI,IACxF,KAAK,MAAM,CAAC,UAAU;YACxB;YACA,OAAO,MAAM,IAAI,CAAC;QACpB;QAEA;;;;;;GAMC,GACD,SAAS,oBAAoB,MAAM,EAAE,iBAAiB,EAAE,KAAK,EAAE,GAAG;YAChE,IAAI,WAAW,mBAAmB,QAAQ,mBAAmB,OAAO;YACpE,6BAA6B;YAC7B,IAAI,UAAU,EAAE;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,OAAO,CAAC,EAAE,GAAG;YACf;YACA,gCAAgC;YAChC,SAAS,OAAO,CAAC,SAAU,GAAG;gBAC5B,IAAI,QAAQ,GAAG,CAAC,EAAE;gBAClB,IAAI,MAAM,GAAG,CAAC,EAAE;gBAEhB,IAAI,QAAQ,QAAQ,KAAK,CAAC,OAAO,MAAM;gBACvC,IAAK,IAAI,IAAI,MAAM,MAAM,EAAE,KAAM;oBAC/B,OAAO,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;gBAC7B;YACF;YACA,OAAO;QACT;QAEA,QAAQ,uBAAuB,GAAG;QAClC,QAAQ,eAAe,GAAG;QAC1B,QAAQ,mBAAmB,GAAG;QAC9B,QAAQ,mBAAmB,GAAG;QAC9B,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,oBAAoB,GAAG;QAC/B,QAAQ,wBAAwB,GAAG;QACnC,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,mBAAmB,GAAG;QAC9B,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,uBAAuB,GAAG;QAElC,OAAO,cAAc,CAAC,SAAS,cAAc;YAAE,OAAO;QAAK;QAE3D,OAAO;IAET,EAAE,CAAC;IACH,OAAO;AAAI;uCAEI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/troika-three-utils/dist/troika-three-utils.esm.js"], "sourcesContent": ["import { ShaderChunk, UniformsUtils, MeshDepthMaterial, RGBADepthPacking, MeshDistanceMaterial, ShaderLib, Matrix4, Vector3, Mesh, CylinderGeometry, Vector2, MeshStandardMaterial, DoubleSide } from 'three';\n\n/**\n * Regular expression for matching the `void main() {` opener line in GLSL.\n * @type {RegExp}\n */\nconst voidMainRegExp = /\\bvoid\\s+main\\s*\\(\\s*\\)\\s*{/g;\n\n/**\n * Recursively expands all `#include <xyz>` statements within string of shader code.\n * Copied from three's WebGLProgram#parseIncludes for external use.\n *\n * @param {string} source - The GLSL source code to evaluate\n * @return {string} The GLSL code with all includes expanded\n */\nfunction expandShaderIncludes( source ) {\n  const pattern = /^[ \\t]*#include +<([\\w\\d./]+)>/gm;\n  function replace(match, include) {\n    let chunk = ShaderChunk[include];\n    return chunk ? expandShaderIncludes(chunk) : match\n  }\n  return source.replace( pattern, replace )\n}\n\n/*\n * This is a direct copy of MathUtils.generateUUID from Three.js, to preserve compatibility with three\n * versions before 0.113.0 as it was changed from Math to MathUtils in that version.\n * https://github.com/mrdoob/three.js/blob/dd8b5aa3b270c17096b90945cd2d6d1b13aaec53/src/math/MathUtils.js#L16\n */\n\nconst _lut = [];\n\nfor (let i = 0; i < 256; i++) {\n  _lut[i] = (i < 16 ? '0' : '') + (i).toString(16);\n}\n\nfunction generateUUID() {\n\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/21963136#21963136\n\n  const d0 = Math.random() * 0xffffffff | 0;\n  const d1 = Math.random() * 0xffffffff | 0;\n  const d2 = Math.random() * 0xffffffff | 0;\n  const d3 = Math.random() * 0xffffffff | 0;\n  const uuid = _lut[d0 & 0xff] + _lut[d0 >> 8 & 0xff] + _lut[d0 >> 16 & 0xff] + _lut[d0 >> 24 & 0xff] + '-' +\n    _lut[d1 & 0xff] + _lut[d1 >> 8 & 0xff] + '-' + _lut[d1 >> 16 & 0x0f | 0x40] + _lut[d1 >> 24 & 0xff] + '-' +\n    _lut[d2 & 0x3f | 0x80] + _lut[d2 >> 8 & 0xff] + '-' + _lut[d2 >> 16 & 0xff] + _lut[d2 >> 24 & 0xff] +\n    _lut[d3 & 0xff] + _lut[d3 >> 8 & 0xff] + _lut[d3 >> 16 & 0xff] + _lut[d3 >> 24 & 0xff];\n\n  // .toUpperCase() here flattens concatenated strings to save heap memory space.\n  return uuid.toUpperCase()\n\n}\n\n// Local assign polyfill to avoid importing troika-core\nconst assign = Object.assign || function(/*target, ...sources*/) {\n  let target = arguments[0];\n  for (let i = 1, len = arguments.length; i < len; i++) {\n    let source = arguments[i];\n    if (source) {\n      for (let prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          target[prop] = source[prop];\n        }\n      }\n    }\n  }\n  return target\n};\n\n\nconst epoch = Date.now();\nconst CONSTRUCTOR_CACHE = new WeakMap();\nconst SHADER_UPGRADE_CACHE = new Map();\n\n// Material ids must be integers, but we can't access the increment from Three's `Material` module,\n// so let's choose a sufficiently large starting value that should theoretically never collide.\nlet materialInstanceId = 1e10;\n\n/**\n * A utility for creating a custom shader material derived from another material's\n * shaders. This allows you to inject custom shader logic and transforms into the\n * builtin ThreeJS materials without having to recreate them from scratch.\n *\n * @param {THREE.Material} baseMaterial - the original material to derive from\n *\n * @param {Object} options - How the base material should be modified.\n * @param {Object=} options.defines - Custom `defines` for the material\n * @param {Object=} options.extensions - Custom `extensions` for the material, e.g. `{derivatives: true}`\n * @param {Object=} options.uniforms - Custom `uniforms` for use in the modified shader. These can\n *        be accessed and manipulated via the resulting material's `uniforms` property, just like\n *        in a ShaderMaterial. You do not need to repeat the base material's own uniforms here.\n * @param {String=} options.timeUniform - If specified, a uniform of this name will be injected into\n *        both shaders, and it will automatically be updated on each render frame with a number of\n *        elapsed milliseconds. The \"zero\" epoch time is not significant so don't rely on this as a\n *        true calendar time.\n * @param {String=} options.vertexDefs - Custom GLSL code to inject into the vertex shader's top-level\n *        definitions, above the `void main()` function.\n * @param {String=} options.vertexMainIntro - Custom GLSL code to inject at the top of the vertex\n *        shader's `void main` function.\n * @param {String=} options.vertexMainOutro - Custom GLSL code to inject at the end of the vertex\n *        shader's `void main` function.\n * @param {String=} options.vertexTransform - Custom GLSL code to manipulate the `position`, `normal`,\n *        and/or `uv` vertex attributes. This code will be wrapped within a standalone function with\n *        those attributes exposed by their normal names as read/write values.\n * @param {String=} options.fragmentDefs - Custom GLSL code to inject into the fragment shader's top-level\n *        definitions, above the `void main()` function.\n * @param {String=} options.fragmentMainIntro - Custom GLSL code to inject at the top of the fragment\n *        shader's `void main` function.\n * @param {String=} options.fragmentMainOutro - Custom GLSL code to inject at the end of the fragment\n *        shader's `void main` function. You can manipulate `gl_FragColor` here but keep in mind it goes\n *        after any of ThreeJS's color postprocessing shader chunks (tonemapping, fog, etc.), so if you\n *        want those to apply to your changes use `fragmentColorTransform` instead.\n * @param {String=} options.fragmentColorTransform - Custom GLSL code to manipulate the `gl_FragColor`\n *        output value. Will be injected near the end of the `void main` function, but before any\n *        of ThreeJS's color postprocessing shader chunks (tonemapping, fog, etc.), and before the\n *        `fragmentMainOutro`.\n * @param {function({fragmentShader: string, vertexShader:string}):\n *        {fragmentShader: string, vertexShader:string}} options.customRewriter - A function\n *        for performing custom rewrites of the full shader code. Useful if you need to do something\n *        special that's not covered by the other builtin options. This function will be executed before\n *        any other transforms are applied.\n * @param {boolean=} options.chained - Set to `true` to prototype-chain the derived material to the base\n *        material, rather than the default behavior of copying it. This allows the derived material to\n *        automatically pick up changes made to the base material and its properties. This can be useful\n *        where the derived material is hidden from the user as an implementation detail, allowing them\n *        to work with the original material like normal. But it can result in unexpected behavior if not\n *        handled carefully.\n *\n * @return {THREE.Material}\n *\n * The returned material will also have two new methods, `getDepthMaterial()` and `getDistanceMaterial()`,\n * which can be called to get a variant of the derived material for use in shadow casting. If the\n * target mesh is expected to cast shadows, then you can assign these to the mesh's `customDepthMaterial`\n * (for directional and spot lights) and/or `customDistanceMaterial` (for point lights) properties to\n * allow the cast shadow to honor your derived shader's vertex transforms and discarded fragments. These\n * will also set a custom `#define IS_DEPTH_MATERIAL` or `#define IS_DISTANCE_MATERIAL` that you can look\n * for in your derived shaders with `#ifdef` to customize their behavior for the depth or distance\n * scenarios, e.g. skipping antialiasing or expensive shader logic.\n */\nfunction createDerivedMaterial(baseMaterial, options) {\n  // Generate a key that is unique to the content of these `options`. We'll use this\n  // throughout for caching and for generating the upgraded shader code. This increases\n  // the likelihood that the resulting shaders will line up across multiple calls so\n  // their GL programs can be shared and cached.\n  const optionsKey = getKeyForOptions(options);\n\n  // First check to see if we've already derived from this baseMaterial using this\n  // unique set of options, and if so reuse the constructor to avoid some allocations.\n  let ctorsByDerivation = CONSTRUCTOR_CACHE.get(baseMaterial);\n  if (!ctorsByDerivation) {\n    CONSTRUCTOR_CACHE.set(baseMaterial, (ctorsByDerivation = Object.create(null)));\n  }\n  if (ctorsByDerivation[optionsKey]) {\n    return new ctorsByDerivation[optionsKey]()\n  }\n\n  const privateBeforeCompileProp = `_onBeforeCompile${optionsKey}`;\n\n  // Private onBeforeCompile handler that injects the modified shaders and uniforms when\n  // the renderer switches to this material's program\n  const onBeforeCompile = function (shaderInfo, renderer) {\n    baseMaterial.onBeforeCompile.call(this, shaderInfo, renderer);\n\n    // Upgrade the shaders, caching the result by incoming source code\n    const cacheKey = this.customProgramCacheKey() + '|' + shaderInfo.vertexShader + '|' + shaderInfo.fragmentShader;\n    let upgradedShaders = SHADER_UPGRADE_CACHE[cacheKey];\n    if (!upgradedShaders) {\n      const upgraded = upgradeShaders(this, shaderInfo, options, optionsKey);\n      upgradedShaders = SHADER_UPGRADE_CACHE[cacheKey] = upgraded;\n    }\n\n    // Inject upgraded shaders and uniforms into the program\n    shaderInfo.vertexShader = upgradedShaders.vertexShader;\n    shaderInfo.fragmentShader = upgradedShaders.fragmentShader;\n    assign(shaderInfo.uniforms, this.uniforms);\n\n    // Inject auto-updating time uniform if requested\n    if (options.timeUniform) {\n      shaderInfo.uniforms[options.timeUniform] = {\n        get value() {return Date.now() - epoch}\n      };\n    }\n\n    // Users can still add their own handlers on top of ours\n    if (this[privateBeforeCompileProp]) {\n      this[privateBeforeCompileProp](shaderInfo);\n    }\n  };\n\n  const DerivedMaterial = function DerivedMaterial() {\n    return derive(options.chained ? baseMaterial : baseMaterial.clone())\n  };\n\n  const derive = function(base) {\n    // Prototype chain to the base material\n    const derived = Object.create(base, descriptor);\n\n    // Store the baseMaterial for reference; this is always the original even when cloning\n    Object.defineProperty(derived, 'baseMaterial', { value: baseMaterial });\n\n    // Needs its own ids\n    Object.defineProperty(derived, 'id', { value: materialInstanceId++ });\n    derived.uuid = generateUUID();\n\n    // Merge uniforms, defines, and extensions\n    derived.uniforms = assign({}, base.uniforms, options.uniforms);\n    derived.defines = assign({}, base.defines, options.defines);\n    derived.defines[`TROIKA_DERIVED_MATERIAL_${optionsKey}`] = ''; //force a program change from the base material\n    derived.extensions = assign({}, base.extensions, options.extensions);\n\n    // Don't inherit EventDispatcher listeners\n    derived._listeners = undefined;\n\n    return derived\n  };\n\n  const descriptor = {\n    constructor: {value: DerivedMaterial},\n    isDerivedMaterial: {value: true},\n\n    type: {\n      get: () => baseMaterial.type,\n      set: (value) => {baseMaterial.type = value;}\n    },\n\n    isDerivedFrom: {\n      writable: true,\n      configurable: true,\n      value: function (testMaterial) {\n        const base = this.baseMaterial;\n        return testMaterial === base || (base.isDerivedMaterial && base.isDerivedFrom(testMaterial)) || false\n      }\n    },\n\n    customProgramCacheKey: {\n      writable: true,\n      configurable: true,\n      value: function () {\n        return baseMaterial.customProgramCacheKey() + '|' + optionsKey\n      }\n    },\n\n    onBeforeCompile: {\n      get() {\n        return onBeforeCompile\n      },\n      set(fn) {\n        this[privateBeforeCompileProp] = fn;\n      }\n    },\n\n    copy: {\n      writable: true,\n      configurable: true,\n      value: function (source) {\n        baseMaterial.copy.call(this, source);\n        if (!baseMaterial.isShaderMaterial && !baseMaterial.isDerivedMaterial) {\n          assign(this.extensions, source.extensions);\n          assign(this.defines, source.defines);\n          assign(this.uniforms, UniformsUtils.clone(source.uniforms));\n        }\n        return this\n      }\n    },\n\n    clone: {\n      writable: true,\n      configurable: true,\n      value: function () {\n        const newBase = new baseMaterial.constructor();\n        return derive(newBase).copy(this)\n      }\n    },\n\n    /**\n     * Utility to get a MeshDepthMaterial that will honor this derived material's vertex\n     * transformations and discarded fragments.\n     */\n    getDepthMaterial: {\n      writable: true,\n      configurable: true,\n      value: function() {\n        let depthMaterial = this._depthMaterial;\n        if (!depthMaterial) {\n          depthMaterial = this._depthMaterial = createDerivedMaterial(\n            baseMaterial.isDerivedMaterial\n              ? baseMaterial.getDepthMaterial()\n              : new MeshDepthMaterial({ depthPacking: RGBADepthPacking }),\n            options\n          );\n          depthMaterial.defines.IS_DEPTH_MATERIAL = '';\n          depthMaterial.uniforms = this.uniforms; //automatically recieve same uniform values\n        }\n        return depthMaterial\n      }\n    },\n\n    /**\n     * Utility to get a MeshDistanceMaterial that will honor this derived material's vertex\n     * transformations and discarded fragments.\n     */\n    getDistanceMaterial: {\n      writable: true,\n      configurable: true,\n      value: function() {\n        let distanceMaterial = this._distanceMaterial;\n        if (!distanceMaterial) {\n          distanceMaterial = this._distanceMaterial = createDerivedMaterial(\n            baseMaterial.isDerivedMaterial\n              ? baseMaterial.getDistanceMaterial()\n              : new MeshDistanceMaterial(),\n            options\n          );\n          distanceMaterial.defines.IS_DISTANCE_MATERIAL = '';\n          distanceMaterial.uniforms = this.uniforms; //automatically recieve same uniform values\n        }\n        return distanceMaterial\n      }\n    },\n\n    dispose: {\n      writable: true,\n      configurable: true,\n      value() {\n        const {_depthMaterial, _distanceMaterial} = this;\n        if (_depthMaterial) _depthMaterial.dispose();\n        if (_distanceMaterial) _distanceMaterial.dispose();\n        baseMaterial.dispose.call(this);\n      }\n    }\n  };\n\n  ctorsByDerivation[optionsKey] = DerivedMaterial;\n  return new DerivedMaterial()\n}\n\n\nfunction upgradeShaders(material, {vertexShader, fragmentShader}, options, key) {\n  let {\n    vertexDefs,\n    vertexMainIntro,\n    vertexMainOutro,\n    vertexTransform,\n    fragmentDefs,\n    fragmentMainIntro,\n    fragmentMainOutro,\n    fragmentColorTransform,\n    customRewriter,\n    timeUniform\n  } = options;\n\n  vertexDefs = vertexDefs || '';\n  vertexMainIntro = vertexMainIntro || '';\n  vertexMainOutro = vertexMainOutro || '';\n  fragmentDefs = fragmentDefs || '';\n  fragmentMainIntro = fragmentMainIntro || '';\n  fragmentMainOutro = fragmentMainOutro || '';\n\n  // Expand includes if needed\n  if (vertexTransform || customRewriter) {\n    vertexShader = expandShaderIncludes(vertexShader);\n  }\n  if (fragmentColorTransform || customRewriter) {\n    // We need to be able to find postprocessing chunks after include expansion in order to\n    // put them after the fragmentColorTransform, so mark them with comments first. Even if\n    // this particular derivation doesn't have a fragmentColorTransform, other derivations may,\n    // so we still mark them.\n    fragmentShader = fragmentShader.replace(\n      /^[ \\t]*#include <((?:tonemapping|encodings|colorspace|fog|premultiplied_alpha|dithering)_fragment)>/gm,\n      '\\n//!BEGIN_POST_CHUNK $1\\n$&\\n//!END_POST_CHUNK\\n'\n    );\n    fragmentShader = expandShaderIncludes(fragmentShader);\n  }\n\n  // Apply custom rewriter function\n  if (customRewriter) {\n    let res = customRewriter({vertexShader, fragmentShader});\n    vertexShader = res.vertexShader;\n    fragmentShader = res.fragmentShader;\n  }\n\n  // The fragmentColorTransform needs to go before any postprocessing chunks, so extract\n  // those and re-insert them into the outro in the correct place:\n  if (fragmentColorTransform) {\n    let postChunks = [];\n    fragmentShader = fragmentShader.replace(\n      /^\\/\\/!BEGIN_POST_CHUNK[^]+?^\\/\\/!END_POST_CHUNK/gm, // [^]+? = non-greedy match of any chars including newlines\n      match => {\n        postChunks.push(match);\n        return ''\n      }\n    );\n    fragmentMainOutro = `${fragmentColorTransform}\\n${postChunks.join('\\n')}\\n${fragmentMainOutro}`;\n  }\n\n  // Inject auto-updating time uniform if requested\n  if (timeUniform) {\n    const code = `\\nuniform float ${timeUniform};\\n`;\n    vertexDefs = code + vertexDefs;\n    fragmentDefs = code + fragmentDefs;\n  }\n\n  // Inject a function for the vertexTransform and rename all usages of position/normal/uv\n  if (vertexTransform) {\n    // Hoist these defs to the very top so they work in other function defs\n    vertexShader = `vec3 troika_position_${key};\nvec3 troika_normal_${key};\nvec2 troika_uv_${key};\n${vertexShader}\n`;\n    vertexDefs = `${vertexDefs}\nvoid troikaVertexTransform${key}(inout vec3 position, inout vec3 normal, inout vec2 uv) {\n  ${vertexTransform}\n}\n`;\n    vertexMainIntro = `\ntroika_position_${key} = vec3(position);\ntroika_normal_${key} = vec3(normal);\ntroika_uv_${key} = vec2(uv);\ntroikaVertexTransform${key}(troika_position_${key}, troika_normal_${key}, troika_uv_${key});\n${vertexMainIntro}\n`;\n    vertexShader = vertexShader.replace(/\\b(position|normal|uv)\\b/g, (match, match1, index, fullStr) => {\n      return /\\battribute\\s+vec[23]\\s+$/.test(fullStr.substr(0, index)) ? match1 : `troika_${match1}_${key}`\n    });\n\n    // Three r152 introduced the MAP_UV token, replace it too if it's pointing to the main 'uv'\n    // Perhaps the other textures too going forward?\n    if (!(material.map && material.map.channel > 0)) {\n      vertexShader = vertexShader.replace(/\\bMAP_UV\\b/g, `troika_uv_${key}`);\n    }\n  }\n\n  // Inject defs and intro/outro snippets\n  vertexShader = injectIntoShaderCode(vertexShader, key, vertexDefs, vertexMainIntro, vertexMainOutro);\n  fragmentShader = injectIntoShaderCode(fragmentShader, key, fragmentDefs, fragmentMainIntro, fragmentMainOutro);\n\n  return {\n    vertexShader,\n    fragmentShader\n  }\n}\n\nfunction injectIntoShaderCode(shaderCode, id, defs, intro, outro) {\n  if (intro || outro || defs) {\n    shaderCode = shaderCode.replace(voidMainRegExp, `\n${defs}\nvoid troikaOrigMain${id}() {`\n    );\n    shaderCode += `\nvoid main() {\n  ${intro}\n  troikaOrigMain${id}();\n  ${outro}\n}`;\n  }\n  return shaderCode\n}\n\n\nfunction optionsJsonReplacer(key, value) {\n  return key === 'uniforms' ? undefined : typeof value === 'function' ? value.toString() : value\n}\n\nlet _idCtr = 0;\nconst optionsHashesToIds = new Map();\nfunction getKeyForOptions(options) {\n  const optionsHash = JSON.stringify(options, optionsJsonReplacer);\n  let id = optionsHashesToIds.get(optionsHash);\n  if (id == null) {\n    optionsHashesToIds.set(optionsHash, (id = ++_idCtr));\n  }\n  return id\n}\n\n// Copied from threejs WebGLPrograms.js so we can resolve builtin materials to their shaders\n// TODO how can we keep this from getting stale?\nconst MATERIAL_TYPES_TO_SHADERS = {\n  MeshDepthMaterial: 'depth',\n  MeshDistanceMaterial: 'distanceRGBA',\n  MeshNormalMaterial: 'normal',\n  MeshBasicMaterial: 'basic',\n  MeshLambertMaterial: 'lambert',\n  MeshPhongMaterial: 'phong',\n  MeshToonMaterial: 'toon',\n  MeshStandardMaterial: 'physical',\n  MeshPhysicalMaterial: 'physical',\n  MeshMatcapMaterial: 'matcap',\n  LineBasicMaterial: 'basic',\n  LineDashedMaterial: 'dashed',\n  PointsMaterial: 'points',\n  ShadowMaterial: 'shadow',\n  SpriteMaterial: 'sprite'\n};\n\n/**\n * Given a Three.js `Material` instance, find the shaders/uniforms that will be\n * used to render that material.\n *\n * @param material - the Material instance\n * @return {object} - the material's shader info: `{uniforms:{}, fragmentShader:'', vertexShader:''}`\n */\nfunction getShadersForMaterial(material) {\n  let builtinType = MATERIAL_TYPES_TO_SHADERS[material.type];\n  return builtinType ? ShaderLib[builtinType] : material //TODO fallback for unknown type?\n}\n\n/**\n * Find all uniforms and their types within a shader code string.\n *\n * @param {string} shader - The shader code to parse\n * @return {object} mapping of uniform names to their glsl type\n */\nfunction getShaderUniformTypes(shader) {\n  let uniformRE = /\\buniform\\s+(int|float|vec[234]|mat[34])\\s+([A-Za-z_][\\w]*)/g;\n  let uniforms = Object.create(null);\n  let match;\n  while ((match = uniformRE.exec(shader)) !== null) {\n    uniforms[match[2]] = match[1];\n  }\n  return uniforms\n}\n\n/**\n * Helper for smoothing out the `m.getInverse(x)` --> `m.copy(x).invert()` conversion\n * that happened in ThreeJS r123.\n * @param {Matrix4} srcMatrix\n * @param {Matrix4} [tgtMatrix]\n */\nfunction invertMatrix4(srcMatrix, tgtMatrix = new Matrix4()) {\n  if (typeof tgtMatrix.invert === 'function') {\n    tgtMatrix.copy(srcMatrix).invert();\n  } else {\n    tgtMatrix.getInverse(srcMatrix);\n  }\n  return tgtMatrix\n}\n\n/*\nInput geometry is a cylinder with r=1, height in y dimension from 0 to 1,\ndivided into a reasonable number of height segments.\n*/\n\nconst vertexDefs = `\nuniform vec3 pointA;\nuniform vec3 controlA;\nuniform vec3 controlB;\nuniform vec3 pointB;\nuniform float radius;\nvarying float bezierT;\n\nvec3 cubicBezier(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {\n  float t2 = 1.0 - t;\n  float b0 = t2 * t2 * t2;\n  float b1 = 3.0 * t * t2 * t2;\n  float b2 = 3.0 * t * t * t2;\n  float b3 = t * t * t;\n  return b0 * p1 + b1 * c1 + b2 * c2 + b3 * p2;\n}\n\nvec3 cubicBezierDerivative(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {\n  float t2 = 1.0 - t;\n  return -3.0 * p1 * t2 * t2 +\n    c1 * (3.0 * t2 * t2 - 6.0 * t2 * t) +\n    c2 * (6.0 * t2 * t - 3.0 * t * t) +\n    3.0 * p2 * t * t;\n}\n`;\n\nconst vertexTransform = `\nfloat t = position.y;\nbezierT = t;\nvec3 bezierCenterPos = cubicBezier(pointA, controlA, controlB, pointB, t);\nvec3 bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t));\n\n// Make \"sideways\" always perpendicular to the camera ray; this ensures that any twists\n// in the cylinder occur where you won't see them: \nvec3 viewDirection = normalMatrix * vec3(0.0, 0.0, 1.0);\nif (bezierDir == viewDirection) {\n  bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t == 1.0 ? t - 0.0001 : t + 0.0001));\n}\nvec3 sideways = normalize(cross(bezierDir, viewDirection));\nvec3 upish = normalize(cross(sideways, bezierDir));\n\n// Build a matrix for transforming this disc in the cylinder:\nmat4 discTx;\ndiscTx[0].xyz = sideways * radius;\ndiscTx[1].xyz = bezierDir * radius;\ndiscTx[2].xyz = upish * radius;\ndiscTx[3].xyz = bezierCenterPos;\ndiscTx[3][3] = 1.0;\n\n// Apply transform, ignoring original y\nposition = (discTx * vec4(position.x, 0.0, position.z, 1.0)).xyz;\nnormal = normalize(mat3(discTx) * normal);\n`;\n\nconst fragmentDefs = `\nuniform vec3 dashing;\nvarying float bezierT;\n`;\n\nconst fragmentMainIntro = `\nif (dashing.x + dashing.y > 0.0) {\n  float dashFrac = mod(bezierT - dashing.z, dashing.x + dashing.y);\n  if (dashFrac > dashing.x) {\n    discard;\n  }\n}\n`;\n\n// Debugging: separate color for each of the 6 sides:\n// const fragmentColorTransform = `\n// float sideNum = floor(vUV.x * 6.0);\n// vec3 mixColor = sideNum < 1.0 ? vec3(1.0, 0.0, 0.0) :\n//   sideNum < 2.0 ? vec3(0.0, 1.0, 1.0) :\n//   sideNum < 3.0 ? vec3(1.0, 1.0, 0.0) :\n//   sideNum < 4.0 ? vec3(0.0, 0.0, 1.0) :\n//   sideNum < 5.0 ? vec3(0.0, 1.0, 0.0) :\n//   vec3(1.0, 0.0, 1.0);\n// gl_FragColor.xyz = mix(gl_FragColor.xyz, mixColor, 0.5);\n// `\n\n\n\nfunction createBezierMeshMaterial(baseMaterial) {\n  return createDerivedMaterial(\n    baseMaterial,\n    {\n      chained: true,\n      uniforms: {\n        pointA: {value: new Vector3()},\n        controlA: {value: new Vector3()},\n        controlB: {value: new Vector3()},\n        pointB: {value: new Vector3()},\n        radius: {value: 0.01},\n        dashing: {value: new Vector3()} //on, off, offset\n      },\n      vertexDefs,\n      vertexTransform,\n      fragmentDefs,\n      fragmentMainIntro\n    }\n  )\n}\n\nlet geometry = null;\n\nconst defaultBaseMaterial = /*#__PURE__*/new MeshStandardMaterial({color: 0xffffff, side: DoubleSide});\n\n\n/**\n * A ThreeJS `Mesh` that bends a tube shape along a 3D cubic bezier path. The bending is done\n * by deforming a straight cylindrical geometry in the vertex shader based on a set of four\n * control point uniforms. It patches the necessary GLSL into the mesh's assigned `material`\n * automatically.\n *\n * The cubiz bezier path is determined by its four `Vector3` properties:\n * - `pointA`\n * - `controlA`\n * - `controlB`\n * - `pointB`\n *\n * The tube's radius is controlled by its `radius` property, which defaults to `0.01`.\n *\n * You can also give the tube a dashed appearance with two properties:\n *\n * - `dashArray` - an array of two numbers, defining the length of \"on\" and \"off\" parts of\n *   the dash. Each is a 0-1 ratio of the entire path's length. (Actually this is the `t` length\n *   used as input to the cubic bezier function, not its visible length.)\n * - `dashOffset` - offset of where the dash starts. You can animate this to make the dashes move.\n *\n * Note that the dashes will appear like a hollow tube, not solid. This will be more apparent on\n * thicker tubes.\n *\n * TODO: proper geometry bounding sphere and raycasting\n * TODO: allow control of the geometry's segment counts\n */\nclass BezierMesh extends Mesh {\n  static getGeometry() {\n    return geometry || (geometry =\n      new CylinderGeometry(1, 1, 1, 6, 64).translate(0, 0.5, 0)\n    )\n  }\n\n  constructor() {\n    super(\n      BezierMesh.getGeometry(),\n      defaultBaseMaterial\n    );\n\n    this.pointA = new Vector3();\n    this.controlA = new Vector3();\n    this.controlB = new Vector3();\n    this.pointB = new Vector3();\n    this.radius = 0.01;\n    this.dashArray = new Vector2();\n    this.dashOffset = 0;\n\n    // TODO - disabling frustum culling until I figure out how to customize the\n    //  geometry's bounding sphere that gets used\n    this.frustumCulled = false;\n  }\n\n  // Handler for automatically wrapping the base material with our upgrades. We do the wrapping\n  // lazily on _read_ rather than write to avoid unnecessary wrapping on transient values.\n  get material() {\n    let derivedMaterial = this._derivedMaterial;\n    const baseMaterial = this._baseMaterial || this._defaultMaterial || (this._defaultMaterial = defaultBaseMaterial.clone());\n    if (!derivedMaterial || derivedMaterial.baseMaterial !== baseMaterial) {\n      derivedMaterial = this._derivedMaterial = createBezierMeshMaterial(baseMaterial);\n      // dispose the derived material when its base material is disposed:\n      baseMaterial.addEventListener('dispose', function onDispose() {\n        baseMaterial.removeEventListener('dispose', onDispose);\n        derivedMaterial.dispose();\n      });\n    }\n    return derivedMaterial\n  }\n  set material(baseMaterial) {\n    this._baseMaterial = baseMaterial;\n  }\n\n  // Create and update material for shadows upon request:\n  get customDepthMaterial() {\n    return this.material.getDepthMaterial()\n  }\n  set customDepthMaterial(m) {\n    // future: let the user override with their own?\n  }\n  get customDistanceMaterial() {\n    return this.material.getDistanceMaterial()\n  }\n  set customDistanceMaterial(m) {\n    // future: let the user override with their own?\n  }\n\n  onBeforeRender() {\n    const {uniforms} = this.material;\n    const {pointA, controlA, controlB, pointB, radius, dashArray, dashOffset} = this;\n    uniforms.pointA.value.copy(pointA);\n    uniforms.controlA.value.copy(controlA);\n    uniforms.controlB.value.copy(controlB);\n    uniforms.pointB.value.copy(pointB);\n    uniforms.radius.value = radius;\n    uniforms.dashing.value.set(dashArray.x, dashArray.y, dashOffset || 0);\n  }\n\n  raycast(/*raycaster, intersects*/) {\n    // TODO - just fail for now\n  }\n}\n\nexport { BezierMesh, createDerivedMaterial, expandShaderIncludes, getShaderUniformTypes, getShadersForMaterial, invertMatrix4, voidMainRegExp };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAEA;;;CAGC,GACD,MAAM,iBAAiB;AAEvB;;;;;;CAMC,GACD,SAAS,qBAAsB,MAAM;IACnC,MAAM,UAAU;IAChB,SAAS,QAAQ,KAAK,EAAE,OAAO;QAC7B,IAAI,QAAQ,iKAAA,CAAA,cAAW,CAAC,QAAQ;QAChC,OAAO,QAAQ,qBAAqB,SAAS;IAC/C;IACA,OAAO,OAAO,OAAO,CAAE,SAAS;AAClC;AAEA;;;;CAIC,GAED,MAAM,OAAO,EAAE;AAEf,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;IAC5B,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,IAAI,AAAC,EAAG,QAAQ,CAAC;AAC/C;AAEA,SAAS;IAEP,sGAAsG;IAEtG,MAAM,KAAK,KAAK,MAAM,KAAK,aAAa;IACxC,MAAM,KAAK,KAAK,MAAM,KAAK,aAAa;IACxC,MAAM,KAAK,KAAK,MAAM,KAAK,aAAa;IACxC,MAAM,KAAK,KAAK,MAAM,KAAK,aAAa;IACxC,MAAM,OAAO,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,MACpG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,MACtG,IAAI,CAAC,KAAK,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GACnG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK;IAExF,+EAA+E;IAC/E,OAAO,KAAK,WAAW;AAEzB;AAEA,uDAAuD;AACvD,MAAM,SAAS,OAAO,MAAM,IAAI;IAC9B,IAAI,SAAS,SAAS,CAAC,EAAE;IACzB,IAAK,IAAI,IAAI,GAAG,MAAM,UAAU,MAAM,EAAE,IAAI,KAAK,IAAK;QACpD,IAAI,SAAS,SAAS,CAAC,EAAE;QACzB,IAAI,QAAQ;YACV,IAAK,IAAI,QAAQ,OAAQ;gBACvB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,OAAO;oBACtD,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;gBAC7B;YACF;QACF;IACF;IACA,OAAO;AACT;AAGA,MAAM,QAAQ,KAAK,GAAG;AACtB,MAAM,oBAAoB,IAAI;AAC9B,MAAM,uBAAuB,IAAI;AAEjC,mGAAmG;AACnG,+FAA+F;AAC/F,IAAI,qBAAqB;AAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4DC,GACD,SAAS,sBAAsB,YAAY,EAAE,OAAO;IAClD,kFAAkF;IAClF,qFAAqF;IACrF,kFAAkF;IAClF,8CAA8C;IAC9C,MAAM,aAAa,iBAAiB;IAEpC,gFAAgF;IAChF,oFAAoF;IACpF,IAAI,oBAAoB,kBAAkB,GAAG,CAAC;IAC9C,IAAI,CAAC,mBAAmB;QACtB,kBAAkB,GAAG,CAAC,cAAe,oBAAoB,OAAO,MAAM,CAAC;IACzE;IACA,IAAI,iBAAiB,CAAC,WAAW,EAAE;QACjC,OAAO,IAAI,iBAAiB,CAAC,WAAW;IAC1C;IAEA,MAAM,2BAA2B,CAAC,gBAAgB,EAAE,YAAY;IAEhE,sFAAsF;IACtF,mDAAmD;IACnD,MAAM,kBAAkB,SAAU,UAAU,EAAE,QAAQ;QACpD,aAAa,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY;QAEpD,kEAAkE;QAClE,MAAM,WAAW,IAAI,CAAC,qBAAqB,KAAK,MAAM,WAAW,YAAY,GAAG,MAAM,WAAW,cAAc;QAC/G,IAAI,kBAAkB,oBAAoB,CAAC,SAAS;QACpD,IAAI,CAAC,iBAAiB;YACpB,MAAM,WAAW,eAAe,IAAI,EAAE,YAAY,SAAS;YAC3D,kBAAkB,oBAAoB,CAAC,SAAS,GAAG;QACrD;QAEA,wDAAwD;QACxD,WAAW,YAAY,GAAG,gBAAgB,YAAY;QACtD,WAAW,cAAc,GAAG,gBAAgB,cAAc;QAC1D,OAAO,WAAW,QAAQ,EAAE,IAAI,CAAC,QAAQ;QAEzC,iDAAiD;QACjD,IAAI,QAAQ,WAAW,EAAE;YACvB,WAAW,QAAQ,CAAC,QAAQ,WAAW,CAAC,GAAG;gBACzC,IAAI,SAAQ;oBAAC,OAAO,KAAK,GAAG,KAAK;gBAAK;YACxC;QACF;QAEA,wDAAwD;QACxD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,IAAI,CAAC,yBAAyB,CAAC;QACjC;IACF;IAEA,MAAM,kBAAkB,SAAS;QAC/B,OAAO,OAAO,QAAQ,OAAO,GAAG,eAAe,aAAa,KAAK;IACnE;IAEA,MAAM,SAAS,SAAS,IAAI;QAC1B,uCAAuC;QACvC,MAAM,UAAU,OAAO,MAAM,CAAC,MAAM;QAEpC,sFAAsF;QACtF,OAAO,cAAc,CAAC,SAAS,gBAAgB;YAAE,OAAO;QAAa;QAErE,oBAAoB;QACpB,OAAO,cAAc,CAAC,SAAS,MAAM;YAAE,OAAO;QAAqB;QACnE,QAAQ,IAAI,GAAG;QAEf,0CAA0C;QAC1C,QAAQ,QAAQ,GAAG,OAAO,CAAC,GAAG,KAAK,QAAQ,EAAE,QAAQ,QAAQ;QAC7D,QAAQ,OAAO,GAAG,OAAO,CAAC,GAAG,KAAK,OAAO,EAAE,QAAQ,OAAO;QAC1D,QAAQ,OAAO,CAAC,CAAC,wBAAwB,EAAE,YAAY,CAAC,GAAG,IAAI,+CAA+C;QAC9G,QAAQ,UAAU,GAAG,OAAO,CAAC,GAAG,KAAK,UAAU,EAAE,QAAQ,UAAU;QAEnE,0CAA0C;QAC1C,QAAQ,UAAU,GAAG;QAErB,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,aAAa;YAAC,OAAO;QAAe;QACpC,mBAAmB;YAAC,OAAO;QAAI;QAE/B,MAAM;YACJ,KAAK,IAAM,aAAa,IAAI;YAC5B,KAAK,CAAC;gBAAW,aAAa,IAAI,GAAG;YAAM;QAC7C;QAEA,eAAe;YACb,UAAU;YACV,cAAc;YACd,OAAO,SAAU,YAAY;gBAC3B,MAAM,OAAO,IAAI,CAAC,YAAY;gBAC9B,OAAO,iBAAiB,QAAS,KAAK,iBAAiB,IAAI,KAAK,aAAa,CAAC,iBAAkB;YAClG;QACF;QAEA,uBAAuB;YACrB,UAAU;YACV,cAAc;YACd,OAAO;gBACL,OAAO,aAAa,qBAAqB,KAAK,MAAM;YACtD;QACF;QAEA,iBAAiB;YACf;gBACE,OAAO;YACT;YACA,KAAI,EAAE;gBACJ,IAAI,CAAC,yBAAyB,GAAG;YACnC;QACF;QAEA,MAAM;YACJ,UAAU;YACV,cAAc;YACd,OAAO,SAAU,MAAM;gBACrB,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC7B,IAAI,CAAC,aAAa,gBAAgB,IAAI,CAAC,aAAa,iBAAiB,EAAE;oBACrE,OAAO,IAAI,CAAC,UAAU,EAAE,OAAO,UAAU;oBACzC,OAAO,IAAI,CAAC,OAAO,EAAE,OAAO,OAAO;oBACnC,OAAO,IAAI,CAAC,QAAQ,EAAE,+IAAA,CAAA,gBAAa,CAAC,KAAK,CAAC,OAAO,QAAQ;gBAC3D;gBACA,OAAO,IAAI;YACb;QACF;QAEA,OAAO;YACL,UAAU;YACV,cAAc;YACd,OAAO;gBACL,MAAM,UAAU,IAAI,aAAa,WAAW;gBAC5C,OAAO,OAAO,SAAS,IAAI,CAAC,IAAI;YAClC;QACF;QAEA;;;KAGC,GACD,kBAAkB;YAChB,UAAU;YACV,cAAc;YACd,OAAO;gBACL,IAAI,gBAAgB,IAAI,CAAC,cAAc;gBACvC,IAAI,CAAC,eAAe;oBAClB,gBAAgB,IAAI,CAAC,cAAc,GAAG,sBACpC,aAAa,iBAAiB,GAC1B,aAAa,gBAAgB,KAC7B,IAAI,+IAAA,CAAA,oBAAiB,CAAC;wBAAE,cAAc,+IAAA,CAAA,mBAAgB;oBAAC,IAC3D;oBAEF,cAAc,OAAO,CAAC,iBAAiB,GAAG;oBAC1C,cAAc,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,2CAA2C;gBACrF;gBACA,OAAO;YACT;QACF;QAEA;;;KAGC,GACD,qBAAqB;YACnB,UAAU;YACV,cAAc;YACd,OAAO;gBACL,IAAI,mBAAmB,IAAI,CAAC,iBAAiB;gBAC7C,IAAI,CAAC,kBAAkB;oBACrB,mBAAmB,IAAI,CAAC,iBAAiB,GAAG,sBAC1C,aAAa,iBAAiB,GAC1B,aAAa,mBAAmB,KAChC,IAAI,+IAAA,CAAA,uBAAoB,IAC5B;oBAEF,iBAAiB,OAAO,CAAC,oBAAoB,GAAG;oBAChD,iBAAiB,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,2CAA2C;gBACxF;gBACA,OAAO;YACT;QACF;QAEA,SAAS;YACP,UAAU;YACV,cAAc;YACd;gBACE,MAAM,EAAC,cAAc,EAAE,iBAAiB,EAAC,GAAG,IAAI;gBAChD,IAAI,gBAAgB,eAAe,OAAO;gBAC1C,IAAI,mBAAmB,kBAAkB,OAAO;gBAChD,aAAa,OAAO,CAAC,IAAI,CAAC,IAAI;YAChC;QACF;IACF;IAEA,iBAAiB,CAAC,WAAW,GAAG;IAChC,OAAO,IAAI;AACb;AAGA,SAAS,eAAe,QAAQ,EAAE,EAAC,YAAY,EAAE,cAAc,EAAC,EAAE,OAAO,EAAE,GAAG;IAC5E,IAAI,EACF,UAAU,EACV,eAAe,EACf,eAAe,EACf,eAAe,EACf,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,sBAAsB,EACtB,cAAc,EACd,WAAW,EACZ,GAAG;IAEJ,aAAa,cAAc;IAC3B,kBAAkB,mBAAmB;IACrC,kBAAkB,mBAAmB;IACrC,eAAe,gBAAgB;IAC/B,oBAAoB,qBAAqB;IACzC,oBAAoB,qBAAqB;IAEzC,4BAA4B;IAC5B,IAAI,mBAAmB,gBAAgB;QACrC,eAAe,qBAAqB;IACtC;IACA,IAAI,0BAA0B,gBAAgB;QAC5C,uFAAuF;QACvF,uFAAuF;QACvF,2FAA2F;QAC3F,yBAAyB;QACzB,iBAAiB,eAAe,OAAO,CACrC,yGACA;QAEF,iBAAiB,qBAAqB;IACxC;IAEA,iCAAiC;IACjC,IAAI,gBAAgB;QAClB,IAAI,MAAM,eAAe;YAAC;YAAc;QAAc;QACtD,eAAe,IAAI,YAAY;QAC/B,iBAAiB,IAAI,cAAc;IACrC;IAEA,sFAAsF;IACtF,gEAAgE;IAChE,IAAI,wBAAwB;QAC1B,IAAI,aAAa,EAAE;QACnB,iBAAiB,eAAe,OAAO,CACrC,qDACA,CAAA;YACE,WAAW,IAAI,CAAC;YAChB,OAAO;QACT;QAEF,oBAAoB,GAAG,uBAAuB,EAAE,EAAE,WAAW,IAAI,CAAC,MAAM,EAAE,EAAE,mBAAmB;IACjG;IAEA,iDAAiD;IACjD,IAAI,aAAa;QACf,MAAM,OAAO,CAAC,gBAAgB,EAAE,YAAY,GAAG,CAAC;QAChD,aAAa,OAAO;QACpB,eAAe,OAAO;IACxB;IAEA,wFAAwF;IACxF,IAAI,iBAAiB;QACnB,uEAAuE;QACvE,eAAe,CAAC,qBAAqB,EAAE,IAAI;mBAC5B,EAAE,IAAI;eACV,EAAE,IAAI;AACrB,EAAE,aAAa;AACf,CAAC;QACG,aAAa,GAAG,WAAW;0BACL,EAAE,IAAI;EAC9B,EAAE,gBAAgB;;AAEpB,CAAC;QACG,kBAAkB,CAAC;gBACP,EAAE,IAAI;cACR,EAAE,IAAI;UACV,EAAE,IAAI;qBACK,EAAE,IAAI,iBAAiB,EAAE,IAAI,gBAAgB,EAAE,IAAI,YAAY,EAAE,IAAI;AAC1F,EAAE,gBAAgB;AAClB,CAAC;QACG,eAAe,aAAa,OAAO,CAAC,6BAA6B,CAAC,OAAO,QAAQ,OAAO;YACtF,OAAO,4BAA4B,IAAI,CAAC,QAAQ,MAAM,CAAC,GAAG,UAAU,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,KAAK;QACxG;QAEA,2FAA2F;QAC3F,gDAAgD;QAChD,IAAI,CAAC,CAAC,SAAS,GAAG,IAAI,SAAS,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG;YAC/C,eAAe,aAAa,OAAO,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK;QACvE;IACF;IAEA,uCAAuC;IACvC,eAAe,qBAAqB,cAAc,KAAK,YAAY,iBAAiB;IACpF,iBAAiB,qBAAqB,gBAAgB,KAAK,cAAc,mBAAmB;IAE5F,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,qBAAqB,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;IAC9D,IAAI,SAAS,SAAS,MAAM;QAC1B,aAAa,WAAW,OAAO,CAAC,gBAAgB,CAAC;AACrD,EAAE,KAAK;mBACY,EAAE,GAAG,IAAI,CAAC;QAEzB,cAAc,CAAC;;EAEjB,EAAE,MAAM;gBACM,EAAE,GAAG;EACnB,EAAE,MAAM;CACT,CAAC;IACA;IACA,OAAO;AACT;AAGA,SAAS,oBAAoB,GAAG,EAAE,KAAK;IACrC,OAAO,QAAQ,aAAa,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ,KAAK;AAC3F;AAEA,IAAI,SAAS;AACb,MAAM,qBAAqB,IAAI;AAC/B,SAAS,iBAAiB,OAAO;IAC/B,MAAM,cAAc,KAAK,SAAS,CAAC,SAAS;IAC5C,IAAI,KAAK,mBAAmB,GAAG,CAAC;IAChC,IAAI,MAAM,MAAM;QACd,mBAAmB,GAAG,CAAC,aAAc,KAAK,EAAE;IAC9C;IACA,OAAO;AACT;AAEA,4FAA4F;AAC5F,gDAAgD;AAChD,MAAM,4BAA4B;IAChC,mBAAmB;IACnB,sBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;IAClB,sBAAsB;IACtB,sBAAsB;IACtB,oBAAoB;IACpB,mBAAmB;IACnB,oBAAoB;IACpB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;AAClB;AAEA;;;;;;CAMC,GACD,SAAS,sBAAsB,QAAQ;IACrC,IAAI,cAAc,yBAAyB,CAAC,SAAS,IAAI,CAAC;IAC1D,OAAO,cAAc,iKAAA,CAAA,YAAS,CAAC,YAAY,GAAG,SAAS,iCAAiC;;AAC1F;AAEA;;;;;CAKC,GACD,SAAS,sBAAsB,MAAM;IACnC,IAAI,YAAY;IAChB,IAAI,WAAW,OAAO,MAAM,CAAC;IAC7B,IAAI;IACJ,MAAO,CAAC,QAAQ,UAAU,IAAI,CAAC,OAAO,MAAM,KAAM;QAChD,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE;IAC/B;IACA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,cAAc,SAAS,EAAE,YAAY,IAAI,+IAAA,CAAA,UAAO,EAAE;IACzD,IAAI,OAAO,UAAU,MAAM,KAAK,YAAY;QAC1C,UAAU,IAAI,CAAC,WAAW,MAAM;IAClC,OAAO;QACL,UAAU,UAAU,CAAC;IACvB;IACA,OAAO;AACT;AAEA;;;AAGA,GAEA,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAwBpB,CAAC;AAED,MAAM,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BzB,CAAC;AAED,MAAM,eAAe,CAAC;;;AAGtB,CAAC;AAED,MAAM,oBAAoB,CAAC;;;;;;;AAO3B,CAAC;AAED,qDAAqD;AACrD,mCAAmC;AACnC,sCAAsC;AACtC,wDAAwD;AACxD,0CAA0C;AAC1C,0CAA0C;AAC1C,0CAA0C;AAC1C,0CAA0C;AAC1C,yBAAyB;AACzB,2DAA2D;AAC3D,IAAI;AAIJ,SAAS,yBAAyB,YAAY;IAC5C,OAAO,sBACL,cACA;QACE,SAAS;QACT,UAAU;YACR,QAAQ;gBAAC,OAAO,IAAI,+IAAA,CAAA,UAAO;YAAE;YAC7B,UAAU;gBAAC,OAAO,IAAI,+IAAA,CAAA,UAAO;YAAE;YAC/B,UAAU;gBAAC,OAAO,IAAI,+IAAA,CAAA,UAAO;YAAE;YAC/B,QAAQ;gBAAC,OAAO,IAAI,+IAAA,CAAA,UAAO;YAAE;YAC7B,QAAQ;gBAAC,OAAO;YAAI;YACpB,SAAS;gBAAC,OAAO,IAAI,+IAAA,CAAA,UAAO;YAAE,EAAE,iBAAiB;QACnD;QACA;QACA;QACA;QACA;IACF;AAEJ;AAEA,IAAI,WAAW;AAEf,MAAM,sBAAsB,WAAW,GAAE,IAAI,+IAAA,CAAA,uBAAoB,CAAC;IAAC,OAAO;IAAU,MAAM,+IAAA,CAAA,aAAU;AAAA;AAGpG;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,MAAM,mBAAmB,+IAAA,CAAA,OAAI;IAC3B,OAAO,cAAc;QACnB,OAAO,YAAY,CAAC,WAClB,IAAI,+IAAA,CAAA,mBAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,SAAS,CAAC,GAAG,KAAK,EACzD;IACF;IAEA,aAAc;QACZ,KAAK,CACH,WAAW,WAAW,IACtB;QAGF,IAAI,CAAC,MAAM,GAAG,IAAI,+IAAA,CAAA,UAAO;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,+IAAA,CAAA,UAAO;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,+IAAA,CAAA,UAAO;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,+IAAA,CAAA,UAAO;QACzB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,+IAAA,CAAA,UAAO;QAC5B,IAAI,CAAC,UAAU,GAAG;QAElB,2EAA2E;QAC3E,6CAA6C;QAC7C,IAAI,CAAC,aAAa,GAAG;IACvB;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI,kBAAkB,IAAI,CAAC,gBAAgB;QAC3C,MAAM,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,oBAAoB,KAAK,EAAE;QACxH,IAAI,CAAC,mBAAmB,gBAAgB,YAAY,KAAK,cAAc;YACrE,kBAAkB,IAAI,CAAC,gBAAgB,GAAG,yBAAyB;YACnE,mEAAmE;YACnE,aAAa,gBAAgB,CAAC,WAAW,SAAS;gBAChD,aAAa,mBAAmB,CAAC,WAAW;gBAC5C,gBAAgB,OAAO;YACzB;QACF;QACA,OAAO;IACT;IACA,IAAI,SAAS,YAAY,EAAE;QACzB,IAAI,CAAC,aAAa,GAAG;IACvB;IAEA,uDAAuD;IACvD,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB;IACvC;IACA,IAAI,oBAAoB,CAAC,EAAE;IACzB,gDAAgD;IAClD;IACA,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IAC1C;IACA,IAAI,uBAAuB,CAAC,EAAE;IAC5B,gDAAgD;IAClD;IAEA,iBAAiB;QACf,MAAM,EAAC,QAAQ,EAAC,GAAG,IAAI,CAAC,QAAQ;QAChC,MAAM,EAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAC,GAAG,IAAI;QAChF,SAAS,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B,SAAS,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAC7B,SAAS,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAC7B,SAAS,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B,SAAS,MAAM,CAAC,KAAK,GAAG;QACxB,SAAS,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,cAAc;IACrE;IAEA,UAAmC;IACjC,2BAA2B;IAC7B;AACF", "ignoreList": [0], "debugId": null}}]}
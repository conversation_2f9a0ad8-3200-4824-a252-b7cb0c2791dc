{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/SceneManager.tsx"], "sourcesContent": ["'use client';\n\nimport { Canvas } from '@react-three/fiber';\nimport { Suspense, useRef, useEffect } from 'react';\nimport { \n  OrbitControls, \n  Environment, \n  PerspectiveCamera, \n  Stats,\n  AdaptiveDpr,\n  AdaptiveEvents,\n  BakeShadows,\n  Preload\n} from '@react-three/drei';\nimport { BaseComponentProps } from '@/types';\nimport { cn } from '@/lib/utils';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface SceneManagerProps extends BaseComponentProps {\n  cameraPosition?: [number, number, number];\n  cameraTarget?: [number, number, number];\n  enableControls?: boolean;\n  enableEnvironment?: boolean;\n  enableStats?: boolean;\n  backgroundColor?: string;\n  shadows?: boolean;\n  antialias?: boolean;\n  performance?: {\n    min: number;\n    max: number;\n    debounce: number;\n  };\n  lighting?: 'default' | 'studio' | 'dramatic' | 'soft';\n  postProcessing?: boolean;\n}\n\n// Lighting presets\nconst lightingPresets = {\n  default: {\n    ambient: { intensity: 0.4, color: '#ffffff' },\n    directional: { \n      intensity: 1, \n      position: [10, 10, 5], \n      color: '#ffffff',\n      castShadow: true \n    },\n    point: { intensity: 0.5, position: [-10, -10, -10], color: '#0088ff' },\n  },\n  studio: {\n    ambient: { intensity: 0.6, color: '#ffffff' },\n    directional: { \n      intensity: 0.8, \n      position: [5, 10, 5], \n      color: '#ffffff',\n      castShadow: true \n    },\n    point: { intensity: 0.3, position: [-5, 5, -5], color: '#00ff88' },\n  },\n  dramatic: {\n    ambient: { intensity: 0.2, color: '#1a1a2e' },\n    directional: { \n      intensity: 1.5, \n      position: [15, 15, 10], \n      color: '#ffffff',\n      castShadow: true \n    },\n    point: { intensity: 0.8, position: [-15, -5, -15], color: '#ff6b00' },\n  },\n  soft: {\n    ambient: { intensity: 0.8, color: '#f0f0f0' },\n    directional: { \n      intensity: 0.6, \n      position: [8, 12, 8], \n      color: '#ffffff',\n      castShadow: false \n    },\n    point: { intensity: 0.2, position: [-8, 8, -8], color: '#0088ff' },\n  },\n};\n\nfunction SceneLighting({ preset = 'default' }: { preset: keyof typeof lightingPresets }) {\n  const lighting = lightingPresets[preset];\n  \n  return (\n    <>\n      <ambientLight \n        intensity={lighting.ambient.intensity} \n        color={lighting.ambient.color} \n      />\n      <directionalLight\n        position={lighting.directional.position as [number, number, number]}\n        intensity={lighting.directional.intensity}\n        color={lighting.directional.color}\n        castShadow={lighting.directional.castShadow}\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n        shadow-camera-far={50}\n        shadow-camera-left={-20}\n        shadow-camera-right={20}\n        shadow-camera-top={20}\n        shadow-camera-bottom={-20}\n      />\n      <pointLight \n        position={lighting.point.position as [number, number, number]}\n        intensity={lighting.point.intensity}\n        color={lighting.point.color}\n      />\n    </>\n  );\n}\n\nfunction SceneEffects({ \n  enableEnvironment, \n  backgroundColor \n}: { \n  enableEnvironment?: boolean; \n  backgroundColor?: string; \n}) {\n  const { theme } = useTheme();\n  \n  return (\n    <>\n      {enableEnvironment && (\n        <Environment \n          preset={theme === 'dark' ? 'night' : 'city'} \n          background={false}\n          intensity={0.5}\n        />\n      )}\n      \n      {backgroundColor && (\n        <color attach=\"background\" args={[backgroundColor]} />\n      )}\n      \n      {/* Fog for depth */}\n      <fog attach=\"fog\" args={[theme === 'dark' ? '#0a0a0a' : '#f0f0f0', 10, 50]} />\n    </>\n  );\n}\n\nexport function SceneManager({\n  children,\n  className,\n  cameraPosition = [0, 0, 8],\n  cameraTarget = [0, 0, 0],\n  enableControls = true,\n  enableEnvironment = true,\n  enableStats = false,\n  backgroundColor,\n  shadows = true,\n  antialias = true,\n  performance = { min: 0.5, max: 1, debounce: 200 },\n  lighting = 'default',\n  postProcessing = false,\n}: SceneManagerProps) {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const { themeConfig } = useTheme();\n\n  // Performance monitoring\n  useEffect(() => {\n    if (enableStats && typeof window !== 'undefined') {\n      console.log('3D Scene Performance Monitoring Enabled');\n    }\n  }, [enableStats]);\n\n  return (\n    <div className={cn('w-full h-full relative', className)}>\n      <Canvas\n        ref={canvasRef}\n        shadows={shadows}\n        gl={{\n          antialias,\n          alpha: true,\n          powerPreference: 'high-performance',\n          stencil: false,\n          depth: true,\n        }}\n        performance={performance}\n        dpr={[1, 2]}\n        camera={{\n          position: cameraPosition,\n          fov: 75,\n          near: 0.1,\n          far: 1000,\n        }}\n      >\n        {/* Performance optimizations */}\n        <AdaptiveDpr pixelated />\n        <AdaptiveEvents />\n        {shadows && <BakeShadows />}\n        \n        {/* Camera setup */}\n        <PerspectiveCamera\n          makeDefault\n          position={cameraPosition}\n          fov={75}\n          near={0.1}\n          far={1000}\n        />\n        \n        {/* Lighting */}\n        <SceneLighting preset={lighting} />\n        \n        {/* Scene effects */}\n        <SceneEffects \n          enableEnvironment={enableEnvironment}\n          backgroundColor={backgroundColor}\n        />\n        \n        {/* Controls */}\n        {enableControls && (\n          <OrbitControls\n            target={cameraTarget}\n            enablePan={true}\n            enableZoom={true}\n            enableRotate={true}\n            minDistance={2}\n            maxDistance={20}\n            maxPolarAngle={Math.PI / 2}\n            enableDamping={true}\n            dampingFactor={0.05}\n          />\n        )}\n        \n        {/* Performance stats */}\n        {enableStats && <Stats />}\n        \n        {/* Scene content */}\n        <Suspense fallback={null}>\n          {children}\n        </Suspense>\n        \n        {/* Preload common assets */}\n        <Preload all />\n      </Canvas>\n      \n      {/* Performance indicator */}\n      {enableStats && (\n        <div className=\"absolute top-4 left-4 bg-black/50 text-white p-2 rounded text-xs\">\n          <div>Theme: {themeConfig.theme}</div>\n          <div>Shadows: {shadows ? 'On' : 'Off'}</div>\n          <div>Lighting: {lighting}</div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n// Specialized scene for homepage hero\nexport function HeroScene({ children, ...props }: SceneManagerProps) {\n  return (\n    <SceneManager\n      lighting=\"dramatic\"\n      enableEnvironment={true}\n      shadows={true}\n      enableStats={false}\n      performance={{ min: 0.8, max: 1, debounce: 100 }}\n      {...props}\n    >\n      {children}\n    </SceneManager>\n  );\n}\n\n// Specialized scene for project showcase\nexport function ProjectScene({ children, ...props }: SceneManagerProps) {\n  return (\n    <SceneManager\n      lighting=\"studio\"\n      enableEnvironment={false}\n      shadows={true}\n      enableStats={false}\n      performance={{ min: 0.6, max: 1, debounce: 150 }}\n      {...props}\n    >\n      {children}\n    </SceneManager>\n  );\n}\n\n// Specialized scene for about section\nexport function AboutScene({ children, ...props }: SceneManagerProps) {\n  return (\n    <SceneManager\n      lighting=\"soft\"\n      enableEnvironment={true}\n      shadows={false}\n      enableStats={false}\n      performance={{ min: 0.5, max: 1, debounce: 200 }}\n      {...props}\n    >\n      {children}\n    </SceneManager>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAhBA;;;;;;;AAoCA,mBAAmB;AACnB,MAAM,kBAAkB;IACtB,SAAS;QACP,SAAS;YAAE,WAAW;YAAK,OAAO;QAAU;QAC5C,aAAa;YACX,WAAW;YACX,UAAU;gBAAC;gBAAI;gBAAI;aAAE;YACrB,OAAO;YACP,YAAY;QACd;QACA,OAAO;YAAE,WAAW;YAAK,UAAU;gBAAC,CAAC;gBAAI,CAAC;gBAAI,CAAC;aAAG;YAAE,OAAO;QAAU;IACvE;IACA,QAAQ;QACN,SAAS;YAAE,WAAW;YAAK,OAAO;QAAU;QAC5C,aAAa;YACX,WAAW;YACX,UAAU;gBAAC;gBAAG;gBAAI;aAAE;YACpB,OAAO;YACP,YAAY;QACd;QACA,OAAO;YAAE,WAAW;YAAK,UAAU;gBAAC,CAAC;gBAAG;gBAAG,CAAC;aAAE;YAAE,OAAO;QAAU;IACnE;IACA,UAAU;QACR,SAAS;YAAE,WAAW;YAAK,OAAO;QAAU;QAC5C,aAAa;YACX,WAAW;YACX,UAAU;gBAAC;gBAAI;gBAAI;aAAG;YACtB,OAAO;YACP,YAAY;QACd;QACA,OAAO;YAAE,WAAW;YAAK,UAAU;gBAAC,CAAC;gBAAI,CAAC;gBAAG,CAAC;aAAG;YAAE,OAAO;QAAU;IACtE;IACA,MAAM;QACJ,SAAS;YAAE,WAAW;YAAK,OAAO;QAAU;QAC5C,aAAa;YACX,WAAW;YACX,UAAU;gBAAC;gBAAG;gBAAI;aAAE;YACpB,OAAO;YACP,YAAY;QACd;QACA,OAAO;YAAE,WAAW;YAAK,UAAU;gBAAC,CAAC;gBAAG;gBAAG,CAAC;aAAE;YAAE,OAAO;QAAU;IACnE;AACF;AAEA,SAAS,cAAc,EAAE,SAAS,SAAS,EAA4C;IACrF,MAAM,WAAW,eAAe,CAAC,OAAO;IAExC,qBACE;;0BACE,8OAAC;gBACC,WAAW,SAAS,OAAO,CAAC,SAAS;gBACrC,OAAO,SAAS,OAAO,CAAC,KAAK;;;;;;0BAE/B,8OAAC;gBACC,UAAU,SAAS,WAAW,CAAC,QAAQ;gBACvC,WAAW,SAAS,WAAW,CAAC,SAAS;gBACzC,OAAO,SAAS,WAAW,CAAC,KAAK;gBACjC,YAAY,SAAS,WAAW,CAAC,UAAU;gBAC3C,wBAAsB;gBACtB,yBAAuB;gBACvB,qBAAmB;gBACnB,sBAAoB,CAAC;gBACrB,uBAAqB;gBACrB,qBAAmB;gBACnB,wBAAsB,CAAC;;;;;;0BAEzB,8OAAC;gBACC,UAAU,SAAS,KAAK,CAAC,QAAQ;gBACjC,WAAW,SAAS,KAAK,CAAC,SAAS;gBACnC,OAAO,SAAS,KAAK,CAAC,KAAK;;;;;;;;AAInC;AAEA,SAAS,aAAa,EACpB,iBAAiB,EACjB,eAAe,EAIhB;IACC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,qBACE;;YACG,mCACC,8OAAC,+JAAA,CAAA,cAAW;gBACV,QAAQ,UAAU,SAAS,UAAU;gBACrC,YAAY;gBACZ,WAAW;;;;;;YAId,iCACC,8OAAC;gBAAM,QAAO;gBAAa,MAAM;oBAAC;iBAAgB;;;;;;0BAIpD,8OAAC;gBAAI,QAAO;gBAAM,MAAM;oBAAC,UAAU,SAAS,YAAY;oBAAW;oBAAI;iBAAG;;;;;;;;AAGhF;AAEO,SAAS,aAAa,EAC3B,QAAQ,EACR,SAAS,EACT,iBAAiB;IAAC;IAAG;IAAG;CAAE,EAC1B,eAAe;IAAC;IAAG;IAAG;CAAE,EACxB,iBAAiB,IAAI,EACrB,oBAAoB,IAAI,EACxB,cAAc,KAAK,EACnB,eAAe,EACf,UAAU,IAAI,EACd,YAAY,IAAI,EAChB,cAAc;IAAE,KAAK;IAAK,KAAK;IAAG,UAAU;AAAI,CAAC,EACjD,WAAW,SAAS,EACpB,iBAAiB,KAAK,EACJ;IAClB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAkD;;QAElD;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;;0BAC3C,8OAAC,mMAAA,CAAA,SAAM;gBACL,KAAK;gBACL,SAAS;gBACT,IAAI;oBACF;oBACA,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,OAAO;gBACT;gBACA,aAAa;gBACb,KAAK;oBAAC;oBAAG;iBAAE;gBACX,QAAQ;oBACN,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,KAAK;gBACP;;kCAGA,8OAAC,+JAAA,CAAA,cAAW;wBAAC,SAAS;;;;;;kCACtB,8OAAC,kKAAA,CAAA,iBAAc;;;;;oBACd,yBAAW,8OAAC,+JAAA,CAAA,cAAW;;;;;kCAGxB,8OAAC,qKAAA,CAAA,oBAAiB;wBAChB,WAAW;wBACX,UAAU;wBACV,KAAK;wBACL,MAAM;wBACN,KAAK;;;;;;kCAIP,8OAAC;wBAAc,QAAQ;;;;;;kCAGvB,8OAAC;wBACC,mBAAmB;wBACnB,iBAAiB;;;;;;oBAIlB,gCACC,8OAAC,iKAAA,CAAA,gBAAa;wBACZ,QAAQ;wBACR,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,aAAa;wBACb,aAAa;wBACb,eAAe,KAAK,EAAE,GAAG;wBACzB,eAAe;wBACf,eAAe;;;;;;oBAKlB,6BAAe,8OAAC,yJAAA,CAAA,QAAK;;;;;kCAGtB,8OAAC,qMAAA,CAAA,WAAQ;wBAAC,UAAU;kCACjB;;;;;;kCAIH,8OAAC,2JAAA,CAAA,UAAO;wBAAC,GAAG;;;;;;;;;;;;YAIb,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAI;4BAAQ,YAAY,KAAK;;;;;;;kCAC9B,8OAAC;;4BAAI;4BAAU,UAAU,OAAO;;;;;;;kCAChC,8OAAC;;4BAAI;4BAAW;;;;;;;;;;;;;;;;;;;AAK1B;AAGO,SAAS,UAAU,EAAE,QAAQ,EAAE,GAAG,OAA0B;IACjE,qBACE,8OAAC;QACC,UAAS;QACT,mBAAmB;QACnB,SAAS;QACT,aAAa;QACb,aAAa;YAAE,KAAK;YAAK,KAAK;YAAG,UAAU;QAAI;QAC9C,GAAG,KAAK;kBAER;;;;;;AAGP;AAGO,SAAS,aAAa,EAAE,QAAQ,EAAE,GAAG,OAA0B;IACpE,qBACE,8OAAC;QACC,UAAS;QACT,mBAAmB;QACnB,SAAS;QACT,aAAa;QACb,aAAa;YAAE,KAAK;YAAK,KAAK;YAAG,UAAU;QAAI;QAC9C,GAAG,KAAK;kBAER;;;;;;AAGP;AAGO,SAAS,WAAW,EAAE,QAAQ,EAAE,GAAG,OAA0B;IAClE,qBACE,8OAAC;QACC,UAAS;QACT,mBAAmB;QACnB,SAAS;QACT,aAAa;QACb,aAAa;YAAE,KAAK;YAAK,KAAK;YAAG,UAAU;QAAI;QAC9C,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/Microchip.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Box, RoundedBox, Text } from '@react-three/drei';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface MicrochipProps {\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  animated?: boolean;\n  glowEffect?: boolean;\n  text?: string;\n  onClick?: () => void;\n  onHover?: (hovered: boolean) => void;\n}\n\nexport function Microchip({\n  position = [0, 0, 0],\n  scale = 1,\n  rotation = [0, 0, 0],\n  animated = true,\n  glowEffect = true,\n  text,\n  onClick,\n  onHover,\n}: MicrochipProps) {\n  const groupRef = useRef<Group>(null);\n  const chipRef = useRef<Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n\n  // Animation loop\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    \n    // Gentle rotation animation\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;\n    \n    // Floating animation\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1;\n    \n    // Hover effect\n    if (hovered && chipRef.current) {\n      chipRef.current.scale.setScalar(1.1);\n    } else if (chipRef.current) {\n      chipRef.current.scale.setScalar(1);\n    }\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(false);\n    document.body.style.cursor = 'auto';\n  };\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      rotation={rotation}\n      onClick={onClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      {/* Main chip body */}\n      <RoundedBox\n        ref={chipRef}\n        args={[2, 0.2, 2]}\n        radius={0.05}\n        smoothness={4}\n        castShadow\n        receiveShadow\n      >\n        <meshStandardMaterial\n          color={hovered ? themeConfig.primaryColor : '#2a2a2a'}\n          metalness={0.8}\n          roughness={0.2}\n          emissive={glowEffect ? themeConfig.primaryColor : '#000000'}\n          emissiveIntensity={hovered ? 0.3 : 0.1}\n        />\n      </RoundedBox>\n\n      {/* Circuit traces */}\n      {Array.from({ length: 8 }, (_, i) => (\n        <Box\n          key={`trace-${i}`}\n          args={[0.05, 0.01, 1.8]}\n          position={[-0.8 + (i * 0.2), 0.11, 0]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={themeConfig.secondaryColor}\n            emissive={themeConfig.secondaryColor}\n            emissiveIntensity={0.2}\n          />\n        </Box>\n      ))}\n\n      {/* Pins */}\n      {Array.from({ length: 16 }, (_, i) => {\n        const side = Math.floor(i / 4);\n        const pinIndex = i % 4;\n        let pinPosition: [number, number, number];\n        \n        switch (side) {\n          case 0: // Top\n            pinPosition = [-0.6 + (pinIndex * 0.4), -0.15, 1.1];\n            break;\n          case 1: // Right\n            pinPosition = [1.1, -0.15, 0.6 - (pinIndex * 0.4)];\n            break;\n          case 2: // Bottom\n            pinPosition = [0.6 - (pinIndex * 0.4), -0.15, -1.1];\n            break;\n          case 3: // Left\n            pinPosition = [-1.1, -0.15, -0.6 + (pinIndex * 0.4)];\n            break;\n          default:\n            pinPosition = [0, 0, 0];\n        }\n\n        return (\n          <Box\n            key={`pin-${i}`}\n            args={[0.1, 0.3, 0.05]}\n            position={pinPosition}\n            castShadow\n          >\n            <meshStandardMaterial\n              color=\"#ffd700\"\n              metalness={1}\n              roughness={0.1}\n            />\n          </Box>\n        );\n      })}\n\n      {/* Central processing unit indicator */}\n      <RoundedBox\n        args={[1, 0.05, 1]}\n        radius={0.02}\n        position={[0, 0.13, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#1a1a1a\"\n          metalness={0.5}\n          roughness={0.3}\n        />\n      </RoundedBox>\n\n      {/* Text label */}\n      {text && (\n        <Text\n          position={[0, 0.2, 0]}\n          fontSize={0.2}\n          color={themeConfig.primaryColor}\n          anchorX=\"center\"\n          anchorY=\"middle\"\n          font=\"/fonts/inter-bold.woff\"\n        >\n          {text}\n        </Text>\n      )}\n\n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <RoundedBox\n          args={[2.2, 0.25, 2.2]}\n          radius={0.05}\n          position={[0, 0, 0]}\n        >\n          <meshBasicMaterial\n            color={themeConfig.primaryColor}\n            transparent\n            opacity={0.1}\n          />\n        </RoundedBox>\n      )}\n    </group>\n  );\n}\n\n// Simplified microchip for performance-critical scenarios\nexport function SimpleMicrochip({\n  position = [0, 0, 0],\n  scale = 1,\n  color = '#2a2a2a',\n}: {\n  position?: [number, number, number];\n  scale?: number;\n  color?: string;\n}) {\n  return (\n    <RoundedBox\n      args={[2, 0.2, 2]}\n      radius={0.05}\n      position={position}\n      scale={scale}\n      castShadow\n      receiveShadow\n    >\n      <meshStandardMaterial\n        color={color}\n        metalness={0.8}\n        roughness={0.2}\n      />\n    </RoundedBox>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAmBO,SAAS,UAAU,EACxB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,IAAI,EACJ,OAAO,EACP,OAAO,EACQ;IACf,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAQ;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QAEpC,4BAA4B;QAC5B,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAExD,qBAAqB;QACrB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;QAEhF,eAAe;QACf,IAAI,WAAW,QAAQ,OAAO,EAAE;YAC9B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QAClC,OAAO,IAAI,QAAQ,OAAO,EAAE;YAC1B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QAClC;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,eAAe;QACf,cAAc;;0BAGd,8OAAC,8JAAA,CAAA,aAAU;gBACT,KAAK;gBACL,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,YAAY;gBACZ,UAAU;gBACV,aAAa;0BAEb,cAAA,8OAAC;oBACC,OAAO,UAAU,YAAY,YAAY,GAAG;oBAC5C,WAAW;oBACX,WAAW;oBACX,UAAU,aAAa,YAAY,YAAY,GAAG;oBAClD,mBAAmB,UAAU,MAAM;;;;;;;;;;;YAKtC,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC;wBAAM;wBAAM;qBAAI;oBACvB,UAAU;wBAAC,CAAC,MAAO,IAAI;wBAAM;wBAAM;qBAAE;oBACrC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAO,YAAY,cAAc;wBACjC,UAAU,YAAY,cAAc;wBACpC,mBAAmB;;;;;;mBARhB,CAAC,MAAM,EAAE,GAAG;;;;;YAcpB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,CAAC,GAAG;gBAC9B,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI;gBAC5B,MAAM,WAAW,IAAI;gBACrB,IAAI;gBAEJ,OAAQ;oBACN,KAAK;wBACH,cAAc;4BAAC,CAAC,MAAO,WAAW;4BAAM,CAAC;4BAAM;yBAAI;wBACnD;oBACF,KAAK;wBACH,cAAc;4BAAC;4BAAK,CAAC;4BAAM,MAAO,WAAW;yBAAK;wBAClD;oBACF,KAAK;wBACH,cAAc;4BAAC,MAAO,WAAW;4BAAM,CAAC;4BAAM,CAAC;yBAAI;wBACnD;oBACF,KAAK;wBACH,cAAc;4BAAC,CAAC;4BAAK,CAAC;4BAAM,CAAC,MAAO,WAAW;yBAAK;wBACpD;oBACF;wBACE,cAAc;4BAAC;4BAAG;4BAAG;yBAAE;gBAC3B;gBAEA,qBACE,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC;wBAAK;wBAAK;qBAAK;oBACtB,UAAU;oBACV,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,WAAW;;;;;;mBARR,CAAC,IAAI,EAAE,GAAG;;;;;YAYrB;0BAGA,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAG;oBAAM;iBAAE;gBAClB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAM;iBAAE;gBACtB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,WAAW;;;;;;;;;;;YAKd,sBACC,8OAAC,wJAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBACrB,UAAU;gBACV,OAAO,YAAY,YAAY;gBAC/B,SAAQ;gBACR,SAAQ;gBACR,MAAK;0BAEJ;;;;;;YAKJ,cAAc,yBACb,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAMrB;AAGO,SAAS,gBAAgB,EAC9B,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,QAAQ,SAAS,EAKlB;IACC,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,MAAM;YAAC;YAAG;YAAK;SAAE;QACjB,QAAQ;QACR,UAAU;QACV,OAAO;QACP,UAAU;QACV,aAAa;kBAEb,cAAA,8OAAC;YACC,OAAO;YACP,WAAW;YACX,WAAW;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/MicrochipModel.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState, Suspense } from 'react';\nimport { useFrame, useLoader } from '@react-three/fiber';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';\nimport { useGLTF, Text, Html } from '@react-three/drei';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface MicrochipModelProps {\n  modelPath?: string;\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  animated?: boolean;\n  glowEffect?: boolean;\n  text?: string;\n  onClick?: () => void;\n  onHover?: (hovered: boolean) => void;\n}\n\n// Component for loading external GLTF models\nfunction LoadedMicrochip({ \n  modelPath, \n  position = [0, 0, 0], \n  scale = 1, \n  animated = true,\n  glowEffect = true,\n  onClick,\n  onHover \n}: MicrochipModelProps) {\n  const groupRef = useRef<Group>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n  \n  // Load the GLTF model\n  const { scene } = useGLTF(modelPath || '/models/microchip.gltf');\n  \n  // Animation loop\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    \n    // Gentle rotation animation\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.3;\n    \n    // Floating animation\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;\n    \n    // Hover effect\n    if (hovered) {\n      groupRef.current.scale.setScalar(scale * 1.1);\n    } else {\n      groupRef.current.scale.setScalar(scale);\n    }\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(false);\n    document.body.style.cursor = 'auto';\n  };\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      onClick={onClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      <primitive \n        object={scene.clone()} \n        castShadow \n        receiveShadow\n      />\n      \n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <mesh position={[0, 0, 0]}>\n          <sphereGeometry args={[2, 16, 16]} />\n          <meshBasicMaterial\n            color={themeConfig.primaryColor}\n            transparent\n            opacity={0.1}\n          />\n        </mesh>\n      )}\n    </group>\n  );\n}\n\n// Fallback component while model loads\nfunction ModelLoader() {\n  return (\n    <Html center>\n      <div className=\"flex flex-col items-center space-y-2 text-white\">\n        <div className=\"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin\" />\n        <p className=\"text-sm\">Loading 3D Model...</p>\n      </div>\n    </Html>\n  );\n}\n\n// Error fallback component\nfunction ModelError() {\n  return (\n    <mesh>\n      <boxGeometry args={[2, 0.5, 2]} />\n      <meshStandardMaterial color=\"#ff4444\" />\n    </mesh>\n  );\n}\n\n// Main component with error boundary\nexport function MicrochipModel(props: MicrochipModelProps) {\n  const [error, setError] = useState(false);\n\n  if (error) {\n    return <ModelError />;\n  }\n\n  return (\n    <Suspense fallback={<ModelLoader />}>\n      <ErrorBoundary onError={() => setError(true)}>\n        <LoadedMicrochip {...props} />\n      </ErrorBoundary>\n    </Suspense>\n  );\n}\n\n// Simple error boundary component\nfunction ErrorBoundary({ \n  children, \n  onError \n}: { \n  children: React.ReactNode; \n  onError: () => void; \n}) {\n  try {\n    return <>{children}</>;\n  } catch (error) {\n    console.error('3D Model loading error:', error);\n    onError();\n    return <ModelError />;\n  }\n}\n\n// Preload models for better performance\nexport function preloadMicrochipModels() {\n  useGLTF.preload('/models/microchip.gltf');\n  useGLTF.preload('/models/cpu.gltf');\n  useGLTF.preload('/models/processor.gltf');\n}\n\n// Enhanced microchip with multiple variants\nexport function EnhancedMicrochip({\n  variant = 'default',\n  ...props\n}: MicrochipModelProps & { variant?: 'default' | 'cpu' | 'processor' | 'memory' }) {\n  const modelPaths = {\n    default: '/models/microchip.gltf',\n    cpu: '/models/cpu.gltf',\n    processor: '/models/processor.gltf',\n    memory: '/models/memory.gltf',\n  };\n\n  return (\n    <MicrochipModel\n      {...props}\n      modelPath={modelPaths[variant]}\n    />\n  );\n}\n\n// Grid of microchips for background effects\nexport function MicrochipGrid({\n  count = 20,\n  spread = 10,\n  animated = true,\n}: {\n  count?: number;\n  spread?: number;\n  animated?: boolean;\n}) {\n  const chips = Array.from({ length: count }, (_, i) => ({\n    id: i,\n    position: [\n      (Math.random() - 0.5) * spread,\n      (Math.random() - 0.5) * spread,\n      (Math.random() - 0.5) * spread,\n    ] as [number, number, number],\n    scale: 0.3 + Math.random() * 0.4,\n    rotation: [\n      Math.random() * Math.PI,\n      Math.random() * Math.PI,\n      Math.random() * Math.PI,\n    ] as [number, number, number],\n  }));\n\n  return (\n    <group>\n      {chips.map((chip) => (\n        <MicrochipModel\n          key={chip.id}\n          position={chip.position}\n          scale={chip.scale}\n          rotation={chip.rotation}\n          animated={animated}\n          glowEffect={false}\n        />\n      ))}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AAAA;AAEA;AAPA;;;;;;AAqBA,6CAA6C;AAC7C,SAAS,gBAAgB,EACvB,SAAS,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,OAAO,EACP,OAAO,EACa;IACpB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,sBAAsB;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvC,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QAEpC,4BAA4B;QAC5B,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAExD,qBAAqB;QACrB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;QAEpF,eAAe;QACf,IAAI,SAAS;YACX,SAAS,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ;QAC3C,OAAO;YACL,SAAS,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QACnC;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,SAAS;QACT,eAAe;QACf,cAAc;;0BAEd,8OAAC;gBACC,QAAQ,MAAM,KAAK;gBACnB,UAAU;gBACV,aAAa;;;;;;YAId,cAAc,yBACb,8OAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;;kCACvB,8OAAC;wBAAe,MAAM;4BAAC;4BAAG;4BAAI;yBAAG;;;;;;kCACjC,8OAAC;wBACC,OAAO,YAAY,YAAY;wBAC/B,WAAW;wBACX,SAAS;;;;;;;;;;;;;;;;;;AAMrB;AAEA,uCAAuC;AACvC,SAAS;IACP,qBACE,8OAAC,uJAAA,CAAA,OAAI;QAAC,MAAM;kBACV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAU;;;;;;;;;;;;;;;;;AAI/B;AAEA,2BAA2B;AAC3B,SAAS;IACP,qBACE,8OAAC;;0BACC,8OAAC;gBAAY,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;;;;;;0BAC9B,8OAAC;gBAAqB,OAAM;;;;;;;;;;;;AAGlC;AAGO,SAAS,eAAe,KAA0B;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,IAAI,OAAO;QACT,qBAAO,8OAAC;;;;;IACV;IAEA,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;;;;;kBACnB,cAAA,8OAAC;YAAc,SAAS,IAAM,SAAS;sBACrC,cAAA,8OAAC;gBAAiB,GAAG,KAAK;;;;;;;;;;;;;;;;AAIlC;AAEA,kCAAkC;AAClC,SAAS,cAAc,EACrB,QAAQ,EACR,OAAO,EAIR;IACC,IAAI;QACF,qBAAO;sBAAG;;IACZ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC;QACA,qBAAO,8OAAC;;;;;IACV;AACF;AAGO,SAAS;IACd,wJAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAChB,wJAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAChB,wJAAA,CAAA,UAAO,CAAC,OAAO,CAAC;AAClB;AAGO,SAAS,kBAAkB,EAChC,UAAU,SAAS,EACnB,GAAG,OAC4E;IAC/E,MAAM,aAAa;QACjB,SAAS;QACT,KAAK;QACL,WAAW;QACX,QAAQ;IACV;IAEA,qBACE,8OAAC;QACE,GAAG,KAAK;QACT,WAAW,UAAU,CAAC,QAAQ;;;;;;AAGpC;AAGO,SAAS,cAAc,EAC5B,QAAQ,EAAE,EACV,SAAS,EAAE,EACX,WAAW,IAAI,EAKhB;IACC,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAM,GAAG,CAAC,GAAG,IAAM,CAAC;YACrD,IAAI;YACJ,UAAU;gBACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;aACzB;YACD,OAAO,MAAM,KAAK,MAAM,KAAK;YAC7B,UAAU;gBACR,KAAK,MAAM,KAAK,KAAK,EAAE;gBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;gBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;aACxB;QACH,CAAC;IAED,qBACE,8OAAC;kBACE,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gBAEC,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;gBACV,YAAY;eALP,KAAK,EAAE;;;;;;;;;;AAUtB", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/PlaceholderModels.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Box, RoundedBox, Cylinder, Sphere } from '@react-three/drei';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface PlaceholderModelProps {\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  animated?: boolean;\n  glowEffect?: boolean;\n  variant?: 'cpu' | 'memory' | 'gpu' | 'motherboard';\n  onClick?: () => void;\n  onHover?: (hovered: boolean) => void;\n}\n\n// CPU Model\nexport function CPUModel({\n  position = [0, 0, 0],\n  scale = 1,\n  rotation = [0, 0, 0],\n  animated = true,\n  glowEffect = true,\n  onClick,\n  onHover,\n}: PlaceholderModelProps) {\n  const groupRef = useRef<Group>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.05;\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(false);\n    document.body.style.cursor = 'auto';\n  };\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      rotation={rotation}\n      onClick={onClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      {/* CPU Base */}\n      <RoundedBox\n        args={[2, 0.3, 2]}\n        radius={0.05}\n        castShadow\n        receiveShadow\n      >\n        <meshStandardMaterial\n          color={hovered ? themeConfig.primaryColor : '#2a2a2a'}\n          metalness={0.8}\n          roughness={0.2}\n          emissive={glowEffect ? themeConfig.primaryColor : '#000000'}\n          emissiveIntensity={hovered ? 0.3 : 0.1}\n        />\n      </RoundedBox>\n\n      {/* Heat spreader */}\n      <RoundedBox\n        args={[1.5, 0.1, 1.5]}\n        radius={0.02}\n        position={[0, 0.2, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#c0c0c0\"\n          metalness={1}\n          roughness={0.1}\n        />\n      </RoundedBox>\n\n      {/* Pins */}\n      {Array.from({ length: 64 }, (_, i) => {\n        const row = Math.floor(i / 8);\n        const col = i % 8;\n        const x = -0.875 + (col * 0.25);\n        const z = -0.875 + (row * 0.25);\n        \n        return (\n          <Cylinder\n            key={i}\n            args={[0.02, 0.02, 0.2]}\n            position={[x, -0.25, z]}\n            castShadow\n          >\n            <meshStandardMaterial\n              color=\"#ffd700\"\n              metalness={1}\n              roughness={0.1}\n            />\n          </Cylinder>\n        );\n      })}\n\n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <RoundedBox\n          args={[2.2, 0.35, 2.2]}\n          radius={0.05}\n          position={[0, 0, 0]}\n        >\n          <meshBasicMaterial\n            color={themeConfig.primaryColor}\n            transparent\n            opacity={0.1}\n          />\n        </RoundedBox>\n      )}\n    </group>\n  );\n}\n\n// Memory Module Model\nexport function MemoryModel({\n  position = [0, 0, 0],\n  scale = 1,\n  animated = true,\n  glowEffect = true,\n}: PlaceholderModelProps) {\n  const groupRef = useRef<Group>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n  });\n\n  return (\n    <group ref={groupRef} position={position} scale={scale}>\n      {/* PCB */}\n      <Box\n        args={[0.2, 1.5, 4]}\n        castShadow\n        receiveShadow\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n      >\n        <meshStandardMaterial\n          color=\"#1a4a1a\"\n          roughness={0.8}\n        />\n      </Box>\n\n      {/* Memory chips */}\n      {Array.from({ length: 8 }, (_, i) => (\n        <RoundedBox\n          key={i}\n          args={[0.15, 0.3, 0.4]}\n          radius={0.01}\n          position={[0.025, 0, -1.4 + (i * 0.4)]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={hovered ? themeConfig.secondaryColor : '#0a0a0a'}\n            emissive={glowEffect ? themeConfig.secondaryColor : '#000000'}\n            emissiveIntensity={hovered ? 0.2 : 0.05}\n          />\n        </RoundedBox>\n      ))}\n\n      {/* Connector pins */}\n      <Box\n        args={[0.05, 0.2, 3.8]}\n        position={[0, -0.85, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#ffd700\"\n          metalness={1}\n          roughness={0.1}\n        />\n      </Box>\n    </group>\n  );\n}\n\n// GPU Model\nexport function GPUModel({\n  position = [0, 0, 0],\n  scale = 1,\n  animated = true,\n  glowEffect = true,\n}: PlaceholderModelProps) {\n  const groupRef = useRef<Group>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n  });\n\n  return (\n    <group ref={groupRef} position={position} scale={scale}>\n      {/* GPU PCB */}\n      <RoundedBox\n        args={[4, 0.2, 2]}\n        radius={0.05}\n        castShadow\n        receiveShadow\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n      >\n        <meshStandardMaterial\n          color=\"#1a1a4a\"\n          roughness={0.8}\n        />\n      </RoundedBox>\n\n      {/* GPU Die */}\n      <RoundedBox\n        args={[1.5, 0.15, 1.5]}\n        radius={0.02}\n        position={[0, 0.175, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color={hovered ? themeConfig.accentColor : '#2a2a2a'}\n          metalness={0.8}\n          roughness={0.2}\n          emissive={glowEffect ? themeConfig.accentColor : '#000000'}\n          emissiveIntensity={hovered ? 0.3 : 0.1}\n        />\n      </RoundedBox>\n\n      {/* Cooling fins */}\n      {Array.from({ length: 8 }, (_, i) => (\n        <Box\n          key={i}\n          args={[0.05, 0.5, 1.8]}\n          position={[-1.5 + (i * 0.2), 0.35, 0]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color=\"#c0c0c0\"\n            metalness={0.9}\n            roughness={0.1}\n          />\n        </Box>\n      ))}\n\n      {/* Memory modules */}\n      {Array.from({ length: 6 }, (_, i) => (\n        <RoundedBox\n          key={i}\n          args={[0.3, 0.1, 0.2]}\n          radius={0.01}\n          position={[1.2, 0.15, -0.6 + (i * 0.24)]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color=\"#0a0a0a\"\n            emissive={themeConfig.secondaryColor}\n            emissiveIntensity={0.1}\n          />\n        </RoundedBox>\n      ))}\n    </group>\n  );\n}\n\n// Motherboard Model\nexport function MotherboardModel({\n  position = [0, 0, 0],\n  scale = 1,\n  animated = false,\n}: PlaceholderModelProps) {\n  const { themeConfig } = useTheme();\n\n  return (\n    <group position={position} scale={scale}>\n      {/* Main PCB */}\n      <RoundedBox\n        args={[6, 0.1, 4]}\n        radius={0.05}\n        castShadow\n        receiveShadow\n      >\n        <meshStandardMaterial\n          color=\"#1a4a1a\"\n          roughness={0.8}\n        />\n      </RoundedBox>\n\n      {/* CPU Socket */}\n      <RoundedBox\n        args={[1.2, 0.05, 1.2]}\n        radius={0.02}\n        position={[-1, 0.075, 0.5]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#0a0a0a\"\n          metalness={0.5}\n        />\n      </RoundedBox>\n\n      {/* RAM Slots */}\n      {Array.from({ length: 4 }, (_, i) => (\n        <Box\n          key={i}\n          args={[0.15, 0.05, 2]}\n          position={[1 + (i * 0.3), 0.075, 0]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color=\"#0a0a0a\"\n            metalness={0.8}\n          />\n        </Box>\n      ))}\n\n      {/* Circuit traces */}\n      {Array.from({ length: 20 }, (_, i) => (\n        <Box\n          key={i}\n          args={[Math.random() * 2 + 1, 0.01, 0.02]}\n          position={[\n            (Math.random() - 0.5) * 5,\n            0.055,\n            (Math.random() - 0.5) * 3\n          ]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={themeConfig.primaryColor}\n            emissive={themeConfig.primaryColor}\n            emissiveIntensity={0.1}\n          />\n        </Box>\n      ))}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAAA;AAEA;AANA;;;;;;AAoBO,SAAS,SAAS,EACvB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,OAAO,EACP,OAAO,EACe;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QACpC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;IAClF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,eAAe;QACf,cAAc;;0BAGd,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,UAAU;gBACV,aAAa;0BAEb,cAAA,8OAAC;oBACC,OAAO,UAAU,YAAY,YAAY,GAAG;oBAC5C,WAAW;oBACX,WAAW;oBACX,UAAU,aAAa,YAAY,YAAY,GAAG;oBAClD,mBAAmB,UAAU,MAAM;;;;;;;;;;;0BAKvC,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBACrB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBACrB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,WAAW;;;;;;;;;;;YAKd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,CAAC,GAAG;gBAC9B,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI;gBAC3B,MAAM,MAAM,IAAI;gBAChB,MAAM,IAAI,CAAC,QAAS,MAAM;gBAC1B,MAAM,IAAI,CAAC,QAAS,MAAM;gBAE1B,qBACE,8OAAC,0JAAA,CAAA,WAAQ;oBAEP,MAAM;wBAAC;wBAAM;wBAAM;qBAAI;oBACvB,UAAU;wBAAC;wBAAG,CAAC;wBAAM;qBAAE;oBACvB,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,WAAW;;;;;;mBARR;;;;;YAYX;YAGC,cAAc,yBACb,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAMrB;AAGO,SAAS,YAAY,EAC1B,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,aAAa,IAAI,EACK;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QACpC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;IAC1E;IAEA,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;QAAU,OAAO;;0BAE/C,8OAAC,0JAAA,CAAA,MAAG;gBACF,MAAM;oBAAC;oBAAK;oBAAK;iBAAE;gBACnB,UAAU;gBACV,aAAa;gBACb,eAAe,IAAM,WAAW;gBAChC,cAAc,IAAM,WAAW;0BAE/B,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;;;;;;;;;;;YAKd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,8JAAA,CAAA,aAAU;oBAET,MAAM;wBAAC;wBAAM;wBAAK;qBAAI;oBACtB,QAAQ;oBACR,UAAU;wBAAC;wBAAO;wBAAG,CAAC,MAAO,IAAI;qBAAK;oBACtC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAO,UAAU,YAAY,cAAc,GAAG;wBAC9C,UAAU,aAAa,YAAY,cAAc,GAAG;wBACpD,mBAAmB,UAAU,MAAM;;;;;;mBAThC;;;;;0BAeT,8OAAC,0JAAA,CAAA,MAAG;gBACF,MAAM;oBAAC;oBAAM;oBAAK;iBAAI;gBACtB,UAAU;oBAAC;oBAAG,CAAC;oBAAM;iBAAE;gBACvB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,WAAW;;;;;;;;;;;;;;;;;AAKrB;AAGO,SAAS,SAAS,EACvB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,aAAa,IAAI,EACK;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QACpC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;IAC1D;IAEA,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;QAAU,OAAO;;0BAE/C,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,UAAU;gBACV,aAAa;gBACb,eAAe,IAAM,WAAW;gBAChC,cAAc,IAAM,WAAW;0BAE/B,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;;;;;;;;;;;0BAKf,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAO;iBAAE;gBACvB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAO,UAAU,YAAY,WAAW,GAAG;oBAC3C,WAAW;oBACX,WAAW;oBACX,UAAU,aAAa,YAAY,WAAW,GAAG;oBACjD,mBAAmB,UAAU,MAAM;;;;;;;;;;;YAKtC,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC;wBAAM;wBAAK;qBAAI;oBACtB,UAAU;wBAAC,CAAC,MAAO,IAAI;wBAAM;wBAAM;qBAAE;oBACrC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,WAAW;;;;;;mBARR;;;;;YAcR,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,8JAAA,CAAA,aAAU;oBAET,MAAM;wBAAC;wBAAK;wBAAK;qBAAI;oBACrB,QAAQ;oBACR,UAAU;wBAAC;wBAAK;wBAAM,CAAC,MAAO,IAAI;qBAAM;oBACxC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,UAAU,YAAY,cAAc;wBACpC,mBAAmB;;;;;;mBAThB;;;;;;;;;;;AAef;AAGO,SAAS,iBAAiB,EAC/B,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW,KAAK,EACM;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,qBACE,8OAAC;QAAM,UAAU;QAAU,OAAO;;0BAEhC,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,UAAU;gBACV,aAAa;0BAEb,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;;;;;;;;;;;0BAKf,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC,CAAC;oBAAG;oBAAO;iBAAI;gBAC1B,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;;;;;;;;;;;YAKd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC;wBAAM;wBAAM;qBAAE;oBACrB,UAAU;wBAAC,IAAK,IAAI;wBAAM;wBAAO;qBAAE;oBACnC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,WAAW;;;;;;mBAPR;;;;;YAaR,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC,KAAK,MAAM,KAAK,IAAI;wBAAG;wBAAM;qBAAK;oBACzC,UAAU;wBACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBACxB;wBACA,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;qBACzB;oBACD,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAO,YAAY,YAAY;wBAC/B,UAAU,YAAY,YAAY;wBAClC,mBAAmB;;;;;;mBAZhB;;;;;;;;;;;AAkBf", "debugId": null}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/InteractiveCamera.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect } from 'react';\nimport { useFrame, useThree } from '@react-three/fiber';\nimport { PerspectiveCamera } from '@react-three/drei';\nimport { Vector3, MathUtils } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface InteractiveCameraProps {\n  enableMouseTracking?: boolean;\n  enableScrollTracking?: boolean;\n  mouseSensitivity?: number;\n  scrollSensitivity?: number;\n  basePosition?: [number, number, number];\n  lookAt?: [number, number, number];\n  smoothing?: number;\n}\n\nexport function InteractiveCamera({\n  enableMouseTracking = true,\n  enableScrollTracking = true,\n  mouseSensitivity = 0.5,\n  scrollSensitivity = 0.3,\n  basePosition = [0, 0, 8],\n  lookAt = [0, 0, 0],\n  smoothing = 0.05,\n}: InteractiveCameraProps) {\n  const cameraRef = useRef<any>(null);\n  const mousePosition = useRef({ x: 0, y: 0 });\n  const scrollPosition = useRef(0);\n  const targetPosition = useRef(new Vector3(...basePosition));\n  const targetLookAt = useRef(new Vector3(...lookAt));\n  const { camera } = useThree();\n\n  // Mouse tracking\n  useEffect(() => {\n    if (!enableMouseTracking) return;\n\n    const handleMouseMove = (event: MouseEvent) => {\n      const x = (event.clientX / window.innerWidth) * 2 - 1;\n      const y = -(event.clientY / window.innerHeight) * 2 + 1;\n      \n      mousePosition.current = { x, y };\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, [enableMouseTracking]);\n\n  // Scroll tracking\n  useEffect(() => {\n    if (!enableScrollTracking) return;\n\n    const handleScroll = () => {\n      scrollPosition.current = window.scrollY;\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [enableScrollTracking]);\n\n  // Camera animation\n  useFrame(() => {\n    if (!cameraRef.current) return;\n\n    // Calculate target position based on mouse\n    const mouseInfluence = {\n      x: mousePosition.current.x * mouseSensitivity,\n      y: mousePosition.current.y * mouseSensitivity,\n      z: 0,\n    };\n\n    // Calculate scroll influence\n    const scrollInfluence = {\n      x: 0,\n      y: 0,\n      z: (scrollPosition.current * scrollSensitivity) / 100,\n    };\n\n    // Update target position\n    targetPosition.current.set(\n      basePosition[0] + mouseInfluence.x + scrollInfluence.x,\n      basePosition[1] + mouseInfluence.y + scrollInfluence.y,\n      basePosition[2] + mouseInfluence.z + scrollInfluence.z\n    );\n\n    // Smooth camera movement\n    cameraRef.current.position.lerp(targetPosition.current, smoothing);\n    \n    // Update look-at target with slight mouse influence\n    const lookAtTarget = new Vector3(\n      lookAt[0] + mouseInfluence.x * 0.2,\n      lookAt[1] + mouseInfluence.y * 0.2,\n      lookAt[2]\n    );\n    \n    cameraRef.current.lookAt(lookAtTarget);\n  });\n\n  return (\n    <PerspectiveCamera\n      ref={cameraRef}\n      makeDefault\n      position={basePosition}\n      fov={75}\n      near={0.1}\n      far={1000}\n    />\n  );\n}\n\n// Cinematic camera for dramatic scenes\nexport function CinematicCamera({\n  keyframes,\n  duration = 10,\n  autoStart = true,\n}: {\n  keyframes: Array<{\n    position: [number, number, number];\n    lookAt: [number, number, number];\n    fov?: number;\n  }>;\n  duration?: number;\n  autoStart?: boolean;\n}) {\n  const cameraRef = useRef<any>(null);\n  const startTime = useRef<number | null>(null);\n  const isPlaying = useRef(autoStart);\n\n  useFrame((state) => {\n    if (!cameraRef.current || !isPlaying.current || keyframes.length < 2) return;\n\n    if (startTime.current === null) {\n      startTime.current = state.clock.elapsedTime;\n    }\n\n    const elapsed = state.clock.elapsedTime - startTime.current;\n    const progress = Math.min(elapsed / duration, 1);\n\n    // Calculate current keyframe\n    const keyframeIndex = Math.floor(progress * (keyframes.length - 1));\n    const nextKeyframeIndex = Math.min(keyframeIndex + 1, keyframes.length - 1);\n    const localProgress = (progress * (keyframes.length - 1)) % 1;\n\n    const currentKeyframe = keyframes[keyframeIndex];\n    const nextKeyframe = keyframes[nextKeyframeIndex];\n\n    // Interpolate position\n    const position = new Vector3(\n      MathUtils.lerp(currentKeyframe.position[0], nextKeyframe.position[0], localProgress),\n      MathUtils.lerp(currentKeyframe.position[1], nextKeyframe.position[1], localProgress),\n      MathUtils.lerp(currentKeyframe.position[2], nextKeyframe.position[2], localProgress)\n    );\n\n    // Interpolate look-at\n    const lookAt = new Vector3(\n      MathUtils.lerp(currentKeyframe.lookAt[0], nextKeyframe.lookAt[0], localProgress),\n      MathUtils.lerp(currentKeyframe.lookAt[1], nextKeyframe.lookAt[1], localProgress),\n      MathUtils.lerp(currentKeyframe.lookAt[2], nextKeyframe.lookAt[2], localProgress)\n    );\n\n    // Interpolate FOV if provided\n    if (currentKeyframe.fov && nextKeyframe.fov) {\n      cameraRef.current.fov = MathUtils.lerp(currentKeyframe.fov, nextKeyframe.fov, localProgress);\n      cameraRef.current.updateProjectionMatrix();\n    }\n\n    cameraRef.current.position.copy(position);\n    cameraRef.current.lookAt(lookAt);\n\n    // Reset when complete\n    if (progress >= 1) {\n      startTime.current = null;\n      isPlaying.current = false;\n    }\n  });\n\n  const play = () => {\n    isPlaying.current = true;\n    startTime.current = null;\n  };\n\n  const stop = () => {\n    isPlaying.current = false;\n    startTime.current = null;\n  };\n\n  return (\n    <>\n      <PerspectiveCamera\n        ref={cameraRef}\n        makeDefault\n        position={keyframes[0].position}\n        fov={keyframes[0].fov || 75}\n        near={0.1}\n        far={1000}\n      />\n      {/* Expose controls via context or props if needed */}\n    </>\n  );\n}\n\n// Orbit camera with constraints\nexport function ConstrainedOrbitCamera({\n  target = [0, 0, 0],\n  distance = 8,\n  minDistance = 2,\n  maxDistance = 20,\n  enablePan = false,\n  enableZoom = true,\n  enableRotate = true,\n  autoRotate = false,\n  autoRotateSpeed = 0.5,\n}: {\n  target?: [number, number, number];\n  distance?: number;\n  minDistance?: number;\n  maxDistance?: number;\n  enablePan?: boolean;\n  enableZoom?: boolean;\n  enableRotate?: boolean;\n  autoRotate?: boolean;\n  autoRotateSpeed?: number;\n}) {\n  const cameraRef = useRef<any>(null);\n  const { themeConfig } = useTheme();\n\n  useFrame((state) => {\n    if (!cameraRef.current || !autoRotate) return;\n\n    const time = state.clock.elapsedTime * autoRotateSpeed;\n    const x = Math.cos(time) * distance;\n    const z = Math.sin(time) * distance;\n    \n    cameraRef.current.position.set(x, target[1], z);\n    cameraRef.current.lookAt(...target);\n  });\n\n  return (\n    <PerspectiveCamera\n      ref={cameraRef}\n      makeDefault\n      position={[0, 0, distance]}\n      fov={75}\n      near={0.1}\n      far={1000}\n    />\n  );\n}\n\n// First-person camera for immersive experiences\nexport function FirstPersonCamera({\n  position = [0, 1.6, 0],\n  sensitivity = 0.002,\n  enableMovement = true,\n  movementSpeed = 5,\n}: {\n  position?: [number, number, number];\n  sensitivity?: number;\n  enableMovement?: boolean;\n  movementSpeed?: number;\n}) {\n  const cameraRef = useRef<any>(null);\n  const keys = useRef<Set<string>>(new Set());\n  const mouseMovement = useRef({ x: 0, y: 0 });\n  const rotation = useRef({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      keys.current.add(event.code);\n    };\n\n    const handleKeyUp = (event: KeyboardEvent) => {\n      keys.current.delete(event.code);\n    };\n\n    const handleMouseMove = (event: MouseEvent) => {\n      if (document.pointerLockElement) {\n        mouseMovement.current.x += event.movementX * sensitivity;\n        mouseMovement.current.y += event.movementY * sensitivity;\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    window.addEventListener('keyup', handleKeyUp);\n    window.addEventListener('mousemove', handleMouseMove);\n\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n      window.removeEventListener('keyup', handleKeyUp);\n      window.removeEventListener('mousemove', handleMouseMove);\n    };\n  }, [sensitivity]);\n\n  useFrame((state, delta) => {\n    if (!cameraRef.current) return;\n\n    // Mouse look\n    rotation.current.y -= mouseMovement.current.x;\n    rotation.current.x -= mouseMovement.current.y;\n    rotation.current.x = MathUtils.clamp(rotation.current.x, -Math.PI / 2, Math.PI / 2);\n\n    cameraRef.current.rotation.set(rotation.current.x, rotation.current.y, 0);\n\n    // Movement\n    if (enableMovement) {\n      const moveVector = new Vector3();\n      \n      if (keys.current.has('KeyW')) moveVector.z -= 1;\n      if (keys.current.has('KeyS')) moveVector.z += 1;\n      if (keys.current.has('KeyA')) moveVector.x -= 1;\n      if (keys.current.has('KeyD')) moveVector.x += 1;\n      \n      moveVector.normalize();\n      moveVector.multiplyScalar(movementSpeed * delta);\n      \n      // Apply rotation to movement vector\n      moveVector.applyEuler(cameraRef.current.rotation);\n      cameraRef.current.position.add(moveVector);\n    }\n\n    // Reset mouse movement\n    mouseMovement.current.x = 0;\n    mouseMovement.current.y = 0;\n  });\n\n  return (\n    <PerspectiveCamera\n      ref={cameraRef}\n      makeDefault\n      position={position}\n      fov={75}\n      near={0.1}\n      far={1000}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAkBO,SAAS,kBAAkB,EAChC,sBAAsB,IAAI,EAC1B,uBAAuB,IAAI,EAC3B,mBAAmB,GAAG,EACtB,oBAAoB,GAAG,EACvB,eAAe;IAAC;IAAG;IAAG;CAAE,EACxB,SAAS;IAAC;IAAG;IAAG;CAAE,EAClB,YAAY,IAAI,EACO;IACvB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC1C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,+IAAA,CAAA,UAAO,IAAI;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,+IAAA,CAAA,UAAO,IAAI;IAC3C,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAE1B,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,qBAAqB;QAE1B,MAAM,kBAAkB,CAAC;YACvB,MAAM,IAAI,AAAC,MAAM,OAAO,GAAG,OAAO,UAAU,GAAI,IAAI;YACpD,MAAM,IAAI,CAAC,CAAC,MAAM,OAAO,GAAG,OAAO,WAAW,IAAI,IAAI;YAEtD,cAAc,OAAO,GAAG;gBAAE;gBAAG;YAAE;QACjC;QAEA,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;IACvD,GAAG;QAAC;KAAoB;IAExB,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,sBAAsB;QAE3B,MAAM,eAAe;YACnB,eAAe,OAAO,GAAG,OAAO,OAAO;QACzC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAqB;IAEzB,mBAAmB;IACnB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,2CAA2C;QAC3C,MAAM,iBAAiB;YACrB,GAAG,cAAc,OAAO,CAAC,CAAC,GAAG;YAC7B,GAAG,cAAc,OAAO,CAAC,CAAC,GAAG;YAC7B,GAAG;QACL;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB;YACtB,GAAG;YACH,GAAG;YACH,GAAG,AAAC,eAAe,OAAO,GAAG,oBAAqB;QACpD;QAEA,yBAAyB;QACzB,eAAe,OAAO,CAAC,GAAG,CACxB,YAAY,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,gBAAgB,CAAC,EACtD,YAAY,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,gBAAgB,CAAC,EACtD,YAAY,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,gBAAgB,CAAC;QAGxD,yBAAyB;QACzB,UAAU,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,OAAO,EAAE;QAExD,oDAAoD;QACpD,MAAM,eAAe,IAAI,+IAAA,CAAA,UAAO,CAC9B,MAAM,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,KAC/B,MAAM,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,KAC/B,MAAM,CAAC,EAAE;QAGX,UAAU,OAAO,CAAC,MAAM,CAAC;IAC3B;IAEA,qBACE,8OAAC,qKAAA,CAAA,oBAAiB;QAChB,KAAK;QACL,WAAW;QACX,UAAU;QACV,KAAK;QACL,MAAM;QACN,KAAK;;;;;;AAGX;AAGO,SAAS,gBAAgB,EAC9B,SAAS,EACT,WAAW,EAAE,EACb,YAAY,IAAI,EASjB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,IAAI,UAAU,MAAM,GAAG,GAAG;QAEtE,IAAI,UAAU,OAAO,KAAK,MAAM;YAC9B,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC,WAAW;QAC7C;QAEA,MAAM,UAAU,MAAM,KAAK,CAAC,WAAW,GAAG,UAAU,OAAO;QAC3D,MAAM,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU;QAE9C,6BAA6B;QAC7B,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,CAAC,UAAU,MAAM,GAAG,CAAC;QACjE,MAAM,oBAAoB,KAAK,GAAG,CAAC,gBAAgB,GAAG,UAAU,MAAM,GAAG;QACzE,MAAM,gBAAgB,AAAC,WAAW,CAAC,UAAU,MAAM,GAAG,CAAC,IAAK;QAE5D,MAAM,kBAAkB,SAAS,CAAC,cAAc;QAChD,MAAM,eAAe,SAAS,CAAC,kBAAkB;QAEjD,uBAAuB;QACvB,MAAM,WAAW,IAAI,+IAAA,CAAA,UAAO,CAC1B,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,QAAQ,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,EAAE,EAAE,gBACtE,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,QAAQ,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,EAAE,EAAE,gBACtE,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,QAAQ,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,EAAE,EAAE;QAGxE,sBAAsB;QACtB,MAAM,SAAS,IAAI,+IAAA,CAAA,UAAO,CACxB,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE,EAAE,gBAClE,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE,EAAE,gBAClE,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE,EAAE;QAGpE,8BAA8B;QAC9B,IAAI,gBAAgB,GAAG,IAAI,aAAa,GAAG,EAAE;YAC3C,UAAU,OAAO,CAAC,GAAG,GAAG,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,GAAG,EAAE,aAAa,GAAG,EAAE;YAC9E,UAAU,OAAO,CAAC,sBAAsB;QAC1C;QAEA,UAAU,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAChC,UAAU,OAAO,CAAC,MAAM,CAAC;QAEzB,sBAAsB;QACtB,IAAI,YAAY,GAAG;YACjB,UAAU,OAAO,GAAG;YACpB,UAAU,OAAO,GAAG;QACtB;IACF;IAEA,MAAM,OAAO;QACX,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;IACtB;IAEA,MAAM,OAAO;QACX,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;IACtB;IAEA,qBACE;kBACE,cAAA,8OAAC,qKAAA,CAAA,oBAAiB;YAChB,KAAK;YACL,WAAW;YACX,UAAU,SAAS,CAAC,EAAE,CAAC,QAAQ;YAC/B,KAAK,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI;YACzB,MAAM;YACN,KAAK;;;;;;;AAKb;AAGO,SAAS,uBAAuB,EACrC,SAAS;IAAC;IAAG;IAAG;CAAE,EAClB,WAAW,CAAC,EACZ,cAAc,CAAC,EACf,cAAc,EAAE,EAChB,YAAY,KAAK,EACjB,aAAa,IAAI,EACjB,eAAe,IAAI,EACnB,aAAa,KAAK,EAClB,kBAAkB,GAAG,EAWtB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,YAAY;QAEvC,MAAM,OAAO,MAAM,KAAK,CAAC,WAAW,GAAG;QACvC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ;QAC3B,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ;QAE3B,UAAU,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE;QAC7C,UAAU,OAAO,CAAC,MAAM,IAAI;IAC9B;IAEA,qBACE,8OAAC,qKAAA,CAAA,oBAAiB;QAChB,KAAK;QACL,WAAW;QACX,UAAU;YAAC;YAAG;YAAG;SAAS;QAC1B,KAAK;QACL,MAAM;QACN,KAAK;;;;;;AAGX;AAGO,SAAS,kBAAkB,EAChC,WAAW;IAAC;IAAG;IAAK;CAAE,EACtB,cAAc,KAAK,EACnB,iBAAiB,IAAI,EACrB,gBAAgB,CAAC,EAMlB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe,IAAI;IACrC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC1C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,KAAK,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI;QAC7B;QAEA,MAAM,cAAc,CAAC;YACnB,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI;QAChC;QAEA,MAAM,kBAAkB,CAAC;YACvB,IAAI,SAAS,kBAAkB,EAAE;gBAC/B,cAAc,OAAO,CAAC,CAAC,IAAI,MAAM,SAAS,GAAG;gBAC7C,cAAc,OAAO,CAAC,CAAC,IAAI,MAAM,SAAS,GAAG;YAC/C;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,gBAAgB,CAAC,SAAS;QACjC,OAAO,gBAAgB,CAAC,aAAa;QAErC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,SAAS;YACpC,OAAO,mBAAmB,CAAC,aAAa;QAC1C;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO;QACf,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,aAAa;QACb,SAAS,OAAO,CAAC,CAAC,IAAI,cAAc,OAAO,CAAC,CAAC;QAC7C,SAAS,OAAO,CAAC,CAAC,IAAI,cAAc,OAAO,CAAC,CAAC;QAC7C,SAAS,OAAO,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAS,CAAC,KAAK,CAAC,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG;QAEjF,UAAU,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,CAAC,EAAE;QAEvE,WAAW;QACX,IAAI,gBAAgB;YAClB,MAAM,aAAa,IAAI,+IAAA,CAAA,UAAO;YAE9B,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;YAC9C,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;YAC9C,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;YAC9C,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;YAE9C,WAAW,SAAS;YACpB,WAAW,cAAc,CAAC,gBAAgB;YAE1C,oCAAoC;YACpC,WAAW,UAAU,CAAC,UAAU,OAAO,CAAC,QAAQ;YAChD,UAAU,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjC;QAEA,uBAAuB;QACvB,cAAc,OAAO,CAAC,CAAC,GAAG;QAC1B,cAAc,OAAO,CAAC,CAAC,GAAG;IAC5B;IAEA,qBACE,8OAAC,qKAAA,CAAA,oBAAiB;QAChB,KAAK;QACL,WAAW;QACX,UAAU;QACV,KAAK;QACL,MAAM;QACN,KAAK;;;;;;AAGX", "debugId": null}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\nimport { InteractiveElementProps } from '@/types';\n\ninterface ButtonProps extends InteractiveElementProps {\n  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'outline';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  fullWidth?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n  type?: 'button' | 'submit' | 'reset';\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({\n    className,\n    variant = 'primary',\n    size = 'md',\n    fullWidth = false,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled = false,\n    loading = false,\n    onClick,\n    type = 'button',\n    ...props\n  }, ref) => {\n    const baseClasses = [\n      'inline-flex items-center justify-center',\n      'font-medium transition-all duration-200',\n      'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',\n      'disabled:pointer-events-none disabled:opacity-50',\n      'relative overflow-hidden',\n    ];\n\n    const variantClasses = {\n      primary: [\n        'bg-gradient-to-r from-primary to-primary-dark',\n        'text-white shadow-lg',\n        'hover:shadow-xl hover:shadow-primary/25',\n        'focus-visible:ring-primary',\n        'glow-primary',\n      ],\n      secondary: [\n        'bg-gradient-to-r from-secondary to-blue-600',\n        'text-white shadow-lg',\n        'hover:shadow-xl hover:shadow-secondary/25',\n        'focus-visible:ring-secondary',\n        'glow-secondary',\n      ],\n      accent: [\n        'bg-gradient-to-r from-accent to-orange-600',\n        'text-white shadow-lg',\n        'hover:shadow-xl hover:shadow-accent/25',\n        'focus-visible:ring-accent',\n        'glow-accent',\n      ],\n      ghost: [\n        'bg-transparent text-text-primary',\n        'hover:bg-bg-secondary',\n        'focus-visible:ring-primary',\n      ],\n      outline: [\n        'border-2 border-primary bg-transparent',\n        'text-primary',\n        'hover:bg-primary hover:text-black',\n        'focus-visible:ring-primary',\n      ],\n    };\n\n    const sizeClasses = {\n      sm: 'px-3 py-1.5 text-sm rounded-md',\n      md: 'px-4 py-2 text-base rounded-lg',\n      lg: 'px-6 py-3 text-lg rounded-lg',\n      xl: 'px-8 py-4 text-xl rounded-xl',\n    };\n\n    const widthClasses = fullWidth ? 'w-full' : '';\n\n    return (\n      <button\n        ref={ref}\n        type={type}\n        className={cn(\n          baseClasses,\n          variantClasses[variant],\n          sizeClasses[size],\n          widthClasses,\n          className\n        )}\n        disabled={disabled || loading}\n        onClick={onClick}\n        {...props}\n      >\n        {/* Loading spinner */}\n        {loading && (\n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\" />\n          </div>\n        )}\n        \n        {/* Button content */}\n        <span className={cn('flex items-center gap-2', loading && 'opacity-0')}>\n          {leftIcon && <span className=\"flex-shrink-0\">{leftIcon}</span>}\n          {children}\n          {rightIcon && <span className=\"flex-shrink-0\">{rightIcon}</span>}\n        </span>\n        \n        {/* Hover effect overlay */}\n        <div className=\"absolute inset-0 bg-white/10 opacity-0 hover:opacity-100 transition-opacity duration-200\" />\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n\n// Icon button variant\nexport const IconButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'leftIcon' | 'rightIcon'> & { icon: React.ReactNode }>(\n  ({ icon, className, size = 'md', ...props }, ref) => {\n    const iconSizeClasses = {\n      sm: 'w-8 h-8',\n      md: 'w-10 h-10',\n      lg: 'w-12 h-12',\n      xl: 'w-14 h-14',\n    };\n\n    return (\n      <Button\n        ref={ref}\n        className={cn(\n          'p-0 rounded-full',\n          iconSizeClasses[size],\n          className\n        )}\n        size={size}\n        {...props}\n      >\n        {icon}\n      </Button>\n    );\n  }\n);\n\nIconButton.displayName = 'IconButton';\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,SAAS;YACP;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YACT;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;YACL;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe,YAAY,WAAW;IAE5C,qBACE,8OAAC;QACC,KAAK;QACL,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,cACA;QAEF,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAGR,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAKnB,8OAAC;gBAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B,WAAW;;oBACvD,0BAAY,8OAAC;wBAAK,WAAU;kCAAiB;;;;;;oBAC7C;oBACA,2BAAa,8OAAC;wBAAK,WAAU;kCAAiB;;;;;;;;;;;;0BAIjD,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;AAGF,OAAO,WAAW,GAAG;;AAKd,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACjC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,IAAI,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oBACA,eAAe,CAAC,KAAK,EACrB;QAEF,MAAM;QACL,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/hooks/usePerformance.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { PerformanceMetrics } from '@/types';\n\ninterface PerformanceConfig {\n  enableMonitoring: boolean;\n  fpsTarget: number;\n  memoryThreshold: number;\n  autoOptimize: boolean;\n}\n\nconst defaultConfig: PerformanceConfig = {\n  enableMonitoring: true,\n  fpsTarget: 60,\n  memoryThreshold: 100, // MB\n  autoOptimize: true,\n};\n\nexport function usePerformance(config: Partial<PerformanceConfig> = {}) {\n  const finalConfig = { ...defaultConfig, ...config };\n  const [metrics, setMetrics] = useState<PerformanceMetrics>({\n    fps: 0,\n    memoryUsage: 0,\n    renderTime: 0,\n    triangleCount: 0,\n  });\n  \n  const [performanceLevel, setPerformanceLevel] = useState<'high' | 'medium' | 'low'>('high');\n  const frameCount = useRef(0);\n  const lastTime = useRef(performance.now());\n  const fpsHistory = useRef<number[]>([]);\n\n  // FPS monitoring\n  useFrame((state, delta) => {\n    if (!finalConfig.enableMonitoring) return;\n\n    frameCount.current++;\n    const currentTime = performance.now();\n    \n    // Calculate FPS every second\n    if (currentTime - lastTime.current >= 1000) {\n      const fps = Math.round((frameCount.current * 1000) / (currentTime - lastTime.current));\n      \n      // Keep FPS history for averaging\n      fpsHistory.current.push(fps);\n      if (fpsHistory.current.length > 10) {\n        fpsHistory.current.shift();\n      }\n      \n      const avgFps = fpsHistory.current.reduce((a, b) => a + b, 0) / fpsHistory.current.length;\n      \n      setMetrics(prev => ({\n        ...prev,\n        fps: Math.round(avgFps),\n        renderTime: delta * 1000, // Convert to milliseconds\n      }));\n      \n      // Auto-optimize performance level\n      if (finalConfig.autoOptimize) {\n        if (avgFps < finalConfig.fpsTarget * 0.6) {\n          setPerformanceLevel('low');\n        } else if (avgFps < finalConfig.fpsTarget * 0.8) {\n          setPerformanceLevel('medium');\n        } else {\n          setPerformanceLevel('high');\n        }\n      }\n      \n      frameCount.current = 0;\n      lastTime.current = currentTime;\n    }\n  });\n\n  // Memory monitoring\n  useEffect(() => {\n    if (!finalConfig.enableMonitoring || typeof window === 'undefined') return;\n\n    const monitorMemory = () => {\n      if ('memory' in performance) {\n        const memory = (performance as any).memory;\n        const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n        \n        setMetrics(prev => ({\n          ...prev,\n          memoryUsage: Math.round(usedMB),\n        }));\n      }\n    };\n\n    const interval = setInterval(monitorMemory, 2000);\n    return () => clearInterval(interval);\n  }, [finalConfig.enableMonitoring]);\n\n  // Performance optimization recommendations\n  const getOptimizationSuggestions = useCallback(() => {\n    const suggestions: string[] = [];\n    \n    if (metrics.fps < finalConfig.fpsTarget * 0.8) {\n      suggestions.push('Consider reducing shadow quality');\n      suggestions.push('Disable post-processing effects');\n      suggestions.push('Reduce model complexity');\n    }\n    \n    if (metrics.memoryUsage > finalConfig.memoryThreshold) {\n      suggestions.push('Enable texture compression');\n      suggestions.push('Implement model LOD (Level of Detail)');\n      suggestions.push('Use instanced rendering for repeated objects');\n    }\n    \n    if (metrics.renderTime > 16.67) { // 60fps = 16.67ms per frame\n      suggestions.push('Optimize shaders');\n      suggestions.push('Reduce draw calls');\n      suggestions.push('Use frustum culling');\n    }\n    \n    return suggestions;\n  }, [metrics, finalConfig]);\n\n  // Performance settings based on current level\n  const getPerformanceSettings = useCallback(() => {\n    switch (performanceLevel) {\n      case 'low':\n        return {\n          shadows: false,\n          antialias: false,\n          postProcessing: false,\n          particleCount: 50,\n          modelLOD: 'low',\n          textureQuality: 'low',\n        };\n      case 'medium':\n        return {\n          shadows: true,\n          antialias: false,\n          postProcessing: false,\n          particleCount: 100,\n          modelLOD: 'medium',\n          textureQuality: 'medium',\n        };\n      case 'high':\n      default:\n        return {\n          shadows: true,\n          antialias: true,\n          postProcessing: true,\n          particleCount: 200,\n          modelLOD: 'high',\n          textureQuality: 'high',\n        };\n    }\n  }, [performanceLevel]);\n\n  return {\n    metrics,\n    performanceLevel,\n    setPerformanceLevel,\n    getOptimizationSuggestions,\n    getPerformanceSettings,\n    isPerformanceGood: metrics.fps >= finalConfig.fpsTarget * 0.8,\n  };\n}\n\n// Hook for device capability detection\nexport function useDeviceCapabilities() {\n  const [capabilities, setCapabilities] = useState({\n    webgl2: false,\n    maxTextureSize: 0,\n    maxVertexUniforms: 0,\n    maxFragmentUniforms: 0,\n    extensions: [] as string[],\n    isMobile: false,\n    isLowEnd: false,\n  });\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const canvas = document.createElement('canvas');\n    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');\n    \n    if (gl) {\n      const webgl2 = gl instanceof WebGL2RenderingContext;\n      const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);\n      const maxVertexUniforms = gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS);\n      const maxFragmentUniforms = gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS);\n      const extensions = gl.getSupportedExtensions() || [];\n      \n      // Detect mobile devices\n      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n        navigator.userAgent\n      );\n      \n      // Detect low-end devices\n      const isLowEnd = isMobile || maxTextureSize < 4096 || navigator.hardwareConcurrency < 4;\n      \n      setCapabilities({\n        webgl2,\n        maxTextureSize,\n        maxVertexUniforms,\n        maxFragmentUniforms,\n        extensions,\n        isMobile,\n        isLowEnd,\n      });\n    }\n  }, []);\n\n  return capabilities;\n}\n\n// Hook for adaptive quality based on performance\nexport function useAdaptiveQuality() {\n  const { performanceLevel, getPerformanceSettings } = usePerformance();\n  const { isLowEnd } = useDeviceCapabilities();\n  \n  const settings = getPerformanceSettings();\n  \n  // Override settings for low-end devices\n  if (isLowEnd) {\n    return {\n      ...settings,\n      shadows: false,\n      antialias: false,\n      postProcessing: false,\n      particleCount: Math.min(settings.particleCount, 25),\n      modelLOD: 'low',\n      textureQuality: 'low',\n    };\n  }\n  \n  return settings;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;AAaA,MAAM,gBAAmC;IACvC,kBAAkB;IAClB,WAAW;IACX,iBAAiB;IACjB,cAAc;AAChB;AAEO,SAAS,eAAe,SAAqC,CAAC,CAAC;IACpE,MAAM,cAAc;QAAE,GAAG,aAAa;QAAE,GAAG,MAAM;IAAC;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACzD,KAAK;QACL,aAAa;QACb,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACpF,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,YAAY,GAAG;IACvC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAY,EAAE;IAEtC,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO;QACf,IAAI,CAAC,YAAY,gBAAgB,EAAE;QAEnC,WAAW,OAAO;QAClB,MAAM,cAAc,YAAY,GAAG;QAEnC,6BAA6B;QAC7B,IAAI,cAAc,SAAS,OAAO,IAAI,MAAM;YAC1C,MAAM,MAAM,KAAK,KAAK,CAAC,AAAC,WAAW,OAAO,GAAG,OAAQ,CAAC,cAAc,SAAS,OAAO;YAEpF,iCAAiC;YACjC,WAAW,OAAO,CAAC,IAAI,CAAC;YACxB,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,IAAI;gBAClC,WAAW,OAAO,CAAC,KAAK;YAC1B;YAEA,MAAM,SAAS,WAAW,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,WAAW,OAAO,CAAC,MAAM;YAExF,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,KAAK,KAAK,KAAK,CAAC;oBAChB,YAAY,QAAQ;gBACtB,CAAC;YAED,kCAAkC;YAClC,IAAI,YAAY,YAAY,EAAE;gBAC5B,IAAI,SAAS,YAAY,SAAS,GAAG,KAAK;oBACxC,oBAAoB;gBACtB,OAAO,IAAI,SAAS,YAAY,SAAS,GAAG,KAAK;oBAC/C,oBAAoB;gBACtB,OAAO;oBACL,oBAAoB;gBACtB;YACF;YAEA,WAAW,OAAO,GAAG;YACrB,SAAS,OAAO,GAAG;QACrB;IACF;IAEA,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAoE;;QAEpE,MAAM;QAYN,MAAM;IAER,GAAG;QAAC,YAAY,gBAAgB;KAAC;IAEjC,2CAA2C;IAC3C,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,MAAM,cAAwB,EAAE;QAEhC,IAAI,QAAQ,GAAG,GAAG,YAAY,SAAS,GAAG,KAAK;YAC7C,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,IAAI,QAAQ,WAAW,GAAG,YAAY,eAAe,EAAE;YACrD,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,IAAI,QAAQ,UAAU,GAAG,OAAO;YAC9B,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,OAAO;IACT,GAAG;QAAC;QAAS;KAAY;IAEzB,8CAA8C;IAC9C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,gBAAgB;oBAChB,eAAe;oBACf,UAAU;oBACV,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,gBAAgB;oBAChB,eAAe;oBACf,UAAU;oBACV,gBAAgB;gBAClB;YACF,KAAK;YACL;gBACE,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,gBAAgB;oBAChB,eAAe;oBACf,UAAU;oBACV,gBAAgB;gBAClB;QACJ;IACF,GAAG;QAAC;KAAiB;IAErB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,mBAAmB,QAAQ,GAAG,IAAI,YAAY,SAAS,GAAG;IAC5D;AACF;AAGO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,QAAQ;QACR,gBAAgB;QAChB,mBAAmB;QACnB,qBAAqB;QACrB,YAAY,EAAE;QACd,UAAU;QACV,UAAU;IACZ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;QACN,MAAM;IA2BR,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,GAAG;IACrD,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,WAAW;IAEjB,wCAAwC;IACxC,IAAI,UAAU;QACZ,OAAO;YACL,GAAG,QAAQ;YACX,SAAS;YACT,WAAW;YACX,gBAAgB;YAChB,eAAe,KAAK,GAAG,CAAC,SAAS,aAAa,EAAE;YAChD,UAAU;YACV,gBAAgB;QAClB;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { HeroScene } from '@/components/3d/SceneManager';\nimport { Microchip } from '@/components/3d/Microchip';\nimport { MicrochipGrid } from '@/components/3d/MicrochipModel';\nimport { CPUModel } from '@/components/3d/PlaceholderModels';\nimport { InteractiveCamera } from '@/components/3d/InteractiveCamera';\nimport { Button } from '@/components/ui/Button';\nimport { useTheme } from '@/hooks/useTheme';\nimport { useAdaptiveQuality } from '@/hooks/usePerformance';\nimport { cn } from '@/lib/utils';\n\ninterface HeroSectionProps {\n  className?: string;\n}\n\nconst heroTexts = [\n  \"3D Portfolio\",\n  \"Interactive Design\",\n  \"Web Innovation\",\n  \"Digital Experience\"\n];\n\nexport function HeroSection({ className }: HeroSectionProps) {\n  const { toggleTheme, theme } = useTheme();\n  const qualitySettings = useAdaptiveQuality();\n  const [currentTextIndex, setCurrentTextIndex] = useState(0);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  // Cycle through hero texts\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentTextIndex((prev) => (prev + 1) % heroTexts.length);\n    }, 3000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Loading animation\n  useEffect(() => {\n    const timer = setTimeout(() => setIsLoaded(true), 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const scrollToSection = (sectionId: string) => {\n    document.getElementById(sectionId)?.scrollIntoView({ \n      behavior: 'smooth',\n      block: 'start'\n    });\n  };\n\n  return (\n    <section className={cn(\n      \"relative h-screen flex items-center justify-center overflow-hidden\",\n      \"bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary\",\n      className\n    )}>\n      {/* Animated Background Pattern */}\n      <div className=\"absolute inset-0\">\n        <div className=\"circuit-bg opacity-20 animate-pulse\" />\n        <div className=\"absolute inset-0 bg-gradient-to-t from-bg-primary/50 to-transparent\" />\n      </div>\n      \n      {/* 3D Scene */}\n      <div className=\"absolute inset-0 z-10\">\n        <HeroScene\n          enableControls={false}\n          enableEnvironment={true}\n          shadows={qualitySettings.shadows}\n          antialias={qualitySettings.antialias}\n          lighting=\"dramatic\"\n        >\n          {/* Interactive Camera */}\n          <InteractiveCamera\n            enableMouseTracking={true}\n            enableScrollTracking={true}\n            mouseSensitivity={0.3}\n            scrollSensitivity={0.2}\n            basePosition={[0, 0, 8]}\n            lookAt={[0, 0, 0]}\n            smoothing={0.08}\n          />\n          {/* Main hero microchip */}\n          <CPUModel\n            position={[0, 0, 0]}\n            scale={1.2}\n            animated={true}\n            glowEffect={true}\n          />\n          \n          {/* Floating microchips around the main one */}\n          <Microchip\n            position={[-3, 1, -2]}\n            scale={0.6}\n            animated={true}\n            glowEffect={true}\n          />\n          <Microchip\n            position={[3, -1, -2]}\n            scale={0.8}\n            animated={true}\n            glowEffect={true}\n          />\n          <Microchip\n            position={[0, 2, -3]}\n            scale={0.5}\n            animated={true}\n            glowEffect={true}\n          />\n          \n          {/* Background grid for depth */}\n          {qualitySettings.particleCount > 50 && (\n            <MicrochipGrid\n              count={Math.min(qualitySettings.particleCount / 4, 15)}\n              spread={20}\n              animated={true}\n            />\n          )}\n        </HeroScene>\n      </div>\n      \n      {/* Content Overlay */}\n      <div className=\"relative z-20 text-center px-4 max-w-6xl mx-auto\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key=\"hero-content\"\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 50 }}\n            transition={{ duration: 1, delay: 0.5 }}\n            className=\"space-y-8\"\n          >\n            {/* Animated Title */}\n            <div className=\"relative h-32 flex items-center justify-center\">\n              <AnimatePresence mode=\"wait\">\n                <motion.h1\n                  key={currentTextIndex}\n                  initial={{ opacity: 0, rotateX: 90 }}\n                  animate={{ opacity: 1, rotateX: 0 }}\n                  exit={{ opacity: 0, rotateX: -90 }}\n                  transition={{ duration: 0.8 }}\n                  className=\"text-6xl md:text-8xl lg:text-9xl font-bold text-glow\"\n                >\n                  <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n                    {heroTexts[currentTextIndex]}\n                  </span>\n                </motion.h1>\n              </AnimatePresence>\n            </div>\n            \n            {/* Subtitle */}\n            <motion.p\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1 }}\n              className=\"text-xl md:text-2xl lg:text-3xl text-text-secondary max-w-4xl mx-auto leading-relaxed\"\n            >\n              Welcome to an immersive 3D experience showcasing cutting-edge web development \n              with interactive microchip-themed design and modern technologies.\n            </motion.p>\n            \n            {/* Action Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.3 }}\n              className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\"\n            >\n              <Button\n                variant=\"primary\"\n                size=\"xl\"\n                className=\"animate-pulse-glow group\"\n                onClick={() => scrollToSection('about')}\n              >\n                <span className=\"group-hover:scale-110 transition-transform\">\n                  🚀 Explore Portfolio\n                </span>\n              </Button>\n              \n              <Button\n                variant=\"outline\"\n                size=\"xl\"\n                onClick={() => scrollToSection('projects')}\n                className=\"group\"\n              >\n                <span className=\"group-hover:scale-110 transition-transform\">\n                  💼 View Projects\n                </span>\n              </Button>\n              \n              <Button\n                variant=\"secondary\"\n                size=\"xl\"\n                onClick={() => scrollToSection('contact')}\n                className=\"group\"\n              >\n                <span className=\"group-hover:scale-110 transition-transform\">\n                  📧 Get In Touch\n                </span>\n              </Button>\n            </motion.div>\n            \n            {/* Tech Stack Indicators */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.6 }}\n              className=\"flex flex-wrap justify-center gap-4 mt-12\"\n            >\n              {['React', 'Three.js', 'TypeScript', 'Next.js', 'Tailwind'].map((tech, index) => (\n                <motion.div\n                  key={tech}\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 1.8 + (index * 0.1) }}\n                  className=\"chip-border px-4 py-2 text-sm font-medium text-primary bg-bg-secondary/50 backdrop-blur-sm\"\n                >\n                  {tech}\n                </motion.div>\n              ))}\n            </motion.div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n      \n      {/* Theme Toggle */}\n      <motion.div\n        initial={{ opacity: 0, x: 50 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.8, delay: 2 }}\n        className=\"absolute top-6 right-6 z-30\"\n      >\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={toggleTheme}\n          className=\"chip-border backdrop-blur-sm bg-bg-secondary/30 hover:bg-bg-secondary/50\"\n        >\n          <motion.span\n            key={theme}\n            initial={{ rotate: 180, opacity: 0 }}\n            animate={{ rotate: 0, opacity: 1 }}\n            transition={{ duration: 0.5 }}\n            className=\"text-2xl\"\n          >\n            {theme === 'dark' ? '☀️' : '🌙'}\n          </motion.span>\n        </Button>\n      </motion.div>\n      \n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, delay: 2.2 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"flex flex-col items-center space-y-2 cursor-pointer\"\n          onClick={() => scrollToSection('about')}\n        >\n          <div className=\"w-6 h-10 border-2 border-primary rounded-full flex justify-center relative overflow-hidden\">\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-primary rounded-full mt-2\"\n            />\n          </div>\n          <span className=\"text-xs text-text-secondary font-medium\">Scroll Down</span>\n        </motion.div>\n      </motion.div>\n      \n      {/* Loading Overlay */}\n      <AnimatePresence>\n        {!isLoaded && (\n          <motion.div\n            initial={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"absolute inset-0 z-50 bg-bg-primary flex items-center justify-center\"\n          >\n            <div className=\"text-center space-y-4\">\n              <motion.div\n                animate={{ rotate: 360 }}\n                transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                className=\"w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto\"\n              />\n              <motion.p\n                animate={{ opacity: [0.5, 1, 0.5] }}\n                transition={{ duration: 1.5, repeat: Infinity }}\n                className=\"text-primary font-medium\"\n              >\n                Initializing 3D Experience...\n              </motion.p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAkBA,MAAM,YAAY;IAChB;IACA;IACA;IACA;CACD;AAEM,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,kBAAkB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,oBAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM;QAC7D,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW,IAAM,YAAY,OAAO;QAClD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,SAAS,cAAc,CAAC,YAAY,eAAe;YACjD,UAAU;YACV,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,sEACA,oEACA;;0BAGA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wIAAA,CAAA,YAAS;oBACR,gBAAgB;oBAChB,mBAAmB;oBACnB,SAAS,gBAAgB,OAAO;oBAChC,WAAW,gBAAgB,SAAS;oBACpC,UAAS;;sCAGT,8OAAC,6IAAA,CAAA,oBAAiB;4BAChB,qBAAqB;4BACrB,sBAAsB;4BACtB,kBAAkB;4BAClB,mBAAmB;4BACnB,cAAc;gCAAC;gCAAG;gCAAG;6BAAE;4BACvB,QAAQ;gCAAC;gCAAG;gCAAG;6BAAE;4BACjB,WAAW;;;;;;sCAGb,8OAAC,6IAAA,CAAA,WAAQ;4BACP,UAAU;gCAAC;gCAAG;gCAAG;6BAAE;4BACnB,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;sCAId,8OAAC,qIAAA,CAAA,YAAS;4BACR,UAAU;gCAAC,CAAC;gCAAG;gCAAG,CAAC;6BAAE;4BACrB,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;sCAEd,8OAAC,qIAAA,CAAA,YAAS;4BACR,UAAU;gCAAC;gCAAG,CAAC;gCAAG,CAAC;6BAAE;4BACrB,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;sCAEd,8OAAC,qIAAA,CAAA,YAAS;4BACR,UAAU;gCAAC;gCAAG;gCAAG,CAAC;6BAAE;4BACpB,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;wBAIb,gBAAgB,aAAa,GAAG,oBAC/B,8OAAC,0IAAA,CAAA,gBAAa;4BACZ,OAAO,KAAK,GAAG,CAAC,gBAAgB,aAAa,GAAG,GAAG;4BACnD,QAAQ;4BACR,UAAU;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS,WAAW,IAAI;4BAAG,GAAG,WAAW,IAAI;wBAAG;wBAC3D,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;wBACtC,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oCAAC,MAAK;8CACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCAER,SAAS;4CAAE,SAAS;4CAAG,SAAS;wCAAG;wCACnC,SAAS;4CAAE,SAAS;4CAAG,SAAS;wCAAE;wCAClC,MAAM;4CAAE,SAAS;4CAAG,SAAS,CAAC;wCAAG;wCACjC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDACb,SAAS,CAAC,iBAAiB;;;;;;uCARzB;;;;;;;;;;;;;;;0CAeX,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAE;gCACtC,WAAU;0CACX;;;;;;0CAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,gBAAgB;kDAE/B,cAAA,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;;;;;;kDAK/D,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;;;;;;kDAK/D,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;;;;;;;;;;;;0CAOjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAET;oCAAC;oCAAS;oCAAY;oCAAc;oCAAW;iCAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAO,QAAQ;wCAAK;wCACxD,WAAU;kDAET;uCANI;;;;;;;;;;;uBArFP;;;;;;;;;;;;;;;0BAoGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAE;gBACtC,WAAU;0BAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBAEV,SAAS;4BAAE,QAAQ;4BAAK,SAAS;wBAAE;wBACnC,SAAS;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBACjC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET,UAAU,SAAS,OAAO;uBANtB;;;;;;;;;;;;;;;0BAYX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;oBACV,SAAS,IAAM,gBAAgB;;sCAE/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;sCAGd,8OAAC;4BAAK,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAK9D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,CAAC,0BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ;gCAAI;gCACvB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAS;gCAC5D,WAAU;;;;;;0CAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCAAC;gCAClC,YAAY;oCAAE,UAAU;oCAAK,QAAQ;gCAAS;gCAC9C,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { HeroSection } from '@/components/sections/HeroSection';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <HeroSection />\n\n      {/* Placeholder sections for future development */}\n      <section id=\"about\" className=\"min-h-screen flex items-center justify-center bg-bg-secondary\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold text-primary mb-4\">About Section</h2>\n          <p className=\"text-text-secondary\">Coming soon in Phase 4...</p>\n        </div>\n      </section>\n\n      <section id=\"projects\" className=\"min-h-screen flex items-center justify-center bg-bg-tertiary\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold text-secondary mb-4\">Projects Section</h2>\n          <p className=\"text-text-secondary\">Coming soon in Phase 5...</p>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,6IAAA,CAAA,cAAW;;;;;0BAGZ,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;0BAIvC,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;;;;;;;AAK7C", "debugId": null}}]}
@import "tailwindcss";

/* Custom CSS Variables for Microchip Theme */
:root {
  /* Primary Colors - Circuit Board Theme */
  --color-primary: #00ff88;
  --color-primary-dark: #00cc6a;
  --color-secondary: #0088ff;
  --color-accent: #ff6b00;

  /* Background Colors */
  --color-bg-primary: #0a0a0a;
  --color-bg-secondary: #1a1a1a;
  --color-bg-tertiary: #2a2a2a;

  /* Text Colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: #cccccc;
  --color-text-muted: #888888;

  /* Circuit/Chip Colors */
  --color-circuit: #00ff88;
  --color-chip-gold: #ffd700;
  --color-chip-silver: #c0c0c0;
  --color-chip-copper: #b87333;

  /* Glow Effects */
  --glow-primary: 0 0 20px var(--color-primary);
  --glow-secondary: 0 0 20px var(--color-secondary);
  --glow-accent: 0 0 20px var(--color-accent);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 2rem;
  --spacing-xl: 4rem;

  /* Legacy variables for compatibility */
  --background: var(--color-bg-primary);
  --foreground: var(--color-text-primary);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Dark Theme (Default) */
[data-theme="dark"] {
  --color-bg-primary: #0a0a0a;
  --color-bg-secondary: #1a1a1a;
  --color-bg-tertiary: #2a2a2a;
  --color-text-primary: #ffffff;
  --color-text-secondary: #cccccc;
}

/* Light Theme */
[data-theme="light"] {
  --color-bg-primary: #f8f9fa;
  --color-bg-secondary: #e9ecef;
  --color-bg-tertiary: #dee2e6;
  --color-text-primary: #212529;
  --color-text-secondary: #495057;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-dark);
}

/* Utility Classes */
.glow-primary {
  box-shadow: var(--glow-primary);
}

.glow-secondary {
  box-shadow: var(--glow-secondary);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

.text-glow {
  text-shadow: 0 0 10px currentColor;
}

/* Circuit Pattern Background */
.circuit-bg {
  background-image:
    radial-gradient(circle at 25% 25%, var(--color-primary) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, var(--color-secondary) 1px, transparent 1px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
}

/* Microchip Border Effect */
.chip-border {
  border: 2px solid var(--color-chip-gold);
  border-radius: 8px;
  position: relative;
}

.chip-border::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(45deg, var(--color-primary), var(--color-secondary), var(--color-accent));
  border-radius: 10px;
  z-index: -1;
  opacity: 0.3;
}

/* Animation Classes */
.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 5px var(--color-primary);
  }
  to {
    box-shadow: 0 0 20px var(--color-primary), 0 0 30px var(--color-primary);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-rotate-slow {
  animation: rotateSlow 10s linear infinite;
}

@keyframes rotateSlow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus Styles for Accessibility */
.focus-visible:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 3D Transform Utilities */
.transform-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

'use client';

import { useRef, useState } from 'react';
import { useFrame, useLoader } from '@react-three/fiber';
import { RoundedBox, Text, Html } from '@react-three/drei';
import { TextureLoader } from 'three';
import { Mesh, Group } from 'three';

interface PhotoFrameProps {
  position?: [number, number, number];
  scale?: number;
  rotation?: [number, number, number];
  imageUrl?: string;
  animated?: boolean;
  glowEffect?: boolean;
  frameStyle?: 'modern' | 'chip' | 'holographic' | 'circuit';
  onClick?: () => void;
  onHover?: (hovered: boolean) => void;
  primaryColor?: string;
  secondaryColor?: string;
}

export function PhotoFrame({
  position = [0, 0, 0],
  scale = 1,
  rotation = [0, 0, 0],
  imageUrl = '/images/developer-photo.jpg',
  animated = true,
  glowEffect = true,
  frameStyle = 'chip',
  onClick,
  onHover,
  primaryColor = '#00ff88',
  secondaryColor = '#0088ff',
}: PhotoFrameProps) {
  const groupRef = useRef<Group>(null);
  const frameRef = useRef<Mesh>(null);
  const [hovered, setHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Try to load texture, fallback to placeholder
  let texture;
  try {
    texture = useLoader(TextureLoader, imageUrl);
    if (texture && !imageLoaded) {
      setImageLoaded(true);
    }
  } catch (error) {
    console.log('Image not found, using placeholder');
    texture = null;
  }

  // Animation loop
  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    
    // Gentle rotation animation
    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    
    // Floating animation
    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.05;
    
    // Hover effect
    if (hovered && frameRef.current) {
      frameRef.current.scale.setScalar(1.05);
    } else if (frameRef.current) {
      frameRef.current.scale.setScalar(1);
    }
  });

  const handlePointerOver = () => {
    setHovered(true);
    onHover?.(true);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    setHovered(false);
    onHover?.(false);
    document.body.style.cursor = 'auto';
  };

  const getFrameStyle = () => {
    switch (frameStyle) {
      case 'modern':
        return {
          frameColor: '#ffffff',
          frameMetalness: 0.9,
          frameRoughness: 0.1,
          borderWidth: 0.1,
        };
      case 'chip':
        return {
          frameColor: themeConfig.primaryColor,
          frameMetalness: 0.8,
          frameRoughness: 0.2,
          borderWidth: 0.15,
        };
      case 'holographic':
        return {
          frameColor: '#00ffff',
          frameMetalness: 1,
          frameRoughness: 0,
          borderWidth: 0.05,
        };
      case 'circuit':
        return {
          frameColor: '#1a4a1a',
          frameMetalness: 0.3,
          frameRoughness: 0.7,
          borderWidth: 0.2,
        };
      default:
        return {
          frameColor: themeConfig.primaryColor,
          frameMetalness: 0.8,
          frameRoughness: 0.2,
          borderWidth: 0.15,
        };
    }
  };

  const frameStyle_config = getFrameStyle();

  return (
    <group
      ref={groupRef}
      position={position}
      scale={scale}
      rotation={rotation}
      onClick={onClick}
      onPointerOver={handlePointerOver}
      onPointerOut={handlePointerOut}
    >
      {/* Main Frame */}
      <RoundedBox
        ref={frameRef}
        args={[3, 4, 0.3]}
        radius={0.1}
        castShadow
        receiveShadow
      >
        <meshStandardMaterial
          color={frameStyle_config.frameColor}
          metalness={frameStyle_config.frameMetalness}
          roughness={frameStyle_config.frameRoughness}
          emissive={glowEffect && hovered ? themeConfig.primaryColor : '#000000'}
          emissiveIntensity={hovered ? 0.2 : 0}
        />
      </RoundedBox>

      {/* Photo/Screen */}
      <RoundedBox
        args={[2.6, 3.6, 0.05]}
        radius={0.05}
        position={[0, 0, 0.18]}
        castShadow
      >
        <meshStandardMaterial
          color={texture ? '#ffffff' : '#2a2a2a'}
          map={texture}
          emissive={!texture ? themeConfig.secondaryColor : '#000000'}
          emissiveIntensity={!texture ? 0.1 : 0}
        />
      </RoundedBox>

      {/* Circuit Pattern Frame (for circuit style) */}
      {frameStyle === 'circuit' && (
        <>
          {/* Circuit traces */}
          {Array.from({ length: 12 }, (_, i) => (
            <RoundedBox
              key={`trace-${i}`}
              args={[0.02, Math.random() * 2 + 1, 0.01]}
              radius={0.005}
              position={[
                -1.3 + (i * 0.22),
                (Math.random() - 0.5) * 3,
                0.16
              ]}
              castShadow
            >
              <meshStandardMaterial
                color={themeConfig.primaryColor}
                emissive={themeConfig.primaryColor}
                emissiveIntensity={0.3}
              />
            </RoundedBox>
          ))}
          
          {/* Corner connectors */}
          {[[-1.3, 1.7], [1.3, 1.7], [-1.3, -1.7], [1.3, -1.7]].map(([x, y], i) => (
            <RoundedBox
              key={`connector-${i}`}
              args={[0.1, 0.1, 0.05]}
              radius={0.02}
              position={[x, y, 0.16]}
              castShadow
            >
              <meshStandardMaterial
                color="#ffd700"
                metalness={1}
                roughness={0.1}
              />
            </RoundedBox>
          ))}
        </>
      )}

      {/* Holographic effect (for holographic style) */}
      {frameStyle === 'holographic' && hovered && (
        <RoundedBox
          args={[3.2, 4.2, 0.35]}
          radius={0.1}
          position={[0, 0, 0]}
        >
          <meshBasicMaterial
            color="#00ffff"
            transparent
            opacity={0.1}
          />
        </RoundedBox>
      )}

      {/* Chip pins (for chip style) */}
      {frameStyle === 'chip' && (
        <>
          {Array.from({ length: 16 }, (_, i) => {
            const side = Math.floor(i / 4);
            const pinIndex = i % 4;
            let pinPosition: [number, number, number];
            
            switch (side) {
              case 0: // Top
                pinPosition = [-0.9 + (pinIndex * 0.6), 2.1, 0];
                break;
              case 1: // Right
                pinPosition = [1.6, 1.2 - (pinIndex * 0.8), 0];
                break;
              case 2: // Bottom
                pinPosition = [0.9 - (pinIndex * 0.6), -2.1, 0];
                break;
              case 3: // Left
                pinPosition = [-1.6, -1.2 + (pinIndex * 0.8), 0];
                break;
              default:
                pinPosition = [0, 0, 0];
            }

            return (
              <RoundedBox
                key={`pin-${i}`}
                args={[0.08, 0.3, 0.05]}
                radius={0.01}
                position={pinPosition}
                castShadow
              >
                <meshStandardMaterial
                  color="#ffd700"
                  metalness={1}
                  roughness={0.1}
                />
              </RoundedBox>
            );
          })}
        </>
      )}

      {/* Loading indicator when image is not available */}
      {!texture && (
        <Html
          position={[0, 0, 0.2]}
          center
          distanceFactor={10}
        >
          <div className="text-center p-4 bg-bg-secondary/80 rounded-lg backdrop-blur-sm">
            <div className="w-16 h-16 mx-auto mb-2 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
              <span className="text-2xl">👨‍💻</span>
            </div>
            <p className="text-sm text-text-secondary">Developer Photo</p>
          </div>
        </Html>
      )}

      {/* Glow effect */}
      {glowEffect && hovered && (
        <RoundedBox
          args={[3.4, 4.4, 0.4]}
          radius={0.1}
          position={[0, 0, 0]}
        >
          <meshBasicMaterial
            color={themeConfig.primaryColor}
            transparent
            opacity={0.1}
          />
        </RoundedBox>
      )}
    </group>
  );
}

// Floating photo gallery component
export function PhotoGallery({
  photos = [],
  position = [0, 0, 0],
  spacing = 2,
}: {
  photos?: string[];
  position?: [number, number, number];
  spacing?: number;
}) {
  const groupRef = useRef<Group>(null);

  useFrame((state) => {
    if (!groupRef.current) return;
    groupRef.current.rotation.y = state.clock.elapsedTime * 0.1;
  });

  return (
    <group ref={groupRef} position={position}>
      {photos.map((photo, index) => {
        const angle = (index / photos.length) * Math.PI * 2;
        const x = Math.cos(angle) * spacing;
        const z = Math.sin(angle) * spacing;
        
        return (
          <PhotoFrame
            key={index}
            position={[x, 0, z]}
            scale={0.5}
            imageUrl={photo}
            frameStyle="modern"
            animated={false}
          />
        );
      })}
    </group>
  );
}

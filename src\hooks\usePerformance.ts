'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { PerformanceMetrics } from '@/types';

interface PerformanceConfig {
  enableMonitoring: boolean;
  fpsTarget: number;
  memoryThreshold: number;
  autoOptimize: boolean;
}

const defaultConfig: PerformanceConfig = {
  enableMonitoring: true,
  fpsTarget: 60,
  memoryThreshold: 100, // MB
  autoOptimize: true,
};

export function usePerformance(config: Partial<PerformanceConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config };
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    memoryUsage: 0,
    renderTime: 0,
    triangleCount: 0,
  });
  
  const [performanceLevel, setPerformanceLevel] = useState<'high' | 'medium' | 'low'>('high');
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const fpsHistory = useRef<number[]>([]);

  // FPS monitoring
  useFrame((state, delta) => {
    if (!finalConfig.enableMonitoring) return;

    frameCount.current++;
    const currentTime = performance.now();
    
    // Calculate FPS every second
    if (currentTime - lastTime.current >= 1000) {
      const fps = Math.round((frameCount.current * 1000) / (currentTime - lastTime.current));
      
      // Keep FPS history for averaging
      fpsHistory.current.push(fps);
      if (fpsHistory.current.length > 10) {
        fpsHistory.current.shift();
      }
      
      const avgFps = fpsHistory.current.reduce((a, b) => a + b, 0) / fpsHistory.current.length;
      
      setMetrics(prev => ({
        ...prev,
        fps: Math.round(avgFps),
        renderTime: delta * 1000, // Convert to milliseconds
      }));
      
      // Auto-optimize performance level
      if (finalConfig.autoOptimize) {
        if (avgFps < finalConfig.fpsTarget * 0.6) {
          setPerformanceLevel('low');
        } else if (avgFps < finalConfig.fpsTarget * 0.8) {
          setPerformanceLevel('medium');
        } else {
          setPerformanceLevel('high');
        }
      }
      
      frameCount.current = 0;
      lastTime.current = currentTime;
    }
  });

  // Memory monitoring
  useEffect(() => {
    if (!finalConfig.enableMonitoring || typeof window === 'undefined') return;

    const monitorMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        
        setMetrics(prev => ({
          ...prev,
          memoryUsage: Math.round(usedMB),
        }));
      }
    };

    const interval = setInterval(monitorMemory, 2000);
    return () => clearInterval(interval);
  }, [finalConfig.enableMonitoring]);

  // Performance optimization recommendations
  const getOptimizationSuggestions = useCallback(() => {
    const suggestions: string[] = [];
    
    if (metrics.fps < finalConfig.fpsTarget * 0.8) {
      suggestions.push('Consider reducing shadow quality');
      suggestions.push('Disable post-processing effects');
      suggestions.push('Reduce model complexity');
    }
    
    if (metrics.memoryUsage > finalConfig.memoryThreshold) {
      suggestions.push('Enable texture compression');
      suggestions.push('Implement model LOD (Level of Detail)');
      suggestions.push('Use instanced rendering for repeated objects');
    }
    
    if (metrics.renderTime > 16.67) { // 60fps = 16.67ms per frame
      suggestions.push('Optimize shaders');
      suggestions.push('Reduce draw calls');
      suggestions.push('Use frustum culling');
    }
    
    return suggestions;
  }, [metrics, finalConfig]);

  // Performance settings based on current level
  const getPerformanceSettings = useCallback(() => {
    switch (performanceLevel) {
      case 'low':
        return {
          shadows: false,
          antialias: false,
          postProcessing: false,
          particleCount: 50,
          modelLOD: 'low',
          textureQuality: 'low',
        };
      case 'medium':
        return {
          shadows: true,
          antialias: false,
          postProcessing: false,
          particleCount: 100,
          modelLOD: 'medium',
          textureQuality: 'medium',
        };
      case 'high':
      default:
        return {
          shadows: true,
          antialias: true,
          postProcessing: true,
          particleCount: 200,
          modelLOD: 'high',
          textureQuality: 'high',
        };
    }
  }, [performanceLevel]);

  return {
    metrics,
    performanceLevel,
    setPerformanceLevel,
    getOptimizationSuggestions,
    getPerformanceSettings,
    isPerformanceGood: metrics.fps >= finalConfig.fpsTarget * 0.8,
  };
}

// Hook for device capability detection
export function useDeviceCapabilities() {
  const [capabilities, setCapabilities] = useState({
    webgl2: false,
    maxTextureSize: 0,
    maxVertexUniforms: 0,
    maxFragmentUniforms: 0,
    extensions: [] as string[],
    isMobile: false,
    isLowEnd: false,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
    
    if (gl) {
      const webgl2 = gl instanceof WebGL2RenderingContext;
      const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
      const maxVertexUniforms = gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS);
      const maxFragmentUniforms = gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS);
      const extensions = gl.getSupportedExtensions() || [];
      
      // Detect mobile devices
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
      
      // Detect low-end devices
      const isLowEnd = isMobile || maxTextureSize < 4096 || navigator.hardwareConcurrency < 4;
      
      setCapabilities({
        webgl2,
        maxTextureSize,
        maxVertexUniforms,
        maxFragmentUniforms,
        extensions,
        isMobile,
        isLowEnd,
      });
    }
  }, []);

  return capabilities;
}

// Hook for adaptive quality based on performance
export function useAdaptiveQuality() {
  const { performanceLevel, getPerformanceSettings } = usePerformance();
  const { isLowEnd } = useDeviceCapabilities();
  
  const settings = getPerformanceSettings();
  
  // Override settings for low-end devices
  if (isLowEnd) {
    return {
      ...settings,
      shadows: false,
      antialias: false,
      postProcessing: false,
      particleCount: Math.min(settings.particleCount, 25),
      modelLOD: 'low',
      textureQuality: 'low',
    };
  }
  
  return settings;
}

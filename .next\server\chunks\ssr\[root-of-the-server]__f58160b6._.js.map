{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/SceneManager.tsx"], "sourcesContent": ["'use client';\n\nimport { Canvas } from '@react-three/fiber';\nimport { Suspense, useRef, useEffect } from 'react';\nimport { \n  OrbitControls, \n  Environment, \n  PerspectiveCamera, \n  Stats,\n  AdaptiveDpr,\n  AdaptiveEvents,\n  BakeShadows,\n  Preload\n} from '@react-three/drei';\nimport { BaseComponentProps } from '@/types';\nimport { cn } from '@/lib/utils';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface SceneManagerProps extends BaseComponentProps {\n  cameraPosition?: [number, number, number];\n  cameraTarget?: [number, number, number];\n  enableControls?: boolean;\n  enableEnvironment?: boolean;\n  enableStats?: boolean;\n  backgroundColor?: string;\n  shadows?: boolean;\n  antialias?: boolean;\n  performance?: {\n    min: number;\n    max: number;\n    debounce: number;\n  };\n  lighting?: 'default' | 'studio' | 'dramatic' | 'soft';\n  postProcessing?: boolean;\n}\n\n// Lighting presets\nconst lightingPresets = {\n  default: {\n    ambient: { intensity: 0.4, color: '#ffffff' },\n    directional: { \n      intensity: 1, \n      position: [10, 10, 5], \n      color: '#ffffff',\n      castShadow: true \n    },\n    point: { intensity: 0.5, position: [-10, -10, -10], color: '#0088ff' },\n  },\n  studio: {\n    ambient: { intensity: 0.6, color: '#ffffff' },\n    directional: { \n      intensity: 0.8, \n      position: [5, 10, 5], \n      color: '#ffffff',\n      castShadow: true \n    },\n    point: { intensity: 0.3, position: [-5, 5, -5], color: '#00ff88' },\n  },\n  dramatic: {\n    ambient: { intensity: 0.2, color: '#1a1a2e' },\n    directional: { \n      intensity: 1.5, \n      position: [15, 15, 10], \n      color: '#ffffff',\n      castShadow: true \n    },\n    point: { intensity: 0.8, position: [-15, -5, -15], color: '#ff6b00' },\n  },\n  soft: {\n    ambient: { intensity: 0.8, color: '#f0f0f0' },\n    directional: { \n      intensity: 0.6, \n      position: [8, 12, 8], \n      color: '#ffffff',\n      castShadow: false \n    },\n    point: { intensity: 0.2, position: [-8, 8, -8], color: '#0088ff' },\n  },\n};\n\nfunction SceneLighting({ preset = 'default' }: { preset: keyof typeof lightingPresets }) {\n  const lighting = lightingPresets[preset];\n  \n  return (\n    <>\n      <ambientLight \n        intensity={lighting.ambient.intensity} \n        color={lighting.ambient.color} \n      />\n      <directionalLight\n        position={lighting.directional.position as [number, number, number]}\n        intensity={lighting.directional.intensity}\n        color={lighting.directional.color}\n        castShadow={lighting.directional.castShadow}\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n        shadow-camera-far={50}\n        shadow-camera-left={-20}\n        shadow-camera-right={20}\n        shadow-camera-top={20}\n        shadow-camera-bottom={-20}\n      />\n      <pointLight \n        position={lighting.point.position as [number, number, number]}\n        intensity={lighting.point.intensity}\n        color={lighting.point.color}\n      />\n    </>\n  );\n}\n\nfunction SceneEffects({ \n  enableEnvironment, \n  backgroundColor \n}: { \n  enableEnvironment?: boolean; \n  backgroundColor?: string; \n}) {\n  const { theme } = useTheme();\n  \n  return (\n    <>\n      {enableEnvironment && (\n        <Environment \n          preset={theme === 'dark' ? 'night' : 'city'} \n          background={false}\n          intensity={0.5}\n        />\n      )}\n      \n      {backgroundColor && (\n        <color attach=\"background\" args={[backgroundColor]} />\n      )}\n      \n      {/* Fog for depth */}\n      <fog attach=\"fog\" args={[theme === 'dark' ? '#0a0a0a' : '#f0f0f0', 10, 50]} />\n    </>\n  );\n}\n\nexport function SceneManager({\n  children,\n  className,\n  cameraPosition = [0, 0, 8],\n  cameraTarget = [0, 0, 0],\n  enableControls = true,\n  enableEnvironment = true,\n  enableStats = false,\n  backgroundColor,\n  shadows = true,\n  antialias = true,\n  performance = { min: 0.5, max: 1, debounce: 200 },\n  lighting = 'default',\n  postProcessing = false,\n}: SceneManagerProps) {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const { themeConfig } = useTheme();\n\n  // Performance monitoring\n  useEffect(() => {\n    if (enableStats && typeof window !== 'undefined') {\n      console.log('3D Scene Performance Monitoring Enabled');\n    }\n  }, [enableStats]);\n\n  return (\n    <div className={cn('w-full h-full relative', className)}>\n      <Canvas\n        ref={canvasRef}\n        shadows={shadows}\n        gl={{\n          antialias,\n          alpha: true,\n          powerPreference: 'high-performance',\n          stencil: false,\n          depth: true,\n        }}\n        performance={performance}\n        dpr={[1, 2]}\n        camera={{\n          position: cameraPosition,\n          fov: 75,\n          near: 0.1,\n          far: 1000,\n        }}\n      >\n        {/* Performance optimizations */}\n        <AdaptiveDpr pixelated />\n        <AdaptiveEvents />\n        {shadows && <BakeShadows />}\n        \n        {/* Camera setup */}\n        <PerspectiveCamera\n          makeDefault\n          position={cameraPosition}\n          fov={75}\n          near={0.1}\n          far={1000}\n        />\n        \n        {/* Lighting */}\n        <SceneLighting preset={lighting} />\n        \n        {/* Scene effects */}\n        <SceneEffects \n          enableEnvironment={enableEnvironment}\n          backgroundColor={backgroundColor}\n        />\n        \n        {/* Controls */}\n        {enableControls && (\n          <OrbitControls\n            target={cameraTarget}\n            enablePan={true}\n            enableZoom={true}\n            enableRotate={true}\n            minDistance={2}\n            maxDistance={20}\n            maxPolarAngle={Math.PI / 2}\n            enableDamping={true}\n            dampingFactor={0.05}\n          />\n        )}\n        \n        {/* Performance stats */}\n        {enableStats && <Stats />}\n        \n        {/* Scene content */}\n        <Suspense fallback={null}>\n          {children}\n        </Suspense>\n        \n        {/* Preload common assets */}\n        <Preload all />\n      </Canvas>\n      \n      {/* Performance indicator */}\n      {enableStats && (\n        <div className=\"absolute top-4 left-4 bg-black/50 text-white p-2 rounded text-xs\">\n          <div>Theme: {themeConfig.theme}</div>\n          <div>Shadows: {shadows ? 'On' : 'Off'}</div>\n          <div>Lighting: {lighting}</div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n// Specialized scene for homepage hero\nexport function HeroScene({ children, ...props }: SceneManagerProps) {\n  return (\n    <SceneManager\n      lighting=\"dramatic\"\n      enableEnvironment={true}\n      shadows={true}\n      enableStats={false}\n      performance={{ min: 0.8, max: 1, debounce: 100 }}\n      {...props}\n    >\n      {children}\n    </SceneManager>\n  );\n}\n\n// Specialized scene for project showcase\nexport function ProjectScene({ children, ...props }: SceneManagerProps) {\n  return (\n    <SceneManager\n      lighting=\"studio\"\n      enableEnvironment={false}\n      shadows={true}\n      enableStats={false}\n      performance={{ min: 0.6, max: 1, debounce: 150 }}\n      {...props}\n    >\n      {children}\n    </SceneManager>\n  );\n}\n\n// Specialized scene for about section\nexport function AboutScene({ children, ...props }: SceneManagerProps) {\n  return (\n    <SceneManager\n      lighting=\"soft\"\n      enableEnvironment={true}\n      shadows={false}\n      enableStats={false}\n      performance={{ min: 0.5, max: 1, debounce: 200 }}\n      {...props}\n    >\n      {children}\n    </SceneManager>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAhBA;;;;;;;AAoCA,mBAAmB;AACnB,MAAM,kBAAkB;IACtB,SAAS;QACP,SAAS;YAAE,WAAW;YAAK,OAAO;QAAU;QAC5C,aAAa;YACX,WAAW;YACX,UAAU;gBAAC;gBAAI;gBAAI;aAAE;YACrB,OAAO;YACP,YAAY;QACd;QACA,OAAO;YAAE,WAAW;YAAK,UAAU;gBAAC,CAAC;gBAAI,CAAC;gBAAI,CAAC;aAAG;YAAE,OAAO;QAAU;IACvE;IACA,QAAQ;QACN,SAAS;YAAE,WAAW;YAAK,OAAO;QAAU;QAC5C,aAAa;YACX,WAAW;YACX,UAAU;gBAAC;gBAAG;gBAAI;aAAE;YACpB,OAAO;YACP,YAAY;QACd;QACA,OAAO;YAAE,WAAW;YAAK,UAAU;gBAAC,CAAC;gBAAG;gBAAG,CAAC;aAAE;YAAE,OAAO;QAAU;IACnE;IACA,UAAU;QACR,SAAS;YAAE,WAAW;YAAK,OAAO;QAAU;QAC5C,aAAa;YACX,WAAW;YACX,UAAU;gBAAC;gBAAI;gBAAI;aAAG;YACtB,OAAO;YACP,YAAY;QACd;QACA,OAAO;YAAE,WAAW;YAAK,UAAU;gBAAC,CAAC;gBAAI,CAAC;gBAAG,CAAC;aAAG;YAAE,OAAO;QAAU;IACtE;IACA,MAAM;QACJ,SAAS;YAAE,WAAW;YAAK,OAAO;QAAU;QAC5C,aAAa;YACX,WAAW;YACX,UAAU;gBAAC;gBAAG;gBAAI;aAAE;YACpB,OAAO;YACP,YAAY;QACd;QACA,OAAO;YAAE,WAAW;YAAK,UAAU;gBAAC,CAAC;gBAAG;gBAAG,CAAC;aAAE;YAAE,OAAO;QAAU;IACnE;AACF;AAEA,SAAS,cAAc,EAAE,SAAS,SAAS,EAA4C;IACrF,MAAM,WAAW,eAAe,CAAC,OAAO;IAExC,qBACE;;0BACE,8OAAC;gBACC,WAAW,SAAS,OAAO,CAAC,SAAS;gBACrC,OAAO,SAAS,OAAO,CAAC,KAAK;;;;;;0BAE/B,8OAAC;gBACC,UAAU,SAAS,WAAW,CAAC,QAAQ;gBACvC,WAAW,SAAS,WAAW,CAAC,SAAS;gBACzC,OAAO,SAAS,WAAW,CAAC,KAAK;gBACjC,YAAY,SAAS,WAAW,CAAC,UAAU;gBAC3C,wBAAsB;gBACtB,yBAAuB;gBACvB,qBAAmB;gBACnB,sBAAoB,CAAC;gBACrB,uBAAqB;gBACrB,qBAAmB;gBACnB,wBAAsB,CAAC;;;;;;0BAEzB,8OAAC;gBACC,UAAU,SAAS,KAAK,CAAC,QAAQ;gBACjC,WAAW,SAAS,KAAK,CAAC,SAAS;gBACnC,OAAO,SAAS,KAAK,CAAC,KAAK;;;;;;;;AAInC;AAEA,SAAS,aAAa,EACpB,iBAAiB,EACjB,eAAe,EAIhB;IACC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,qBACE;;YACG,mCACC,8OAAC,+JAAA,CAAA,cAAW;gBACV,QAAQ,UAAU,SAAS,UAAU;gBACrC,YAAY;gBACZ,WAAW;;;;;;YAId,iCACC,8OAAC;gBAAM,QAAO;gBAAa,MAAM;oBAAC;iBAAgB;;;;;;0BAIpD,8OAAC;gBAAI,QAAO;gBAAM,MAAM;oBAAC,UAAU,SAAS,YAAY;oBAAW;oBAAI;iBAAG;;;;;;;;AAGhF;AAEO,SAAS,aAAa,EAC3B,QAAQ,EACR,SAAS,EACT,iBAAiB;IAAC;IAAG;IAAG;CAAE,EAC1B,eAAe;IAAC;IAAG;IAAG;CAAE,EACxB,iBAAiB,IAAI,EACrB,oBAAoB,IAAI,EACxB,cAAc,KAAK,EACnB,eAAe,EACf,UAAU,IAAI,EACd,YAAY,IAAI,EAChB,cAAc;IAAE,KAAK;IAAK,KAAK;IAAG,UAAU;AAAI,CAAC,EACjD,WAAW,SAAS,EACpB,iBAAiB,KAAK,EACJ;IAClB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAkD;;QAElD;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;;0BAC3C,8OAAC,mMAAA,CAAA,SAAM;gBACL,KAAK;gBACL,SAAS;gBACT,IAAI;oBACF;oBACA,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,OAAO;gBACT;gBACA,aAAa;gBACb,KAAK;oBAAC;oBAAG;iBAAE;gBACX,QAAQ;oBACN,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,KAAK;gBACP;;kCAGA,8OAAC,+JAAA,CAAA,cAAW;wBAAC,SAAS;;;;;;kCACtB,8OAAC,kKAAA,CAAA,iBAAc;;;;;oBACd,yBAAW,8OAAC,+JAAA,CAAA,cAAW;;;;;kCAGxB,8OAAC,qKAAA,CAAA,oBAAiB;wBAChB,WAAW;wBACX,UAAU;wBACV,KAAK;wBACL,MAAM;wBACN,KAAK;;;;;;kCAIP,8OAAC;wBAAc,QAAQ;;;;;;kCAGvB,8OAAC;wBACC,mBAAmB;wBACnB,iBAAiB;;;;;;oBAIlB,gCACC,8OAAC,iKAAA,CAAA,gBAAa;wBACZ,QAAQ;wBACR,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,aAAa;wBACb,aAAa;wBACb,eAAe,KAAK,EAAE,GAAG;wBACzB,eAAe;wBACf,eAAe;;;;;;oBAKlB,6BAAe,8OAAC,yJAAA,CAAA,QAAK;;;;;kCAGtB,8OAAC,qMAAA,CAAA,WAAQ;wBAAC,UAAU;kCACjB;;;;;;kCAIH,8OAAC,2JAAA,CAAA,UAAO;wBAAC,GAAG;;;;;;;;;;;;YAIb,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAI;4BAAQ,YAAY,KAAK;;;;;;;kCAC9B,8OAAC;;4BAAI;4BAAU,UAAU,OAAO;;;;;;;kCAChC,8OAAC;;4BAAI;4BAAW;;;;;;;;;;;;;;;;;;;AAK1B;AAGO,SAAS,UAAU,EAAE,QAAQ,EAAE,GAAG,OAA0B;IACjE,qBACE,8OAAC;QACC,UAAS;QACT,mBAAmB;QACnB,SAAS;QACT,aAAa;QACb,aAAa;YAAE,KAAK;YAAK,KAAK;YAAG,UAAU;QAAI;QAC9C,GAAG,KAAK;kBAER;;;;;;AAGP;AAGO,SAAS,aAAa,EAAE,QAAQ,EAAE,GAAG,OAA0B;IACpE,qBACE,8OAAC;QACC,UAAS;QACT,mBAAmB;QACnB,SAAS;QACT,aAAa;QACb,aAAa;YAAE,KAAK;YAAK,KAAK;YAAG,UAAU;QAAI;QAC9C,GAAG,KAAK;kBAER;;;;;;AAGP;AAGO,SAAS,WAAW,EAAE,QAAQ,EAAE,GAAG,OAA0B;IAClE,qBACE,8OAAC;QACC,UAAS;QACT,mBAAmB;QACnB,SAAS;QACT,aAAa;QACb,aAAa;YAAE,KAAK;YAAK,KAAK;YAAG,UAAU;QAAI;QAC9C,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/Microchip.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Box, RoundedBox, Text } from '@react-three/drei';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface MicrochipProps {\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  animated?: boolean;\n  glowEffect?: boolean;\n  text?: string;\n  onClick?: () => void;\n  onHover?: (hovered: boolean) => void;\n}\n\nexport function Microchip({\n  position = [0, 0, 0],\n  scale = 1,\n  rotation = [0, 0, 0],\n  animated = true,\n  glowEffect = true,\n  text,\n  onClick,\n  onHover,\n}: MicrochipProps) {\n  const groupRef = useRef<Group>(null);\n  const chipRef = useRef<Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n\n  // Animation loop\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    \n    // Gentle rotation animation\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;\n    \n    // Floating animation\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1;\n    \n    // Hover effect\n    if (hovered && chipRef.current) {\n      chipRef.current.scale.setScalar(1.1);\n    } else if (chipRef.current) {\n      chipRef.current.scale.setScalar(1);\n    }\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(false);\n    document.body.style.cursor = 'auto';\n  };\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      rotation={rotation}\n      onClick={onClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      {/* Main chip body */}\n      <RoundedBox\n        ref={chipRef}\n        args={[2, 0.2, 2]}\n        radius={0.05}\n        smoothness={4}\n        castShadow\n        receiveShadow\n      >\n        <meshStandardMaterial\n          color={hovered ? themeConfig.primaryColor : '#2a2a2a'}\n          metalness={0.8}\n          roughness={0.2}\n          emissive={glowEffect ? themeConfig.primaryColor : '#000000'}\n          emissiveIntensity={hovered ? 0.3 : 0.1}\n        />\n      </RoundedBox>\n\n      {/* Circuit traces */}\n      {Array.from({ length: 8 }, (_, i) => (\n        <Box\n          key={`trace-${i}`}\n          args={[0.05, 0.01, 1.8]}\n          position={[-0.8 + (i * 0.2), 0.11, 0]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={themeConfig.secondaryColor}\n            emissive={themeConfig.secondaryColor}\n            emissiveIntensity={0.2}\n          />\n        </Box>\n      ))}\n\n      {/* Pins */}\n      {Array.from({ length: 16 }, (_, i) => {\n        const side = Math.floor(i / 4);\n        const pinIndex = i % 4;\n        let pinPosition: [number, number, number];\n        \n        switch (side) {\n          case 0: // Top\n            pinPosition = [-0.6 + (pinIndex * 0.4), -0.15, 1.1];\n            break;\n          case 1: // Right\n            pinPosition = [1.1, -0.15, 0.6 - (pinIndex * 0.4)];\n            break;\n          case 2: // Bottom\n            pinPosition = [0.6 - (pinIndex * 0.4), -0.15, -1.1];\n            break;\n          case 3: // Left\n            pinPosition = [-1.1, -0.15, -0.6 + (pinIndex * 0.4)];\n            break;\n          default:\n            pinPosition = [0, 0, 0];\n        }\n\n        return (\n          <Box\n            key={`pin-${i}`}\n            args={[0.1, 0.3, 0.05]}\n            position={pinPosition}\n            castShadow\n          >\n            <meshStandardMaterial\n              color=\"#ffd700\"\n              metalness={1}\n              roughness={0.1}\n            />\n          </Box>\n        );\n      })}\n\n      {/* Central processing unit indicator */}\n      <RoundedBox\n        args={[1, 0.05, 1]}\n        radius={0.02}\n        position={[0, 0.13, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#1a1a1a\"\n          metalness={0.5}\n          roughness={0.3}\n        />\n      </RoundedBox>\n\n      {/* Text label */}\n      {text && (\n        <Text\n          position={[0, 0.2, 0]}\n          fontSize={0.2}\n          color={themeConfig.primaryColor}\n          anchorX=\"center\"\n          anchorY=\"middle\"\n          font=\"/fonts/inter-bold.woff\"\n        >\n          {text}\n        </Text>\n      )}\n\n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <RoundedBox\n          args={[2.2, 0.25, 2.2]}\n          radius={0.05}\n          position={[0, 0, 0]}\n        >\n          <meshBasicMaterial\n            color={themeConfig.primaryColor}\n            transparent\n            opacity={0.1}\n          />\n        </RoundedBox>\n      )}\n    </group>\n  );\n}\n\n// Simplified microchip for performance-critical scenarios\nexport function SimpleMicrochip({\n  position = [0, 0, 0],\n  scale = 1,\n  color = '#2a2a2a',\n}: {\n  position?: [number, number, number];\n  scale?: number;\n  color?: string;\n}) {\n  return (\n    <RoundedBox\n      args={[2, 0.2, 2]}\n      radius={0.05}\n      position={position}\n      scale={scale}\n      castShadow\n      receiveShadow\n    >\n      <meshStandardMaterial\n        color={color}\n        metalness={0.8}\n        roughness={0.2}\n      />\n    </RoundedBox>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAmBO,SAAS,UAAU,EACxB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,IAAI,EACJ,OAAO,EACP,OAAO,EACQ;IACf,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAQ;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QAEpC,4BAA4B;QAC5B,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAExD,qBAAqB;QACrB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;QAEhF,eAAe;QACf,IAAI,WAAW,QAAQ,OAAO,EAAE;YAC9B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QAClC,OAAO,IAAI,QAAQ,OAAO,EAAE;YAC1B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QAClC;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,eAAe;QACf,cAAc;;0BAGd,8OAAC,8JAAA,CAAA,aAAU;gBACT,KAAK;gBACL,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,YAAY;gBACZ,UAAU;gBACV,aAAa;0BAEb,cAAA,8OAAC;oBACC,OAAO,UAAU,YAAY,YAAY,GAAG;oBAC5C,WAAW;oBACX,WAAW;oBACX,UAAU,aAAa,YAAY,YAAY,GAAG;oBAClD,mBAAmB,UAAU,MAAM;;;;;;;;;;;YAKtC,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC;wBAAM;wBAAM;qBAAI;oBACvB,UAAU;wBAAC,CAAC,MAAO,IAAI;wBAAM;wBAAM;qBAAE;oBACrC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAO,YAAY,cAAc;wBACjC,UAAU,YAAY,cAAc;wBACpC,mBAAmB;;;;;;mBARhB,CAAC,MAAM,EAAE,GAAG;;;;;YAcpB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,CAAC,GAAG;gBAC9B,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI;gBAC5B,MAAM,WAAW,IAAI;gBACrB,IAAI;gBAEJ,OAAQ;oBACN,KAAK;wBACH,cAAc;4BAAC,CAAC,MAAO,WAAW;4BAAM,CAAC;4BAAM;yBAAI;wBACnD;oBACF,KAAK;wBACH,cAAc;4BAAC;4BAAK,CAAC;4BAAM,MAAO,WAAW;yBAAK;wBAClD;oBACF,KAAK;wBACH,cAAc;4BAAC,MAAO,WAAW;4BAAM,CAAC;4BAAM,CAAC;yBAAI;wBACnD;oBACF,KAAK;wBACH,cAAc;4BAAC,CAAC;4BAAK,CAAC;4BAAM,CAAC,MAAO,WAAW;yBAAK;wBACpD;oBACF;wBACE,cAAc;4BAAC;4BAAG;4BAAG;yBAAE;gBAC3B;gBAEA,qBACE,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC;wBAAK;wBAAK;qBAAK;oBACtB,UAAU;oBACV,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,WAAW;;;;;;mBARR,CAAC,IAAI,EAAE,GAAG;;;;;YAYrB;0BAGA,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAG;oBAAM;iBAAE;gBAClB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAM;iBAAE;gBACtB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,WAAW;;;;;;;;;;;YAKd,sBACC,8OAAC,wJAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBACrB,UAAU;gBACV,OAAO,YAAY,YAAY;gBAC/B,SAAQ;gBACR,SAAQ;gBACR,MAAK;0BAEJ;;;;;;YAKJ,cAAc,yBACb,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAMrB;AAGO,SAAS,gBAAgB,EAC9B,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,QAAQ,SAAS,EAKlB;IACC,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,MAAM;YAAC;YAAG;YAAK;SAAE;QACjB,QAAQ;QACR,UAAU;QACV,OAAO;QACP,UAAU;QACV,aAAa;kBAEb,cAAA,8OAAC;YACC,OAAO;YACP,WAAW;YACX,WAAW;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/MicrochipModel.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState, Suspense } from 'react';\nimport { useFrame, useLoader } from '@react-three/fiber';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';\nimport { useGLTF, Text, Html } from '@react-three/drei';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface MicrochipModelProps {\n  modelPath?: string;\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  animated?: boolean;\n  glowEffect?: boolean;\n  text?: string;\n  onClick?: () => void;\n  onHover?: (hovered: boolean) => void;\n}\n\n// Component for loading external GLTF models\nfunction LoadedMicrochip({ \n  modelPath, \n  position = [0, 0, 0], \n  scale = 1, \n  animated = true,\n  glowEffect = true,\n  onClick,\n  onHover \n}: MicrochipModelProps) {\n  const groupRef = useRef<Group>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n  \n  // Load the GLTF model\n  const { scene } = useGLTF(modelPath || '/models/microchip.gltf');\n  \n  // Animation loop\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    \n    // Gentle rotation animation\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.3;\n    \n    // Floating animation\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;\n    \n    // Hover effect\n    if (hovered) {\n      groupRef.current.scale.setScalar(scale * 1.1);\n    } else {\n      groupRef.current.scale.setScalar(scale);\n    }\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(false);\n    document.body.style.cursor = 'auto';\n  };\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      onClick={onClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      <primitive \n        object={scene.clone()} \n        castShadow \n        receiveShadow\n      />\n      \n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <mesh position={[0, 0, 0]}>\n          <sphereGeometry args={[2, 16, 16]} />\n          <meshBasicMaterial\n            color={themeConfig.primaryColor}\n            transparent\n            opacity={0.1}\n          />\n        </mesh>\n      )}\n    </group>\n  );\n}\n\n// Fallback component while model loads\nfunction ModelLoader() {\n  return (\n    <Html center>\n      <div className=\"flex flex-col items-center space-y-2 text-white\">\n        <div className=\"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin\" />\n        <p className=\"text-sm\">Loading 3D Model...</p>\n      </div>\n    </Html>\n  );\n}\n\n// Error fallback component\nfunction ModelError() {\n  return (\n    <mesh>\n      <boxGeometry args={[2, 0.5, 2]} />\n      <meshStandardMaterial color=\"#ff4444\" />\n    </mesh>\n  );\n}\n\n// Main component with error boundary\nexport function MicrochipModel(props: MicrochipModelProps) {\n  const [error, setError] = useState(false);\n\n  if (error) {\n    return <ModelError />;\n  }\n\n  return (\n    <Suspense fallback={<ModelLoader />}>\n      <ErrorBoundary onError={() => setError(true)}>\n        <LoadedMicrochip {...props} />\n      </ErrorBoundary>\n    </Suspense>\n  );\n}\n\n// Simple error boundary component\nfunction ErrorBoundary({ \n  children, \n  onError \n}: { \n  children: React.ReactNode; \n  onError: () => void; \n}) {\n  try {\n    return <>{children}</>;\n  } catch (error) {\n    console.error('3D Model loading error:', error);\n    onError();\n    return <ModelError />;\n  }\n}\n\n// Preload models for better performance\nexport function preloadMicrochipModels() {\n  useGLTF.preload('/models/microchip.gltf');\n  useGLTF.preload('/models/cpu.gltf');\n  useGLTF.preload('/models/processor.gltf');\n}\n\n// Enhanced microchip with multiple variants\nexport function EnhancedMicrochip({\n  variant = 'default',\n  ...props\n}: MicrochipModelProps & { variant?: 'default' | 'cpu' | 'processor' | 'memory' }) {\n  const modelPaths = {\n    default: '/models/microchip.gltf',\n    cpu: '/models/cpu.gltf',\n    processor: '/models/processor.gltf',\n    memory: '/models/memory.gltf',\n  };\n\n  return (\n    <MicrochipModel\n      {...props}\n      modelPath={modelPaths[variant]}\n    />\n  );\n}\n\n// Grid of microchips for background effects\nexport function MicrochipGrid({\n  count = 20,\n  spread = 10,\n  animated = true,\n}: {\n  count?: number;\n  spread?: number;\n  animated?: boolean;\n}) {\n  const chips = Array.from({ length: count }, (_, i) => ({\n    id: i,\n    position: [\n      (Math.random() - 0.5) * spread,\n      (Math.random() - 0.5) * spread,\n      (Math.random() - 0.5) * spread,\n    ] as [number, number, number],\n    scale: 0.3 + Math.random() * 0.4,\n    rotation: [\n      Math.random() * Math.PI,\n      Math.random() * Math.PI,\n      Math.random() * Math.PI,\n    ] as [number, number, number],\n  }));\n\n  return (\n    <group>\n      {chips.map((chip) => (\n        <MicrochipModel\n          key={chip.id}\n          position={chip.position}\n          scale={chip.scale}\n          rotation={chip.rotation}\n          animated={animated}\n          glowEffect={false}\n        />\n      ))}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AAAA;AAEA;AAPA;;;;;;AAqBA,6CAA6C;AAC7C,SAAS,gBAAgB,EACvB,SAAS,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,OAAO,EACP,OAAO,EACa;IACpB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,sBAAsB;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvC,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QAEpC,4BAA4B;QAC5B,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAExD,qBAAqB;QACrB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;QAEpF,eAAe;QACf,IAAI,SAAS;YACX,SAAS,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ;QAC3C,OAAO;YACL,SAAS,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QACnC;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,SAAS;QACT,eAAe;QACf,cAAc;;0BAEd,8OAAC;gBACC,QAAQ,MAAM,KAAK;gBACnB,UAAU;gBACV,aAAa;;;;;;YAId,cAAc,yBACb,8OAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;;kCACvB,8OAAC;wBAAe,MAAM;4BAAC;4BAAG;4BAAI;yBAAG;;;;;;kCACjC,8OAAC;wBACC,OAAO,YAAY,YAAY;wBAC/B,WAAW;wBACX,SAAS;;;;;;;;;;;;;;;;;;AAMrB;AAEA,uCAAuC;AACvC,SAAS;IACP,qBACE,8OAAC,uJAAA,CAAA,OAAI;QAAC,MAAM;kBACV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAU;;;;;;;;;;;;;;;;;AAI/B;AAEA,2BAA2B;AAC3B,SAAS;IACP,qBACE,8OAAC;;0BACC,8OAAC;gBAAY,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;;;;;;0BAC9B,8OAAC;gBAAqB,OAAM;;;;;;;;;;;;AAGlC;AAGO,SAAS,eAAe,KAA0B;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,IAAI,OAAO;QACT,qBAAO,8OAAC;;;;;IACV;IAEA,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;;;;;kBACnB,cAAA,8OAAC;YAAc,SAAS,IAAM,SAAS;sBACrC,cAAA,8OAAC;gBAAiB,GAAG,KAAK;;;;;;;;;;;;;;;;AAIlC;AAEA,kCAAkC;AAClC,SAAS,cAAc,EACrB,QAAQ,EACR,OAAO,EAIR;IACC,IAAI;QACF,qBAAO;sBAAG;;IACZ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC;QACA,qBAAO,8OAAC;;;;;IACV;AACF;AAGO,SAAS;IACd,wJAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAChB,wJAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAChB,wJAAA,CAAA,UAAO,CAAC,OAAO,CAAC;AAClB;AAGO,SAAS,kBAAkB,EAChC,UAAU,SAAS,EACnB,GAAG,OAC4E;IAC/E,MAAM,aAAa;QACjB,SAAS;QACT,KAAK;QACL,WAAW;QACX,QAAQ;IACV;IAEA,qBACE,8OAAC;QACE,GAAG,KAAK;QACT,WAAW,UAAU,CAAC,QAAQ;;;;;;AAGpC;AAGO,SAAS,cAAc,EAC5B,QAAQ,EAAE,EACV,SAAS,EAAE,EACX,WAAW,IAAI,EAKhB;IACC,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAM,GAAG,CAAC,GAAG,IAAM,CAAC;YACrD,IAAI;YACJ,UAAU;gBACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;aACzB;YACD,OAAO,MAAM,KAAK,MAAM,KAAK;YAC7B,UAAU;gBACR,KAAK,MAAM,KAAK,KAAK,EAAE;gBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;gBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;aACxB;QACH,CAAC;IAED,qBACE,8OAAC;kBACE,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gBAEC,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;gBACV,YAAY;eALP,KAAK,EAAE;;;;;;;;;;AAUtB", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/PlaceholderModels.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Box, RoundedBox, Cylinder, Sphere } from '@react-three/drei';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface PlaceholderModelProps {\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  animated?: boolean;\n  glowEffect?: boolean;\n  variant?: 'cpu' | 'memory' | 'gpu' | 'motherboard';\n  onClick?: () => void;\n  onHover?: (hovered: boolean) => void;\n}\n\n// CPU Model\nexport function CPUModel({\n  position = [0, 0, 0],\n  scale = 1,\n  rotation = [0, 0, 0],\n  animated = true,\n  glowEffect = true,\n  onClick,\n  onHover,\n}: PlaceholderModelProps) {\n  const groupRef = useRef<Group>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.05;\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(false);\n    document.body.style.cursor = 'auto';\n  };\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      rotation={rotation}\n      onClick={onClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      {/* CPU Base */}\n      <RoundedBox\n        args={[2, 0.3, 2]}\n        radius={0.05}\n        castShadow\n        receiveShadow\n      >\n        <meshStandardMaterial\n          color={hovered ? themeConfig.primaryColor : '#2a2a2a'}\n          metalness={0.8}\n          roughness={0.2}\n          emissive={glowEffect ? themeConfig.primaryColor : '#000000'}\n          emissiveIntensity={hovered ? 0.3 : 0.1}\n        />\n      </RoundedBox>\n\n      {/* Heat spreader */}\n      <RoundedBox\n        args={[1.5, 0.1, 1.5]}\n        radius={0.02}\n        position={[0, 0.2, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#c0c0c0\"\n          metalness={1}\n          roughness={0.1}\n        />\n      </RoundedBox>\n\n      {/* Pins */}\n      {Array.from({ length: 64 }, (_, i) => {\n        const row = Math.floor(i / 8);\n        const col = i % 8;\n        const x = -0.875 + (col * 0.25);\n        const z = -0.875 + (row * 0.25);\n        \n        return (\n          <Cylinder\n            key={i}\n            args={[0.02, 0.02, 0.2]}\n            position={[x, -0.25, z]}\n            castShadow\n          >\n            <meshStandardMaterial\n              color=\"#ffd700\"\n              metalness={1}\n              roughness={0.1}\n            />\n          </Cylinder>\n        );\n      })}\n\n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <RoundedBox\n          args={[2.2, 0.35, 2.2]}\n          radius={0.05}\n          position={[0, 0, 0]}\n        >\n          <meshBasicMaterial\n            color={themeConfig.primaryColor}\n            transparent\n            opacity={0.1}\n          />\n        </RoundedBox>\n      )}\n    </group>\n  );\n}\n\n// Memory Module Model\nexport function MemoryModel({\n  position = [0, 0, 0],\n  scale = 1,\n  animated = true,\n  glowEffect = true,\n}: PlaceholderModelProps) {\n  const groupRef = useRef<Group>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n  });\n\n  return (\n    <group ref={groupRef} position={position} scale={scale}>\n      {/* PCB */}\n      <Box\n        args={[0.2, 1.5, 4]}\n        castShadow\n        receiveShadow\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n      >\n        <meshStandardMaterial\n          color=\"#1a4a1a\"\n          roughness={0.8}\n        />\n      </Box>\n\n      {/* Memory chips */}\n      {Array.from({ length: 8 }, (_, i) => (\n        <RoundedBox\n          key={i}\n          args={[0.15, 0.3, 0.4]}\n          radius={0.01}\n          position={[0.025, 0, -1.4 + (i * 0.4)]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={hovered ? themeConfig.secondaryColor : '#0a0a0a'}\n            emissive={glowEffect ? themeConfig.secondaryColor : '#000000'}\n            emissiveIntensity={hovered ? 0.2 : 0.05}\n          />\n        </RoundedBox>\n      ))}\n\n      {/* Connector pins */}\n      <Box\n        args={[0.05, 0.2, 3.8]}\n        position={[0, -0.85, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#ffd700\"\n          metalness={1}\n          roughness={0.1}\n        />\n      </Box>\n    </group>\n  );\n}\n\n// GPU Model\nexport function GPUModel({\n  position = [0, 0, 0],\n  scale = 1,\n  animated = true,\n  glowEffect = true,\n}: PlaceholderModelProps) {\n  const groupRef = useRef<Group>(null);\n  const [hovered, setHovered] = useState(false);\n  const { themeConfig } = useTheme();\n\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n  });\n\n  return (\n    <group ref={groupRef} position={position} scale={scale}>\n      {/* GPU PCB */}\n      <RoundedBox\n        args={[4, 0.2, 2]}\n        radius={0.05}\n        castShadow\n        receiveShadow\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n      >\n        <meshStandardMaterial\n          color=\"#1a1a4a\"\n          roughness={0.8}\n        />\n      </RoundedBox>\n\n      {/* GPU Die */}\n      <RoundedBox\n        args={[1.5, 0.15, 1.5]}\n        radius={0.02}\n        position={[0, 0.175, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color={hovered ? themeConfig.accentColor : '#2a2a2a'}\n          metalness={0.8}\n          roughness={0.2}\n          emissive={glowEffect ? themeConfig.accentColor : '#000000'}\n          emissiveIntensity={hovered ? 0.3 : 0.1}\n        />\n      </RoundedBox>\n\n      {/* Cooling fins */}\n      {Array.from({ length: 8 }, (_, i) => (\n        <Box\n          key={i}\n          args={[0.05, 0.5, 1.8]}\n          position={[-1.5 + (i * 0.2), 0.35, 0]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color=\"#c0c0c0\"\n            metalness={0.9}\n            roughness={0.1}\n          />\n        </Box>\n      ))}\n\n      {/* Memory modules */}\n      {Array.from({ length: 6 }, (_, i) => (\n        <RoundedBox\n          key={i}\n          args={[0.3, 0.1, 0.2]}\n          radius={0.01}\n          position={[1.2, 0.15, -0.6 + (i * 0.24)]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color=\"#0a0a0a\"\n            emissive={themeConfig.secondaryColor}\n            emissiveIntensity={0.1}\n          />\n        </RoundedBox>\n      ))}\n    </group>\n  );\n}\n\n// Motherboard Model\nexport function MotherboardModel({\n  position = [0, 0, 0],\n  scale = 1,\n  animated = false,\n}: PlaceholderModelProps) {\n  const { themeConfig } = useTheme();\n\n  return (\n    <group position={position} scale={scale}>\n      {/* Main PCB */}\n      <RoundedBox\n        args={[6, 0.1, 4]}\n        radius={0.05}\n        castShadow\n        receiveShadow\n      >\n        <meshStandardMaterial\n          color=\"#1a4a1a\"\n          roughness={0.8}\n        />\n      </RoundedBox>\n\n      {/* CPU Socket */}\n      <RoundedBox\n        args={[1.2, 0.05, 1.2]}\n        radius={0.02}\n        position={[-1, 0.075, 0.5]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#0a0a0a\"\n          metalness={0.5}\n        />\n      </RoundedBox>\n\n      {/* RAM Slots */}\n      {Array.from({ length: 4 }, (_, i) => (\n        <Box\n          key={i}\n          args={[0.15, 0.05, 2]}\n          position={[1 + (i * 0.3), 0.075, 0]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color=\"#0a0a0a\"\n            metalness={0.8}\n          />\n        </Box>\n      ))}\n\n      {/* Circuit traces */}\n      {Array.from({ length: 20 }, (_, i) => (\n        <Box\n          key={i}\n          args={[Math.random() * 2 + 1, 0.01, 0.02]}\n          position={[\n            (Math.random() - 0.5) * 5,\n            0.055,\n            (Math.random() - 0.5) * 3\n          ]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={themeConfig.primaryColor}\n            emissive={themeConfig.primaryColor}\n            emissiveIntensity={0.1}\n          />\n        </Box>\n      ))}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAAA;AAEA;AANA;;;;;;AAoBO,SAAS,SAAS,EACvB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,OAAO,EACP,OAAO,EACe;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QACpC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QACxD,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;IAClF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,eAAe;QACf,cAAc;;0BAGd,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,UAAU;gBACV,aAAa;0BAEb,cAAA,8OAAC;oBACC,OAAO,UAAU,YAAY,YAAY,GAAG;oBAC5C,WAAW;oBACX,WAAW;oBACX,UAAU,aAAa,YAAY,YAAY,GAAG;oBAClD,mBAAmB,UAAU,MAAM;;;;;;;;;;;0BAKvC,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBACrB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBACrB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,WAAW;;;;;;;;;;;YAKd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,CAAC,GAAG;gBAC9B,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI;gBAC3B,MAAM,MAAM,IAAI;gBAChB,MAAM,IAAI,CAAC,QAAS,MAAM;gBAC1B,MAAM,IAAI,CAAC,QAAS,MAAM;gBAE1B,qBACE,8OAAC,0JAAA,CAAA,WAAQ;oBAEP,MAAM;wBAAC;wBAAM;wBAAM;qBAAI;oBACvB,UAAU;wBAAC;wBAAG,CAAC;wBAAM;qBAAE;oBACvB,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,WAAW;;;;;;mBARR;;;;;YAYX;YAGC,cAAc,yBACb,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAMrB;AAGO,SAAS,YAAY,EAC1B,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,aAAa,IAAI,EACK;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QACpC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;IAC1E;IAEA,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;QAAU,OAAO;;0BAE/C,8OAAC,0JAAA,CAAA,MAAG;gBACF,MAAM;oBAAC;oBAAK;oBAAK;iBAAE;gBACnB,UAAU;gBACV,aAAa;gBACb,eAAe,IAAM,WAAW;gBAChC,cAAc,IAAM,WAAW;0BAE/B,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;;;;;;;;;;;YAKd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,8JAAA,CAAA,aAAU;oBAET,MAAM;wBAAC;wBAAM;wBAAK;qBAAI;oBACtB,QAAQ;oBACR,UAAU;wBAAC;wBAAO;wBAAG,CAAC,MAAO,IAAI;qBAAK;oBACtC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAO,UAAU,YAAY,cAAc,GAAG;wBAC9C,UAAU,aAAa,YAAY,cAAc,GAAG;wBACpD,mBAAmB,UAAU,MAAM;;;;;;mBAThC;;;;;0BAeT,8OAAC,0JAAA,CAAA,MAAG;gBACF,MAAM;oBAAC;oBAAM;oBAAK;iBAAI;gBACtB,UAAU;oBAAC;oBAAG,CAAC;oBAAM;iBAAE;gBACvB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,WAAW;;;;;;;;;;;;;;;;;AAKrB;AAGO,SAAS,SAAS,EACvB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,aAAa,IAAI,EACK;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QACpC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;IAC1D;IAEA,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;QAAU,OAAO;;0BAE/C,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,UAAU;gBACV,aAAa;gBACb,eAAe,IAAM,WAAW;gBAChC,cAAc,IAAM,WAAW;0BAE/B,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;;;;;;;;;;;0BAKf,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAO;iBAAE;gBACvB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAO,UAAU,YAAY,WAAW,GAAG;oBAC3C,WAAW;oBACX,WAAW;oBACX,UAAU,aAAa,YAAY,WAAW,GAAG;oBACjD,mBAAmB,UAAU,MAAM;;;;;;;;;;;YAKtC,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC;wBAAM;wBAAK;qBAAI;oBACtB,UAAU;wBAAC,CAAC,MAAO,IAAI;wBAAM;wBAAM;qBAAE;oBACrC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,WAAW;;;;;;mBARR;;;;;YAcR,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,8JAAA,CAAA,aAAU;oBAET,MAAM;wBAAC;wBAAK;wBAAK;qBAAI;oBACrB,QAAQ;oBACR,UAAU;wBAAC;wBAAK;wBAAM,CAAC,MAAO,IAAI;qBAAM;oBACxC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,UAAU,YAAY,cAAc;wBACpC,mBAAmB;;;;;;mBAThB;;;;;;;;;;;AAef;AAGO,SAAS,iBAAiB,EAC/B,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW,KAAK,EACM;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,qBACE,8OAAC;QAAM,UAAU;QAAU,OAAO;;0BAEhC,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,UAAU;gBACV,aAAa;0BAEb,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;;;;;;;;;;;0BAKf,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC,CAAC;oBAAG;oBAAO;iBAAI;gBAC1B,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;;;;;;;;;;;YAKd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC;wBAAM;wBAAM;qBAAE;oBACrB,UAAU;wBAAC,IAAK,IAAI;wBAAM;wBAAO;qBAAE;oBACnC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,WAAW;;;;;;mBAPR;;;;;YAaR,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,0JAAA,CAAA,MAAG;oBAEF,MAAM;wBAAC,KAAK,MAAM,KAAK,IAAI;wBAAG;wBAAM;qBAAK;oBACzC,UAAU;wBACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBACxB;wBACA,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;qBACzB;oBACD,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAO,YAAY,YAAY;wBAC/B,UAAU,YAAY,YAAY;wBAClC,mBAAmB;;;;;;mBAZhB;;;;;;;;;;;AAkBf", "debugId": null}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/InteractiveCamera.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect } from 'react';\nimport { useFrame, useThree } from '@react-three/fiber';\nimport { PerspectiveCamera } from '@react-three/drei';\nimport { Vector3, MathUtils } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface InteractiveCameraProps {\n  enableMouseTracking?: boolean;\n  enableScrollTracking?: boolean;\n  mouseSensitivity?: number;\n  scrollSensitivity?: number;\n  basePosition?: [number, number, number];\n  lookAt?: [number, number, number];\n  smoothing?: number;\n}\n\nexport function InteractiveCamera({\n  enableMouseTracking = true,\n  enableScrollTracking = true,\n  mouseSensitivity = 0.5,\n  scrollSensitivity = 0.3,\n  basePosition = [0, 0, 8],\n  lookAt = [0, 0, 0],\n  smoothing = 0.05,\n}: InteractiveCameraProps) {\n  const cameraRef = useRef<any>(null);\n  const mousePosition = useRef({ x: 0, y: 0 });\n  const scrollPosition = useRef(0);\n  const targetPosition = useRef(new Vector3(...basePosition));\n  const targetLookAt = useRef(new Vector3(...lookAt));\n  const { camera } = useThree();\n\n  // Mouse tracking\n  useEffect(() => {\n    if (!enableMouseTracking) return;\n\n    const handleMouseMove = (event: MouseEvent) => {\n      const x = (event.clientX / window.innerWidth) * 2 - 1;\n      const y = -(event.clientY / window.innerHeight) * 2 + 1;\n      \n      mousePosition.current = { x, y };\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, [enableMouseTracking]);\n\n  // Scroll tracking\n  useEffect(() => {\n    if (!enableScrollTracking) return;\n\n    const handleScroll = () => {\n      scrollPosition.current = window.scrollY;\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [enableScrollTracking]);\n\n  // Camera animation\n  useFrame(() => {\n    if (!cameraRef.current) return;\n\n    // Calculate target position based on mouse\n    const mouseInfluence = {\n      x: mousePosition.current.x * mouseSensitivity,\n      y: mousePosition.current.y * mouseSensitivity,\n      z: 0,\n    };\n\n    // Calculate scroll influence\n    const scrollInfluence = {\n      x: 0,\n      y: 0,\n      z: (scrollPosition.current * scrollSensitivity) / 100,\n    };\n\n    // Update target position\n    targetPosition.current.set(\n      basePosition[0] + mouseInfluence.x + scrollInfluence.x,\n      basePosition[1] + mouseInfluence.y + scrollInfluence.y,\n      basePosition[2] + mouseInfluence.z + scrollInfluence.z\n    );\n\n    // Smooth camera movement\n    cameraRef.current.position.lerp(targetPosition.current, smoothing);\n    \n    // Update look-at target with slight mouse influence\n    const lookAtTarget = new Vector3(\n      lookAt[0] + mouseInfluence.x * 0.2,\n      lookAt[1] + mouseInfluence.y * 0.2,\n      lookAt[2]\n    );\n    \n    cameraRef.current.lookAt(lookAtTarget);\n  });\n\n  return (\n    <PerspectiveCamera\n      ref={cameraRef}\n      makeDefault\n      position={basePosition}\n      fov={75}\n      near={0.1}\n      far={1000}\n    />\n  );\n}\n\n// Cinematic camera for dramatic scenes\nexport function CinematicCamera({\n  keyframes,\n  duration = 10,\n  autoStart = true,\n}: {\n  keyframes: Array<{\n    position: [number, number, number];\n    lookAt: [number, number, number];\n    fov?: number;\n  }>;\n  duration?: number;\n  autoStart?: boolean;\n}) {\n  const cameraRef = useRef<any>(null);\n  const startTime = useRef<number | null>(null);\n  const isPlaying = useRef(autoStart);\n\n  useFrame((state) => {\n    if (!cameraRef.current || !isPlaying.current || keyframes.length < 2) return;\n\n    if (startTime.current === null) {\n      startTime.current = state.clock.elapsedTime;\n    }\n\n    const elapsed = state.clock.elapsedTime - startTime.current;\n    const progress = Math.min(elapsed / duration, 1);\n\n    // Calculate current keyframe\n    const keyframeIndex = Math.floor(progress * (keyframes.length - 1));\n    const nextKeyframeIndex = Math.min(keyframeIndex + 1, keyframes.length - 1);\n    const localProgress = (progress * (keyframes.length - 1)) % 1;\n\n    const currentKeyframe = keyframes[keyframeIndex];\n    const nextKeyframe = keyframes[nextKeyframeIndex];\n\n    // Interpolate position\n    const position = new Vector3(\n      MathUtils.lerp(currentKeyframe.position[0], nextKeyframe.position[0], localProgress),\n      MathUtils.lerp(currentKeyframe.position[1], nextKeyframe.position[1], localProgress),\n      MathUtils.lerp(currentKeyframe.position[2], nextKeyframe.position[2], localProgress)\n    );\n\n    // Interpolate look-at\n    const lookAt = new Vector3(\n      MathUtils.lerp(currentKeyframe.lookAt[0], nextKeyframe.lookAt[0], localProgress),\n      MathUtils.lerp(currentKeyframe.lookAt[1], nextKeyframe.lookAt[1], localProgress),\n      MathUtils.lerp(currentKeyframe.lookAt[2], nextKeyframe.lookAt[2], localProgress)\n    );\n\n    // Interpolate FOV if provided\n    if (currentKeyframe.fov && nextKeyframe.fov) {\n      cameraRef.current.fov = MathUtils.lerp(currentKeyframe.fov, nextKeyframe.fov, localProgress);\n      cameraRef.current.updateProjectionMatrix();\n    }\n\n    cameraRef.current.position.copy(position);\n    cameraRef.current.lookAt(lookAt);\n\n    // Reset when complete\n    if (progress >= 1) {\n      startTime.current = null;\n      isPlaying.current = false;\n    }\n  });\n\n  const play = () => {\n    isPlaying.current = true;\n    startTime.current = null;\n  };\n\n  const stop = () => {\n    isPlaying.current = false;\n    startTime.current = null;\n  };\n\n  return (\n    <>\n      <PerspectiveCamera\n        ref={cameraRef}\n        makeDefault\n        position={keyframes[0].position}\n        fov={keyframes[0].fov || 75}\n        near={0.1}\n        far={1000}\n      />\n      {/* Expose controls via context or props if needed */}\n    </>\n  );\n}\n\n// Orbit camera with constraints\nexport function ConstrainedOrbitCamera({\n  target = [0, 0, 0],\n  distance = 8,\n  minDistance = 2,\n  maxDistance = 20,\n  enablePan = false,\n  enableZoom = true,\n  enableRotate = true,\n  autoRotate = false,\n  autoRotateSpeed = 0.5,\n}: {\n  target?: [number, number, number];\n  distance?: number;\n  minDistance?: number;\n  maxDistance?: number;\n  enablePan?: boolean;\n  enableZoom?: boolean;\n  enableRotate?: boolean;\n  autoRotate?: boolean;\n  autoRotateSpeed?: number;\n}) {\n  const cameraRef = useRef<any>(null);\n  const { themeConfig } = useTheme();\n\n  useFrame((state) => {\n    if (!cameraRef.current || !autoRotate) return;\n\n    const time = state.clock.elapsedTime * autoRotateSpeed;\n    const x = Math.cos(time) * distance;\n    const z = Math.sin(time) * distance;\n    \n    cameraRef.current.position.set(x, target[1], z);\n    cameraRef.current.lookAt(...target);\n  });\n\n  return (\n    <PerspectiveCamera\n      ref={cameraRef}\n      makeDefault\n      position={[0, 0, distance]}\n      fov={75}\n      near={0.1}\n      far={1000}\n    />\n  );\n}\n\n// First-person camera for immersive experiences\nexport function FirstPersonCamera({\n  position = [0, 1.6, 0],\n  sensitivity = 0.002,\n  enableMovement = true,\n  movementSpeed = 5,\n}: {\n  position?: [number, number, number];\n  sensitivity?: number;\n  enableMovement?: boolean;\n  movementSpeed?: number;\n}) {\n  const cameraRef = useRef<any>(null);\n  const keys = useRef<Set<string>>(new Set());\n  const mouseMovement = useRef({ x: 0, y: 0 });\n  const rotation = useRef({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      keys.current.add(event.code);\n    };\n\n    const handleKeyUp = (event: KeyboardEvent) => {\n      keys.current.delete(event.code);\n    };\n\n    const handleMouseMove = (event: MouseEvent) => {\n      if (document.pointerLockElement) {\n        mouseMovement.current.x += event.movementX * sensitivity;\n        mouseMovement.current.y += event.movementY * sensitivity;\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    window.addEventListener('keyup', handleKeyUp);\n    window.addEventListener('mousemove', handleMouseMove);\n\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n      window.removeEventListener('keyup', handleKeyUp);\n      window.removeEventListener('mousemove', handleMouseMove);\n    };\n  }, [sensitivity]);\n\n  useFrame((state, delta) => {\n    if (!cameraRef.current) return;\n\n    // Mouse look\n    rotation.current.y -= mouseMovement.current.x;\n    rotation.current.x -= mouseMovement.current.y;\n    rotation.current.x = MathUtils.clamp(rotation.current.x, -Math.PI / 2, Math.PI / 2);\n\n    cameraRef.current.rotation.set(rotation.current.x, rotation.current.y, 0);\n\n    // Movement\n    if (enableMovement) {\n      const moveVector = new Vector3();\n      \n      if (keys.current.has('KeyW')) moveVector.z -= 1;\n      if (keys.current.has('KeyS')) moveVector.z += 1;\n      if (keys.current.has('KeyA')) moveVector.x -= 1;\n      if (keys.current.has('KeyD')) moveVector.x += 1;\n      \n      moveVector.normalize();\n      moveVector.multiplyScalar(movementSpeed * delta);\n      \n      // Apply rotation to movement vector\n      moveVector.applyEuler(cameraRef.current.rotation);\n      cameraRef.current.position.add(moveVector);\n    }\n\n    // Reset mouse movement\n    mouseMovement.current.x = 0;\n    mouseMovement.current.y = 0;\n  });\n\n  return (\n    <PerspectiveCamera\n      ref={cameraRef}\n      makeDefault\n      position={position}\n      fov={75}\n      near={0.1}\n      far={1000}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAkBO,SAAS,kBAAkB,EAChC,sBAAsB,IAAI,EAC1B,uBAAuB,IAAI,EAC3B,mBAAmB,GAAG,EACtB,oBAAoB,GAAG,EACvB,eAAe;IAAC;IAAG;IAAG;CAAE,EACxB,SAAS;IAAC;IAAG;IAAG;CAAE,EAClB,YAAY,IAAI,EACO;IACvB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC1C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,+IAAA,CAAA,UAAO,IAAI;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,+IAAA,CAAA,UAAO,IAAI;IAC3C,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAE1B,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,qBAAqB;QAE1B,MAAM,kBAAkB,CAAC;YACvB,MAAM,IAAI,AAAC,MAAM,OAAO,GAAG,OAAO,UAAU,GAAI,IAAI;YACpD,MAAM,IAAI,CAAC,CAAC,MAAM,OAAO,GAAG,OAAO,WAAW,IAAI,IAAI;YAEtD,cAAc,OAAO,GAAG;gBAAE;gBAAG;YAAE;QACjC;QAEA,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;IACvD,GAAG;QAAC;KAAoB;IAExB,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,sBAAsB;QAE3B,MAAM,eAAe;YACnB,eAAe,OAAO,GAAG,OAAO,OAAO;QACzC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAqB;IAEzB,mBAAmB;IACnB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,2CAA2C;QAC3C,MAAM,iBAAiB;YACrB,GAAG,cAAc,OAAO,CAAC,CAAC,GAAG;YAC7B,GAAG,cAAc,OAAO,CAAC,CAAC,GAAG;YAC7B,GAAG;QACL;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB;YACtB,GAAG;YACH,GAAG;YACH,GAAG,AAAC,eAAe,OAAO,GAAG,oBAAqB;QACpD;QAEA,yBAAyB;QACzB,eAAe,OAAO,CAAC,GAAG,CACxB,YAAY,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,gBAAgB,CAAC,EACtD,YAAY,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,gBAAgB,CAAC,EACtD,YAAY,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,gBAAgB,CAAC;QAGxD,yBAAyB;QACzB,UAAU,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,OAAO,EAAE;QAExD,oDAAoD;QACpD,MAAM,eAAe,IAAI,+IAAA,CAAA,UAAO,CAC9B,MAAM,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,KAC/B,MAAM,CAAC,EAAE,GAAG,eAAe,CAAC,GAAG,KAC/B,MAAM,CAAC,EAAE;QAGX,UAAU,OAAO,CAAC,MAAM,CAAC;IAC3B;IAEA,qBACE,8OAAC,qKAAA,CAAA,oBAAiB;QAChB,KAAK;QACL,WAAW;QACX,UAAU;QACV,KAAK;QACL,MAAM;QACN,KAAK;;;;;;AAGX;AAGO,SAAS,gBAAgB,EAC9B,SAAS,EACT,WAAW,EAAE,EACb,YAAY,IAAI,EASjB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,IAAI,UAAU,MAAM,GAAG,GAAG;QAEtE,IAAI,UAAU,OAAO,KAAK,MAAM;YAC9B,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC,WAAW;QAC7C;QAEA,MAAM,UAAU,MAAM,KAAK,CAAC,WAAW,GAAG,UAAU,OAAO;QAC3D,MAAM,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU;QAE9C,6BAA6B;QAC7B,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,CAAC,UAAU,MAAM,GAAG,CAAC;QACjE,MAAM,oBAAoB,KAAK,GAAG,CAAC,gBAAgB,GAAG,UAAU,MAAM,GAAG;QACzE,MAAM,gBAAgB,AAAC,WAAW,CAAC,UAAU,MAAM,GAAG,CAAC,IAAK;QAE5D,MAAM,kBAAkB,SAAS,CAAC,cAAc;QAChD,MAAM,eAAe,SAAS,CAAC,kBAAkB;QAEjD,uBAAuB;QACvB,MAAM,WAAW,IAAI,+IAAA,CAAA,UAAO,CAC1B,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,QAAQ,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,EAAE,EAAE,gBACtE,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,QAAQ,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,EAAE,EAAE,gBACtE,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,QAAQ,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,EAAE,EAAE;QAGxE,sBAAsB;QACtB,MAAM,SAAS,IAAI,+IAAA,CAAA,UAAO,CACxB,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE,EAAE,gBAClE,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE,EAAE,gBAClE,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE,EAAE;QAGpE,8BAA8B;QAC9B,IAAI,gBAAgB,GAAG,IAAI,aAAa,GAAG,EAAE;YAC3C,UAAU,OAAO,CAAC,GAAG,GAAG,+IAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB,GAAG,EAAE,aAAa,GAAG,EAAE;YAC9E,UAAU,OAAO,CAAC,sBAAsB;QAC1C;QAEA,UAAU,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAChC,UAAU,OAAO,CAAC,MAAM,CAAC;QAEzB,sBAAsB;QACtB,IAAI,YAAY,GAAG;YACjB,UAAU,OAAO,GAAG;YACpB,UAAU,OAAO,GAAG;QACtB;IACF;IAEA,MAAM,OAAO;QACX,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;IACtB;IAEA,MAAM,OAAO;QACX,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;IACtB;IAEA,qBACE;kBACE,cAAA,8OAAC,qKAAA,CAAA,oBAAiB;YAChB,KAAK;YACL,WAAW;YACX,UAAU,SAAS,CAAC,EAAE,CAAC,QAAQ;YAC/B,KAAK,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI;YACzB,MAAM;YACN,KAAK;;;;;;;AAKb;AAGO,SAAS,uBAAuB,EACrC,SAAS;IAAC;IAAG;IAAG;CAAE,EAClB,WAAW,CAAC,EACZ,cAAc,CAAC,EACf,cAAc,EAAE,EAChB,YAAY,KAAK,EACjB,aAAa,IAAI,EACjB,eAAe,IAAI,EACnB,aAAa,KAAK,EAClB,kBAAkB,GAAG,EAWtB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,YAAY;QAEvC,MAAM,OAAO,MAAM,KAAK,CAAC,WAAW,GAAG;QACvC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ;QAC3B,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ;QAE3B,UAAU,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE;QAC7C,UAAU,OAAO,CAAC,MAAM,IAAI;IAC9B;IAEA,qBACE,8OAAC,qKAAA,CAAA,oBAAiB;QAChB,KAAK;QACL,WAAW;QACX,UAAU;YAAC;YAAG;YAAG;SAAS;QAC1B,KAAK;QACL,MAAM;QACN,KAAK;;;;;;AAGX;AAGO,SAAS,kBAAkB,EAChC,WAAW;IAAC;IAAG;IAAK;CAAE,EACtB,cAAc,KAAK,EACnB,iBAAiB,IAAI,EACrB,gBAAgB,CAAC,EAMlB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe,IAAI;IACrC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC1C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,KAAK,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI;QAC7B;QAEA,MAAM,cAAc,CAAC;YACnB,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI;QAChC;QAEA,MAAM,kBAAkB,CAAC;YACvB,IAAI,SAAS,kBAAkB,EAAE;gBAC/B,cAAc,OAAO,CAAC,CAAC,IAAI,MAAM,SAAS,GAAG;gBAC7C,cAAc,OAAO,CAAC,CAAC,IAAI,MAAM,SAAS,GAAG;YAC/C;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,gBAAgB,CAAC,SAAS;QACjC,OAAO,gBAAgB,CAAC,aAAa;QAErC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,SAAS;YACpC,OAAO,mBAAmB,CAAC,aAAa;QAC1C;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO;QACf,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,aAAa;QACb,SAAS,OAAO,CAAC,CAAC,IAAI,cAAc,OAAO,CAAC,CAAC;QAC7C,SAAS,OAAO,CAAC,CAAC,IAAI,cAAc,OAAO,CAAC,CAAC;QAC7C,SAAS,OAAO,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAS,CAAC,KAAK,CAAC,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG;QAEjF,UAAU,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,CAAC,EAAE;QAEvE,WAAW;QACX,IAAI,gBAAgB;YAClB,MAAM,aAAa,IAAI,+IAAA,CAAA,UAAO;YAE9B,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;YAC9C,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;YAC9C,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;YAC9C,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;YAE9C,WAAW,SAAS;YACpB,WAAW,cAAc,CAAC,gBAAgB;YAE1C,oCAAoC;YACpC,WAAW,UAAU,CAAC,UAAU,OAAO,CAAC,QAAQ;YAChD,UAAU,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjC;QAEA,uBAAuB;QACvB,cAAc,OAAO,CAAC,CAAC,GAAG;QAC1B,cAAc,OAAO,CAAC,CAAC,GAAG;IAC5B;IAEA,qBACE,8OAAC,qKAAA,CAAA,oBAAiB;QAChB,KAAK;QACL,WAAW;QACX,UAAU;QACV,KAAK;QACL,MAAM;QACN,KAAK;;;;;;AAGX", "debugId": null}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/hooks/usePerformance.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { PerformanceMetrics } from '@/types';\n\ninterface PerformanceConfig {\n  enableMonitoring: boolean;\n  fpsTarget: number;\n  memoryThreshold: number;\n  autoOptimize: boolean;\n}\n\nconst defaultConfig: PerformanceConfig = {\n  enableMonitoring: true,\n  fpsTarget: 60,\n  memoryThreshold: 100, // MB\n  autoOptimize: true,\n};\n\nexport function usePerformance(config: Partial<PerformanceConfig> = {}) {\n  const finalConfig = { ...defaultConfig, ...config };\n  const [metrics, setMetrics] = useState<PerformanceMetrics>({\n    fps: 0,\n    memoryUsage: 0,\n    renderTime: 0,\n    triangleCount: 0,\n  });\n  \n  const [performanceLevel, setPerformanceLevel] = useState<'high' | 'medium' | 'low'>('high');\n  const frameCount = useRef(0);\n  const lastTime = useRef(performance.now());\n  const fpsHistory = useRef<number[]>([]);\n\n  // FPS monitoring\n  useFrame((state, delta) => {\n    if (!finalConfig.enableMonitoring) return;\n\n    frameCount.current++;\n    const currentTime = performance.now();\n    \n    // Calculate FPS every second\n    if (currentTime - lastTime.current >= 1000) {\n      const fps = Math.round((frameCount.current * 1000) / (currentTime - lastTime.current));\n      \n      // Keep FPS history for averaging\n      fpsHistory.current.push(fps);\n      if (fpsHistory.current.length > 10) {\n        fpsHistory.current.shift();\n      }\n      \n      const avgFps = fpsHistory.current.reduce((a, b) => a + b, 0) / fpsHistory.current.length;\n      \n      setMetrics(prev => ({\n        ...prev,\n        fps: Math.round(avgFps),\n        renderTime: delta * 1000, // Convert to milliseconds\n      }));\n      \n      // Auto-optimize performance level\n      if (finalConfig.autoOptimize) {\n        if (avgFps < finalConfig.fpsTarget * 0.6) {\n          setPerformanceLevel('low');\n        } else if (avgFps < finalConfig.fpsTarget * 0.8) {\n          setPerformanceLevel('medium');\n        } else {\n          setPerformanceLevel('high');\n        }\n      }\n      \n      frameCount.current = 0;\n      lastTime.current = currentTime;\n    }\n  });\n\n  // Memory monitoring\n  useEffect(() => {\n    if (!finalConfig.enableMonitoring || typeof window === 'undefined') return;\n\n    const monitorMemory = () => {\n      if ('memory' in performance) {\n        const memory = (performance as any).memory;\n        const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n        \n        setMetrics(prev => ({\n          ...prev,\n          memoryUsage: Math.round(usedMB),\n        }));\n      }\n    };\n\n    const interval = setInterval(monitorMemory, 2000);\n    return () => clearInterval(interval);\n  }, [finalConfig.enableMonitoring]);\n\n  // Performance optimization recommendations\n  const getOptimizationSuggestions = useCallback(() => {\n    const suggestions: string[] = [];\n    \n    if (metrics.fps < finalConfig.fpsTarget * 0.8) {\n      suggestions.push('Consider reducing shadow quality');\n      suggestions.push('Disable post-processing effects');\n      suggestions.push('Reduce model complexity');\n    }\n    \n    if (metrics.memoryUsage > finalConfig.memoryThreshold) {\n      suggestions.push('Enable texture compression');\n      suggestions.push('Implement model LOD (Level of Detail)');\n      suggestions.push('Use instanced rendering for repeated objects');\n    }\n    \n    if (metrics.renderTime > 16.67) { // 60fps = 16.67ms per frame\n      suggestions.push('Optimize shaders');\n      suggestions.push('Reduce draw calls');\n      suggestions.push('Use frustum culling');\n    }\n    \n    return suggestions;\n  }, [metrics, finalConfig]);\n\n  // Performance settings based on current level\n  const getPerformanceSettings = useCallback(() => {\n    switch (performanceLevel) {\n      case 'low':\n        return {\n          shadows: false,\n          antialias: false,\n          postProcessing: false,\n          particleCount: 50,\n          modelLOD: 'low',\n          textureQuality: 'low',\n        };\n      case 'medium':\n        return {\n          shadows: true,\n          antialias: false,\n          postProcessing: false,\n          particleCount: 100,\n          modelLOD: 'medium',\n          textureQuality: 'medium',\n        };\n      case 'high':\n      default:\n        return {\n          shadows: true,\n          antialias: true,\n          postProcessing: true,\n          particleCount: 200,\n          modelLOD: 'high',\n          textureQuality: 'high',\n        };\n    }\n  }, [performanceLevel]);\n\n  return {\n    metrics,\n    performanceLevel,\n    setPerformanceLevel,\n    getOptimizationSuggestions,\n    getPerformanceSettings,\n    isPerformanceGood: metrics.fps >= finalConfig.fpsTarget * 0.8,\n  };\n}\n\n// Hook for device capability detection\nexport function useDeviceCapabilities() {\n  const [capabilities, setCapabilities] = useState({\n    webgl2: false,\n    maxTextureSize: 0,\n    maxVertexUniforms: 0,\n    maxFragmentUniforms: 0,\n    extensions: [] as string[],\n    isMobile: false,\n    isLowEnd: false,\n  });\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const canvas = document.createElement('canvas');\n    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');\n    \n    if (gl) {\n      const webgl2 = gl instanceof WebGL2RenderingContext;\n      const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);\n      const maxVertexUniforms = gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS);\n      const maxFragmentUniforms = gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS);\n      const extensions = gl.getSupportedExtensions() || [];\n      \n      // Detect mobile devices\n      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n        navigator.userAgent\n      );\n      \n      // Detect low-end devices\n      const isLowEnd = isMobile || maxTextureSize < 4096 || navigator.hardwareConcurrency < 4;\n      \n      setCapabilities({\n        webgl2,\n        maxTextureSize,\n        maxVertexUniforms,\n        maxFragmentUniforms,\n        extensions,\n        isMobile,\n        isLowEnd,\n      });\n    }\n  }, []);\n\n  return capabilities;\n}\n\n// Hook for adaptive quality based on performance\nexport function useAdaptiveQuality() {\n  const { performanceLevel, getPerformanceSettings } = usePerformance();\n  const { isLowEnd } = useDeviceCapabilities();\n  \n  const settings = getPerformanceSettings();\n  \n  // Override settings for low-end devices\n  if (isLowEnd) {\n    return {\n      ...settings,\n      shadows: false,\n      antialias: false,\n      postProcessing: false,\n      particleCount: Math.min(settings.particleCount, 25),\n      modelLOD: 'low',\n      textureQuality: 'low',\n    };\n  }\n  \n  return settings;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;AAaA,MAAM,gBAAmC;IACvC,kBAAkB;IAClB,WAAW;IACX,iBAAiB;IACjB,cAAc;AAChB;AAEO,SAAS,eAAe,SAAqC,CAAC,CAAC;IACpE,MAAM,cAAc;QAAE,GAAG,aAAa;QAAE,GAAG,MAAM;IAAC;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACzD,KAAK;QACL,aAAa;QACb,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACpF,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,YAAY,GAAG;IACvC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAY,EAAE;IAEtC,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO;QACf,IAAI,CAAC,YAAY,gBAAgB,EAAE;QAEnC,WAAW,OAAO;QAClB,MAAM,cAAc,YAAY,GAAG;QAEnC,6BAA6B;QAC7B,IAAI,cAAc,SAAS,OAAO,IAAI,MAAM;YAC1C,MAAM,MAAM,KAAK,KAAK,CAAC,AAAC,WAAW,OAAO,GAAG,OAAQ,CAAC,cAAc,SAAS,OAAO;YAEpF,iCAAiC;YACjC,WAAW,OAAO,CAAC,IAAI,CAAC;YACxB,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,IAAI;gBAClC,WAAW,OAAO,CAAC,KAAK;YAC1B;YAEA,MAAM,SAAS,WAAW,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,WAAW,OAAO,CAAC,MAAM;YAExF,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,KAAK,KAAK,KAAK,CAAC;oBAChB,YAAY,QAAQ;gBACtB,CAAC;YAED,kCAAkC;YAClC,IAAI,YAAY,YAAY,EAAE;gBAC5B,IAAI,SAAS,YAAY,SAAS,GAAG,KAAK;oBACxC,oBAAoB;gBACtB,OAAO,IAAI,SAAS,YAAY,SAAS,GAAG,KAAK;oBAC/C,oBAAoB;gBACtB,OAAO;oBACL,oBAAoB;gBACtB;YACF;YAEA,WAAW,OAAO,GAAG;YACrB,SAAS,OAAO,GAAG;QACrB;IACF;IAEA,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAoE;;QAEpE,MAAM;QAYN,MAAM;IAER,GAAG;QAAC,YAAY,gBAAgB;KAAC;IAEjC,2CAA2C;IAC3C,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,MAAM,cAAwB,EAAE;QAEhC,IAAI,QAAQ,GAAG,GAAG,YAAY,SAAS,GAAG,KAAK;YAC7C,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,IAAI,QAAQ,WAAW,GAAG,YAAY,eAAe,EAAE;YACrD,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,IAAI,QAAQ,UAAU,GAAG,OAAO;YAC9B,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,OAAO;IACT,GAAG;QAAC;QAAS;KAAY;IAEzB,8CAA8C;IAC9C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,gBAAgB;oBAChB,eAAe;oBACf,UAAU;oBACV,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,gBAAgB;oBAChB,eAAe;oBACf,UAAU;oBACV,gBAAgB;gBAClB;YACF,KAAK;YACL;gBACE,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,gBAAgB;oBAChB,eAAe;oBACf,UAAU;oBACV,gBAAgB;gBAClB;QACJ;IACF,GAAG;QAAC;KAAiB;IAErB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,mBAAmB,QAAQ,GAAG,IAAI,YAAY,SAAS,GAAG;IAC5D;AACF;AAGO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,QAAQ;QACR,gBAAgB;QAChB,mBAAmB;QACnB,qBAAqB;QACrB,YAAY,EAAE;QACd,UAAU;QACV,UAAU;IACZ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;QACN,MAAM;IA2BR,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,GAAG;IACrD,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,WAAW;IAEjB,wCAAwC;IACxC,IAAI,UAAU;QACZ,OAAO;YACL,GAAG,QAAQ;YACX,SAAS;YACT,WAAW;YACX,gBAAgB;YAChB,eAAe,KAAK,GAAG,CAAC,SAAS,aAAa,EAAE;YAChD,UAAU;YACV,gBAAgB;QAClB;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { HeroScene } from '@/components/3d/SceneManager';\nimport { Microchip } from '@/components/3d/Microchip';\nimport { MicrochipGrid } from '@/components/3d/MicrochipModel';\nimport { CPUModel } from '@/components/3d/PlaceholderModels';\nimport { InteractiveCamera } from '@/components/3d/InteractiveCamera';\nimport { Button } from '@/components/ui/Button';\nimport { useTheme } from '@/hooks/useTheme';\nimport { useAdaptiveQuality } from '@/hooks/usePerformance';\nimport { cn } from '@/lib/utils';\n\ninterface HeroSectionProps {\n  className?: string;\n}\n\nconst heroTexts = [\n  \"3D Portfolio\",\n  \"Interactive Design\",\n  \"Web Innovation\",\n  \"Digital Experience\"\n];\n\nexport function HeroSection({ className }: HeroSectionProps) {\n  const { toggleTheme, theme } = useTheme();\n  const qualitySettings = useAdaptiveQuality();\n  const [currentTextIndex, setCurrentTextIndex] = useState(0);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  // Cycle through hero texts\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentTextIndex((prev) => (prev + 1) % heroTexts.length);\n    }, 3000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Loading animation\n  useEffect(() => {\n    const timer = setTimeout(() => setIsLoaded(true), 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const scrollToSection = (sectionId: string) => {\n    document.getElementById(sectionId)?.scrollIntoView({ \n      behavior: 'smooth',\n      block: 'start'\n    });\n  };\n\n  return (\n    <section className={cn(\n      \"relative h-screen flex items-center justify-center overflow-hidden\",\n      \"bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary\",\n      className\n    )}>\n      {/* Animated Background Pattern */}\n      <div className=\"absolute inset-0\">\n        <div className=\"circuit-bg opacity-20 animate-pulse\" />\n        <div className=\"absolute inset-0 bg-gradient-to-t from-bg-primary/50 to-transparent\" />\n      </div>\n      \n      {/* 3D Scene */}\n      <div className=\"absolute inset-0 z-10\">\n        <HeroScene\n          enableControls={false}\n          enableEnvironment={true}\n          shadows={qualitySettings.shadows}\n          antialias={qualitySettings.antialias}\n          lighting=\"dramatic\"\n        >\n          {/* Interactive Camera */}\n          <InteractiveCamera\n            enableMouseTracking={true}\n            enableScrollTracking={true}\n            mouseSensitivity={0.3}\n            scrollSensitivity={0.2}\n            basePosition={[0, 0, 8]}\n            lookAt={[0, 0, 0]}\n            smoothing={0.08}\n          />\n          {/* Main hero microchip */}\n          <CPUModel\n            position={[0, 0, 0]}\n            scale={1.2}\n            animated={true}\n            glowEffect={true}\n          />\n          \n          {/* Floating microchips around the main one */}\n          <Microchip\n            position={[-3, 1, -2]}\n            scale={0.6}\n            animated={true}\n            glowEffect={true}\n          />\n          <Microchip\n            position={[3, -1, -2]}\n            scale={0.8}\n            animated={true}\n            glowEffect={true}\n          />\n          <Microchip\n            position={[0, 2, -3]}\n            scale={0.5}\n            animated={true}\n            glowEffect={true}\n          />\n          \n          {/* Background grid for depth */}\n          {qualitySettings.particleCount > 50 && (\n            <MicrochipGrid\n              count={Math.min(qualitySettings.particleCount / 4, 15)}\n              spread={20}\n              animated={true}\n            />\n          )}\n        </HeroScene>\n      </div>\n      \n      {/* Content Overlay */}\n      <div className=\"relative z-20 text-center px-4 max-w-6xl mx-auto\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key=\"hero-content\"\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 50 }}\n            transition={{ duration: 1, delay: 0.5 }}\n            className=\"space-y-8\"\n          >\n            {/* Animated Title */}\n            <div className=\"relative h-32 flex items-center justify-center\">\n              <AnimatePresence mode=\"wait\">\n                <motion.h1\n                  key={currentTextIndex}\n                  initial={{ opacity: 0, rotateX: 90 }}\n                  animate={{ opacity: 1, rotateX: 0 }}\n                  exit={{ opacity: 0, rotateX: -90 }}\n                  transition={{ duration: 0.8 }}\n                  className=\"text-6xl md:text-8xl lg:text-9xl font-bold text-glow\"\n                >\n                  <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n                    {heroTexts[currentTextIndex]}\n                  </span>\n                </motion.h1>\n              </AnimatePresence>\n            </div>\n            \n            {/* Subtitle */}\n            <motion.p\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1 }}\n              className=\"text-xl md:text-2xl lg:text-3xl text-text-secondary max-w-4xl mx-auto leading-relaxed\"\n            >\n              Welcome to an immersive 3D experience showcasing cutting-edge web development \n              with interactive microchip-themed design and modern technologies.\n            </motion.p>\n            \n            {/* Action Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.3 }}\n              className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\"\n            >\n              <Button\n                variant=\"primary\"\n                size=\"xl\"\n                className=\"animate-pulse-glow group\"\n                onClick={() => scrollToSection('about')}\n              >\n                <span className=\"group-hover:scale-110 transition-transform\">\n                  🚀 Explore Portfolio\n                </span>\n              </Button>\n              \n              <Button\n                variant=\"outline\"\n                size=\"xl\"\n                onClick={() => scrollToSection('projects')}\n                className=\"group\"\n              >\n                <span className=\"group-hover:scale-110 transition-transform\">\n                  💼 View Projects\n                </span>\n              </Button>\n              \n              <Button\n                variant=\"secondary\"\n                size=\"xl\"\n                onClick={() => scrollToSection('contact')}\n                className=\"group\"\n              >\n                <span className=\"group-hover:scale-110 transition-transform\">\n                  📧 Get In Touch\n                </span>\n              </Button>\n            </motion.div>\n            \n            {/* Tech Stack Indicators */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.6 }}\n              className=\"flex flex-wrap justify-center gap-4 mt-12\"\n            >\n              {['React', 'Three.js', 'TypeScript', 'Next.js', 'Tailwind'].map((tech, index) => (\n                <motion.div\n                  key={tech}\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 1.8 + (index * 0.1) }}\n                  className=\"chip-border px-4 py-2 text-sm font-medium text-primary bg-bg-secondary/50 backdrop-blur-sm\"\n                >\n                  {tech}\n                </motion.div>\n              ))}\n            </motion.div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n      \n      {/* Theme Toggle */}\n      <motion.div\n        initial={{ opacity: 0, x: 50 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.8, delay: 2 }}\n        className=\"absolute top-6 right-6 z-30\"\n      >\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={toggleTheme}\n          className=\"chip-border backdrop-blur-sm bg-bg-secondary/30 hover:bg-bg-secondary/50\"\n        >\n          <motion.span\n            key={theme}\n            initial={{ rotate: 180, opacity: 0 }}\n            animate={{ rotate: 0, opacity: 1 }}\n            transition={{ duration: 0.5 }}\n            className=\"text-2xl\"\n          >\n            {theme === 'dark' ? '☀️' : '🌙'}\n          </motion.span>\n        </Button>\n      </motion.div>\n      \n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, delay: 2.2 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"flex flex-col items-center space-y-2 cursor-pointer\"\n          onClick={() => scrollToSection('about')}\n        >\n          <div className=\"w-6 h-10 border-2 border-primary rounded-full flex justify-center relative overflow-hidden\">\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-primary rounded-full mt-2\"\n            />\n          </div>\n          <span className=\"text-xs text-text-secondary font-medium\">Scroll Down</span>\n        </motion.div>\n      </motion.div>\n      \n      {/* Loading Overlay */}\n      <AnimatePresence>\n        {!isLoaded && (\n          <motion.div\n            initial={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"absolute inset-0 z-50 bg-bg-primary flex items-center justify-center\"\n          >\n            <div className=\"text-center space-y-4\">\n              <motion.div\n                animate={{ rotate: 360 }}\n                transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                className=\"w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto\"\n              />\n              <motion.p\n                animate={{ opacity: [0.5, 1, 0.5] }}\n                transition={{ duration: 1.5, repeat: Infinity }}\n                className=\"text-primary font-medium\"\n              >\n                Initializing 3D Experience...\n              </motion.p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAkBA,MAAM,YAAY;IAChB;IACA;IACA;IACA;CACD;AAEM,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,kBAAkB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,oBAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM;QAC7D,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW,IAAM,YAAY,OAAO;QAClD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,SAAS,cAAc,CAAC,YAAY,eAAe;YACjD,UAAU;YACV,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,sEACA,oEACA;;0BAGA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wIAAA,CAAA,YAAS;oBACR,gBAAgB;oBAChB,mBAAmB;oBACnB,SAAS,gBAAgB,OAAO;oBAChC,WAAW,gBAAgB,SAAS;oBACpC,UAAS;;sCAGT,8OAAC,6IAAA,CAAA,oBAAiB;4BAChB,qBAAqB;4BACrB,sBAAsB;4BACtB,kBAAkB;4BAClB,mBAAmB;4BACnB,cAAc;gCAAC;gCAAG;gCAAG;6BAAE;4BACvB,QAAQ;gCAAC;gCAAG;gCAAG;6BAAE;4BACjB,WAAW;;;;;;sCAGb,8OAAC,6IAAA,CAAA,WAAQ;4BACP,UAAU;gCAAC;gCAAG;gCAAG;6BAAE;4BACnB,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;sCAId,8OAAC,qIAAA,CAAA,YAAS;4BACR,UAAU;gCAAC,CAAC;gCAAG;gCAAG,CAAC;6BAAE;4BACrB,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;sCAEd,8OAAC,qIAAA,CAAA,YAAS;4BACR,UAAU;gCAAC;gCAAG,CAAC;gCAAG,CAAC;6BAAE;4BACrB,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;sCAEd,8OAAC,qIAAA,CAAA,YAAS;4BACR,UAAU;gCAAC;gCAAG;gCAAG,CAAC;6BAAE;4BACpB,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;wBAIb,gBAAgB,aAAa,GAAG,oBAC/B,8OAAC,0IAAA,CAAA,gBAAa;4BACZ,OAAO,KAAK,GAAG,CAAC,gBAAgB,aAAa,GAAG,GAAG;4BACnD,QAAQ;4BACR,UAAU;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS,WAAW,IAAI;4BAAG,GAAG,WAAW,IAAI;wBAAG;wBAC3D,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;wBACtC,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oCAAC,MAAK;8CACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCAER,SAAS;4CAAE,SAAS;4CAAG,SAAS;wCAAG;wCACnC,SAAS;4CAAE,SAAS;4CAAG,SAAS;wCAAE;wCAClC,MAAM;4CAAE,SAAS;4CAAG,SAAS,CAAC;wCAAG;wCACjC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDACb,SAAS,CAAC,iBAAiB;;;;;;uCARzB;;;;;;;;;;;;;;;0CAeX,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAE;gCACtC,WAAU;0CACX;;;;;;0CAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,gBAAgB;kDAE/B,cAAA,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;;;;;;kDAK/D,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;;;;;;kDAK/D,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAA6C;;;;;;;;;;;;;;;;;0CAOjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAET;oCAAC;oCAAS;oCAAY;oCAAc;oCAAW;iCAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAO,QAAQ;wCAAK;wCACxD,WAAU;kDAET;uCANI;;;;;;;;;;;uBArFP;;;;;;;;;;;;;;;0BAoGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAE;gBACtC,WAAU;0BAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBAEV,SAAS;4BAAE,QAAQ;4BAAK,SAAS;wBAAE;wBACnC,SAAS;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBACjC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET,UAAU,SAAS,OAAO;uBANtB;;;;;;;;;;;;;;;0BAYX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;oBACV,SAAS,IAAM,gBAAgB;;sCAE/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;sCAGd,8OAAC;4BAAK,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAK9D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,CAAC,0BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ;gCAAI;gCACvB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAS;gCAC5D,WAAU;;;;;;0CAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCAAC;gCAClC,YAAY;oCAAE,UAAU;oCAAK,QAAQ;gCAAS;gCAC9C,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/PhotoFrame.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame, useLoader } from '@react-three/fiber';\nimport { RoundedBox, Text, Html } from '@react-three/drei';\nimport { TextureLoader } from 'three';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface PhotoFrameProps {\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  imageUrl?: string;\n  animated?: boolean;\n  glowEffect?: boolean;\n  frameStyle?: 'modern' | 'chip' | 'holographic' | 'circuit';\n  onClick?: () => void;\n  onHover?: (hovered: boolean) => void;\n}\n\nexport function PhotoFrame({\n  position = [0, 0, 0],\n  scale = 1,\n  rotation = [0, 0, 0],\n  imageUrl = '/images/developer-photo.jpg',\n  animated = true,\n  glowEffect = true,\n  frameStyle = 'chip',\n  onClick,\n  onHover,\n}: PhotoFrameProps) {\n  const groupRef = useRef<Group>(null);\n  const frameRef = useRef<Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const { themeConfig } = useTheme();\n\n  // Try to load texture, fallback to placeholder\n  let texture;\n  try {\n    texture = useLoader(TextureLoader, imageUrl);\n    if (texture && !imageLoaded) {\n      setImageLoaded(true);\n    }\n  } catch (error) {\n    console.log('Image not found, using placeholder');\n    texture = null;\n  }\n\n  // Animation loop\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    \n    // Gentle rotation animation\n    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n    \n    // Floating animation\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.05;\n    \n    // Hover effect\n    if (hovered && frameRef.current) {\n      frameRef.current.scale.setScalar(1.05);\n    } else if (frameRef.current) {\n      frameRef.current.scale.setScalar(1);\n    }\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(false);\n    document.body.style.cursor = 'auto';\n  };\n\n  const getFrameStyle = () => {\n    switch (frameStyle) {\n      case 'modern':\n        return {\n          frameColor: '#ffffff',\n          frameMetalness: 0.9,\n          frameRoughness: 0.1,\n          borderWidth: 0.1,\n        };\n      case 'chip':\n        return {\n          frameColor: themeConfig.primaryColor,\n          frameMetalness: 0.8,\n          frameRoughness: 0.2,\n          borderWidth: 0.15,\n        };\n      case 'holographic':\n        return {\n          frameColor: '#00ffff',\n          frameMetalness: 1,\n          frameRoughness: 0,\n          borderWidth: 0.05,\n        };\n      case 'circuit':\n        return {\n          frameColor: '#1a4a1a',\n          frameMetalness: 0.3,\n          frameRoughness: 0.7,\n          borderWidth: 0.2,\n        };\n      default:\n        return {\n          frameColor: themeConfig.primaryColor,\n          frameMetalness: 0.8,\n          frameRoughness: 0.2,\n          borderWidth: 0.15,\n        };\n    }\n  };\n\n  const frameStyle_config = getFrameStyle();\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      rotation={rotation}\n      onClick={onClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      {/* Main Frame */}\n      <RoundedBox\n        ref={frameRef}\n        args={[3, 4, 0.3]}\n        radius={0.1}\n        castShadow\n        receiveShadow\n      >\n        <meshStandardMaterial\n          color={frameStyle_config.frameColor}\n          metalness={frameStyle_config.frameMetalness}\n          roughness={frameStyle_config.frameRoughness}\n          emissive={glowEffect && hovered ? themeConfig.primaryColor : '#000000'}\n          emissiveIntensity={hovered ? 0.2 : 0}\n        />\n      </RoundedBox>\n\n      {/* Photo/Screen */}\n      <RoundedBox\n        args={[2.6, 3.6, 0.05]}\n        radius={0.05}\n        position={[0, 0, 0.18]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color={texture ? '#ffffff' : '#2a2a2a'}\n          map={texture}\n          emissive={!texture ? themeConfig.secondaryColor : '#000000'}\n          emissiveIntensity={!texture ? 0.1 : 0}\n        />\n      </RoundedBox>\n\n      {/* Circuit Pattern Frame (for circuit style) */}\n      {frameStyle === 'circuit' && (\n        <>\n          {/* Circuit traces */}\n          {Array.from({ length: 12 }, (_, i) => (\n            <RoundedBox\n              key={`trace-${i}`}\n              args={[0.02, Math.random() * 2 + 1, 0.01]}\n              radius={0.005}\n              position={[\n                -1.3 + (i * 0.22),\n                (Math.random() - 0.5) * 3,\n                0.16\n              ]}\n              castShadow\n            >\n              <meshStandardMaterial\n                color={themeConfig.primaryColor}\n                emissive={themeConfig.primaryColor}\n                emissiveIntensity={0.3}\n              />\n            </RoundedBox>\n          ))}\n          \n          {/* Corner connectors */}\n          {[[-1.3, 1.7], [1.3, 1.7], [-1.3, -1.7], [1.3, -1.7]].map(([x, y], i) => (\n            <RoundedBox\n              key={`connector-${i}`}\n              args={[0.1, 0.1, 0.05]}\n              radius={0.02}\n              position={[x, y, 0.16]}\n              castShadow\n            >\n              <meshStandardMaterial\n                color=\"#ffd700\"\n                metalness={1}\n                roughness={0.1}\n              />\n            </RoundedBox>\n          ))}\n        </>\n      )}\n\n      {/* Holographic effect (for holographic style) */}\n      {frameStyle === 'holographic' && hovered && (\n        <RoundedBox\n          args={[3.2, 4.2, 0.35]}\n          radius={0.1}\n          position={[0, 0, 0]}\n        >\n          <meshBasicMaterial\n            color=\"#00ffff\"\n            transparent\n            opacity={0.1}\n          />\n        </RoundedBox>\n      )}\n\n      {/* Chip pins (for chip style) */}\n      {frameStyle === 'chip' && (\n        <>\n          {Array.from({ length: 16 }, (_, i) => {\n            const side = Math.floor(i / 4);\n            const pinIndex = i % 4;\n            let pinPosition: [number, number, number];\n            \n            switch (side) {\n              case 0: // Top\n                pinPosition = [-0.9 + (pinIndex * 0.6), 2.1, 0];\n                break;\n              case 1: // Right\n                pinPosition = [1.6, 1.2 - (pinIndex * 0.8), 0];\n                break;\n              case 2: // Bottom\n                pinPosition = [0.9 - (pinIndex * 0.6), -2.1, 0];\n                break;\n              case 3: // Left\n                pinPosition = [-1.6, -1.2 + (pinIndex * 0.8), 0];\n                break;\n              default:\n                pinPosition = [0, 0, 0];\n            }\n\n            return (\n              <RoundedBox\n                key={`pin-${i}`}\n                args={[0.08, 0.3, 0.05]}\n                radius={0.01}\n                position={pinPosition}\n                castShadow\n              >\n                <meshStandardMaterial\n                  color=\"#ffd700\"\n                  metalness={1}\n                  roughness={0.1}\n                />\n              </RoundedBox>\n            );\n          })}\n        </>\n      )}\n\n      {/* Loading indicator when image is not available */}\n      {!texture && (\n        <Html\n          position={[0, 0, 0.2]}\n          center\n          distanceFactor={10}\n        >\n          <div className=\"text-center p-4 bg-bg-secondary/80 rounded-lg backdrop-blur-sm\">\n            <div className=\"w-16 h-16 mx-auto mb-2 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center\">\n              <span className=\"text-2xl\">👨‍💻</span>\n            </div>\n            <p className=\"text-sm text-text-secondary\">Developer Photo</p>\n          </div>\n        </Html>\n      )}\n\n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <RoundedBox\n          args={[3.4, 4.4, 0.4]}\n          radius={0.1}\n          position={[0, 0, 0]}\n        >\n          <meshBasicMaterial\n            color={themeConfig.primaryColor}\n            transparent\n            opacity={0.1}\n          />\n        </RoundedBox>\n      )}\n    </group>\n  );\n}\n\n// Floating photo gallery component\nexport function PhotoGallery({\n  photos = [],\n  position = [0, 0, 0],\n  spacing = 2,\n}: {\n  photos?: string[];\n  position?: [number, number, number];\n  spacing?: number;\n}) {\n  const groupRef = useRef<Group>(null);\n\n  useFrame((state) => {\n    if (!groupRef.current) return;\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n  });\n\n  return (\n    <group ref={groupRef} position={position}>\n      {photos.map((photo, index) => {\n        const angle = (index / photos.length) * Math.PI * 2;\n        const x = Math.cos(angle) * spacing;\n        const z = Math.sin(angle) * spacing;\n        \n        return (\n          <PhotoFrame\n            key={index}\n            position={[x, 0, z]}\n            scale={0.5}\n            imageUrl={photo}\n            frameStyle=\"modern\"\n            animated={false}\n          />\n        );\n      })}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAEA;AAPA;;;;;;;AAqBO,SAAS,WAAW,EACzB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,WAAW,6BAA6B,EACxC,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,aAAa,MAAM,EACnB,OAAO,EACP,OAAO,EACS;IAChB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAQ;IAC9B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,+CAA+C;IAC/C,IAAI;IACJ,IAAI;QACF,UAAU,CAAA,GAAA,gNAAA,CAAA,YAAS,AAAD,EAAE,+IAAA,CAAA,gBAAa,EAAE;QACnC,IAAI,WAAW,CAAC,aAAa;YAC3B,eAAe;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;QACZ,UAAU;IACZ;IAEA,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QAEpC,4BAA4B;QAC5B,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QAExE,qBAAqB;QACrB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;QAEpF,eAAe;QACf,IAAI,WAAW,SAAS,OAAO,EAAE;YAC/B,SAAS,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QACnC,OAAO,IAAI,SAAS,OAAO,EAAE;YAC3B,SAAS,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QACnC;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU;QACV,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,YAAY;oBACZ,gBAAgB;oBAChB,gBAAgB;oBAChB,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,YAAY,YAAY,YAAY;oBACpC,gBAAgB;oBAChB,gBAAgB;oBAChB,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,YAAY;oBACZ,gBAAgB;oBAChB,gBAAgB;oBAChB,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,YAAY;oBACZ,gBAAgB;oBAChB,gBAAgB;oBAChB,aAAa;gBACf;YACF;gBACE,OAAO;oBACL,YAAY,YAAY,YAAY;oBACpC,gBAAgB;oBAChB,gBAAgB;oBAChB,aAAa;gBACf;QACJ;IACF;IAEA,MAAM,oBAAoB;IAE1B,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,eAAe;QACf,cAAc;;0BAGd,8OAAC,8JAAA,CAAA,aAAU;gBACT,KAAK;gBACL,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBACjB,QAAQ;gBACR,UAAU;gBACV,aAAa;0BAEb,cAAA,8OAAC;oBACC,OAAO,kBAAkB,UAAU;oBACnC,WAAW,kBAAkB,cAAc;oBAC3C,WAAW,kBAAkB,cAAc;oBAC3C,UAAU,cAAc,UAAU,YAAY,YAAY,GAAG;oBAC7D,mBAAmB,UAAU,MAAM;;;;;;;;;;;0BAKvC,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAK;iBAAK;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBACtB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAO,UAAU,YAAY;oBAC7B,KAAK;oBACL,UAAU,CAAC,UAAU,YAAY,cAAc,GAAG;oBAClD,mBAAmB,CAAC,UAAU,MAAM;;;;;;;;;;;YAKvC,eAAe,2BACd;;oBAEG,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,8JAAA,CAAA,aAAU;4BAET,MAAM;gCAAC;gCAAM,KAAK,MAAM,KAAK,IAAI;gCAAG;6BAAK;4BACzC,QAAQ;4BACR,UAAU;gCACR,CAAC,MAAO,IAAI;gCACZ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCACxB;6BACD;4BACD,UAAU;sCAEV,cAAA,8OAAC;gCACC,OAAO,YAAY,YAAY;gCAC/B,UAAU,YAAY,YAAY;gCAClC,mBAAmB;;;;;;2BAbhB,CAAC,MAAM,EAAE,GAAG;;;;;oBAmBpB;wBAAC;4BAAC,CAAC;4BAAK;yBAAI;wBAAE;4BAAC;4BAAK;yBAAI;wBAAE;4BAAC,CAAC;4BAAK,CAAC;yBAAI;wBAAE;4BAAC;4BAAK,CAAC;yBAAI;qBAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,kBACjE,8OAAC,8JAAA,CAAA,aAAU;4BAET,MAAM;gCAAC;gCAAK;gCAAK;6BAAK;4BACtB,QAAQ;4BACR,UAAU;gCAAC;gCAAG;gCAAG;6BAAK;4BACtB,UAAU;sCAEV,cAAA,8OAAC;gCACC,OAAM;gCACN,WAAW;gCACX,WAAW;;;;;;2BATR,CAAC,UAAU,EAAE,GAAG;;;;;;;YAiB5B,eAAe,iBAAiB,yBAC/B,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAK;iBAAK;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;;;;;;;;;;;YAMd,eAAe,wBACd;0BACG,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAG,GAAG,CAAC,GAAG;oBAC9B,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI;oBAC5B,MAAM,WAAW,IAAI;oBACrB,IAAI;oBAEJ,OAAQ;wBACN,KAAK;4BACH,cAAc;gCAAC,CAAC,MAAO,WAAW;gCAAM;gCAAK;6BAAE;4BAC/C;wBACF,KAAK;4BACH,cAAc;gCAAC;gCAAK,MAAO,WAAW;gCAAM;6BAAE;4BAC9C;wBACF,KAAK;4BACH,cAAc;gCAAC,MAAO,WAAW;gCAAM,CAAC;gCAAK;6BAAE;4BAC/C;wBACF,KAAK;4BACH,cAAc;gCAAC,CAAC;gCAAK,CAAC,MAAO,WAAW;gCAAM;6BAAE;4BAChD;wBACF;4BACE,cAAc;gCAAC;gCAAG;gCAAG;6BAAE;oBAC3B;oBAEA,qBACE,8OAAC,8JAAA,CAAA,aAAU;wBAET,MAAM;4BAAC;4BAAM;4BAAK;yBAAK;wBACvB,QAAQ;wBACR,UAAU;wBACV,UAAU;kCAEV,cAAA,8OAAC;4BACC,OAAM;4BACN,WAAW;4BACX,WAAW;;;;;;uBATR,CAAC,IAAI,EAAE,GAAG;;;;;gBAarB;;YAKH,CAAC,yBACA,8OAAC,uJAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBACrB,MAAM;gBACN,gBAAgB;0BAEhB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;sCAE7B,8OAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;;;;;;YAMhD,cAAc,yBACb,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBACrB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAMrB;AAGO,SAAS,aAAa,EAC3B,SAAS,EAAE,EACX,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,UAAU,CAAC,EAKZ;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,SAAS,OAAO,EAAE;QACvB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;IAC1D;IAEA,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;kBAC7B,OAAO,GAAG,CAAC,CAAC,OAAO;YAClB,MAAM,QAAQ,AAAC,QAAQ,OAAO,MAAM,GAAI,KAAK,EAAE,GAAG;YAClD,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS;YAC5B,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS;YAE5B,qBACE,8OAAC;gBAEC,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBACnB,OAAO;gBACP,UAAU;gBACV,YAAW;gBACX,UAAU;eALL;;;;;QAQX;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 3109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/SkillChip.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { RoundedBox, Text, Html } from '@react-three/drei';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\nimport { Skill } from '@/types';\n\ninterface SkillChipProps {\n  skill: Skill;\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  animated?: boolean;\n  glowEffect?: boolean;\n  onClick?: (skill: Skill) => void;\n  onHover?: (skill: Skill, hovered: boolean) => void;\n}\n\nexport function SkillChip({\n  skill,\n  position = [0, 0, 0],\n  scale = 1,\n  rotation = [0, 0, 0],\n  animated = true,\n  glowEffect = true,\n  onClick,\n  onHover,\n}: SkillChipProps) {\n  const groupRef = useRef<Group>(null);\n  const chipRef = useRef<Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  const [clicked, setClicked] = useState(false);\n  const { themeConfig } = useTheme();\n\n  // Get color based on skill category\n  const getCategoryColor = (category: string) => {\n    const colors = {\n      frontend: themeConfig.primaryColor,\n      backend: themeConfig.secondaryColor,\n      database: themeConfig.accentColor,\n      devops: '#9333ea',\n      mobile: '#06b6d4',\n      'ai-ml': '#f59e0b',\n      tools: '#10b981',\n      'soft-skills': '#ef4444',\n    };\n    return colors[category as keyof typeof colors] || themeConfig.primaryColor;\n  };\n\n  // Get skill level intensity\n  const getLevelIntensity = (level: string) => {\n    const intensities = {\n      beginner: 0.3,\n      intermediate: 0.5,\n      advanced: 0.7,\n      expert: 1.0,\n    };\n    return intensities[level as keyof typeof intensities] || 0.5;\n  };\n\n  const categoryColor = getCategoryColor(skill.category);\n  const levelIntensity = getLevelIntensity(skill.level);\n\n  // Animation loop\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    \n    // Gentle floating animation\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2 + position[0]) * 0.1;\n    \n    // Rotation based on skill level\n    if (skill.level === 'expert') {\n      groupRef.current.rotation.y = state.clock.elapsedTime * 0.5;\n    } else if (skill.level === 'advanced') {\n      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.3;\n    }\n    \n    // Hover and click effects\n    if (chipRef.current) {\n      const targetScale = clicked ? 1.2 : (hovered ? 1.1 : 1);\n      chipRef.current.scale.lerp({ x: targetScale, y: targetScale, z: targetScale } as any, 0.1);\n    }\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(skill, true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(skill, false);\n    document.body.style.cursor = 'auto';\n  };\n\n  const handleClick = () => {\n    setClicked(!clicked);\n    onClick?.(skill);\n  };\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      rotation={rotation}\n      onClick={handleClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      {/* Main chip body */}\n      <RoundedBox\n        ref={chipRef}\n        args={[2, 0.3, 1]}\n        radius={0.1}\n        castShadow\n        receiveShadow\n      >\n        <meshStandardMaterial\n          color={hovered ? categoryColor : '#2a2a2a'}\n          metalness={0.8}\n          roughness={0.2}\n          emissive={glowEffect ? categoryColor : '#000000'}\n          emissiveIntensity={(hovered ? 0.3 : 0.1) * levelIntensity}\n        />\n      </RoundedBox>\n\n      {/* Skill level indicators */}\n      {Array.from({ length: 4 }, (_, i) => (\n        <RoundedBox\n          key={`level-${i}`}\n          args={[0.15, 0.05, 0.8]}\n          radius={0.02}\n          position={[-0.6 + (i * 0.4), 0.18, 0]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={i < ['beginner', 'intermediate', 'advanced', 'expert'].indexOf(skill.level) + 1 ? categoryColor : '#555555'}\n            emissive={i < ['beginner', 'intermediate', 'advanced', 'expert'].indexOf(skill.level) + 1 ? categoryColor : '#000000'}\n            emissiveIntensity={0.2}\n          />\n        </RoundedBox>\n      ))}\n\n      {/* Years of experience indicator */}\n      <RoundedBox\n        args={[0.3, 0.1, 0.3]}\n        radius={0.05}\n        position={[0.7, 0.2, 0]}\n        castShadow\n      >\n        <meshStandardMaterial\n          color=\"#ffd700\"\n          metalness={1}\n          roughness={0.1}\n        />\n      </RoundedBox>\n\n      {/* Skill name text */}\n      <Text\n        position={[0, 0, 0.16]}\n        fontSize={0.15}\n        color={hovered ? '#ffffff' : categoryColor}\n        anchorX=\"center\"\n        anchorY=\"middle\"\n        font=\"/fonts/inter-bold.woff\"\n        maxWidth={1.8}\n      >\n        {skill.name}\n      </Text>\n\n      {/* Years text */}\n      <Text\n        position={[0.7, 0.2, 0.16]}\n        fontSize={0.08}\n        color=\"#000000\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n        font=\"/fonts/inter-bold.woff\"\n      >\n        {skill.yearsOfExperience}Y\n      </Text>\n\n      {/* Detailed info popup when clicked */}\n      {clicked && (\n        <Html\n          position={[0, 0.8, 0]}\n          center\n          distanceFactor={8}\n        >\n          <div className=\"bg-bg-secondary/95 backdrop-blur-md border border-primary/30 rounded-lg p-4 max-w-xs\">\n            <h3 className=\"text-lg font-bold text-primary mb-2\">{skill.name}</h3>\n            <div className=\"space-y-2 text-sm\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-text-secondary\">Category:</span>\n                <span className=\"text-text-primary capitalize\">{skill.category.replace('-', ' ')}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-text-secondary\">Level:</span>\n                <span className=\"text-text-primary capitalize\">{skill.level}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-text-secondary\">Experience:</span>\n                <span className=\"text-text-primary\">{skill.yearsOfExperience} years</span>\n              </div>\n              {skill.description && (\n                <div className=\"mt-3 pt-2 border-t border-primary/20\">\n                  <p className=\"text-text-secondary text-xs\">{skill.description}</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </Html>\n      )}\n\n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <RoundedBox\n          args={[2.2, 0.35, 1.2]}\n          radius={0.1}\n          position={[0, 0, 0]}\n        >\n          <meshBasicMaterial\n            color={categoryColor}\n            transparent\n            opacity={0.1}\n          />\n        </RoundedBox>\n      )}\n    </group>\n  );\n}\n\n// Skill grid component\nexport function SkillGrid({\n  skills,\n  position = [0, 0, 0],\n  spacing = 2.5,\n  animated = true,\n}: {\n  skills: Skill[];\n  position?: [number, number, number];\n  spacing?: number;\n  animated?: boolean;\n}) {\n  const groupRef = useRef<Group>(null);\n  const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null);\n\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;\n  });\n\n  const handleSkillClick = (skill: Skill) => {\n    setSelectedSkill(selectedSkill?.id === skill.id ? null : skill);\n  };\n\n  // Arrange skills in a grid\n  const rows = Math.ceil(Math.sqrt(skills.length));\n  const cols = Math.ceil(skills.length / rows);\n\n  return (\n    <group ref={groupRef} position={position}>\n      {skills.map((skill, index) => {\n        const row = Math.floor(index / cols);\n        const col = index % cols;\n        const x = (col - (cols - 1) / 2) * spacing;\n        const z = (row - (rows - 1) / 2) * spacing;\n        \n        return (\n          <SkillChip\n            key={skill.id}\n            skill={skill}\n            position={[x, 0, z]}\n            scale={selectedSkill?.id === skill.id ? 1.2 : 1}\n            animated={animated}\n            onClick={handleSkillClick}\n          />\n        );\n      })}\n    </group>\n  );\n}\n\n// Floating skill constellation\nexport function SkillConstellation({\n  skills,\n  position = [0, 0, 0],\n  radius = 4,\n}: {\n  skills: Skill[];\n  position?: [number, number, number];\n  radius?: number;\n}) {\n  const groupRef = useRef<Group>(null);\n\n  useFrame((state) => {\n    if (!groupRef.current) return;\n    groupRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n  });\n\n  return (\n    <group ref={groupRef} position={position}>\n      {skills.map((skill, index) => {\n        const angle = (index / skills.length) * Math.PI * 2;\n        const height = (Math.random() - 0.5) * 2;\n        const x = Math.cos(angle) * radius;\n        const z = Math.sin(angle) * radius;\n        \n        return (\n          <SkillChip\n            key={skill.id}\n            skill={skill}\n            position={[x, height, z]}\n            scale={0.8}\n            rotation={[0, -angle, 0]}\n            animated={true}\n          />\n        );\n      })}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAoBO,SAAS,UAAU,EACxB,KAAK,EACL,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,OAAO,EACP,OAAO,EACQ;IACf,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAQ;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,oCAAoC;IACpC,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,UAAU,YAAY,YAAY;YAClC,SAAS,YAAY,cAAc;YACnC,UAAU,YAAY,WAAW;YACjC,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,OAAO;YACP,eAAe;QACjB;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI,YAAY,YAAY;IAC5E;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACzB,MAAM,cAAc;YAClB,UAAU;YACV,cAAc;YACd,UAAU;YACV,QAAQ;QACV;QACA,OAAO,WAAW,CAAC,MAAkC,IAAI;IAC3D;IAEA,MAAM,gBAAgB,iBAAiB,MAAM,QAAQ;IACrD,MAAM,iBAAiB,kBAAkB,MAAM,KAAK;IAEpD,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QAEpC,4BAA4B;QAC5B,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,IAAI,QAAQ,CAAC,EAAE,IAAI;QAElG,gCAAgC;QAChC,IAAI,MAAM,KAAK,KAAK,UAAU;YAC5B,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC1D,OAAO,IAAI,MAAM,KAAK,KAAK,YAAY;YACrC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QAC1E;QAEA,0BAA0B;QAC1B,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,cAAc,UAAU,MAAO,UAAU,MAAM;YACrD,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAE,GAAG;gBAAa,GAAG;gBAAa,GAAG;YAAY,GAAU;QACxF;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU,OAAO;QACjB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU,OAAO;QACjB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,cAAc;QAClB,WAAW,CAAC;QACZ,UAAU;IACZ;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,eAAe;QACf,cAAc;;0BAGd,8OAAC,8JAAA,CAAA,aAAU;gBACT,KAAK;gBACL,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBACjB,QAAQ;gBACR,UAAU;gBACV,aAAa;0BAEb,cAAA,8OAAC;oBACC,OAAO,UAAU,gBAAgB;oBACjC,WAAW;oBACX,WAAW;oBACX,UAAU,aAAa,gBAAgB;oBACvC,mBAAmB,CAAC,UAAU,MAAM,GAAG,IAAI;;;;;;;;;;;YAK9C,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,8JAAA,CAAA,aAAU;oBAET,MAAM;wBAAC;wBAAM;wBAAM;qBAAI;oBACvB,QAAQ;oBACR,UAAU;wBAAC,CAAC,MAAO,IAAI;wBAAM;wBAAM;qBAAE;oBACrC,UAAU;8BAEV,cAAA,8OAAC;wBACC,OAAO,IAAI;4BAAC;4BAAY;4BAAgB;4BAAY;yBAAS,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,IAAI,gBAAgB;wBACzG,UAAU,IAAI;4BAAC;4BAAY;4BAAgB;4BAAY;yBAAS,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,IAAI,gBAAgB;wBAC5G,mBAAmB;;;;;;mBAThB,CAAC,MAAM,EAAE,GAAG;;;;;0BAerB,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBACrB,QAAQ;gBACR,UAAU;oBAAC;oBAAK;oBAAK;iBAAE;gBACvB,UAAU;0BAEV,cAAA,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,WAAW;;;;;;;;;;;0BAKf,8OAAC,wJAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBACtB,UAAU;gBACV,OAAO,UAAU,YAAY;gBAC7B,SAAQ;gBACR,SAAQ;gBACR,MAAK;gBACL,UAAU;0BAET,MAAM,IAAI;;;;;;0BAIb,8OAAC,wJAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAK;oBAAK;iBAAK;gBAC1B,UAAU;gBACV,OAAM;gBACN,SAAQ;gBACR,SAAQ;gBACR,MAAK;;oBAEJ,MAAM,iBAAiB;oBAAC;;;;;;;YAI1B,yBACC,8OAAC,uJAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBACrB,MAAM;gBACN,gBAAgB;0BAEhB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC,MAAM,IAAI;;;;;;sCAC/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAK,WAAU;sDAAgC,MAAM,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8CAE9E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAK,WAAU;sDAAgC,MAAM,KAAK;;;;;;;;;;;;8CAE7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAK,WAAU;;gDAAqB,MAAM,iBAAiB;gDAAC;;;;;;;;;;;;;gCAE9D,MAAM,WAAW,kBAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAA+B,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASxE,cAAc,yBACb,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBACC,OAAO;oBACP,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAMrB;AAGO,SAAS,UAAU,EACxB,MAAM,EACN,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,UAAU,GAAG,EACb,WAAW,IAAI,EAMhB;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjE,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QACpC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;IAC1E;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,eAAe,OAAO,MAAM,EAAE,GAAG,OAAO;IAC3D;IAEA,2BAA2B;IAC3B,MAAM,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM;IAC9C,MAAM,OAAO,KAAK,IAAI,CAAC,OAAO,MAAM,GAAG;IAEvC,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;kBAC7B,OAAO,GAAG,CAAC,CAAC,OAAO;YAClB,MAAM,MAAM,KAAK,KAAK,CAAC,QAAQ;YAC/B,MAAM,MAAM,QAAQ;YACpB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;YACnC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;YAEnC,qBACE,8OAAC;gBAEC,OAAO;gBACP,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBACnB,OAAO,eAAe,OAAO,MAAM,EAAE,GAAG,MAAM;gBAC9C,UAAU;gBACV,SAAS;eALJ,MAAM,EAAE;;;;;QAQnB;;;;;;AAGN;AAGO,SAAS,mBAAmB,EACjC,MAAM,EACN,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,SAAS,CAAC,EAKX;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,SAAS,OAAO,EAAE;QACvB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;IAC1D;IAEA,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;kBAC7B,OAAO,GAAG,CAAC,CAAC,OAAO;YAClB,MAAM,QAAQ,AAAC,QAAQ,OAAO,MAAM,GAAI,KAAK,EAAE,GAAG;YAClD,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACvC,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS;YAC5B,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS;YAE5B,qBACE,8OAAC;gBAEC,OAAO;gBACP,UAAU;oBAAC;oBAAG;oBAAQ;iBAAE;gBACxB,OAAO;gBACP,UAAU;oBAAC;oBAAG,CAAC;oBAAO;iBAAE;gBACxB,UAAU;eALL,MAAM,EAAE;;;;;QAQnB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 3603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/ui/Timeline.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useTheme } from '@/hooks/useTheme';\nimport { cn } from '@/lib/utils';\nimport { Experience, Education } from '@/types';\n\ninterface TimelineProps {\n  experiences?: Experience[];\n  education?: Education[];\n  className?: string;\n}\n\ninterface TimelineItemProps {\n  item: Experience | Education;\n  index: number;\n  isActive: boolean;\n  onClick: () => void;\n}\n\nfunction TimelineItem({ item, index, isActive, onClick }: TimelineItemProps) {\n  const { themeConfig } = useTheme();\n  const isExperience = 'company' in item;\n  \n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('en-US', { \n      year: 'numeric', \n      month: 'short' \n    });\n  };\n\n  const getDuration = () => {\n    const start = item.startDate;\n    const end = item.endDate || new Date();\n    const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());\n    const years = Math.floor(months / 12);\n    const remainingMonths = months % 12;\n    \n    if (years === 0) return `${months} months`;\n    if (remainingMonths === 0) return `${years} year${years > 1 ? 's' : ''}`;\n    return `${years} year${years > 1 ? 's' : ''} ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}`;\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.6, delay: index * 0.1 }}\n      className={cn(\n        \"relative flex items-center\",\n        index % 2 === 0 ? \"flex-row\" : \"flex-row-reverse\"\n      )}\n    >\n      {/* Timeline connector */}\n      <div className=\"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary to-secondary opacity-30\" />\n      \n      {/* Timeline node */}\n      <motion.div\n        whileHover={{ scale: 1.2 }}\n        whileTap={{ scale: 0.9 }}\n        onClick={onClick}\n        className={cn(\n          \"absolute left-1/2 transform -translate-x-1/2 z-10\",\n          \"w-6 h-6 rounded-full border-4 cursor-pointer\",\n          \"transition-all duration-300\",\n          isActive \n            ? \"bg-primary border-primary shadow-lg glow-primary\" \n            : \"bg-bg-secondary border-primary/50 hover:border-primary\"\n        )}\n      >\n        <div className={cn(\n          \"w-full h-full rounded-full\",\n          isActive && \"animate-pulse-glow\"\n        )} />\n      </motion.div>\n\n      {/* Content card */}\n      <motion.div\n        layout\n        className={cn(\n          \"w-5/12 p-6 rounded-lg chip-border\",\n          \"bg-gradient-to-br from-bg-secondary/80 to-bg-tertiary/80\",\n          \"backdrop-blur-sm cursor-pointer\",\n          \"hover:shadow-xl transition-all duration-300\",\n          isActive && \"glow-primary\",\n          index % 2 === 0 ? \"mr-auto\" : \"ml-auto\"\n        )}\n        onClick={onClick}\n        whileHover={{ scale: 1.02 }}\n      >\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <div>\n            <h3 className=\"text-lg font-bold text-primary\">\n              {isExperience ? (item as Experience).position : (item as Education).degree}\n            </h3>\n            <p className=\"text-text-secondary\">\n              {isExperience ? (item as Experience).company : (item as Education).institution}\n            </p>\n          </div>\n          <div className=\"text-right\">\n            <p className=\"text-sm text-accent font-medium\">\n              {formatDate(item.startDate)} - {item.endDate ? formatDate(item.endDate) : 'Present'}\n            </p>\n            <p className=\"text-xs text-text-muted\">{getDuration()}</p>\n          </div>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-text-secondary text-sm mb-4 line-clamp-3\">\n          {isExperience ? (item as Experience).description : `${(item as Education).field} studies`}\n        </p>\n\n        {/* Technologies/Achievements preview */}\n        {isExperience && (item as Experience).technologies.length > 0 && (\n          <div className=\"flex flex-wrap gap-1 mb-3\">\n            {(item as Experience).technologies.slice(0, 3).map((tech, i) => (\n              <span\n                key={i}\n                className=\"px-2 py-1 text-xs bg-primary/20 text-primary rounded chip-border\"\n              >\n                {tech}\n              </span>\n            ))}\n            {(item as Experience).technologies.length > 3 && (\n              <span className=\"px-2 py-1 text-xs bg-secondary/20 text-secondary rounded\">\n                +{(item as Experience).technologies.length - 3} more\n              </span>\n            )}\n          </div>\n        )}\n\n        {/* Expand indicator */}\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-xs text-text-muted\">\n            {isExperience ? 'Experience' : 'Education'}\n          </span>\n          <motion.div\n            animate={{ rotate: isActive ? 180 : 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"text-primary\"\n          >\n            ▼\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Detailed view */}\n      <AnimatePresence>\n        {isActive && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className={cn(\n              \"absolute top-full mt-4 w-5/12 p-6 rounded-lg\",\n              \"bg-bg-primary/95 backdrop-blur-md border border-primary/30\",\n              \"shadow-xl z-20\",\n              index % 2 === 0 ? \"left-0\" : \"right-0\"\n            )}\n          >\n            {isExperience ? (\n              <ExperienceDetails experience={item as Experience} />\n            ) : (\n              <EducationDetails education={item as Education} />\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.div>\n  );\n}\n\nfunction ExperienceDetails({ experience }: { experience: Experience }) {\n  return (\n    <div className=\"space-y-4\">\n      <div>\n        <h4 className=\"text-lg font-bold text-primary mb-2\">Role Description</h4>\n        <p className=\"text-text-secondary text-sm\">{experience.description}</p>\n      </div>\n\n      {experience.achievements.length > 0 && (\n        <div>\n          <h4 className=\"text-lg font-bold text-secondary mb-2\">Key Achievements</h4>\n          <ul className=\"space-y-1\">\n            {experience.achievements.map((achievement, i) => (\n              <li key={i} className=\"text-text-secondary text-sm flex items-start\">\n                <span className=\"text-accent mr-2\">•</span>\n                {achievement}\n              </li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div>\n        <h4 className=\"text-lg font-bold text-accent mb-2\">Technologies Used</h4>\n        <div className=\"flex flex-wrap gap-2\">\n          {experience.technologies.map((tech, i) => (\n            <span\n              key={i}\n              className=\"px-3 py-1 text-sm bg-primary/20 text-primary rounded-full chip-border\"\n            >\n              {tech}\n            </span>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"flex justify-between text-sm text-text-muted\">\n        <span>{experience.location}</span>\n        <span className=\"capitalize\">{experience.type.replace('-', ' ')}</span>\n      </div>\n    </div>\n  );\n}\n\nfunction EducationDetails({ education }: { education: Education }) {\n  return (\n    <div className=\"space-y-4\">\n      <div>\n        <h4 className=\"text-lg font-bold text-primary mb-2\">Field of Study</h4>\n        <p className=\"text-text-secondary\">{education.field}</p>\n      </div>\n\n      {education.gpa && (\n        <div>\n          <h4 className=\"text-lg font-bold text-secondary mb-2\">GPA</h4>\n          <p className=\"text-text-secondary\">{education.gpa}</p>\n        </div>\n      )}\n\n      {education.achievements && education.achievements.length > 0 && (\n        <div>\n          <h4 className=\"text-lg font-bold text-accent mb-2\">Achievements</h4>\n          <ul className=\"space-y-1\">\n            {education.achievements.map((achievement, i) => (\n              <li key={i} className=\"text-text-secondary text-sm flex items-start\">\n                <span className=\"text-accent mr-2\">•</span>\n                {achievement}\n              </li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div className=\"text-sm text-text-muted\">\n        <span>{education.location}</span>\n      </div>\n    </div>\n  );\n}\n\nexport function Timeline({ experiences = [], education = [], className }: TimelineProps) {\n  const [activeItem, setActiveItem] = useState<string | null>(null);\n  const timelineRef = useRef<HTMLDivElement>(null);\n\n  // Combine and sort all items by date\n  const allItems = [\n    ...experiences.map(exp => ({ ...exp, type: 'experience' as const })),\n    ...education.map(edu => ({ ...edu, type: 'education' as const }))\n  ].sort((a, b) => b.startDate.getTime() - a.startDate.getTime());\n\n  const handleItemClick = (itemId: string) => {\n    setActiveItem(activeItem === itemId ? null : itemId);\n  };\n\n  return (\n    <div className={cn(\"relative py-12\", className)}>\n      <div className=\"max-w-6xl mx-auto px-4\">\n        {/* Timeline header */}\n        <motion.div\n          initial={{ opacity: 0, y: -30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold text-primary mb-4\">\n            My Journey\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-2xl mx-auto\">\n            A timeline of my professional experience and educational background\n          </p>\n        </motion.div>\n\n        {/* Timeline */}\n        <div ref={timelineRef} className=\"relative space-y-16\">\n          {allItems.map((item, index) => (\n            <TimelineItem\n              key={item.id}\n              item={item}\n              index={index}\n              isActive={activeItem === item.id}\n              onClick={() => handleItemClick(item.id)}\n            />\n          ))}\n        </div>\n\n        {/* Timeline end marker */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6, delay: allItems.length * 0.1 }}\n          className=\"relative mt-16\"\n        >\n          <div className=\"absolute left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full glow-primary\" />\n          <div className=\"text-center pt-12\">\n            <p className=\"text-text-secondary\">The journey continues...</p>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAqBA,SAAS,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAqB;IACzE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,eAAe,aAAa;IAElC,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,QAAQ,KAAK,SAAS;QAC5B,MAAM,MAAM,KAAK,OAAO,IAAI,IAAI;QAChC,MAAM,SAAS,CAAC,IAAI,WAAW,KAAK,MAAM,WAAW,EAAE,IAAI,KAAK,CAAC,IAAI,QAAQ,KAAK,MAAM,QAAQ,EAAE;QAClG,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;QAClC,MAAM,kBAAkB,SAAS;QAEjC,IAAI,UAAU,GAAG,OAAO,GAAG,OAAO,OAAO,CAAC;QAC1C,IAAI,oBAAoB,GAAG,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,IAAI;QACxE,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,CAAC,EAAE,gBAAgB,MAAM,EAAE,kBAAkB,IAAI,MAAM,IAAI;IACzG;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,KAAK;QAAG;QACrD,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,QAAQ,MAAM,IAAI,aAAa;;0BAIjC,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,SAAS;gBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA,gDACA,+BACA,WACI,qDACA;0BAGN,cAAA,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,8BACA,YAAY;;;;;;;;;;;0BAKhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,4DACA,mCACA,+CACA,YAAY,gBACZ,QAAQ,MAAM,IAAI,YAAY;gBAEhC,SAAS;gBACT,YAAY;oBAAE,OAAO;gBAAK;;kCAG1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,eAAe,AAAC,KAAoB,QAAQ,GAAG,AAAC,KAAmB,MAAM;;;;;;kDAE5E,8OAAC;wCAAE,WAAU;kDACV,eAAe,AAAC,KAAoB,OAAO,GAAG,AAAC,KAAmB,WAAW;;;;;;;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CACV,WAAW,KAAK,SAAS;4CAAE;4CAAI,KAAK,OAAO,GAAG,WAAW,KAAK,OAAO,IAAI;;;;;;;kDAE5E,8OAAC;wCAAE,WAAU;kDAA2B;;;;;;;;;;;;;;;;;;kCAK5C,8OAAC;wBAAE,WAAU;kCACV,eAAe,AAAC,KAAoB,WAAW,GAAG,GAAG,AAAC,KAAmB,KAAK,CAAC,QAAQ,CAAC;;;;;;oBAI1F,gBAAgB,AAAC,KAAoB,YAAY,CAAC,MAAM,GAAG,mBAC1D,8OAAC;wBAAI,WAAU;;4BACX,KAAoB,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,kBACxD,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;4BAMP,KAAoB,YAAY,CAAC,MAAM,GAAG,mBAC1C,8OAAC;gCAAK,WAAU;;oCAA2D;oCACtE,KAAoB,YAAY,CAAC,MAAM,GAAG;oCAAE;;;;;;;;;;;;;kCAOvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,eAAe,eAAe;;;;;;0CAEjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ,WAAW,MAAM;gCAAE;gCACtC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,8OAAC,yLAAA,CAAA,kBAAe;0BACb,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gDACA,8DACA,kBACA,QAAQ,MAAM,IAAI,WAAW;8BAG9B,6BACC,8OAAC;wBAAkB,YAAY;;;;;6CAE/B,8OAAC;wBAAiB,WAAW;;;;;;;;;;;;;;;;;;;;;;AAO3C;AAEA,SAAS,kBAAkB,EAAE,UAAU,EAA8B;IACnE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAA+B,WAAW,WAAW;;;;;;;;;;;;YAGnE,WAAW,YAAY,CAAC,MAAM,GAAG,mBAChC,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAG,WAAU;kCACX,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,kBACzC,8OAAC;gCAAW,WAAU;;kDACpB,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;oCAClC;;+BAFM;;;;;;;;;;;;;;;;0BASjB,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAI,WAAU;kCACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,kBAClC,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;;;;;;;0BASb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAM,WAAW,QAAQ;;;;;;kCAC1B,8OAAC;wBAAK,WAAU;kCAAc,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;AAInE;AAEA,SAAS,iBAAiB,EAAE,SAAS,EAA4B;IAC/D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAAuB,UAAU,KAAK;;;;;;;;;;;;YAGpD,UAAU,GAAG,kBACZ,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAuB,UAAU,GAAG;;;;;;;;;;;;YAIpD,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,MAAM,GAAG,mBACzD,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAG,WAAU;kCACX,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,kBACxC,8OAAC;gCAAW,WAAU;;kDACpB,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;oCAClC;;+BAFM;;;;;;;;;;;;;;;;0BASjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BAAM,UAAU,QAAQ;;;;;;;;;;;;;;;;;AAIjC;AAEO,SAAS,SAAS,EAAE,cAAc,EAAE,EAAE,YAAY,EAAE,EAAE,SAAS,EAAiB;IACrF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,qCAAqC;IACrC,MAAM,WAAW;WACZ,YAAY,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE,GAAG,GAAG;gBAAE,MAAM;YAAsB,CAAC;WAC/D,UAAU,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE,GAAG,GAAG;gBAAE,MAAM;YAAqB,CAAC;KAChE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;IAE5D,MAAM,kBAAkB,CAAC;QACvB,cAAc,eAAe,SAAS,OAAO;IAC/C;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAM/D,8OAAC;oBAAI,KAAK;oBAAa,WAAU;8BAC9B,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;4BAEC,MAAM;4BACN,OAAO;4BACP,UAAU,eAAe,KAAK,EAAE;4BAChC,SAAS,IAAM,gBAAgB,KAAK,EAAE;2BAJjC,KAAK,EAAE;;;;;;;;;;8BAUlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO,SAAS,MAAM,GAAG;oBAAI;oBAC1D,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/C", "debugId": null}}, {"offset": {"line": 4254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/sections/AboutSection.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { AboutScene } from '@/components/3d/SceneManager';\nimport { PhotoFrame } from '@/components/3d/PhotoFrame';\nimport { SkillGrid, SkillConstellation } from '@/components/3d/SkillChip';\nimport { Timeline } from '@/components/ui/Timeline';\nimport { Button } from '@/components/ui/Button';\nimport { useTheme } from '@/hooks/useTheme';\nimport { useAdaptiveQuality } from '@/hooks/usePerformance';\nimport { cn } from '@/lib/utils';\nimport { Skill, Experience, Education } from '@/types';\n\ninterface AboutSectionProps {\n  className?: string;\n}\n\n// Sample data - in a real app, this would come from a CMS or API\nconst sampleSkills: Skill[] = [\n  {\n    id: '1',\n    name: 'React',\n    category: 'frontend',\n    level: 'expert',\n    yearsOfExperience: 5,\n    description: 'Building complex user interfaces with React and its ecosystem'\n  },\n  {\n    id: '2',\n    name: 'Three.js',\n    category: 'frontend',\n    level: 'advanced',\n    yearsOfExperience: 3,\n    description: 'Creating immersive 3D web experiences'\n  },\n  {\n    id: '3',\n    name: 'TypeScript',\n    category: 'frontend',\n    level: 'expert',\n    yearsOfExperience: 4,\n    description: 'Type-safe JavaScript development'\n  },\n  {\n    id: '4',\n    name: 'Node.js',\n    category: 'backend',\n    level: 'advanced',\n    yearsOfExperience: 4,\n    description: 'Server-side JavaScript development'\n  },\n  {\n    id: '5',\n    name: 'Python',\n    category: 'backend',\n    level: 'advanced',\n    yearsOfExperience: 3,\n    description: 'Backend development and data science'\n  },\n  {\n    id: '6',\n    name: 'PostgreSQL',\n    category: 'database',\n    level: 'intermediate',\n    yearsOfExperience: 3,\n    description: 'Relational database design and optimization'\n  },\n  {\n    id: '7',\n    name: 'Docker',\n    category: 'devops',\n    level: 'intermediate',\n    yearsOfExperience: 2,\n    description: 'Containerization and deployment'\n  },\n  {\n    id: '8',\n    name: 'AWS',\n    category: 'devops',\n    level: 'intermediate',\n    yearsOfExperience: 2,\n    description: 'Cloud infrastructure and services'\n  }\n];\n\nconst sampleExperiences: Experience[] = [\n  {\n    id: '1',\n    company: 'Tech Innovations Inc.',\n    position: 'Senior Frontend Developer',\n    startDate: new Date('2022-01-01'),\n    endDate: undefined,\n    description: 'Leading the development of interactive 3D web applications using React and Three.js',\n    achievements: [\n      'Increased user engagement by 40% with immersive 3D interfaces',\n      'Led a team of 5 developers on multiple projects',\n      'Implemented performance optimizations reducing load times by 60%'\n    ],\n    technologies: ['React', 'Three.js', 'TypeScript', 'WebGL', 'Next.js'],\n    location: 'San Francisco, CA',\n    type: 'full-time'\n  },\n  {\n    id: '2',\n    company: 'Digital Solutions Ltd.',\n    position: 'Full Stack Developer',\n    startDate: new Date('2020-06-01'),\n    endDate: new Date('2021-12-31'),\n    description: 'Developed and maintained web applications using modern JavaScript frameworks',\n    achievements: [\n      'Built 15+ responsive web applications',\n      'Improved application performance by 35%',\n      'Mentored junior developers'\n    ],\n    technologies: ['React', 'Node.js', 'MongoDB', 'Express.js', 'AWS'],\n    location: 'New York, NY',\n    type: 'full-time'\n  }\n];\n\nconst sampleEducation: Education[] = [\n  {\n    id: '1',\n    institution: 'University of Technology',\n    degree: 'Bachelor of Science',\n    field: 'Computer Science',\n    startDate: new Date('2016-09-01'),\n    endDate: new Date('2020-05-31'),\n    gpa: 3.8,\n    achievements: [\n      'Magna Cum Laude',\n      'Dean\\'s List for 6 semesters',\n      'Computer Science Society President'\n    ],\n    location: 'Boston, MA'\n  }\n];\n\nexport function AboutSection({ className }: AboutSectionProps) {\n  const { themeConfig } = useTheme();\n  const qualitySettings = useAdaptiveQuality();\n  const [activeView, setActiveView] = useState<'overview' | 'skills' | 'timeline'>('overview');\n\n  const scrollToSection = (sectionId: string) => {\n    document.getElementById(sectionId)?.scrollIntoView({ \n      behavior: 'smooth',\n      block: 'start'\n    });\n  };\n\n  return (\n    <section id=\"about\" className={cn(\n      \"min-h-screen bg-gradient-to-br from-bg-secondary via-bg-tertiary to-bg-secondary\",\n      \"relative overflow-hidden\",\n      className\n    )}>\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 circuit-bg opacity-10\" />\n      \n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 py-20\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold text-primary mb-6\">\n            About Me\n          </h2>\n          <p className=\"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto\">\n            A passionate developer creating immersive 3D web experiences with cutting-edge technologies\n          </p>\n        </motion.div>\n\n        {/* View Toggle */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"flex justify-center mb-12\"\n        >\n          <div className=\"flex bg-bg-primary/50 rounded-lg p-1 chip-border\">\n            {[\n              { id: 'overview', label: 'Overview', icon: '👨‍💻' },\n              { id: 'skills', label: 'Skills', icon: '🛠️' },\n              { id: 'timeline', label: 'Timeline', icon: '📅' }\n            ].map((tab) => (\n              <Button\n                key={tab.id}\n                variant={activeView === tab.id ? 'primary' : 'ghost'}\n                size=\"md\"\n                onClick={() => setActiveView(tab.id as any)}\n                className=\"mx-1\"\n              >\n                <span className=\"mr-2\">{tab.icon}</span>\n                {tab.label}\n              </Button>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Content Views */}\n        <div className=\"relative\">\n          {/* Overview */}\n          {activeView === 'overview' && (\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: 50 }}\n              transition={{ duration: 0.6 }}\n              className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\"\n            >\n              {/* 3D Photo Frame */}\n              <div className=\"relative h-96\">\n                <AboutScene\n                  enableControls={false}\n                  enableEnvironment={true}\n                  shadows={qualitySettings.shadows}\n                  antialias={qualitySettings.antialias}\n                  lighting=\"soft\"\n                >\n                  <PhotoFrame\n                    position={[0, 0, 0]}\n                    scale={1}\n                    frameStyle=\"chip\"\n                    animated={true}\n                    glowEffect={true}\n                  />\n                </AboutScene>\n              </div>\n\n              {/* About Content */}\n              <div className=\"space-y-6\">\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.2 }}\n                >\n                  <h3 className=\"text-2xl font-bold text-secondary mb-4\">\n                    Hello, I'm a Creative Developer\n                  </h3>\n                  <p className=\"text-text-secondary text-lg leading-relaxed mb-6\">\n                    With over 5 years of experience in web development, I specialize in creating \n                    immersive 3D web experiences that push the boundaries of what's possible in the browser. \n                    I'm passionate about combining cutting-edge technology with beautiful design to create \n                    memorable digital experiences.\n                  </p>\n                </motion.div>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.4 }}\n                  className=\"grid grid-cols-2 gap-4\"\n                >\n                  <div className=\"chip-border p-4 bg-bg-primary/30\">\n                    <h4 className=\"text-lg font-bold text-primary mb-2\">Frontend</h4>\n                    <p className=\"text-text-secondary text-sm\">React, Three.js, TypeScript</p>\n                  </div>\n                  <div className=\"chip-border p-4 bg-bg-primary/30\">\n                    <h4 className=\"text-lg font-bold text-secondary mb-2\">3D Graphics</h4>\n                    <p className=\"text-text-secondary text-sm\">WebGL, Blender, GLTF</p>\n                  </div>\n                  <div className=\"chip-border p-4 bg-bg-primary/30\">\n                    <h4 className=\"text-lg font-bold text-accent mb-2\">Backend</h4>\n                    <p className=\"text-text-secondary text-sm\">Node.js, Python, Databases</p>\n                  </div>\n                  <div className=\"chip-border p-4 bg-bg-primary/30\">\n                    <h4 className=\"text-lg font-bold text-primary mb-2\">DevOps</h4>\n                    <p className=\"text-text-secondary text-sm\">Docker, AWS, CI/CD</p>\n                  </div>\n                </motion.div>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.6 }}\n                  className=\"flex flex-col sm:flex-row gap-4\"\n                >\n                  <Button\n                    variant=\"primary\"\n                    size=\"lg\"\n                    onClick={() => scrollToSection('projects')}\n                    className=\"group\"\n                  >\n                    <span className=\"group-hover:scale-110 transition-transform\">\n                      💼 View My Work\n                    </span>\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"lg\"\n                    onClick={() => scrollToSection('contact')}\n                    className=\"group\"\n                  >\n                    <span className=\"group-hover:scale-110 transition-transform\">\n                      📧 Get In Touch\n                    </span>\n                  </Button>\n                </motion.div>\n              </div>\n            </motion.div>\n          )}\n\n          {/* Skills View */}\n          {activeView === 'skills' && (\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -50 }}\n              transition={{ duration: 0.6 }}\n              className=\"space-y-12\"\n            >\n              <div className=\"text-center\">\n                <h3 className=\"text-3xl font-bold text-accent mb-4\">Technical Skills</h3>\n                <p className=\"text-text-secondary max-w-2xl mx-auto\">\n                  Click on any skill chip to learn more about my experience and proficiency level\n                </p>\n              </div>\n\n              {/* 3D Skills Display */}\n              <div className=\"relative h-96\">\n                <AboutScene\n                  enableControls={true}\n                  enableEnvironment={false}\n                  shadows={qualitySettings.shadows}\n                  antialias={qualitySettings.antialias}\n                  lighting=\"studio\"\n                >\n                  {qualitySettings.particleCount > 100 ? (\n                    <SkillConstellation\n                      skills={sampleSkills}\n                      position={[0, 0, 0]}\n                      radius={3}\n                    />\n                  ) : (\n                    <SkillGrid\n                      skills={sampleSkills}\n                      position={[0, 0, 0]}\n                      spacing={2}\n                      animated={true}\n                    />\n                  )}\n                </AboutScene>\n              </div>\n            </motion.div>\n          )}\n\n          {/* Timeline View */}\n          {activeView === 'timeline' && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 1.05 }}\n              transition={{ duration: 0.6 }}\n            >\n              <Timeline\n                experiences={sampleExperiences}\n                education={sampleEducation}\n              />\n            </motion.div>\n          )}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAkBA,iEAAiE;AACjE,MAAM,eAAwB;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,aAAa;IACf;CACD;AAED,MAAM,oBAAkC;IACtC;QACE,IAAI;QACJ,SAAS;QACT,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS;QACT,aAAa;QACb,cAAc;YACZ;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAY;YAAc;YAAS;SAAU;QACrE,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,SAAS;QACT,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,aAAa;QACb,cAAc;YACZ;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAW;YAAc;SAAM;QAClE,UAAU;QACV,MAAM;IACR;CACD;AAED,MAAM,kBAA+B;IACnC;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,KAAK;QACL,cAAc;YACZ;YACA;YACA;SACD;QACD,UAAU;IACZ;CACD;AAEM,SAAS,aAAa,EAAE,SAAS,EAAqB;IAC3D,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,kBAAkB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IAEjF,MAAM,kBAAkB,CAAC;QACvB,SAAS,cAAc,CAAC,YAAY,eAAe;YACjD,UAAU;YACV,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC9B,oFACA,4BACA;;0BAGA,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAA4D;;;;;;;;;;;;kCAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAY,MAAM;gCAAQ;gCACnD;oCAAE,IAAI;oCAAU,OAAO;oCAAU,MAAM;gCAAM;gCAC7C;oCAAE,IAAI;oCAAY,OAAO;oCAAY,MAAM;gCAAK;6BACjD,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,eAAe,IAAI,EAAE,GAAG,YAAY;oCAC7C,MAAK;oCACL,SAAS,IAAM,cAAc,IAAI,EAAE;oCACnC,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAQ,IAAI,IAAI;;;;;;wCAC/B,IAAI,KAAK;;mCAPL,IAAI,EAAE;;;;;;;;;;;;;;;kCAcnB,8OAAC;wBAAI,WAAU;;4BAEZ,eAAe,4BACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC1B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wIAAA,CAAA,aAAU;4CACT,gBAAgB;4CAChB,mBAAmB;4CACnB,SAAS,gBAAgB,OAAO;4CAChC,WAAW,gBAAgB,SAAS;4CACpC,UAAS;sDAET,cAAA,8OAAC,sIAAA,CAAA,aAAU;gDACT,UAAU;oDAAC;oDAAG;oDAAG;iDAAE;gDACnB,OAAO;gDACP,YAAW;gDACX,UAAU;gDACV,YAAY;;;;;;;;;;;;;;;;kDAMlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;;kEAExC,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEAGvD,8OAAC;wDAAE,WAAU;kEAAmD;;;;;;;;;;;;0DAQlE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;kEAE7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAwC;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;kEAE7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;kEAE7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;0DAI/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;kEAEV,cAAA,8OAAC;4DAAK,WAAU;sEAA6C;;;;;;;;;;;kEAI/D,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;kEAEV,cAAA,8OAAC;4DAAK,WAAU;sEAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUtE,eAAe,0BACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;kDAMvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wIAAA,CAAA,aAAU;4CACT,gBAAgB;4CAChB,mBAAmB;4CACnB,SAAS,gBAAgB,OAAO;4CAChC,WAAW,gBAAgB,SAAS;4CACpC,UAAS;sDAER,gBAAgB,aAAa,GAAG,oBAC/B,8OAAC,qIAAA,CAAA,qBAAkB;gDACjB,QAAQ;gDACR,UAAU;oDAAC;oDAAG;oDAAG;iDAAE;gDACnB,QAAQ;;;;;qEAGV,8OAAC,qIAAA,CAAA,YAAS;gDACR,QAAQ;gDACR,UAAU;oDAAC;oDAAG;oDAAG;iDAAE;gDACnB,SAAS;gDACT,UAAU;;;;;;;;;;;;;;;;;;;;;;4BASrB,eAAe,4BACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAK;gCACnC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAK;gCAChC,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oCACP,aAAa;oCACb,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3B", "debugId": null}}, {"offset": {"line": 4969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/3d/ProjectCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { RoundedBox, Text, Html, Image as DreiImage } from '@react-three/drei';\nimport { Mesh, Group } from 'three';\nimport { useTheme } from '@/hooks/useTheme';\nimport { Project } from '@/types';\n\ninterface ProjectCardProps {\n  project: Project;\n  position?: [number, number, number];\n  scale?: number;\n  rotation?: [number, number, number];\n  animated?: boolean;\n  glowEffect?: boolean;\n  onClick?: (project: Project) => void;\n  onHover?: (project: Project, hovered: boolean) => void;\n}\n\nexport function ProjectCard({\n  project,\n  position = [0, 0, 0],\n  scale = 1,\n  rotation = [0, 0, 0],\n  animated = true,\n  glowEffect = true,\n  onClick,\n  onHover,\n}: ProjectCardProps) {\n  const groupRef = useRef<Group>(null);\n  const cardRef = useRef<Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  const [flipped, setFlipped] = useState(false);\n  const { themeConfig } = useTheme();\n\n  // Get category color\n  const getCategoryColor = (category: string) => {\n    const colors = {\n      'web-development': themeConfig.primaryColor,\n      'mobile-development': themeConfig.secondaryColor,\n      'backend': themeConfig.accentColor,\n      'fullstack': '#9333ea',\n      'ai-ml': '#f59e0b',\n      'devops': '#10b981',\n      'other': '#6b7280',\n    };\n    return colors[category as keyof typeof colors] || themeConfig.primaryColor;\n  };\n\n  const categoryColor = getCategoryColor(project.category);\n\n  // Animation loop\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    \n    // Gentle floating animation\n    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2 + position[0]) * 0.05;\n    \n    // Rotation animation when hovered\n    if (hovered && !flipped) {\n      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 3) * 0.1;\n    }\n    \n    // Flip animation\n    if (flipped) {\n      groupRef.current.rotation.y = Math.PI;\n    } else {\n      groupRef.current.rotation.y = 0;\n    }\n    \n    // Scale effect\n    if (cardRef.current) {\n      const targetScale = hovered ? 1.05 : 1;\n      cardRef.current.scale.lerp({ x: targetScale, y: targetScale, z: targetScale } as any, 0.1);\n    }\n  });\n\n  const handlePointerOver = () => {\n    setHovered(true);\n    onHover?.(project, true);\n    document.body.style.cursor = 'pointer';\n  };\n\n  const handlePointerOut = () => {\n    setHovered(false);\n    onHover?.(project, false);\n    document.body.style.cursor = 'auto';\n  };\n\n  const handleClick = () => {\n    setFlipped(!flipped);\n    onClick?.(project);\n  };\n\n  return (\n    <group\n      ref={groupRef}\n      position={position}\n      scale={scale}\n      rotation={rotation}\n      onClick={handleClick}\n      onPointerOver={handlePointerOver}\n      onPointerOut={handlePointerOut}\n    >\n      {/* Front side */}\n      <group visible={!flipped}>\n        {/* Main card body */}\n        <RoundedBox\n          ref={cardRef}\n          args={[3, 4, 0.2]}\n          radius={0.1}\n          castShadow\n          receiveShadow\n        >\n          <meshStandardMaterial\n            color={hovered ? categoryColor : '#2a2a2a'}\n            metalness={0.8}\n            roughness={0.2}\n            emissive={glowEffect ? categoryColor : '#000000'}\n            emissiveIntensity={hovered ? 0.2 : 0.05}\n          />\n        </RoundedBox>\n\n        {/* Image placeholder */}\n        <RoundedBox\n          args={[2.6, 1.8, 0.05]}\n          radius={0.05}\n          position={[0, 0.8, 0.11]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={project.imageUrl ? '#ffffff' : '#1a1a1a'}\n            emissive={!project.imageUrl ? categoryColor : '#000000'}\n            emissiveIntensity={!project.imageUrl ? 0.1 : 0}\n          />\n        </RoundedBox>\n\n        {/* Project title */}\n        <Text\n          position={[0, -0.2, 0.11]}\n          fontSize={0.2}\n          color={hovered ? '#ffffff' : categoryColor}\n          anchorX=\"center\"\n          anchorY=\"middle\"\n          font=\"/fonts/inter-bold.woff\"\n          maxWidth={2.8}\n        >\n          {project.title}\n        </Text>\n\n        {/* Category badge */}\n        <RoundedBox\n          args={[1.2, 0.3, 0.05]}\n          radius={0.05}\n          position={[0, -0.8, 0.11]}\n          castShadow\n        >\n          <meshStandardMaterial\n            color={categoryColor}\n            emissive={categoryColor}\n            emissiveIntensity={0.3}\n          />\n        </RoundedBox>\n\n        <Text\n          position={[0, -0.8, 0.16]}\n          fontSize={0.1}\n          color=\"#ffffff\"\n          anchorX=\"center\"\n          anchorY=\"middle\"\n          font=\"/fonts/inter-bold.woff\"\n        >\n          {project.category.replace('-', ' ').toUpperCase()}\n        </Text>\n\n        {/* Featured indicator */}\n        {project.featured && (\n          <RoundedBox\n            args={[0.3, 0.3, 0.05]}\n            radius={0.05}\n            position={[1.2, 1.5, 0.11]}\n            castShadow\n          >\n            <meshStandardMaterial\n              color=\"#ffd700\"\n              emissive=\"#ffd700\"\n              emissiveIntensity={0.5}\n            />\n          </RoundedBox>\n        )}\n\n        {/* Technology indicators */}\n        {project.technologies.slice(0, 3).map((tech, i) => (\n          <RoundedBox\n            key={tech}\n            args={[0.15, 0.15, 0.03]}\n            radius={0.02}\n            position={[-1 + (i * 0.4), -1.4, 0.11]}\n            castShadow\n          >\n            <meshStandardMaterial\n              color={themeConfig.secondaryColor}\n              emissive={themeConfig.secondaryColor}\n              emissiveIntensity={0.2}\n            />\n          </RoundedBox>\n        ))}\n      </group>\n\n      {/* Back side */}\n      <group visible={flipped} rotation={[0, Math.PI, 0]}>\n        {/* Main card body */}\n        <RoundedBox\n          args={[3, 4, 0.2]}\n          radius={0.1}\n          castShadow\n          receiveShadow\n        >\n          <meshStandardMaterial\n            color=\"#1a1a1a\"\n            metalness={0.9}\n            roughness={0.1}\n            emissive={categoryColor}\n            emissiveIntensity={0.1}\n          />\n        </RoundedBox>\n\n        {/* Circuit pattern */}\n        {Array.from({ length: 8 }, (_, i) => (\n          <RoundedBox\n            key={`circuit-${i}`}\n            args={[0.02, Math.random() * 2 + 1, 0.01]}\n            radius={0.005}\n            position={[\n              -1.2 + (i * 0.3),\n              (Math.random() - 0.5) * 3,\n              0.11\n            ]}\n            castShadow\n          >\n            <meshStandardMaterial\n              color={categoryColor}\n              emissive={categoryColor}\n              emissiveIntensity={0.5}\n            />\n          </RoundedBox>\n        ))}\n\n        {/* Back content overlay */}\n        <Html\n          position={[0, 0, 0.11]}\n          center\n          distanceFactor={6}\n          transform\n          occlude\n        >\n          <div className=\"w-48 h-64 bg-bg-primary/95 backdrop-blur-md rounded-lg p-4 border border-primary/30\">\n            <h3 className=\"text-lg font-bold text-primary mb-2\">{project.title}</h3>\n            <p className=\"text-text-secondary text-sm mb-3 line-clamp-4\">\n              {project.description}\n            </p>\n            \n            <div className=\"space-y-2\">\n              <div className=\"flex flex-wrap gap-1\">\n                {project.technologies.slice(0, 4).map((tech) => (\n                  <span\n                    key={tech}\n                    className=\"px-2 py-1 text-xs bg-primary/20 text-primary rounded\"\n                  >\n                    {tech}\n                  </span>\n                ))}\n              </div>\n              \n              <div className=\"flex gap-2 mt-4\">\n                {project.demoUrl && (\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      window.open(project.demoUrl, '_blank');\n                    }}\n                    className=\"px-3 py-1 bg-primary text-white text-xs rounded hover:bg-primary-dark transition-colors\"\n                  >\n                    Demo\n                  </button>\n                )}\n                {project.githubUrl && (\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      window.open(project.githubUrl, '_blank');\n                    }}\n                    className=\"px-3 py-1 bg-secondary text-white text-xs rounded hover:bg-blue-600 transition-colors\"\n                  >\n                    Code\n                  </button>\n                )}\n              </div>\n            </div>\n          </div>\n        </Html>\n      </group>\n\n      {/* Glow effect */}\n      {glowEffect && hovered && (\n        <RoundedBox\n          args={[3.2, 4.2, 0.25]}\n          radius={0.1}\n          position={[0, 0, 0]}\n        >\n          <meshBasicMaterial\n            color={categoryColor}\n            transparent\n            opacity={0.1}\n          />\n        </RoundedBox>\n      )}\n    </group>\n  );\n}\n\n// Project grid component\nexport function ProjectGrid({\n  projects,\n  position = [0, 0, 0],\n  spacing = 4,\n  animated = true,\n  onProjectClick,\n}: {\n  projects: Project[];\n  position?: [number, number, number];\n  spacing?: number;\n  animated?: boolean;\n  onProjectClick?: (project: Project) => void;\n}) {\n  const groupRef = useRef<Group>(null);\n\n  useFrame((state) => {\n    if (!animated || !groupRef.current) return;\n    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.05;\n  });\n\n  // Arrange projects in a grid\n  const cols = Math.ceil(Math.sqrt(projects.length));\n  const rows = Math.ceil(projects.length / cols);\n\n  return (\n    <group ref={groupRef} position={position}>\n      {projects.map((project, index) => {\n        const row = Math.floor(index / cols);\n        const col = index % cols;\n        const x = (col - (cols - 1) / 2) * spacing;\n        const z = (row - (rows - 1) / 2) * spacing;\n        \n        return (\n          <ProjectCard\n            key={project.id}\n            project={project}\n            position={[x, 0, z]}\n            scale={1}\n            animated={animated}\n            onClick={onProjectClick}\n          />\n        );\n      })}\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAoBO,SAAS,YAAY,EAC1B,OAAO,EACP,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,OAAO,EACP,OAAO,EACU;IACjB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAC/B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAQ;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/B,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,mBAAmB,YAAY,YAAY;YAC3C,sBAAsB,YAAY,cAAc;YAChD,WAAW,YAAY,WAAW;YAClC,aAAa;YACb,SAAS;YACT,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI,YAAY,YAAY;IAC5E;IAEA,MAAM,gBAAgB,iBAAiB,QAAQ,QAAQ;IAEvD,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QAEpC,4BAA4B;QAC5B,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,IAAI,QAAQ,CAAC,EAAE,IAAI;QAElG,kCAAkC;QAClC,IAAI,WAAW,CAAC,SAAS;YACvB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;QACxE;QAEA,iBAAiB;QACjB,IAAI,SAAS;YACX,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,EAAE;QACvC,OAAO;YACL,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG;QAChC;QAEA,eAAe;QACf,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,cAAc,UAAU,OAAO;YACrC,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAE,GAAG;gBAAa,GAAG;gBAAa,GAAG;YAAY,GAAU;QACxF;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU,SAAS;QACnB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,UAAU,SAAS;QACnB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IAC/B;IAEA,MAAM,cAAc;QAClB,WAAW,CAAC;QACZ,UAAU;IACZ;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,eAAe;QACf,cAAc;;0BAGd,8OAAC;gBAAM,SAAS,CAAC;;kCAEf,8OAAC,8JAAA,CAAA,aAAU;wBACT,KAAK;wBACL,MAAM;4BAAC;4BAAG;4BAAG;yBAAI;wBACjB,QAAQ;wBACR,UAAU;wBACV,aAAa;kCAEb,cAAA,8OAAC;4BACC,OAAO,UAAU,gBAAgB;4BACjC,WAAW;4BACX,WAAW;4BACX,UAAU,aAAa,gBAAgB;4BACvC,mBAAmB,UAAU,MAAM;;;;;;;;;;;kCAKvC,8OAAC,8JAAA,CAAA,aAAU;wBACT,MAAM;4BAAC;4BAAK;4BAAK;yBAAK;wBACtB,QAAQ;wBACR,UAAU;4BAAC;4BAAG;4BAAK;yBAAK;wBACxB,UAAU;kCAEV,cAAA,8OAAC;4BACC,OAAO,QAAQ,QAAQ,GAAG,YAAY;4BACtC,UAAU,CAAC,QAAQ,QAAQ,GAAG,gBAAgB;4BAC9C,mBAAmB,CAAC,QAAQ,QAAQ,GAAG,MAAM;;;;;;;;;;;kCAKjD,8OAAC,wJAAA,CAAA,OAAI;wBACH,UAAU;4BAAC;4BAAG,CAAC;4BAAK;yBAAK;wBACzB,UAAU;wBACV,OAAO,UAAU,YAAY;wBAC7B,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,UAAU;kCAET,QAAQ,KAAK;;;;;;kCAIhB,8OAAC,8JAAA,CAAA,aAAU;wBACT,MAAM;4BAAC;4BAAK;4BAAK;yBAAK;wBACtB,QAAQ;wBACR,UAAU;4BAAC;4BAAG,CAAC;4BAAK;yBAAK;wBACzB,UAAU;kCAEV,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU;4BACV,mBAAmB;;;;;;;;;;;kCAIvB,8OAAC,wJAAA,CAAA,OAAI;wBACH,UAAU;4BAAC;4BAAG,CAAC;4BAAK;yBAAK;wBACzB,UAAU;wBACV,OAAM;wBACN,SAAQ;wBACR,SAAQ;wBACR,MAAK;kCAEJ,QAAQ,QAAQ,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;oBAIhD,QAAQ,QAAQ,kBACf,8OAAC,8JAAA,CAAA,aAAU;wBACT,MAAM;4BAAC;4BAAK;4BAAK;yBAAK;wBACtB,QAAQ;wBACR,UAAU;4BAAC;4BAAK;4BAAK;yBAAK;wBAC1B,UAAU;kCAEV,cAAA,8OAAC;4BACC,OAAM;4BACN,UAAS;4BACT,mBAAmB;;;;;;;;;;;oBAMxB,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,kBAC3C,8OAAC,8JAAA,CAAA,aAAU;4BAET,MAAM;gCAAC;gCAAM;gCAAM;6BAAK;4BACxB,QAAQ;4BACR,UAAU;gCAAC,CAAC,IAAK,IAAI;gCAAM,CAAC;gCAAK;6BAAK;4BACtC,UAAU;sCAEV,cAAA,8OAAC;gCACC,OAAO,YAAY,cAAc;gCACjC,UAAU,YAAY,cAAc;gCACpC,mBAAmB;;;;;;2BAThB;;;;;;;;;;;0BAgBX,8OAAC;gBAAM,SAAS;gBAAS,UAAU;oBAAC;oBAAG,KAAK,EAAE;oBAAE;iBAAE;;kCAEhD,8OAAC,8JAAA,CAAA,aAAU;wBACT,MAAM;4BAAC;4BAAG;4BAAG;yBAAI;wBACjB,QAAQ;wBACR,UAAU;wBACV,aAAa;kCAEb,cAAA,8OAAC;4BACC,OAAM;4BACN,WAAW;4BACX,WAAW;4BACX,UAAU;4BACV,mBAAmB;;;;;;;;;;;oBAKtB,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,8JAAA,CAAA,aAAU;4BAET,MAAM;gCAAC;gCAAM,KAAK,MAAM,KAAK,IAAI;gCAAG;6BAAK;4BACzC,QAAQ;4BACR,UAAU;gCACR,CAAC,MAAO,IAAI;gCACZ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCACxB;6BACD;4BACD,UAAU;sCAEV,cAAA,8OAAC;gCACC,OAAO;gCACP,UAAU;gCACV,mBAAmB;;;;;;2BAbhB,CAAC,QAAQ,EAAE,GAAG;;;;;kCAmBvB,8OAAC,uJAAA,CAAA,OAAI;wBACH,UAAU;4BAAC;4BAAG;4BAAG;yBAAK;wBACtB,MAAM;wBACN,gBAAgB;wBAChB,SAAS;wBACT,OAAO;kCAEP,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC,QAAQ,KAAK;;;;;;8CAClE,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDAQX,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,OAAO,kBACd,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE;oDAC/B;oDACA,WAAU;8DACX;;;;;;gDAIF,QAAQ,SAAS,kBAChB,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,OAAO,IAAI,CAAC,QAAQ,SAAS,EAAE;oDACjC;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWZ,cAAc,yBACb,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;oBAAC;oBAAK;oBAAK;iBAAK;gBACtB,QAAQ;gBACR,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBACC,OAAO;oBACP,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAMrB;AAGO,SAAS,YAAY,EAC1B,QAAQ,EACR,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,UAAU,CAAC,EACX,WAAW,IAAI,EACf,cAAc,EAOf;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAS;IAE/B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;QACpC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;IAC1E;IAEA,6BAA6B;IAC7B,MAAM,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM;IAChD,MAAM,OAAO,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG;IAEzC,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;kBAC7B,SAAS,GAAG,CAAC,CAAC,SAAS;YACtB,MAAM,MAAM,KAAK,KAAK,CAAC,QAAQ;YAC/B,MAAM,MAAM,QAAQ;YACpB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;YACnC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;YAEnC,qBACE,8OAAC;gBAEC,SAAS;gBACT,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBACnB,OAAO;gBACP,UAAU;gBACV,SAAS;eALJ,QAAQ,EAAE;;;;;QAQrB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 5487, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/components/sections/ProjectsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ProjectScene } from '@/components/3d/SceneManager';\nimport { ProjectGrid } from '@/components/3d/ProjectCard';\nimport { Button } from '@/components/ui/Button';\nimport { useTheme } from '@/hooks/useTheme';\nimport { useAdaptiveQuality } from '@/hooks/usePerformance';\nimport { cn } from '@/lib/utils';\nimport { Project, ProjectCategory } from '@/types';\n\ninterface ProjectsSectionProps {\n  className?: string;\n}\n\n// Sample projects data\nconst sampleProjects: Project[] = [\n  {\n    id: '1',\n    title: '3D Portfolio Website',\n    description: 'An immersive 3D portfolio website built with React, Three.js, and Next.js featuring interactive microchip-themed design.',\n    longDescription: 'This project showcases advanced 3D web development techniques using React Three Fiber and Three.js. Features include interactive camera controls, performance optimization, and responsive design.',\n    technologies: ['React', 'Three.js', 'Next.js', 'TypeScript', 'Tailwind CSS'],\n    imageUrl: '/images/project-1.jpg',\n    demoUrl: 'https://demo.example.com',\n    githubUrl: 'https://github.com/example/project',\n    featured: true,\n    category: 'web-development',\n    completedAt: new Date('2024-01-15')\n  },\n  {\n    id: '2',\n    title: 'AI-Powered Dashboard',\n    description: 'A modern dashboard application with AI-driven analytics and real-time data visualization.',\n    technologies: ['React', 'Python', 'TensorFlow', 'D3.js', 'PostgreSQL'],\n    featured: true,\n    category: 'ai-ml',\n    completedAt: new Date('2023-11-20')\n  },\n  {\n    id: '3',\n    title: 'Mobile E-commerce App',\n    description: 'Cross-platform mobile application for e-commerce with advanced features and smooth animations.',\n    technologies: ['React Native', 'Node.js', 'MongoDB', 'Stripe', 'Firebase'],\n    featured: false,\n    category: 'mobile-development',\n    completedAt: new Date('2023-09-10')\n  },\n  {\n    id: '4',\n    title: 'Microservices Architecture',\n    description: 'Scalable backend system using microservices architecture with Docker and Kubernetes.',\n    technologies: ['Node.js', 'Docker', 'Kubernetes', 'Redis', 'PostgreSQL'],\n    featured: false,\n    category: 'backend',\n    completedAt: new Date('2023-07-05')\n  },\n  {\n    id: '5',\n    title: 'Real-time Chat Platform',\n    description: 'Full-stack real-time chat application with video calling and file sharing capabilities.',\n    technologies: ['React', 'Socket.io', 'WebRTC', 'Node.js', 'MongoDB'],\n    featured: true,\n    category: 'fullstack',\n    completedAt: new Date('2023-05-15')\n  },\n  {\n    id: '6',\n    title: 'DevOps Pipeline',\n    description: 'Automated CI/CD pipeline with monitoring and deployment automation for cloud infrastructure.',\n    technologies: ['Jenkins', 'Docker', 'AWS', 'Terraform', 'Prometheus'],\n    featured: false,\n    category: 'devops',\n    completedAt: new Date('2023-03-20')\n  }\n];\n\nconst categories: { id: ProjectCategory; label: string; icon: string }[] = [\n  { id: 'web-development', label: 'Web Dev', icon: '🌐' },\n  { id: 'mobile-development', label: 'Mobile', icon: '📱' },\n  { id: 'backend', label: 'Backend', icon: '⚙️' },\n  { id: 'fullstack', label: 'Full Stack', icon: '🔧' },\n  { id: 'ai-ml', label: 'AI/ML', icon: '🤖' },\n  { id: 'devops', label: 'DevOps', icon: '🚀' },\n  { id: 'other', label: 'Other', icon: '💡' }\n];\n\nconst technologies = [\n  'React', 'Next.js', 'Three.js', 'TypeScript', 'Node.js', \n  'Python', 'MongoDB', 'PostgreSQL', 'Docker', 'AWS'\n];\n\nexport function ProjectsSection({ className }: ProjectsSectionProps) {\n  const { themeConfig } = useTheme();\n  const qualitySettings = useAdaptiveQuality();\n  const [selectedCategory, setSelectedCategory] = useState<ProjectCategory | 'all'>('all');\n  const [selectedTech, setSelectedTech] = useState<string | 'all'>('all');\n  const [viewMode, setViewMode] = useState<'grid' | 'featured'>('grid');\n  const [selectedProject, setSelectedProject] = useState<Project | null>(null);\n\n  // Filter projects based on selected filters\n  const filteredProjects = useMemo(() => {\n    let filtered = sampleProjects;\n\n    if (selectedCategory !== 'all') {\n      filtered = filtered.filter(project => project.category === selectedCategory);\n    }\n\n    if (selectedTech !== 'all') {\n      filtered = filtered.filter(project => \n        project.technologies.some(tech => \n          tech.toLowerCase().includes(selectedTech.toLowerCase())\n        )\n      );\n    }\n\n    if (viewMode === 'featured') {\n      filtered = filtered.filter(project => project.featured);\n    }\n\n    return filtered.sort((a, b) => b.completedAt.getTime() - a.completedAt.getTime());\n  }, [selectedCategory, selectedTech, viewMode]);\n\n  const handleProjectClick = (project: Project) => {\n    setSelectedProject(selectedProject?.id === project.id ? null : project);\n  };\n\n  const clearFilters = () => {\n    setSelectedCategory('all');\n    setSelectedTech('all');\n    setViewMode('grid');\n  };\n\n  return (\n    <section id=\"projects\" className={cn(\n      \"min-h-screen bg-gradient-to-br from-bg-tertiary via-bg-primary to-bg-secondary\",\n      \"relative overflow-hidden py-20\",\n      className\n    )}>\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 circuit-bg opacity-5\" />\n      \n      <div className=\"relative z-10 max-w-7xl mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold text-secondary mb-6\">\n            My Projects\n          </h2>\n          <p className=\"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto\">\n            Explore my portfolio of interactive applications and innovative digital solutions\n          </p>\n        </motion.div>\n\n        {/* Filters */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"mb-12 space-y-6\"\n        >\n          {/* View Mode Toggle */}\n          <div className=\"flex justify-center\">\n            <div className=\"flex bg-bg-primary/50 rounded-lg p-1 chip-border\">\n              <Button\n                variant={viewMode === 'grid' ? 'primary' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setViewMode('grid')}\n              >\n                🔲 All Projects\n              </Button>\n              <Button\n                variant={viewMode === 'featured' ? 'primary' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setViewMode('featured')}\n              >\n                ⭐ Featured\n              </Button>\n            </div>\n          </div>\n\n          {/* Category Filters */}\n          <div className=\"flex flex-wrap justify-center gap-2\">\n            <Button\n              variant={selectedCategory === 'all' ? 'primary' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setSelectedCategory('all')}\n            >\n              🌟 All Categories\n            </Button>\n            {categories.map((category) => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'secondary' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                <span className=\"mr-1\">{category.icon}</span>\n                {category.label}\n              </Button>\n            ))}\n          </div>\n\n          {/* Technology Filters */}\n          <div className=\"flex flex-wrap justify-center gap-2\">\n            <Button\n              variant={selectedTech === 'all' ? 'accent' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setSelectedTech('all')}\n            >\n              🛠️ All Tech\n            </Button>\n            {technologies.map((tech) => (\n              <Button\n                key={tech}\n                variant={selectedTech === tech ? 'accent' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setSelectedTech(tech)}\n                className=\"text-xs\"\n              >\n                {tech}\n              </Button>\n            ))}\n          </div>\n\n          {/* Clear Filters */}\n          {(selectedCategory !== 'all' || selectedTech !== 'all' || viewMode !== 'grid') && (\n            <div className=\"flex justify-center\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={clearFilters}\n              >\n                ✨ Clear Filters\n              </Button>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Results Count */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center mb-8\"\n        >\n          <p className=\"text-text-secondary\">\n            Showing {filteredProjects.length} of {sampleProjects.length} projects\n          </p>\n        </motion.div>\n\n        {/* 3D Projects Display */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          whileInView={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"relative h-96 mb-12\"\n        >\n          <ProjectScene\n            enableControls={true}\n            enableEnvironment={false}\n            shadows={qualitySettings.shadows}\n            antialias={qualitySettings.antialias}\n            lighting=\"studio\"\n          >\n            <AnimatePresence mode=\"wait\">\n              <ProjectGrid\n                key={`${selectedCategory}-${selectedTech}-${viewMode}`}\n                projects={filteredProjects}\n                position={[0, 0, 0]}\n                spacing={4}\n                animated={true}\n                onProjectClick={handleProjectClick}\n              />\n            </AnimatePresence>\n          </ProjectScene>\n        </motion.div>\n\n        {/* Project Details Modal */}\n        <AnimatePresence>\n          {selectedProject && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\"\n              onClick={() => setSelectedProject(null)}\n            >\n              <motion.div\n                initial={{ scale: 0.9, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0.9, opacity: 0 }}\n                className=\"bg-bg-primary/95 backdrop-blur-md rounded-lg p-6 max-w-2xl w-full chip-border\"\n                onClick={(e) => e.stopPropagation()}\n              >\n                <div className=\"flex justify-between items-start mb-4\">\n                  <h3 className=\"text-2xl font-bold text-primary\">{selectedProject.title}</h3>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => setSelectedProject(null)}\n                  >\n                    ✕\n                  </Button>\n                </div>\n                \n                <p className=\"text-text-secondary mb-4\">\n                  {selectedProject.longDescription || selectedProject.description}\n                </p>\n                \n                <div className=\"space-y-4\">\n                  <div>\n                    <h4 className=\"text-lg font-bold text-secondary mb-2\">Technologies</h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {selectedProject.technologies.map((tech) => (\n                        <span\n                          key={tech}\n                          className=\"px-3 py-1 bg-primary/20 text-primary rounded-full text-sm chip-border\"\n                        >\n                          {tech}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex gap-4\">\n                    {selectedProject.demoUrl && (\n                      <Button\n                        variant=\"primary\"\n                        size=\"md\"\n                        onClick={() => window.open(selectedProject.demoUrl, '_blank')}\n                      >\n                        🚀 Live Demo\n                      </Button>\n                    )}\n                    {selectedProject.githubUrl && (\n                      <Button\n                        variant=\"secondary\"\n                        size=\"md\"\n                        onClick={() => window.open(selectedProject.githubUrl, '_blank')}\n                      >\n                        💻 View Code\n                      </Button>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* No Results */}\n        {filteredProjects.length === 0 && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"text-center py-12\"\n          >\n            <div className=\"text-6xl mb-4\">🔍</div>\n            <h3 className=\"text-2xl font-bold text-text-secondary mb-2\">No projects found</h3>\n            <p className=\"text-text-muted mb-6\">Try adjusting your filters to see more projects</p>\n            <Button variant=\"primary\" onClick={clearFilters}>\n              Clear All Filters\n            </Button>\n          </motion.div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAgBA,uBAAuB;AACvB,MAAM,iBAA4B;IAChC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAS;YAAY;YAAW;YAAc;SAAe;QAC5E,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;QACV,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;YAAC;YAAS;YAAU;YAAc;YAAS;SAAa;QACtE,UAAU;QACV,UAAU;QACV,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;YAAC;YAAgB;YAAW;YAAW;YAAU;SAAW;QAC1E,UAAU;QACV,UAAU;QACV,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;YAAC;YAAW;YAAU;YAAc;YAAS;SAAa;QACxE,UAAU;QACV,UAAU;QACV,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;YAAC;YAAS;YAAa;YAAU;YAAW;SAAU;QACpE,UAAU;QACV,UAAU;QACV,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;YAAC;YAAW;YAAU;YAAO;YAAa;SAAa;QACrE,UAAU;QACV,UAAU;QACV,aAAa,IAAI,KAAK;IACxB;CACD;AAED,MAAM,aAAqE;IACzE;QAAE,IAAI;QAAmB,OAAO;QAAW,MAAM;IAAK;IACtD;QAAE,IAAI;QAAsB,OAAO;QAAU,MAAM;IAAK;IACxD;QAAE,IAAI;QAAW,OAAO;QAAW,MAAM;IAAK;IAC9C;QAAE,IAAI;QAAa,OAAO;QAAc,MAAM;IAAK;IACnD;QAAE,IAAI;QAAS,OAAO;QAAS,MAAM;IAAK;IAC1C;QAAE,IAAI;QAAU,OAAO;QAAU,MAAM;IAAK;IAC5C;QAAE,IAAI;QAAS,OAAO;QAAS,MAAM;IAAK;CAC3C;AAED,MAAM,eAAe;IACnB;IAAS;IAAW;IAAY;IAAc;IAC9C;IAAU;IAAW;IAAc;IAAU;CAC9C;AAEM,SAAS,gBAAgB,EAAE,SAAS,EAAwB;IACjE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,kBAAkB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvE,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,WAAW;QAEf,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;QAC7D;QAEA,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,YAAY,CAAC,IAAI,CAAC,CAAA,OACxB,KAAK,WAAW,GAAG,QAAQ,CAAC,aAAa,WAAW;QAG1D;QAEA,IAAI,aAAa,YAAY;YAC3B,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;QACxD;QAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,CAAC,OAAO,KAAK,EAAE,WAAW,CAAC,OAAO;IAChF,GAAG;QAAC;QAAkB;QAAc;KAAS;IAE7C,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB,iBAAiB,OAAO,QAAQ,EAAE,GAAG,OAAO;IACjE;IAEA,MAAM,eAAe;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,YAAY;IACd;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACjC,kFACA,kCACA;;0BAGA,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAE,WAAU;0CAA4D;;;;;;;;;;;;kCAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,aAAa,SAAS,YAAY;4CAC3C,MAAK;4CACL,SAAS,IAAM,YAAY;sDAC5B;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,aAAa,aAAa,YAAY;4CAC/C,MAAK;4CACL,SAAS,IAAM,YAAY;sDAC5B;;;;;;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,qBAAqB,QAAQ,YAAY;wCAClD,MAAK;wCACL,SAAS,IAAM,oBAAoB;kDACpC;;;;;;oCAGA,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;4CAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,cAAc;4CAC1D,MAAK;4CACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;8DAE9C,8OAAC;oDAAK,WAAU;8DAAQ,SAAS,IAAI;;;;;;gDACpC,SAAS,KAAK;;2CANV,SAAS,EAAE;;;;;;;;;;;0CAYtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,iBAAiB,QAAQ,WAAW;wCAC7C,MAAK;wCACL,SAAS,IAAM,gBAAgB;kDAChC;;;;;;oCAGA,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,SAAM;4CAEL,SAAS,iBAAiB,OAAO,WAAW;4CAC5C,MAAK;4CACL,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;sDAET;2CANI;;;;;;;;;;;4BAYV,CAAC,qBAAqB,SAAS,iBAAiB,SAAS,aAAa,MAAM,mBAC3E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;8CACV;;;;;;;;;;;;;;;;;kCAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,WAAU;kCAEV,cAAA,8OAAC;4BAAE,WAAU;;gCAAsB;gCACxB,iBAAiB,MAAM;gCAAC;gCAAK,eAAe,MAAM;gCAAC;;;;;;;;;;;;kCAKhE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,aAAa;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC,wIAAA,CAAA,eAAY;4BACX,gBAAgB;4BAChB,mBAAmB;4BACnB,SAAS,gBAAgB,OAAO;4BAChC,WAAW,gBAAgB,SAAS;4BACpC,UAAS;sCAET,cAAA,8OAAC,yLAAA,CAAA,kBAAe;gCAAC,MAAK;0CACpB,cAAA,8OAAC,uIAAA,CAAA,cAAW;oCAEV,UAAU;oCACV,UAAU;wCAAC;wCAAG;wCAAG;qCAAE;oCACnB,SAAS;oCACT,UAAU;oCACV,gBAAgB;mCALX,GAAG,iBAAiB,CAAC,EAAE,aAAa,CAAC,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;kCAY9D,8OAAC,yLAAA,CAAA,kBAAe;kCACb,iCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,WAAU;4BACV,SAAS,IAAM,mBAAmB;sCAElC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAClC,SAAS;oCAAE,OAAO;oCAAG,SAAS;gCAAE;gCAChC,MAAM;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAC/B,WAAU;gCACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC,gBAAgB,KAAK;;;;;;0DACtE,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,mBAAmB;0DACnC;;;;;;;;;;;;kDAKH,8OAAC;wCAAE,WAAU;kDACV,gBAAgB,eAAe,IAAI,gBAAgB,WAAW;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;kEACZ,gBAAgB,YAAY,CAAC,GAAG,CAAC,CAAC,qBACjC,8OAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;;;;;;;;;;;;0DASb,8OAAC;gDAAI,WAAU;;oDACZ,gBAAgB,OAAO,kBACtB,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,OAAO,IAAI,CAAC,gBAAgB,OAAO,EAAE;kEACrD;;;;;;oDAIF,gBAAgB,SAAS,kBACxB,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,OAAO,IAAI,CAAC,gBAAgB,SAAS,EAAE;kEACvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAYd,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAAuB;;;;;;0CACpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D", "debugId": null}}, {"offset": {"line": 6188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { HeroSection } from '@/components/sections/HeroSection';\nimport { AboutSection } from '@/components/sections/AboutSection';\nimport { ProjectsSection } from '@/components/sections/ProjectsSection';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section id=\"home\">\n        <HeroSection />\n      </section>\n\n      {/* About Section */}\n      <AboutSection />\n\n      {/* Projects Section */}\n      <ProjectsSection />\n\n      <section id=\"skills\" className=\"min-h-screen flex items-center justify-center bg-bg-secondary\">\n        <div className=\"text-center max-w-4xl mx-auto px-4\">\n          <h2 className=\"text-4xl md:text-6xl font-bold text-accent mb-6\">Skills</h2>\n          <p className=\"text-xl text-text-secondary mb-12\">\n            Technologies and tools I use to create amazing digital experiences.\n          </p>\n          <p className=\"text-text-muted\">Coming soon in Phase 7...</p>\n        </div>\n      </section>\n\n      <section id=\"contact\" className=\"min-h-screen flex items-center justify-center bg-bg-primary\">\n        <div className=\"text-center max-w-4xl mx-auto px-4\">\n          <h2 className=\"text-4xl md:text-6xl font-bold text-primary mb-6\">Get In Touch</h2>\n          <p className=\"text-xl text-text-secondary mb-12\">\n            Ready to collaborate on your next 3D web project? Let's create something amazing together.\n          </p>\n          <p className=\"text-text-muted\">Coming soon in Phase 6...</p>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,IAAG;0BACV,cAAA,8OAAC,6IAAA,CAAA,cAAW;;;;;;;;;;0BAId,8OAAC,8IAAA,CAAA,eAAY;;;;;0BAGb,8OAAC,iJAAA,CAAA,kBAAe;;;;;0BAEhB,8OAAC;gBAAQ,IAAG;gBAAS,WAAU;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAChE,8OAAC;4BAAE,WAAU;sCAAoC;;;;;;sCAGjD,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAInC,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCACjE,8OAAC;4BAAE,WAAU;sCAAoC;;;;;;sCAGjD,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;;;;;;;;;;;;AAKzC", "debugId": null}}]}
'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@/hooks/useTheme';
import { cn } from '@/lib/utils';
import { Experience, Education } from '@/types';

interface TimelineProps {
  experiences?: Experience[];
  education?: Education[];
  className?: string;
}

interface TimelineItemProps {
  item: Experience | Education;
  index: number;
  isActive: boolean;
  onClick: () => void;
}

function TimelineItem({ item, index, isActive, onClick }: TimelineItemProps) {
  const { themeConfig } = useTheme();
  const isExperience = 'company' in item;
  
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short' 
    });
  };

  const getDuration = () => {
    const start = item.startDate;
    const end = item.endDate || new Date();
    const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;
    
    if (years === 0) return `${months} months`;
    if (remainingMonths === 0) return `${years} year${years > 1 ? 's' : ''}`;
    return `${years} year${years > 1 ? 's' : ''} ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className={cn(
        "relative flex items-center",
        index % 2 === 0 ? "flex-row" : "flex-row-reverse"
      )}
    >
      {/* Timeline connector */}
      <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary to-secondary opacity-30" />
      
      {/* Timeline node */}
      <motion.div
        whileHover={{ scale: 1.2 }}
        whileTap={{ scale: 0.9 }}
        onClick={onClick}
        className={cn(
          "absolute left-1/2 transform -translate-x-1/2 z-10",
          "w-6 h-6 rounded-full border-4 cursor-pointer",
          "transition-all duration-300",
          isActive 
            ? "bg-primary border-primary shadow-lg glow-primary" 
            : "bg-bg-secondary border-primary/50 hover:border-primary"
        )}
      >
        <div className={cn(
          "w-full h-full rounded-full",
          isActive && "animate-pulse-glow"
        )} />
      </motion.div>

      {/* Content card */}
      <motion.div
        layout
        className={cn(
          "w-5/12 p-6 rounded-lg chip-border",
          "bg-gradient-to-br from-bg-secondary/80 to-bg-tertiary/80",
          "backdrop-blur-sm cursor-pointer",
          "hover:shadow-xl transition-all duration-300",
          isActive && "glow-primary",
          index % 2 === 0 ? "mr-auto" : "ml-auto"
        )}
        onClick={onClick}
        whileHover={{ scale: 1.02 }}
      >
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div>
            <h3 className="text-lg font-bold text-primary">
              {isExperience ? (item as Experience).position : (item as Education).degree}
            </h3>
            <p className="text-text-secondary">
              {isExperience ? (item as Experience).company : (item as Education).institution}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-accent font-medium">
              {formatDate(item.startDate)} - {item.endDate ? formatDate(item.endDate) : 'Present'}
            </p>
            <p className="text-xs text-text-muted">{getDuration()}</p>
          </div>
        </div>

        {/* Description */}
        <p className="text-text-secondary text-sm mb-4 line-clamp-3">
          {isExperience ? (item as Experience).description : `${(item as Education).field} studies`}
        </p>

        {/* Technologies/Achievements preview */}
        {isExperience && (item as Experience).technologies.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {(item as Experience).technologies.slice(0, 3).map((tech, i) => (
              <span
                key={i}
                className="px-2 py-1 text-xs bg-primary/20 text-primary rounded chip-border"
              >
                {tech}
              </span>
            ))}
            {(item as Experience).technologies.length > 3 && (
              <span className="px-2 py-1 text-xs bg-secondary/20 text-secondary rounded">
                +{(item as Experience).technologies.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Expand indicator */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-text-muted">
            {isExperience ? 'Experience' : 'Education'}
          </span>
          <motion.div
            animate={{ rotate: isActive ? 180 : 0 }}
            transition={{ duration: 0.3 }}
            className="text-primary"
          >
            ▼
          </motion.div>
        </div>
      </motion.div>

      {/* Detailed view */}
      <AnimatePresence>
        {isActive && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className={cn(
              "absolute top-full mt-4 w-5/12 p-6 rounded-lg",
              "bg-bg-primary/95 backdrop-blur-md border border-primary/30",
              "shadow-xl z-20",
              index % 2 === 0 ? "left-0" : "right-0"
            )}
          >
            {isExperience ? (
              <ExperienceDetails experience={item as Experience} />
            ) : (
              <EducationDetails education={item as Education} />
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

function ExperienceDetails({ experience }: { experience: Experience }) {
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-lg font-bold text-primary mb-2">Role Description</h4>
        <p className="text-text-secondary text-sm">{experience.description}</p>
      </div>

      {experience.achievements.length > 0 && (
        <div>
          <h4 className="text-lg font-bold text-secondary mb-2">Key Achievements</h4>
          <ul className="space-y-1">
            {experience.achievements.map((achievement, i) => (
              <li key={i} className="text-text-secondary text-sm flex items-start">
                <span className="text-accent mr-2">•</span>
                {achievement}
              </li>
            ))}
          </ul>
        </div>
      )}

      <div>
        <h4 className="text-lg font-bold text-accent mb-2">Technologies Used</h4>
        <div className="flex flex-wrap gap-2">
          {experience.technologies.map((tech, i) => (
            <span
              key={i}
              className="px-3 py-1 text-sm bg-primary/20 text-primary rounded-full chip-border"
            >
              {tech}
            </span>
          ))}
        </div>
      </div>

      <div className="flex justify-between text-sm text-text-muted">
        <span>{experience.location}</span>
        <span className="capitalize">{experience.type.replace('-', ' ')}</span>
      </div>
    </div>
  );
}

function EducationDetails({ education }: { education: Education }) {
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-lg font-bold text-primary mb-2">Field of Study</h4>
        <p className="text-text-secondary">{education.field}</p>
      </div>

      {education.gpa && (
        <div>
          <h4 className="text-lg font-bold text-secondary mb-2">GPA</h4>
          <p className="text-text-secondary">{education.gpa}</p>
        </div>
      )}

      {education.achievements && education.achievements.length > 0 && (
        <div>
          <h4 className="text-lg font-bold text-accent mb-2">Achievements</h4>
          <ul className="space-y-1">
            {education.achievements.map((achievement, i) => (
              <li key={i} className="text-text-secondary text-sm flex items-start">
                <span className="text-accent mr-2">•</span>
                {achievement}
              </li>
            ))}
          </ul>
        </div>
      )}

      <div className="text-sm text-text-muted">
        <span>{education.location}</span>
      </div>
    </div>
  );
}

export function Timeline({ experiences = [], education = [], className }: TimelineProps) {
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);

  // Combine and sort all items by date
  const allItems = [
    ...experiences.map(exp => ({ ...exp, type: 'experience' as const })),
    ...education.map(edu => ({ ...edu, type: 'education' as const }))
  ].sort((a, b) => b.startDate.getTime() - a.startDate.getTime());

  const handleItemClick = (itemId: string) => {
    setActiveItem(activeItem === itemId ? null : itemId);
  };

  return (
    <div className={cn("relative py-12", className)}>
      <div className="max-w-6xl mx-auto px-4">
        {/* Timeline header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-primary mb-4">
            My Journey
          </h2>
          <p className="text-xl text-text-secondary max-w-2xl mx-auto">
            A timeline of my professional experience and educational background
          </p>
        </motion.div>

        {/* Timeline */}
        <div ref={timelineRef} className="relative space-y-16">
          {allItems.map((item, index) => (
            <TimelineItem
              key={item.id}
              item={item}
              index={index}
              isActive={activeItem === item.id}
              onClick={() => handleItemClick(item.id)}
            />
          ))}
        </div>

        {/* Timeline end marker */}
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: allItems.length * 0.1 }}
          className="relative mt-16"
        >
          <div className="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full glow-primary" />
          <div className="text-center pt-12">
            <p className="text-text-secondary">The journey continues...</p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

'use client';

import { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { RoundedBox, Text, Html, Image as DreiImage } from '@react-three/drei';
import { Mesh, Group } from 'three';
import { Project } from '@/types';

interface ProjectCardProps {
  project: Project;
  position?: [number, number, number];
  scale?: number;
  rotation?: [number, number, number];
  animated?: boolean;
  glowEffect?: boolean;
  onClick?: (project: Project) => void;
  onHover?: (project: Project, hovered: boolean) => void;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
}

export function ProjectCard({
  project,
  position = [0, 0, 0],
  scale = 1,
  rotation = [0, 0, 0],
  animated = true,
  glowEffect = true,
  onClick,
  onHover,
  primaryColor = '#00ff88',
  secondaryColor = '#0088ff',
  accentColor = '#ff6b00',
}: ProjectCardProps) {
  const groupRef = useRef<Group>(null);
  const cardRef = useRef<Mesh>(null);
  const [hovered, setHovered] = useState(false);
  const [flipped, setFlipped] = useState(false);

  // Get category color
  const getCategoryColor = (category: string) => {
    const colors = {
      'web-development': primaryColor,
      'mobile-development': secondaryColor,
      'backend': accentColor,
      'fullstack': '#9333ea',
      'ai-ml': '#f59e0b',
      'devops': '#10b981',
      'other': '#6b7280',
    };
    return colors[category as keyof typeof colors] || primaryColor;
  };

  const categoryColor = getCategoryColor(project.category);

  // Animation loop
  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    
    // Gentle floating animation
    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2 + position[0]) * 0.05;
    
    // Rotation animation when hovered
    if (hovered && !flipped) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 3) * 0.1;
    }
    
    // Flip animation
    if (flipped) {
      groupRef.current.rotation.y = Math.PI;
    } else {
      groupRef.current.rotation.y = 0;
    }
    
    // Scale effect
    if (cardRef.current) {
      const targetScale = hovered ? 1.05 : 1;
      cardRef.current.scale.lerp({ x: targetScale, y: targetScale, z: targetScale } as any, 0.1);
    }
  });

  const handlePointerOver = () => {
    setHovered(true);
    onHover?.(project, true);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    setHovered(false);
    onHover?.(project, false);
    document.body.style.cursor = 'auto';
  };

  const handleClick = () => {
    setFlipped(!flipped);
    onClick?.(project);
  };

  return (
    <group
      ref={groupRef}
      position={position}
      scale={scale}
      rotation={rotation}
      onClick={handleClick}
      onPointerOver={handlePointerOver}
      onPointerOut={handlePointerOut}
    >
      {/* Front side */}
      <group visible={!flipped}>
        {/* Main card body */}
        <RoundedBox
          ref={cardRef}
          args={[3, 4, 0.2]}
          radius={0.1}
          castShadow
          receiveShadow
        >
          <meshStandardMaterial
            color={hovered ? categoryColor : '#2a2a2a'}
            metalness={0.8}
            roughness={0.2}
            emissive={glowEffect ? categoryColor : '#000000'}
            emissiveIntensity={hovered ? 0.2 : 0.05}
          />
        </RoundedBox>

        {/* Image placeholder */}
        <RoundedBox
          args={[2.6, 1.8, 0.05]}
          radius={0.05}
          position={[0, 0.8, 0.11]}
          castShadow
        >
          <meshStandardMaterial
            color={project.imageUrl ? '#ffffff' : '#1a1a1a'}
            emissive={!project.imageUrl ? categoryColor : '#000000'}
            emissiveIntensity={!project.imageUrl ? 0.1 : 0}
          />
        </RoundedBox>

        {/* Project title */}
        <Text
          position={[0, -0.2, 0.11]}
          fontSize={0.2}
          color={hovered ? '#ffffff' : categoryColor}
          anchorX="center"
          anchorY="middle"
          font="/fonts/inter-bold.woff"
          maxWidth={2.8}
        >
          {project.title}
        </Text>

        {/* Category badge */}
        <RoundedBox
          args={[1.2, 0.3, 0.05]}
          radius={0.05}
          position={[0, -0.8, 0.11]}
          castShadow
        >
          <meshStandardMaterial
            color={categoryColor}
            emissive={categoryColor}
            emissiveIntensity={0.3}
          />
        </RoundedBox>

        <Text
          position={[0, -0.8, 0.16]}
          fontSize={0.1}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
          font="/fonts/inter-bold.woff"
        >
          {project.category.replace('-', ' ').toUpperCase()}
        </Text>

        {/* Featured indicator */}
        {project.featured && (
          <RoundedBox
            args={[0.3, 0.3, 0.05]}
            radius={0.05}
            position={[1.2, 1.5, 0.11]}
            castShadow
          >
            <meshStandardMaterial
              color="#ffd700"
              emissive="#ffd700"
              emissiveIntensity={0.5}
            />
          </RoundedBox>
        )}

        {/* Technology indicators */}
        {project.technologies.slice(0, 3).map((tech, i) => (
          <RoundedBox
            key={tech}
            args={[0.15, 0.15, 0.03]}
            radius={0.02}
            position={[-1 + (i * 0.4), -1.4, 0.11]}
            castShadow
          >
            <meshStandardMaterial
              color={secondaryColor}
              emissive={secondaryColor}
              emissiveIntensity={0.2}
            />
          </RoundedBox>
        ))}
      </group>

      {/* Back side */}
      <group visible={flipped} rotation={[0, Math.PI, 0]}>
        {/* Main card body */}
        <RoundedBox
          args={[3, 4, 0.2]}
          radius={0.1}
          castShadow
          receiveShadow
        >
          <meshStandardMaterial
            color="#1a1a1a"
            metalness={0.9}
            roughness={0.1}
            emissive={categoryColor}
            emissiveIntensity={0.1}
          />
        </RoundedBox>

        {/* Circuit pattern */}
        {Array.from({ length: 8 }, (_, i) => (
          <RoundedBox
            key={`circuit-${i}`}
            args={[0.02, Math.random() * 2 + 1, 0.01]}
            radius={0.005}
            position={[
              -1.2 + (i * 0.3),
              (Math.random() - 0.5) * 3,
              0.11
            ]}
            castShadow
          >
            <meshStandardMaterial
              color={categoryColor}
              emissive={categoryColor}
              emissiveIntensity={0.5}
            />
          </RoundedBox>
        ))}

        {/* Back content overlay */}
        <Html
          position={[0, 0, 0.11]}
          center
          distanceFactor={6}
          transform
          occlude
        >
          <div className="w-48 h-64 bg-bg-primary/95 backdrop-blur-md rounded-lg p-4 border border-primary/30">
            <h3 className="text-lg font-bold text-primary mb-2">{project.title}</h3>
            <p className="text-text-secondary text-sm mb-3 line-clamp-4">
              {project.description}
            </p>
            
            <div className="space-y-2">
              <div className="flex flex-wrap gap-1">
                {project.technologies.slice(0, 4).map((tech) => (
                  <span
                    key={tech}
                    className="px-2 py-1 text-xs bg-primary/20 text-primary rounded"
                  >
                    {tech}
                  </span>
                ))}
              </div>
              
              <div className="flex gap-2 mt-4">
                {project.demoUrl && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(project.demoUrl, '_blank');
                    }}
                    className="px-3 py-1 bg-primary text-white text-xs rounded hover:bg-primary-dark transition-colors"
                  >
                    Demo
                  </button>
                )}
                {project.githubUrl && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(project.githubUrl, '_blank');
                    }}
                    className="px-3 py-1 bg-secondary text-white text-xs rounded hover:bg-blue-600 transition-colors"
                  >
                    Code
                  </button>
                )}
              </div>
            </div>
          </div>
        </Html>
      </group>

      {/* Glow effect */}
      {glowEffect && hovered && (
        <RoundedBox
          args={[3.2, 4.2, 0.25]}
          radius={0.1}
          position={[0, 0, 0]}
        >
          <meshBasicMaterial
            color={categoryColor}
            transparent
            opacity={0.1}
          />
        </RoundedBox>
      )}
    </group>
  );
}

// Project grid component
export function ProjectGrid({
  projects,
  position = [0, 0, 0],
  spacing = 4,
  animated = true,
  onProjectClick,
}: {
  projects: Project[];
  position?: [number, number, number];
  spacing?: number;
  animated?: boolean;
  onProjectClick?: (project: Project) => void;
}) {
  const groupRef = useRef<Group>(null);

  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.05;
  });

  // Arrange projects in a grid
  const cols = Math.ceil(Math.sqrt(projects.length));
  const rows = Math.ceil(projects.length / cols);

  return (
    <group ref={groupRef} position={position}>
      {projects.map((project, index) => {
        const row = Math.floor(index / cols);
        const col = index % cols;
        const x = (col - (cols - 1) / 2) * spacing;
        const z = (row - (rows - 1) / 2) * spacing;
        
        return (
          <ProjectCard
            key={project.id}
            project={project}
            position={[x, 0, z]}
            scale={1}
            animated={animated}
            onClick={onProjectClick}
          />
        );
      })}
    </group>
  );
}

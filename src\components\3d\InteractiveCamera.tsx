'use client';

import { useRef, useEffect } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { PerspectiveCamera } from '@react-three/drei';
import { Vector3, MathUtils } from 'three';

interface InteractiveCameraProps {
  enableMouseTracking?: boolean;
  enableScrollTracking?: boolean;
  mouseSensitivity?: number;
  scrollSensitivity?: number;
  basePosition?: [number, number, number];
  lookAt?: [number, number, number];
  smoothing?: number;
}

export function InteractiveCamera({
  enableMouseTracking = true,
  enableScrollTracking = true,
  mouseSensitivity = 0.5,
  scrollSensitivity = 0.3,
  basePosition = [0, 0, 8],
  lookAt = [0, 0, 0],
  smoothing = 0.05,
}: InteractiveCameraProps) {
  const cameraRef = useRef<any>(null);
  const mousePosition = useRef({ x: 0, y: 0 });
  const scrollPosition = useRef(0);
  const targetPosition = useRef(new Vector3(...basePosition));
  const targetLookAt = useRef(new Vector3(...lookAt));
  const { camera } = useThree();

  // Mouse tracking
  useEffect(() => {
    if (!enableMouseTracking) return;

    const handleMouseMove = (event: MouseEvent) => {
      const x = (event.clientX / window.innerWidth) * 2 - 1;
      const y = -(event.clientY / window.innerHeight) * 2 + 1;
      
      mousePosition.current = { x, y };
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [enableMouseTracking]);

  // Scroll tracking
  useEffect(() => {
    if (!enableScrollTracking) return;

    const handleScroll = () => {
      scrollPosition.current = window.scrollY;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [enableScrollTracking]);

  // Camera animation
  useFrame(() => {
    if (!cameraRef.current) return;

    // Calculate target position based on mouse
    const mouseInfluence = {
      x: mousePosition.current.x * mouseSensitivity,
      y: mousePosition.current.y * mouseSensitivity,
      z: 0,
    };

    // Calculate scroll influence
    const scrollInfluence = {
      x: 0,
      y: 0,
      z: (scrollPosition.current * scrollSensitivity) / 100,
    };

    // Update target position
    targetPosition.current.set(
      basePosition[0] + mouseInfluence.x + scrollInfluence.x,
      basePosition[1] + mouseInfluence.y + scrollInfluence.y,
      basePosition[2] + mouseInfluence.z + scrollInfluence.z
    );

    // Smooth camera movement
    cameraRef.current.position.lerp(targetPosition.current, smoothing);
    
    // Update look-at target with slight mouse influence
    const lookAtTarget = new Vector3(
      lookAt[0] + mouseInfluence.x * 0.2,
      lookAt[1] + mouseInfluence.y * 0.2,
      lookAt[2]
    );
    
    cameraRef.current.lookAt(lookAtTarget);
  });

  return (
    <PerspectiveCamera
      ref={cameraRef}
      makeDefault
      position={basePosition}
      fov={75}
      near={0.1}
      far={1000}
    />
  );
}

// Cinematic camera for dramatic scenes
export function CinematicCamera({
  keyframes,
  duration = 10,
  autoStart = true,
}: {
  keyframes: Array<{
    position: [number, number, number];
    lookAt: [number, number, number];
    fov?: number;
  }>;
  duration?: number;
  autoStart?: boolean;
}) {
  const cameraRef = useRef<any>(null);
  const startTime = useRef<number | null>(null);
  const isPlaying = useRef(autoStart);

  useFrame((state) => {
    if (!cameraRef.current || !isPlaying.current || keyframes.length < 2) return;

    if (startTime.current === null) {
      startTime.current = state.clock.elapsedTime;
    }

    const elapsed = state.clock.elapsedTime - startTime.current;
    const progress = Math.min(elapsed / duration, 1);

    // Calculate current keyframe
    const keyframeIndex = Math.floor(progress * (keyframes.length - 1));
    const nextKeyframeIndex = Math.min(keyframeIndex + 1, keyframes.length - 1);
    const localProgress = (progress * (keyframes.length - 1)) % 1;

    const currentKeyframe = keyframes[keyframeIndex];
    const nextKeyframe = keyframes[nextKeyframeIndex];

    // Interpolate position
    const position = new Vector3(
      MathUtils.lerp(currentKeyframe.position[0], nextKeyframe.position[0], localProgress),
      MathUtils.lerp(currentKeyframe.position[1], nextKeyframe.position[1], localProgress),
      MathUtils.lerp(currentKeyframe.position[2], nextKeyframe.position[2], localProgress)
    );

    // Interpolate look-at
    const lookAt = new Vector3(
      MathUtils.lerp(currentKeyframe.lookAt[0], nextKeyframe.lookAt[0], localProgress),
      MathUtils.lerp(currentKeyframe.lookAt[1], nextKeyframe.lookAt[1], localProgress),
      MathUtils.lerp(currentKeyframe.lookAt[2], nextKeyframe.lookAt[2], localProgress)
    );

    // Interpolate FOV if provided
    if (currentKeyframe.fov && nextKeyframe.fov) {
      cameraRef.current.fov = MathUtils.lerp(currentKeyframe.fov, nextKeyframe.fov, localProgress);
      cameraRef.current.updateProjectionMatrix();
    }

    cameraRef.current.position.copy(position);
    cameraRef.current.lookAt(lookAt);

    // Reset when complete
    if (progress >= 1) {
      startTime.current = null;
      isPlaying.current = false;
    }
  });

  const play = () => {
    isPlaying.current = true;
    startTime.current = null;
  };

  const stop = () => {
    isPlaying.current = false;
    startTime.current = null;
  };

  return (
    <>
      <PerspectiveCamera
        ref={cameraRef}
        makeDefault
        position={keyframes[0].position}
        fov={keyframes[0].fov || 75}
        near={0.1}
        far={1000}
      />
      {/* Expose controls via context or props if needed */}
    </>
  );
}

// Orbit camera with constraints
export function ConstrainedOrbitCamera({
  target = [0, 0, 0],
  distance = 8,
  minDistance = 2,
  maxDistance = 20,
  enablePan = false,
  enableZoom = true,
  enableRotate = true,
  autoRotate = false,
  autoRotateSpeed = 0.5,
}: {
  target?: [number, number, number];
  distance?: number;
  minDistance?: number;
  maxDistance?: number;
  enablePan?: boolean;
  enableZoom?: boolean;
  enableRotate?: boolean;
  autoRotate?: boolean;
  autoRotateSpeed?: number;
}) {
  const cameraRef = useRef<any>(null);

  useFrame((state) => {
    if (!cameraRef.current || !autoRotate) return;

    const time = state.clock.elapsedTime * autoRotateSpeed;
    const x = Math.cos(time) * distance;
    const z = Math.sin(time) * distance;
    
    cameraRef.current.position.set(x, target[1], z);
    cameraRef.current.lookAt(...target);
  });

  return (
    <PerspectiveCamera
      ref={cameraRef}
      makeDefault
      position={[0, 0, distance]}
      fov={75}
      near={0.1}
      far={1000}
    />
  );
}

// First-person camera for immersive experiences
export function FirstPersonCamera({
  position = [0, 1.6, 0],
  sensitivity = 0.002,
  enableMovement = true,
  movementSpeed = 5,
}: {
  position?: [number, number, number];
  sensitivity?: number;
  enableMovement?: boolean;
  movementSpeed?: number;
}) {
  const cameraRef = useRef<any>(null);
  const keys = useRef<Set<string>>(new Set());
  const mouseMovement = useRef({ x: 0, y: 0 });
  const rotation = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      keys.current.add(event.code);
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      keys.current.delete(event.code);
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (document.pointerLockElement) {
        mouseMovement.current.x += event.movementX * sensitivity;
        mouseMovement.current.y += event.movementY * sensitivity;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [sensitivity]);

  useFrame((state, delta) => {
    if (!cameraRef.current) return;

    // Mouse look
    rotation.current.y -= mouseMovement.current.x;
    rotation.current.x -= mouseMovement.current.y;
    rotation.current.x = MathUtils.clamp(rotation.current.x, -Math.PI / 2, Math.PI / 2);

    cameraRef.current.rotation.set(rotation.current.x, rotation.current.y, 0);

    // Movement
    if (enableMovement) {
      const moveVector = new Vector3();
      
      if (keys.current.has('KeyW')) moveVector.z -= 1;
      if (keys.current.has('KeyS')) moveVector.z += 1;
      if (keys.current.has('KeyA')) moveVector.x -= 1;
      if (keys.current.has('KeyD')) moveVector.x += 1;
      
      moveVector.normalize();
      moveVector.multiplyScalar(movementSpeed * delta);
      
      // Apply rotation to movement vector
      moveVector.applyEuler(cameraRef.current.rotation);
      cameraRef.current.position.add(moveVector);
    }

    // Reset mouse movement
    mouseMovement.current.x = 0;
    mouseMovement.current.y = 0;
  });

  return (
    <PerspectiveCamera
      ref={cameraRef}
      makeDefault
      position={position}
      fov={75}
      near={0.1}
      far={1000}
    />
  );
}

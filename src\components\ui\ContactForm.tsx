'use client';

import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/hooks/useTheme';
import { cn, isValidEmail } from '@/lib/utils';
import { ContactFormData, ContactFormErrors } from '@/types';

interface ContactFormProps {
  className?: string;
  onSubmit?: (data: ContactFormData) => Promise<void>;
}

export function ContactForm({ className, onSubmit }: ContactFormProps) {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  
  const [errors, setErrors] = useState<ContactFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  
  const formRef = useRef<HTMLFormElement>(null);
  const { themeConfig } = useTheme();

  // Real-time validation
  const validateField = (name: keyof ContactFormData, value: string): string | undefined => {
    switch (name) {
      case 'name':
        if (!value.trim()) return 'Name is required';
        if (value.trim().length < 2) return 'Name must be at least 2 characters';
        break;
      case 'email':
        if (!value.trim()) return 'Email is required';
        if (!isValidEmail(value)) return 'Please enter a valid email address';
        break;
      case 'subject':
        if (!value.trim()) return 'Subject is required';
        if (value.trim().length < 5) return 'Subject must be at least 5 characters';
        break;
      case 'message':
        if (!value.trim()) return 'Message is required';
        if (value.trim().length < 10) return 'Message must be at least 10 characters';
        break;
    }
    return undefined;
  };

  const handleInputChange = (name: keyof ContactFormData, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleInputBlur = (name: keyof ContactFormData, value: string) => {
    const error = validateField(name, value);
    if (error) {
      setErrors(prev => ({ ...prev, [name]: error }));
    }
    setFocusedField(null);
  };

  const validateForm = (): boolean => {
    const newErrors: ContactFormErrors = {};
    
    Object.keys(formData).forEach(key => {
      const fieldName = key as keyof ContactFormData;
      const error = validateField(fieldName, formData[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      if (onSubmit) {
        await onSubmit(formData);
      } else {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('Form submitted:', formData);
      }
      
      setIsSubmitted(true);
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      console.error('Form submission error:', error);
      // Handle error (show toast, etc.)
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setIsSubmitted(false);
    setErrors({});
    setFormData({ name: '', email: '', subject: '', message: '' });
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={cn(
          "bg-bg-secondary/80 backdrop-blur-md rounded-lg p-8 chip-border",
          "text-center space-y-6",
          className
        )}
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring" }}
          className="w-16 h-16 mx-auto bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center"
        >
          <span className="text-2xl">✓</span>
        </motion.div>
        
        <div>
          <h3 className="text-2xl font-bold text-primary mb-2">Message Sent!</h3>
          <p className="text-text-secondary">
            Thank you for reaching out. I'll get back to you as soon as possible.
          </p>
        </div>
        
        <Button
          variant="outline"
          onClick={resetForm}
          className="mx-auto"
        >
          Send Another Message
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.form
      ref={formRef}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      onSubmit={handleSubmit}
      className={cn(
        "bg-bg-secondary/80 backdrop-blur-md rounded-lg p-8 chip-border space-y-6",
        className
      )}
    >
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-primary mb-2">Get In Touch</h3>
        <p className="text-text-secondary">
          Ready to collaborate? Send me a message and let's create something amazing together.
        </p>
      </div>

      {/* Name Field */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-text-primary">
          Name *
        </label>
        <div className="relative">
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            onFocus={() => setFocusedField('name')}
            onBlur={(e) => handleInputBlur('name', e.target.value)}
            className={cn(
              "w-full px-4 py-3 rounded-lg border-2 transition-all duration-200",
              "bg-bg-primary/50 text-text-primary placeholder-text-muted",
              "focus:outline-none focus:ring-2 focus:ring-primary/50",
              errors.name 
                ? "border-red-500 focus:border-red-500" 
                : focusedField === 'name'
                  ? "border-primary"
                  : "border-primary/30 hover:border-primary/50"
            )}
            placeholder="Your full name"
          />
          {focusedField === 'name' && (
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full"
            />
          )}
        </div>
        <AnimatePresence>
          {errors.name && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-red-500 text-sm"
            >
              {errors.name}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Email Field */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-text-primary">
          Email *
        </label>
        <div className="relative">
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            onFocus={() => setFocusedField('email')}
            onBlur={(e) => handleInputBlur('email', e.target.value)}
            className={cn(
              "w-full px-4 py-3 rounded-lg border-2 transition-all duration-200",
              "bg-bg-primary/50 text-text-primary placeholder-text-muted",
              "focus:outline-none focus:ring-2 focus:ring-primary/50",
              errors.email 
                ? "border-red-500 focus:border-red-500" 
                : focusedField === 'email'
                  ? "border-primary"
                  : "border-primary/30 hover:border-primary/50"
            )}
            placeholder="<EMAIL>"
          />
          {focusedField === 'email' && (
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full"
            />
          )}
        </div>
        <AnimatePresence>
          {errors.email && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-red-500 text-sm"
            >
              {errors.email}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Subject Field */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-text-primary">
          Subject *
        </label>
        <div className="relative">
          <input
            type="text"
            value={formData.subject}
            onChange={(e) => handleInputChange('subject', e.target.value)}
            onFocus={() => setFocusedField('subject')}
            onBlur={(e) => handleInputBlur('subject', e.target.value)}
            className={cn(
              "w-full px-4 py-3 rounded-lg border-2 transition-all duration-200",
              "bg-bg-primary/50 text-text-primary placeholder-text-muted",
              "focus:outline-none focus:ring-2 focus:ring-primary/50",
              errors.subject 
                ? "border-red-500 focus:border-red-500" 
                : focusedField === 'subject'
                  ? "border-primary"
                  : "border-primary/30 hover:border-primary/50"
            )}
            placeholder="What's this about?"
          />
          {focusedField === 'subject' && (
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full"
            />
          )}
        </div>
        <AnimatePresence>
          {errors.subject && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-red-500 text-sm"
            >
              {errors.subject}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Message Field */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-text-primary">
          Message *
        </label>
        <div className="relative">
          <textarea
            value={formData.message}
            onChange={(e) => handleInputChange('message', e.target.value)}
            onFocus={() => setFocusedField('message')}
            onBlur={(e) => handleInputBlur('message', e.target.value)}
            rows={5}
            className={cn(
              "w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 resize-none",
              "bg-bg-primary/50 text-text-primary placeholder-text-muted",
              "focus:outline-none focus:ring-2 focus:ring-primary/50",
              errors.message 
                ? "border-red-500 focus:border-red-500" 
                : focusedField === 'message'
                  ? "border-primary"
                  : "border-primary/30 hover:border-primary/50"
            )}
            placeholder="Tell me about your project or just say hello..."
          />
          {focusedField === 'message' && (
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full"
            />
          )}
        </div>
        <AnimatePresence>
          {errors.message && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-red-500 text-sm"
            >
              {errors.message}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        variant="primary"
        size="lg"
        fullWidth
        loading={isSubmitting}
        disabled={isSubmitting}
        className="animate-pulse-glow"
      >
        {isSubmitting ? 'Sending Message...' : 'Send Message 🚀'}
      </Button>

      <p className="text-xs text-text-muted text-center">
        * Required fields. Your information will be kept private and secure.
      </p>
    </motion.form>
  );
}

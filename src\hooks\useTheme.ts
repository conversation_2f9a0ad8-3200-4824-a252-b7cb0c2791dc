'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { Theme, ThemeConfig } from '@/types';
import { storage } from '@/lib/utils';

interface ThemeContextType {
  theme: Theme;
  themeConfig: ThemeConfig;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  updateThemeConfig: (config: Partial<ThemeConfig>) => void;
}

const defaultThemeConfig: ThemeConfig = {
  theme: 'dark',
  primaryColor: '#00ff88',
  secondaryColor: '#0088ff',
  accentColor: '#ff6b00',
  enableGlowEffects: true,
  enableAnimations: true,
};

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

export function useThemeState() {
  const [themeConfig, setThemeConfig] = useState<ThemeConfig>(defaultThemeConfig);

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedConfig = storage.get('themeConfig', defaultThemeConfig);
    setThemeConfig(savedConfig);
    
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', savedConfig.theme);
    
    // Apply custom CSS variables
    const root = document.documentElement;
    root.style.setProperty('--color-primary', savedConfig.primaryColor);
    root.style.setProperty('--color-secondary', savedConfig.secondaryColor);
    root.style.setProperty('--color-accent', savedConfig.accentColor);
  }, []);

  // Save theme to localStorage when it changes
  useEffect(() => {
    storage.set('themeConfig', themeConfig);
    document.documentElement.setAttribute('data-theme', themeConfig.theme);
    
    // Apply custom CSS variables
    const root = document.documentElement;
    root.style.setProperty('--color-primary', themeConfig.primaryColor);
    root.style.setProperty('--color-secondary', themeConfig.secondaryColor);
    root.style.setProperty('--color-accent', themeConfig.accentColor);
  }, [themeConfig]);

  const toggleTheme = () => {
    setThemeConfig(prev => ({
      ...prev,
      theme: prev.theme === 'light' ? 'dark' : 'light'
    }));
  };

  const setTheme = (theme: Theme) => {
    setThemeConfig(prev => ({
      ...prev,
      theme
    }));
  };

  const updateThemeConfig = (config: Partial<ThemeConfig>) => {
    setThemeConfig(prev => ({
      ...prev,
      ...config
    }));
  };

  return {
    theme: themeConfig.theme,
    themeConfig,
    toggleTheme,
    setTheme,
    updateThemeConfig,
  };
}

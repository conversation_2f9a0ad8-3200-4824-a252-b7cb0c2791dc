'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { Theme, ThemeConfig } from '@/types';
import { storage } from '@/lib/utils';

interface ThemeContextType {
  theme: Theme;
  themeConfig: ThemeConfig;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  updateThemeConfig: (config: Partial<ThemeConfig>) => void;
}

const defaultThemeConfig: ThemeConfig = {
  theme: 'dark',
  primaryColor: '#00ff88',
  secondaryColor: '#0088ff',
  accentColor: '#ff6b00',
  enableGlowEffects: true,
  enableAnimations: true,
};

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

export function useThemeState() {
  const [themeConfig, setThemeConfig] = useState<ThemeConfig>(defaultThemeConfig);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Apply theme changes to document
  const applyTheme = (config: ThemeConfig) => {
    const root = document.documentElement;

    // Set theme attribute
    root.setAttribute('data-theme', config.theme);

    // Apply custom CSS variables
    root.style.setProperty('--color-primary', config.primaryColor);
    root.style.setProperty('--color-secondary', config.secondaryColor);
    root.style.setProperty('--color-accent', config.accentColor);

    // Apply glow and animation settings
    root.style.setProperty('--enable-glow', config.enableGlowEffects ? '1' : '0');
    root.style.setProperty('--enable-animations', config.enableAnimations ? '1' : '0');

    // Update meta theme color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', config.primaryColor);
    }
  };

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedConfig = storage.get('themeConfig', defaultThemeConfig);

    // Check for system preference if no saved theme
    if (!storage.get('themeConfig', null)) {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      savedConfig.theme = prefersDark ? 'dark' : 'light';
    }

    setThemeConfig(savedConfig);
    applyTheme(savedConfig);
  }, []);

  // Save theme to localStorage when it changes
  useEffect(() => {
    storage.set('themeConfig', themeConfig);
    applyTheme(themeConfig);
  }, [themeConfig]);

  const toggleTheme = () => {
    setIsTransitioning(true);
    setThemeConfig(prev => ({
      ...prev,
      theme: prev.theme === 'light' ? 'dark' : 'light'
    }));

    // Reset transition state after animation
    setTimeout(() => setIsTransitioning(false), 300);
  };

  const setTheme = (theme: Theme) => {
    setIsTransitioning(true);
    setThemeConfig(prev => ({
      ...prev,
      theme
    }));
    setTimeout(() => setIsTransitioning(false), 300);
  };

  const updateThemeConfig = (config: Partial<ThemeConfig>) => {
    setThemeConfig(prev => ({
      ...prev,
      ...config
    }));
  };

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      // Only auto-switch if user hasn't manually set a preference
      const hasManualPreference = storage.get('themeConfig', null);
      if (!hasManualPreference) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return {
    theme: themeConfig.theme,
    themeConfig,
    isTransitioning,
    toggleTheme,
    setTheme,
    updateThemeConfig,
  };
}

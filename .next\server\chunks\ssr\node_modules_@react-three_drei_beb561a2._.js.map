{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/OrbitControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { OrbitControls as OrbitControls$1 } from 'three-stdlib';\n\nconst OrbitControls = /* @__PURE__ */React.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new OrbitControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\n\nexport { OrbitControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACrD,WAAW,EACX,MAAM,EACN,OAAO,EACP,UAAU,EACV,gBAAgB,IAAI,EACpB,YAAY,KAAK,EACjB,QAAQ,EACR,OAAO,EACP,KAAK,EACL,GAAG,WACJ,EAAE;IACD,MAAM,aAAa,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,UAAU;IACrD,MAAM,gBAAgB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,MAAM;IACpD,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,MAAM;IAC7C,MAAM,YAAY,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,SAAS;IACnD,MAAM,MAAM,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,GAAG;IACvC,MAAM,MAAM,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,GAAG;IACvC,MAAM,cAAc,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,WAAW;IACvD,MAAM,aAAa,UAAU;IAC7B,MAAM,iBAAiB,cAAc,OAAO,SAAS,IAAI,GAAG,UAAU;IACtE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,IAAI,4JAAA,CAAA,gBAAe,CAAC,aAAa;QAAC;KAAW;IAClF,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,SAAS,OAAO,EAAE,SAAS,MAAM;IACvC,GAAG,CAAC;IACJ,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,WAAW;YACb,SAAS,OAAO,CAAC,cAAc,OAAO,iBAAiB;QACzD;QACA,SAAS,OAAO,CAAC;QACjB,OAAO,IAAM,KAAK,SAAS,OAAO;IACpC,GAAG;QAAC;QAAW;QAAgB;QAAS;QAAU;KAAW;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,WAAW,CAAA;YACf;YACA,IAAI,SAAS,YAAY,OAAO;YAChC,IAAI,UAAU,SAAS;QACzB;QACA,MAAM,YAAY,CAAA;YAChB,IAAI,SAAS,QAAQ;QACvB;QACA,MAAM,UAAU,CAAA;YACd,IAAI,OAAO,MAAM;QACnB;QACA,SAAS,gBAAgB,CAAC,UAAU;QACpC,SAAS,gBAAgB,CAAC,SAAS;QACnC,SAAS,gBAAgB,CAAC,OAAO;QACjC,OAAO;YACL,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,OAAO;YACpC,SAAS,mBAAmB,CAAC,UAAU;QACzC;IACF,GAAG;QAAC;QAAU;QAAS;QAAO;QAAU;QAAY;KAAU;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,aAAa;YACf,MAAM,MAAM,MAAM,QAAQ;YAC1B,qEAAqE;YACrE,IAAI;gBACF;YACF;YACA,OAAO,IAAM,IAAI;oBACf,UAAU;gBACZ;QACF;IACF,GAAG;QAAC;QAAa;KAAS;IAC1B,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,KAAK;QACL,QAAQ;QACR,eAAe;IACjB,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/helpers/environment-assets.js"], "sourcesContent": ["const presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\n\nexport { presetsObj };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa;IACjB,WAAW;IACX,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,WAAW;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/useEnvironment.js"], "sourcesContent": ["import { useThree, useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, CubeTextureLoader } from 'three';\nimport { RGBELoader, EXRLoader } from 'three-stdlib';\nimport { HDRJPGLoader, GainMapLoader } from '@monogrid/gainmap-js';\nimport { presetsObj } from '../helpers/environment-assets.js';\nimport { useLayoutEffect } from 'react';\n\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nconst defaultFiles = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'];\nfunction useEnvironment({\n  files = defaultFiles,\n  path = '',\n  preset = undefined,\n  colorSpace = undefined,\n  extensions\n} = {}) {\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  // Everything else\n  const multiFile = isArray(files);\n  const {\n    extension,\n    isCubemap\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const gl = useThree(state => state.gl);\n  useLayoutEffect(() => {\n    // Only required for gainmap\n    if (extension !== 'webp' && extension !== 'jpg' && extension !== 'jpeg') return;\n    function clearGainmapTexture() {\n      useLoader.clear(loader, multiFile ? [files] : files);\n    }\n    gl.domElement.addEventListener('webglcontextlost', clearGainmapTexture, {\n      once: true\n    });\n  }, [files, gl.domElement]);\n  const loaderResult = useLoader(loader, multiFile ? [files] : files, loader => {\n    // Gainmap requires a renderer\n    if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n      // @ts-expect-error\n      loader.setRenderer(gl);\n    }\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n  let texture = multiFile ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  if (extension === 'jpg' || extension === 'jpeg' || extension === 'webp') {\n    var _renderTarget;\n    texture = (_renderTarget = texture.renderTarget) == null ? void 0 : _renderTarget.texture;\n  }\n  texture.mapping = isCubemap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  texture.colorSpace = colorSpace !== null && colorSpace !== void 0 ? colorSpace : isCubemap ? 'srgb' : 'srgb-linear';\n  return texture;\n}\nconst preloadDefaultOptions = {\n  files: defaultFiles,\n  path: '',\n  preset: undefined,\n  extensions: undefined\n};\nuseEnvironment.preload = preloadOptions => {\n  const options = {\n    ...preloadDefaultOptions,\n    ...preloadOptions\n  };\n  let {\n    files,\n    path = ''\n  } = options;\n  const {\n    preset,\n    extensions\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n  const {\n    extension\n  } = getExtension(files);\n  if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n    throw new Error('useEnvironment: Preloading gainmaps is not supported');\n  }\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.preload(loader, isArray(files) ? [files] : files, loader => {\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n};\nconst clearDefaultOptins = {\n  files: defaultFiles,\n  preset: undefined\n};\nuseEnvironment.clear = clearOptions => {\n  const options = {\n    ...clearDefaultOptins,\n    ...clearOptions\n  };\n  let {\n    files\n  } = options;\n  const {\n    preset\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n  }\n  const {\n    extension\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.clear(loader, isArray(files) ? [files] : files);\n};\nfunction validatePreset(preset) {\n  if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n}\nfunction getExtension(files) {\n  var _firstEntry$split$pop;\n  const isCubemap = isArray(files) && files.length === 6;\n  const isGainmap = isArray(files) && files.length === 3 && files.some(file => file.endsWith('json'));\n  const firstEntry = isArray(files) ? files[0] : files;\n\n  // Everything else\n  const extension = isCubemap ? 'cube' : isGainmap ? 'webp' : firstEntry.startsWith('data:application/exr') ? 'exr' : firstEntry.startsWith('data:application/hdr') ? 'hdr' : firstEntry.startsWith('data:image/jpeg') ? 'jpg' : (_firstEntry$split$pop = firstEntry.split('.').pop()) == null || (_firstEntry$split$pop = _firstEntry$split$pop.split('?')) == null || (_firstEntry$split$pop = _firstEntry$split$pop.shift()) == null ? void 0 : _firstEntry$split$pop.toLowerCase();\n  return {\n    extension,\n    isCubemap,\n    isGainmap\n  };\n}\nfunction getLoader(extension) {\n  const loader = extension === 'cube' ? CubeTextureLoader : extension === 'hdr' ? RGBELoader : extension === 'exr' ? EXRLoader : extension === 'jpg' || extension === 'jpeg' ? HDRJPGLoader : extension === 'webp' ? GainMapLoader : null;\n  return loader;\n}\n\nexport { useEnvironment };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,eAAe;AACrB,MAAM,UAAU,CAAA,MAAO,MAAM,OAAO,CAAC;AACrC,MAAM,eAAe;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU;AACvF,SAAS,eAAe,EACtB,QAAQ,YAAY,EACpB,OAAO,EAAE,EACT,SAAS,SAAS,EAClB,aAAa,SAAS,EACtB,UAAU,EACX,GAAG,CAAC,CAAC;IACJ,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,4KAAA,CAAA,aAAU,CAAC,OAAO;QAC1B,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,YAAY,QAAQ;IAC1B,MAAM,EACJ,SAAS,EACT,SAAS,EACV,GAAG,aAAa;IACjB,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,EAAE;IACrC,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE;QACd,4BAA4B;QAC5B,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;QACzE,SAAS;YACP,gNAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,YAAY;gBAAC;aAAM,GAAG;QAChD;QACA,GAAG,UAAU,CAAC,gBAAgB,CAAC,oBAAoB,qBAAqB;YACtE,MAAM;QACR;IACF,GAAG;QAAC;QAAO,GAAG,UAAU;KAAC;IACzB,MAAM,eAAe,CAAA,GAAA,gNAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,YAAY;QAAC;KAAM,GAAG,OAAO,CAAA;QAClE,8BAA8B;QAC9B,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;YACvE,mBAAmB;YACnB,OAAO,WAAW,CAAC;QACrB;QACA,OAAO,OAAO,IAAI,QAAQ,OAAO,OAAO,CAAC;QACzC,mBAAmB;QACnB,IAAI,YAAY,WAAW;IAC7B;IACA,IAAI,UAAU,YACd,aAAa;IACb,YAAY,CAAC,EAAE,GAAG;IAClB,IAAI,cAAc,SAAS,cAAc,UAAU,cAAc,QAAQ;QACvE,IAAI;QACJ,UAAU,CAAC,gBAAgB,QAAQ,YAAY,KAAK,OAAO,KAAK,IAAI,cAAc,OAAO;IAC3F;IACA,QAAQ,OAAO,GAAG,YAAY,+IAAA,CAAA,wBAAqB,GAAG,+IAAA,CAAA,mCAAgC;IACtF,QAAQ,UAAU,GAAG,eAAe,QAAQ,eAAe,KAAK,IAAI,aAAa,YAAY,SAAS;IACtG,OAAO;AACT;AACA,MAAM,wBAAwB;IAC5B,OAAO;IACP,MAAM;IACN,QAAQ;IACR,YAAY;AACd;AACA,eAAe,OAAO,GAAG,CAAA;IACvB,MAAM,UAAU;QACd,GAAG,qBAAqB;QACxB,GAAG,cAAc;IACnB;IACA,IAAI,EACF,KAAK,EACL,OAAO,EAAE,EACV,GAAG;IACJ,MAAM,EACJ,MAAM,EACN,UAAU,EACX,GAAG;IACJ,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,4KAAA,CAAA,aAAU,CAAC,OAAO;QAC1B,OAAO;IACT;IACA,MAAM,EACJ,SAAS,EACV,GAAG,aAAa;IACjB,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;QACvE,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,gNAAA,CAAA,YAAS,CAAC,OAAO,CAAC,QAAQ,QAAQ,SAAS;QAAC;KAAM,GAAG,OAAO,CAAA;QAC1D,OAAO,OAAO,IAAI,QAAQ,OAAO,OAAO,CAAC;QACzC,mBAAmB;QACnB,IAAI,YAAY,WAAW;IAC7B;AACF;AACA,MAAM,qBAAqB;IACzB,OAAO;IACP,QAAQ;AACV;AACA,eAAe,KAAK,GAAG,CAAA;IACrB,MAAM,UAAU;QACd,GAAG,kBAAkB;QACrB,GAAG,YAAY;IACjB;IACA,IAAI,EACF,KAAK,EACN,GAAG;IACJ,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,4KAAA,CAAA,aAAU,CAAC,OAAO;IAC5B;IACA,MAAM,EACJ,SAAS,EACV,GAAG,aAAa;IACjB,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,gNAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,QAAQ,SAAS;QAAC;KAAM,GAAG;AACrD;AACA,SAAS,eAAe,MAAM;IAC5B,IAAI,CAAC,CAAC,UAAU,4KAAA,CAAA,aAAU,GAAG,MAAM,IAAI,MAAM,4BAA4B,OAAO,IAAI,CAAC,4KAAA,CAAA,aAAU,EAAE,IAAI,CAAC;AACxG;AACA,SAAS,aAAa,KAAK;IACzB,IAAI;IACJ,MAAM,YAAY,QAAQ,UAAU,MAAM,MAAM,KAAK;IACrD,MAAM,YAAY,QAAQ,UAAU,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;IAC3F,MAAM,aAAa,QAAQ,SAAS,KAAK,CAAC,EAAE,GAAG;IAE/C,kBAAkB;IAClB,MAAM,YAAY,YAAY,SAAS,YAAY,SAAS,WAAW,UAAU,CAAC,0BAA0B,QAAQ,WAAW,UAAU,CAAC,0BAA0B,QAAQ,WAAW,UAAU,CAAC,qBAAqB,QAAQ,CAAC,wBAAwB,WAAW,KAAK,CAAC,KAAK,GAAG,EAAE,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,sBAAsB,WAAW;IACld,OAAO;QACL;QACA;QACA;IACF;AACF;AACA,SAAS,UAAU,SAAS;IAC1B,MAAM,SAAS,cAAc,SAAS,+IAAA,CAAA,oBAAiB,GAAG,cAAc,QAAQ,wJAAA,CAAA,aAAU,GAAG,cAAc,QAAQ,uJAAA,CAAA,YAAS,GAAG,cAAc,SAAS,cAAc,SAAS,6KAAA,CAAA,eAAY,GAAG,cAAc,SAAS,6KAAA,CAAA,gBAAa,GAAG;IACnO,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/Environment.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, use<PERSON>rame, createPortal, applyProps, extend } from '@react-three/fiber';\nimport { Scene, WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport { GroundProjectedEnv } from 'three-stdlib';\nimport { useEnvironment } from './useEnvironment.js';\n\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, sceneProps = {}) {\n  var _target$backgroundRot, _target$backgroundRot2, _target$environmentRo, _target$environmentRo2;\n  // defaults\n  sceneProps = {\n    backgroundBlurriness: 0,\n    backgroundIntensity: 1,\n    backgroundRotation: [0, 0, 0],\n    environmentIntensity: 1,\n    environmentRotation: [0, 0, 0],\n    ...sceneProps\n  };\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment;\n  const oldSceneProps = {\n    // @ts-ignore\n    backgroundBlurriness: target.backgroundBlurriness,\n    // @ts-ignore\n    backgroundIntensity: target.backgroundIntensity,\n    // @ts-ignore\n    backgroundRotation: (_target$backgroundRot = (_target$backgroundRot2 = target.backgroundRotation) == null || _target$backgroundRot2.clone == null ? void 0 : _target$backgroundRot2.clone()) !== null && _target$backgroundRot !== void 0 ? _target$backgroundRot : [0, 0, 0],\n    // @ts-ignore\n    environmentIntensity: target.environmentIntensity,\n    // @ts-ignore\n    environmentRotation: (_target$environmentRo = (_target$environmentRo2 = target.environmentRotation) == null || _target$environmentRo2.clone == null ? void 0 : _target$environmentRo2.clone()) !== null && _target$environmentRo !== void 0 ? _target$environmentRo : [0, 0, 0]\n  };\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture;\n  applyProps(target, sceneProps);\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg;\n    applyProps(target, oldSceneProps);\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  map,\n  ...config\n}) {\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, config);\n  });\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  ...rest\n}) {\n  const texture = useEnvironment(rest);\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  });\n  React.useEffect(() => {\n    return () => {\n      texture.dispose();\n    };\n  }, [texture]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 0.1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = useThree(state => state.gl);\n  const defaultScene = useThree(state => state.scene);\n  const camera = React.useRef(null);\n  const [virtualScene] = React.useState(() => new Scene());\n  const fbo = React.useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  React.useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  React.useLayoutEffect(() => {\n    if (frames === 1) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n    }\n    return setEnvProps(background, scene, defaultScene, fbo.texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/React.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/React.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = useEnvironment(props);\n  const texture = props.map || textureDefault;\n  React.useMemo(() => extend({\n    GroundProjectedEnvImpl: GroundProjectedEnv\n  }), []);\n  React.useEffect(() => {\n    return () => {\n      textureDefault.dispose();\n    };\n  }, [textureDefault]);\n  const args = React.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(EnvironmentMap, _extends({}, props, {\n    map: texture\n  })), /*#__PURE__*/React.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/React.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/React.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/React.createElement(EnvironmentPortal, props) : /*#__PURE__*/React.createElement(EnvironmentCube, props);\n}\n\nexport { Environment, EnvironmentCube, EnvironmentMap, EnvironmentPortal };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,QAAQ,CAAA,MAAO,IAAI,OAAO,IAAI,IAAI,OAAO,CAAC,OAAO;AACvD,MAAM,eAAe,CAAA,QAAS,MAAM,SAAS,MAAM,OAAO,GAAG;AAC7D,SAAS,YAAY,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;IAC5E,IAAI,uBAAuB,wBAAwB,uBAAuB;IAC1E,WAAW;IACX,aAAa;QACX,sBAAsB;QACtB,qBAAqB;QACrB,oBAAoB;YAAC;YAAG;YAAG;SAAE;QAC7B,sBAAsB;QACtB,qBAAqB;YAAC;YAAG;YAAG;SAAE;QAC9B,GAAG,UAAU;IACf;IACA,MAAM,SAAS,aAAa,SAAS;IACrC,MAAM,QAAQ,OAAO,UAAU;IAC/B,MAAM,SAAS,OAAO,WAAW;IACjC,MAAM,gBAAgB;QACpB,aAAa;QACb,sBAAsB,OAAO,oBAAoB;QACjD,aAAa;QACb,qBAAqB,OAAO,mBAAmB;QAC/C,aAAa;QACb,oBAAoB,CAAC,wBAAwB,CAAC,yBAAyB,OAAO,kBAAkB,KAAK,QAAQ,uBAAuB,KAAK,IAAI,OAAO,KAAK,IAAI,uBAAuB,KAAK,EAAE,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;YAAC;YAAG;YAAG;SAAE;QAC7Q,aAAa;QACb,sBAAsB,OAAO,oBAAoB;QACjD,aAAa;QACb,qBAAqB,CAAC,wBAAwB,CAAC,yBAAyB,OAAO,mBAAmB,KAAK,QAAQ,uBAAuB,KAAK,IAAI,OAAO,KAAK,IAAI,uBAAuB,KAAK,EAAE,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;YAAC;YAAG;YAAG;SAAE;IACjR;IACA,IAAI,eAAe,QAAQ,OAAO,WAAW,GAAG;IAChD,IAAI,YAAY,OAAO,UAAU,GAAG;IACpC,CAAA,GAAA,iNAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IACnB,OAAO;QACL,IAAI,eAAe,QAAQ,OAAO,WAAW,GAAG;QAChD,IAAI,YAAY,OAAO,UAAU,GAAG;QACpC,CAAA,GAAA,iNAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IACrB;AACF;AACA,SAAS,eAAe,EACtB,KAAK,EACL,aAAa,KAAK,EAClB,GAAG,EACH,GAAG,QACJ;IACC,MAAM,eAAe,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,KAAK;IAClD,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,KAAK,OAAO,YAAY,YAAY,OAAO,cAAc,KAAK;IACpE;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,EACvB,aAAa,KAAK,EAClB,KAAK,EACL,IAAI,EACJ,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,mBAAmB,EACnB,GAAG,MACJ;IACC,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,MAAM,eAAe,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,KAAK;IAClD,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,OAAO,YAAY,YAAY,OAAO,cAAc,SAAS;YAC3D,sBAAsB,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO;YAChE;YACA;YACA;YACA;QACF;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO;YACL,QAAQ,OAAO;QACjB;IACF,GAAG;QAAC;KAAQ;IACZ,OAAO;AACT;AACA,SAAS,kBAAkB,EACzB,QAAQ,EACR,OAAO,GAAG,EACV,MAAM,IAAI,EACV,aAAa,GAAG,EAChB,SAAS,CAAC,EACV,GAAG,EACH,aAAa,KAAK,EAClB,IAAI,EACJ,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,mBAAmB,EACnB,KAAK,EACL,KAAK,EACL,IAAI,EACJ,SAAS,SAAS,EAClB,UAAU,EACX;IACC,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,EAAE;IACrC,MAAM,eAAe,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,KAAK;IAClD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,IAAM,IAAI,+IAAA,CAAA,QAAK;IACrD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACxB,MAAM,MAAM,IAAI,+IAAA,CAAA,wBAAqB,CAAC;QACtC,IAAI,OAAO,CAAC,IAAI,GAAG,+IAAA,CAAA,gBAAa;QAChC,OAAO;IACT,GAAG;QAAC;KAAW;IACf,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO;YACL,IAAI,OAAO;QACb;IACF,GAAG;QAAC;KAAI;IACR,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,WAAW,GAAG;YAChB,MAAM,YAAY,GAAG,SAAS;YAC9B,GAAG,SAAS,GAAG;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI;YAC1B,GAAG,SAAS,GAAG;QACjB;QACA,OAAO,YAAY,YAAY,OAAO,cAAc,IAAI,OAAO,EAAE;YAC/D,sBAAsB,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO;YAChE;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAU;QAAc,IAAI,OAAO;QAAE;QAAO;QAAc;QAAY;QAAQ;KAAG;IACrF,IAAI,QAAQ;IACZ,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,WAAW,YAAY,QAAQ,QAAQ;YACzC,MAAM,YAAY,GAAG,SAAS;YAC9B,GAAG,SAAS,GAAG;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI;YAC1B,GAAG,SAAS,GAAG;YACf;QACF;IACF;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,CAAA,GAAA,mNAAA,CAAA,eAAY,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,UAAU,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,cAAc;QACzL,KAAK;QACL,MAAM;YAAC;YAAM;YAAK;SAAI;IACxB,IAAI,SAAS,SAAS,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB;QACtE,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,MAAM;QACN,YAAY;IACd,KAAK,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,gBAAgB;QAC1D,YAAY;QACZ,KAAK;QACL,YAAY;IACd,KAAK,OAAO;AACd;AACA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,eAAe,gBAAgB,QAAQ;IAC3C,MAAM,iBAAiB,CAAA,GAAA,kKAAA,CAAA,iBAAc,AAAD,EAAE;IACtC,MAAM,UAAU,MAAM,GAAG,IAAI;IAC7B,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE;YACzB,wBAAwB,gKAAA,CAAA,qBAAkB;QAC5C,IAAI,EAAE;IACN,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO;YACL,eAAe,OAAO;QACxB;IACF,GAAG;QAAC;KAAe;IACnB,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM;YAAC;SAAQ,EAAE;QAAC;KAAQ;IACrD,MAAM,SAAS,CAAC,gBAAgB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,cAAc,MAAM;IACrF,MAAM,SAAS,CAAC,iBAAiB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,eAAe,MAAM;IACvF,MAAM,QAAQ,CAAC,SAAS,CAAC,iBAAiB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,eAAe,KAAK,MAAM,QAAQ,WAAW,KAAK,IAAI,SAAS;IAC1I,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,gBAAgB,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACjI,KAAK;IACP,KAAK,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,0BAA0B;QAC9D,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;AACF;AACA,SAAS,YAAY,KAAK;IACxB,OAAO,MAAM,MAAM,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,mBAAmB,SAAS,MAAM,GAAG,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,gBAAgB,SAAS,MAAM,QAAQ,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,mBAAmB,SAAS,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB;AAC3R", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/Fbo.js"], "sourcesContent": ["import * as React from 'react';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(/** Width in pixels, or settings (will render fullscreen by default) */\nwidth, /** Height in pixels */\nheight, /**Settings */\nsettings) {\n  const size = useThree(state => state.size);\n  const viewport = useThree(state => state.viewport);\n  const _width = typeof width === 'number' ? width : size.width * viewport.dpr;\n  const _height = typeof height === 'number' ? height : size.height * viewport.dpr;\n  const _settings = (typeof width === 'number' ? settings : width) || {};\n  const {\n    samples = 0,\n    depth,\n    ...targetSettings\n  } = _settings;\n  const depthBuffer = depth !== null && depth !== void 0 ? depth : _settings.depthBuffer; // backwards compatibility for deprecated `depth` prop\n\n  const target = React.useMemo(() => {\n    const target = new THREE.WebGLRenderTarget(_width, _height, {\n      minFilter: THREE.LinearFilter,\n      magFilter: THREE.LinearFilter,\n      type: THREE.HalfFloatType,\n      ...targetSettings\n    });\n    if (depthBuffer) {\n      target.depthTexture = new THREE.DepthTexture(_width, _height, THREE.FloatType);\n    }\n    target.samples = samples;\n    return target;\n  }, []);\n  React.useLayoutEffect(() => {\n    target.setSize(_width, _height);\n    if (samples) target.samples = samples;\n  }, [samples, target, _width, _height]);\n  React.useEffect(() => {\n    return () => target.dispose();\n  }, []);\n  return target;\n}\n\n//\n// Fbo component\n//\n\nconst Fbo = /* @__PURE__ */forwardRef(({\n  children,\n  width,\n  height,\n  ...settings\n}, fref) => {\n  const target = useFBO(width, height, settings);\n  useImperativeHandle(fref, () => target, [target]); // expose target through ref\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(target));\n});\n\nexport { Fbo, useFBO };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;;AAEA,2DAA2D;AAC3D,iDAAiD;AACjD,SAAS,OAAO,qEAAqE,GACrF,KAAK,EAAE,qBAAqB,GAC5B,MAAM,EAAE,YAAY,GACpB,QAAQ;IACN,MAAM,OAAO,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,IAAI;IACzC,MAAM,WAAW,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,QAAQ;IACjD,MAAM,SAAS,OAAO,UAAU,WAAW,QAAQ,KAAK,KAAK,GAAG,SAAS,GAAG;IAC5E,MAAM,UAAU,OAAO,WAAW,WAAW,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG;IAChF,MAAM,YAAY,CAAC,OAAO,UAAU,WAAW,WAAW,KAAK,KAAK,CAAC;IACrE,MAAM,EACJ,UAAU,CAAC,EACX,KAAK,EACL,GAAG,gBACJ,GAAG;IACJ,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,QAAQ,UAAU,WAAW,EAAE,sDAAsD;IAE9I,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC3B,MAAM,SAAS,IAAI,+IAAA,CAAA,oBAAuB,CAAC,QAAQ,SAAS;YAC1D,WAAW,+IAAA,CAAA,eAAkB;YAC7B,WAAW,+IAAA,CAAA,eAAkB;YAC7B,MAAM,+IAAA,CAAA,gBAAmB;YACzB,GAAG,cAAc;QACnB;QACA,IAAI,aAAa;YACf,OAAO,YAAY,GAAG,IAAI,+IAAA,CAAA,eAAkB,CAAC,QAAQ,SAAS,+IAAA,CAAA,YAAe;QAC/E;QACA,OAAO,OAAO,GAAG;QACjB,OAAO;IACT,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,OAAO,OAAO,CAAC,QAAQ;QACvB,IAAI,SAAS,OAAO,OAAO,GAAG;IAChC,GAAG;QAAC;QAAS;QAAQ;QAAQ;KAAQ;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO,IAAM,OAAO,OAAO;IAC7B,GAAG,EAAE;IACL,OAAO;AACT;AAEA,EAAE;AACF,gBAAgB;AAChB,EAAE;AAEF,MAAM,MAAM,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACrC,QAAQ,EACR,KAAK,EACL,MAAM,EACN,GAAG,UACJ,EAAE;IACD,MAAM,SAAS,OAAO,OAAO,QAAQ;IACrC,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,IAAM,QAAQ;QAAC;KAAO,GAAG,4BAA4B;IAE/E,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,YAAY,OAAO,KAAK,IAAI,SAAS;AACrG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/PerspectiveCamera.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\n\nconst isFunction = node => typeof node === 'function';\nconst PerspectiveCamera = /* @__PURE__ */React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  makeDefault,\n  children,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.aspect = size.width / size.height;\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"perspectiveCamera\", _extends({\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\nexport { PerspectiveCamera };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEA,MAAM,aAAa,CAAA,OAAQ,OAAO,SAAS;AAC3C,MAAM,oBAAoB,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACzD,MAAM,EACN,aAAa,GAAG,EAChB,SAAS,QAAQ,EACjB,WAAW,EACX,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,MAAM,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EACpB,GAAG,EACJ,GAAK;IACN,MAAM,SAAS,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EACvB,MAAM,EACP,GAAK;IACN,MAAM,OAAO,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EACrB,IAAI,EACL,GAAK;IACN,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,CAAA,GAAA,qMAAA,CAAA,sBAAyB,AAAD,EAAE,KAAK,IAAM,UAAU,OAAO,EAAE,EAAE;IAC1D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC9B,MAAM,MAAM,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE;IACnB,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,MAAM,MAAM,EAAE;YACjB,UAAU,OAAO,CAAC,MAAM,GAAG,KAAK,KAAK,GAAG,KAAK,MAAM;QACrD;IACF,GAAG;QAAC;QAAM;KAAM;IAChB,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,UAAU,OAAO,CAAC,sBAAsB;IAC1C;IACA,IAAI,QAAQ;IACZ,IAAI,YAAY;IAChB,MAAM,aAAa,WAAW;IAC9B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA;QACP,IAAI,cAAc,CAAC,WAAW,YAAY,QAAQ,MAAM,GAAG;YACzD,SAAS,OAAO,CAAC,OAAO,GAAG;YAC3B,MAAM,EAAE,CAAC,eAAe,CAAC;YACzB,YAAY,MAAM,KAAK,CAAC,UAAU;YAClC,IAAI,QAAQ,MAAM,KAAK,CAAC,UAAU,GAAG;YACrC,MAAM,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,EAAE,UAAU,OAAO;YAC9C,MAAM,KAAK,CAAC,UAAU,GAAG;YACzB,MAAM,EAAE,CAAC,eAAe,CAAC;YACzB,SAAS,OAAO,CAAC,OAAO,GAAG;YAC3B;QACF;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,aAAa;YACf,MAAM,SAAS;YACf,IAAI,IAAM,CAAC;oBACT,QAAQ,UAAU,OAAO;gBAC3B,CAAC;YACD,OAAO,IAAM,IAAI,IAAM,CAAC;wBACtB,QAAQ;oBACV,CAAC;QACH;IACA,4GAA4G;IAC5G,wEAAwE;IAC1E,GAAG;QAAC;QAAW;QAAa;KAAI;IAChC,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qBAAqB,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;QAC3H,KAAK;IACP,GAAG,QAAQ,CAAC,cAAc,WAAW,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC7E,KAAK;IACP,GAAG,cAAc,SAAS,IAAI,OAAO;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/helpers/useEffectfulState.js"], "sourcesContent": ["import * as React from 'react';\n\nfunction call(ref, value) {\n  if (typeof ref === 'function') ref(value);else if (ref != null) ref.current = value;\n}\nfunction useEffectfulState(fn, deps = [], cb) {\n  const [state, set] = React.useState();\n  React.useLayoutEffect(() => {\n    const value = fn();\n    set(value);\n    call(cb, value);\n    return () => call(cb, null);\n  }, deps);\n  return state;\n}\n\nexport { useEffectfulState };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,KAAK,GAAG,EAAE,KAAK;IACtB,IAAI,OAAO,QAAQ,YAAY,IAAI;SAAY,IAAI,OAAO,MAAM,IAAI,OAAO,GAAG;AAChF;AACA,SAAS,kBAAkB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;IAC1C,MAAM,CAAC,OAAO,IAAI,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD;IAClC,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,MAAM,QAAQ;QACd,IAAI;QACJ,KAAK,IAAI;QACT,OAAO,IAAM,KAAK,IAAI;IACxB,GAAG;IACH,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/Stats.js"], "sourcesContent": ["import * as React from 'react';\nimport { addEffect, addAfterEffect } from '@react-three/fiber';\nimport StatsImpl from 'stats.js';\nimport { useEffectfulState } from '../helpers/useEffectfulState.js';\n\nfunction Stats({\n  showPanel = 0,\n  className,\n  parent\n}) {\n  const stats = useEffectfulState(() => new StatsImpl(), []);\n  React.useEffect(() => {\n    if (stats) {\n      const node = parent && parent.current || document.body;\n      stats.showPanel(showPanel);\n      node == null || node.appendChild(stats.dom);\n      const classNames = (className !== null && className !== void 0 ? className : '').split(' ').filter(cls => cls);\n      if (classNames.length) stats.dom.classList.add(...classNames);\n      const begin = addEffect(() => stats.begin());\n      const end = addAfterEffect(() => stats.end());\n      return () => {\n        if (classNames.length) stats.dom.classList.remove(...classNames);\n        node == null || node.removeChild(stats.dom);\n        begin();\n        end();\n      };\n    }\n  }, [parent, stats, className, showPanel]);\n  return null;\n}\n\nexport { Stats };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,SAAS,MAAM,EACb,YAAY,CAAC,EACb,SAAS,EACT,MAAM,EACP;IACC,MAAM,QAAQ,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM,IAAI,oJAAA,CAAA,UAAS,IAAI,EAAE;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,OAAO;YACT,MAAM,OAAO,UAAU,OAAO,OAAO,IAAI,SAAS,IAAI;YACtD,MAAM,SAAS,CAAC;YAChB,QAAQ,QAAQ,KAAK,WAAW,CAAC,MAAM,GAAG;YAC1C,MAAM,aAAa,CAAC,cAAc,QAAQ,cAAc,KAAK,IAAI,YAAY,EAAE,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,MAAO;YAC1G,IAAI,WAAW,MAAM,EAAE,MAAM,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI;YAClD,MAAM,QAAQ,CAAA,GAAA,gNAAA,CAAA,YAAS,AAAD,EAAE,IAAM,MAAM,KAAK;YACzC,MAAM,MAAM,CAAA,GAAA,qNAAA,CAAA,iBAAc,AAAD,EAAE,IAAM,MAAM,GAAG;YAC1C,OAAO;gBACL,IAAI,WAAW,MAAM,EAAE,MAAM,GAAG,CAAC,SAAS,CAAC,MAAM,IAAI;gBACrD,QAAQ,QAAQ,KAAK,WAAW,CAAC,MAAM,GAAG;gBAC1C;gBACA;YACF;QACF;IACF,GAAG;QAAC;QAAQ;QAAO;QAAW;KAAU;IACxC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/AdaptiveDpr.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction AdaptiveDpr({\n  pixelated\n}) {\n  const gl = useThree(state => state.gl);\n  const active = useThree(state => state.internal.active);\n  const current = useThree(state => state.performance.current);\n  const initialDpr = useThree(state => state.viewport.initialDpr);\n  const setDpr = useThree(state => state.setDpr);\n  // Restore initial pixelratio on unmount\n  React.useEffect(() => {\n    const domElement = gl.domElement;\n    return () => {\n      if (active) setDpr(initialDpr);\n      if (pixelated && domElement) domElement.style.imageRendering = 'auto';\n    };\n  }, []);\n  // Set adaptive pixelratio\n  React.useEffect(() => {\n    setDpr(current * initialDpr);\n    if (pixelated && gl.domElement) gl.domElement.style.imageRendering = current === 1 ? 'auto' : 'pixelated';\n  }, [current]);\n  return null;\n}\n\nexport { AdaptiveDpr };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,YAAY,EACnB,SAAS,EACV;IACC,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,QAAQ,CAAC,MAAM;IACtD,MAAM,UAAU,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,WAAW,CAAC,OAAO;IAC3D,MAAM,aAAa,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,QAAQ,CAAC,UAAU;IAC9D,MAAM,SAAS,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,MAAM;IAC7C,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,aAAa,GAAG,UAAU;QAChC,OAAO;YACL,IAAI,QAAQ,OAAO;YACnB,IAAI,aAAa,YAAY,WAAW,KAAK,CAAC,cAAc,GAAG;QACjE;IACF,GAAG,EAAE;IACL,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO,UAAU;QACjB,IAAI,aAAa,GAAG,UAAU,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,YAAY,IAAI,SAAS;IAChG,GAAG;QAAC;KAAQ;IACZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/AdaptiveEvents.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction AdaptiveEvents() {\n  const get = useThree(state => state.get);\n  const setEvents = useThree(state => state.setEvents);\n  const current = useThree(state => state.performance.current);\n  React.useEffect(() => {\n    const enabled = get().events.enabled;\n    return () => setEvents({\n      enabled\n    });\n  }, []);\n  React.useEffect(() => setEvents({\n    enabled: current === 1\n  }), [current]);\n  return null;\n}\n\nexport { AdaptiveEvents };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS;IACP,MAAM,MAAM,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,GAAG;IACvC,MAAM,YAAY,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,SAAS;IACnD,MAAM,UAAU,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,WAAW,CAAC,OAAO;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,UAAU,MAAM,MAAM,CAAC,OAAO;QACpC,OAAO,IAAM,UAAU;gBACrB;YACF;IACF,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE,IAAM,UAAU;YAC9B,SAAS,YAAY;QACvB,IAAI;QAAC;KAAQ;IACb,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/BakeShadows.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction BakeShadows() {\n  const gl = useThree(state => state.gl);\n  useEffect(() => {\n    gl.shadowMap.autoUpdate = false;\n    gl.shadowMap.needsUpdate = true;\n    return () => {\n      gl.shadowMap.autoUpdate = gl.shadowMap.needsUpdate = true;\n    };\n  }, [gl.shadowMap]);\n  return null;\n}\n\nexport { BakeShadows };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS;IACP,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,EAAE;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,GAAG,SAAS,CAAC,UAAU,GAAG;QAC1B,GAAG,SAAS,CAAC,WAAW,GAAG;QAC3B,OAAO;YACL,GAAG,SAAS,CAAC,UAAU,GAAG,GAAG,SAAS,CAAC,WAAW,GAAG;QACvD;IACF,GAAG;QAAC,GAAG,SAAS;KAAC;IACjB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/Preload.js"], "sourcesContent": ["import { WebGLCubeRenderTarget, CubeCamera } from 'three';\nimport * as React from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction Preload({\n  all,\n  scene,\n  camera\n}) {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const dCamera = useThree(({\n    camera\n  }) => camera);\n  const dScene = useThree(({\n    scene\n  }) => scene);\n\n  // Layout effect because it must run before React commits\n  React.useLayoutEffect(() => {\n    const invisible = [];\n    if (all) {\n      (scene || dScene).traverse(object => {\n        if (object.visible === false) {\n          invisible.push(object);\n          object.visible = true;\n        }\n      });\n    }\n    // Now compile the scene\n    gl.compile(scene || dScene, camera || dCamera);\n    // And for good measure, hit it with a cube camera\n    const cubeRenderTarget = new WebGLCubeRenderTarget(128);\n    const cubeCamera = new CubeCamera(0.01, 100000, cubeRenderTarget);\n    cubeCamera.update(gl, scene || dScene);\n    cubeRenderTarget.dispose();\n    // Flips these objects back\n    invisible.forEach(object => object.visible = false);\n  }, []);\n  return null;\n}\n\nexport { Preload };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,QAAQ,EACf,GAAG,EACH,KAAK,EACL,MAAM,EACP;IACC,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EACnB,EAAE,EACH,GAAK;IACN,MAAM,UAAU,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EACxB,MAAM,EACP,GAAK;IACN,MAAM,SAAS,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EACvB,KAAK,EACN,GAAK;IAEN,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,MAAM,YAAY,EAAE;QACpB,IAAI,KAAK;YACP,CAAC,SAAS,MAAM,EAAE,QAAQ,CAAC,CAAA;gBACzB,IAAI,OAAO,OAAO,KAAK,OAAO;oBAC5B,UAAU,IAAI,CAAC;oBACf,OAAO,OAAO,GAAG;gBACnB;YACF;QACF;QACA,wBAAwB;QACxB,GAAG,OAAO,CAAC,SAAS,QAAQ,UAAU;QACtC,kDAAkD;QAClD,MAAM,mBAAmB,IAAI,+IAAA,CAAA,wBAAqB,CAAC;QACnD,MAAM,aAAa,IAAI,+IAAA,CAAA,aAAU,CAAC,MAAM,QAAQ;QAChD,WAAW,MAAM,CAAC,IAAI,SAAS;QAC/B,iBAAiB,OAAO;QACxB,2BAA2B;QAC3B,UAAU,OAAO,CAAC,CAAA,SAAU,OAAO,OAAO,GAAG;IAC/C,GAAG,EAAE;IACL,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/shapes.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\n\nfunction create(type, effect) {\n  const El = type + 'Geometry';\n  return /*#__PURE__*/React.forwardRef(({\n    args,\n    children,\n    ...props\n  }, fref) => {\n    const ref = React.useRef(null);\n    React.useImperativeHandle(fref, () => ref.current);\n    React.useLayoutEffect(() => void (effect == null ? void 0 : effect(ref.current)));\n    return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      ref: ref\n    }, props), /*#__PURE__*/React.createElement(El, {\n      attach: \"geometry\",\n      args: args\n    }), children);\n  });\n}\nconst Box = /* @__PURE__ */create('box');\nconst Circle = /* @__PURE__ */create('circle');\nconst Cone = /* @__PURE__ */create('cone');\nconst Cylinder = /* @__PURE__ */create('cylinder');\nconst Sphere = /* @__PURE__ */create('sphere');\nconst Plane = /* @__PURE__ */create('plane');\nconst Tube = /* @__PURE__ */create('tube');\nconst Torus = /* @__PURE__ */create('torus');\nconst TorusKnot = /* @__PURE__ */create('torusKnot');\nconst Tetrahedron = /* @__PURE__ */create('tetrahedron');\nconst Ring = /* @__PURE__ */create('ring');\nconst Polyhedron = /* @__PURE__ */create('polyhedron');\nconst Icosahedron = /* @__PURE__ */create('icosahedron');\nconst Octahedron = /* @__PURE__ */create('octahedron');\nconst Dodecahedron = /* @__PURE__ */create('dodecahedron');\nconst Extrude = /* @__PURE__ */create('extrude');\nconst Lathe = /* @__PURE__ */create('lathe');\nconst Capsule = /* @__PURE__ */create('capsule');\nconst Shape = /* @__PURE__ */create('shape', ({\n  geometry\n}) => {\n  // Calculate UVs (by https://discourse.threejs.org/u/prisoner849)\n  // https://discourse.threejs.org/t/custom-shape-in-image-not-working/49348/10\n  const pos = geometry.attributes.position;\n  const b3 = new THREE.Box3().setFromBufferAttribute(pos);\n  const b3size = new THREE.Vector3();\n  b3.getSize(b3size);\n  const uv = [];\n  let x = 0,\n    y = 0,\n    u = 0,\n    v = 0;\n  for (let i = 0; i < pos.count; i++) {\n    x = pos.getX(i);\n    y = pos.getY(i);\n    u = (x - b3.min.x) / b3size.x;\n    v = (y - b3.min.y) / b3size.y;\n    uv.push(u, v);\n  }\n  geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uv, 2));\n});\n\nexport { Box, Capsule, Circle, Cone, Cylinder, Dodecahedron, Extrude, Icosahedron, Lathe, Octahedron, Plane, Polyhedron, Ring, Shape, Sphere, Tetrahedron, Torus, TorusKnot, Tube };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,SAAS,OAAO,IAAI,EAAE,MAAM;IAC1B,MAAM,KAAK,OAAO;IAClB,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACpC,IAAI,EACJ,QAAQ,EACR,GAAG,OACJ,EAAE;QACD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;QACzB,CAAA,GAAA,qMAAA,CAAA,sBAAyB,AAAD,EAAE,MAAM,IAAM,IAAI,OAAO;QACjD,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE,IAAM,KAAK,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,IAAI,OAAO,CAAC;QAC/E,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;YACvD,KAAK;QACP,GAAG,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,IAAI;YAC9C,QAAQ;YACR,MAAM;QACR,IAAI;IACN;AACF;AACA,MAAM,MAAM,aAAa,GAAE,OAAO;AAClC,MAAM,SAAS,aAAa,GAAE,OAAO;AACrC,MAAM,OAAO,aAAa,GAAE,OAAO;AACnC,MAAM,WAAW,aAAa,GAAE,OAAO;AACvC,MAAM,SAAS,aAAa,GAAE,OAAO;AACrC,MAAM,QAAQ,aAAa,GAAE,OAAO;AACpC,MAAM,OAAO,aAAa,GAAE,OAAO;AACnC,MAAM,QAAQ,aAAa,GAAE,OAAO;AACpC,MAAM,YAAY,aAAa,GAAE,OAAO;AACxC,MAAM,cAAc,aAAa,GAAE,OAAO;AAC1C,MAAM,OAAO,aAAa,GAAE,OAAO;AACnC,MAAM,aAAa,aAAa,GAAE,OAAO;AACzC,MAAM,cAAc,aAAa,GAAE,OAAO;AAC1C,MAAM,aAAa,aAAa,GAAE,OAAO;AACzC,MAAM,eAAe,aAAa,GAAE,OAAO;AAC3C,MAAM,UAAU,aAAa,GAAE,OAAO;AACtC,MAAM,QAAQ,aAAa,GAAE,OAAO;AACpC,MAAM,UAAU,aAAa,GAAE,OAAO;AACtC,MAAM,QAAQ,aAAa,GAAE,OAAO,SAAS,CAAC,EAC5C,QAAQ,EACT;IACC,iEAAiE;IACjE,6EAA6E;IAC7E,MAAM,MAAM,SAAS,UAAU,CAAC,QAAQ;IACxC,MAAM,KAAK,IAAI,+IAAA,CAAA,OAAU,GAAG,sBAAsB,CAAC;IACnD,MAAM,SAAS,IAAI,+IAAA,CAAA,UAAa;IAChC,GAAG,OAAO,CAAC;IACX,MAAM,KAAK,EAAE;IACb,IAAI,IAAI,GACN,IAAI,GACJ,IAAI,GACJ,IAAI;IACN,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,EAAE,IAAK;QAClC,IAAI,IAAI,IAAI,CAAC;QACb,IAAI,IAAI,IAAI,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC;QAC7B,GAAG,IAAI,CAAC,GAAG;IACb;IACA,SAAS,YAAY,CAAC,MAAM,IAAI,+IAAA,CAAA,yBAA4B,CAAC,IAAI;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/RoundedBox.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Shape } from 'three';\nimport { toCreasedNormals } from 'three-stdlib';\n\nconst eps = 0.00001;\nfunction createShape(width, height, radius0) {\n  const shape = new Shape();\n  const radius = radius0 - eps;\n  shape.absarc(eps, eps, eps, -Math.PI / 2, -Math.PI, true);\n  shape.absarc(eps, height - radius * 2, eps, Math.PI, Math.PI / 2, true);\n  shape.absarc(width - radius * 2, height - radius * 2, eps, Math.PI / 2, 0, true);\n  shape.absarc(width - radius * 2, eps, eps, 0, -Math.PI / 2, true);\n  return shape;\n}\nconst RoundedBox = /* @__PURE__ */React.forwardRef(function RoundedBox({\n  args: [width = 1, height = 1, depth = 1] = [],\n  radius = 0.05,\n  steps = 1,\n  smoothness = 4,\n  bevelSegments = 4,\n  creaseAngle = 0.4,\n  children,\n  ...rest\n}, ref) {\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(RoundedBoxGeometry, {\n    args: [width, height, depth],\n    radius: radius,\n    steps: steps,\n    smoothness: smoothness,\n    bevelSegments: bevelSegments,\n    creaseAngle: creaseAngle\n  }), children);\n});\nconst RoundedBoxGeometry = /* @__PURE__ */React.forwardRef(function RoundedBoxGeometry({\n  args: [width = 1, height = 1, depth = 1] = [],\n  radius = 0.05,\n  steps = 1,\n  smoothness = 4,\n  bevelSegments = 4,\n  creaseAngle = 0.4,\n  ...rest\n}, ref) {\n  const shape = React.useMemo(() => createShape(width, height, radius), [width, height, radius]);\n  const params = React.useMemo(() => ({\n    depth: depth - radius * 2,\n    bevelEnabled: true,\n    bevelSegments: bevelSegments * 2,\n    steps,\n    bevelSize: radius - eps,\n    bevelThickness: radius,\n    curveSegments: smoothness\n  }), [depth, radius, smoothness, bevelSegments, steps]);\n  const geomRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (geomRef.current) {\n      geomRef.current.center();\n      toCreasedNormals(geomRef.current, creaseAngle);\n    }\n  }, [shape, params, creaseAngle]);\n  React.useImperativeHandle(ref, () => geomRef.current);\n  return /*#__PURE__*/React.createElement(\"extrudeGeometry\", _extends({\n    ref: geomRef,\n    args: [shape, params]\n  }, rest));\n});\n\nexport { RoundedBox, RoundedBoxGeometry };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,MAAM;AACZ,SAAS,YAAY,KAAK,EAAE,MAAM,EAAE,OAAO;IACzC,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAK;IACvB,MAAM,SAAS,UAAU;IACzB,MAAM,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE;IACpD,MAAM,MAAM,CAAC,KAAK,SAAS,SAAS,GAAG,KAAK,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,GAAG;IAClE,MAAM,MAAM,CAAC,QAAQ,SAAS,GAAG,SAAS,SAAS,GAAG,KAAK,KAAK,EAAE,GAAG,GAAG,GAAG;IAC3E,MAAM,MAAM,CAAC,QAAQ,SAAS,GAAG,KAAK,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG;IAC5D,OAAO;AACT;AACA,MAAM,aAAa,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,WAAW,EACrE,MAAM,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,EAAE,EAC7C,SAAS,IAAI,EACb,QAAQ,CAAC,EACT,aAAa,CAAC,EACd,gBAAgB,CAAC,EACjB,cAAc,GAAG,EACjB,QAAQ,EACR,GAAG,MACJ,EAAE,GAAG;IACJ,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;QACvD,KAAK;IACP,GAAG,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,oBAAoB;QAC7D,MAAM;YAAC;YAAO;YAAQ;SAAM;QAC5B,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,eAAe;QACf,aAAa;IACf,IAAI;AACN;AACA,MAAM,qBAAqB,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,mBAAmB,EACrF,MAAM,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,EAAE,EAC7C,SAAS,IAAI,EACb,QAAQ,CAAC,EACT,aAAa,CAAC,EACd,gBAAgB,CAAC,EACjB,cAAc,GAAG,EACjB,GAAG,MACJ,EAAE,GAAG;IACJ,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,YAAY,OAAO,QAAQ,SAAS;QAAC;QAAO;QAAQ;KAAO;IAC7F,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAC;YAClC,OAAO,QAAQ,SAAS;YACxB,cAAc;YACd,eAAe,gBAAgB;YAC/B;YACA,WAAW,SAAS;YACpB,gBAAgB;YAChB,eAAe;QACjB,CAAC,GAAG;QAAC;QAAO;QAAQ;QAAY;QAAe;KAAM;IACrD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,MAAM;YACtB,CAAA,GAAA,+JAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,OAAO,EAAE;QACpC;IACF,GAAG;QAAC;QAAO;QAAQ;KAAY;IAC/B,CAAA,GAAA,qMAAA,CAAA,sBAAyB,AAAD,EAAE,KAAK,IAAM,QAAQ,OAAO;IACpD,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;QAClE,KAAK;QACL,MAAM;YAAC;YAAO;SAAO;IACvB,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/Text.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Text as Text$1, preloadFont } from 'troika-three-text';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\n\nconst Text = /* @__PURE__ */React.forwardRef(({\n  sdfGlyphSize = 64,\n  anchorX = 'center',\n  anchorY = 'middle',\n  font,\n  fontSize = 1,\n  children,\n  characters,\n  onSync,\n  ...props\n}, ref) => {\n  const invalidate = useThree(({\n    invalidate\n  }) => invalidate);\n  const [troikaMesh] = React.useState(() => new Text$1());\n  const [nodes, text] = React.useMemo(() => {\n    const n = [];\n    let t = '';\n    React.Children.forEach(children, child => {\n      if (typeof child === 'string' || typeof child === 'number') {\n        t += child;\n      } else {\n        n.push(child);\n      }\n    });\n    return [n, t];\n  }, [children]);\n  suspend(() => new Promise(res => preloadFont({\n    font,\n    characters\n  }, res)), ['troika-text', font, characters]);\n  React.useLayoutEffect(() => void troikaMesh.sync(() => {\n    invalidate();\n    if (onSync) onSync(troikaMesh);\n  }));\n  React.useEffect(() => {\n    return () => troikaMesh.dispose();\n  }, [troikaMesh]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: troikaMesh,\n    ref: ref,\n    font: font,\n    text: text,\n    anchorX: anchorX,\n    anchorY: anchorY,\n    fontSize: fontSize,\n    sdfGlyphSize: sdfGlyphSize\n  }, props), nodes);\n});\n\nexport { Text };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,OAAO,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC5C,eAAe,EAAE,EACjB,UAAU,QAAQ,EAClB,UAAU,QAAQ,EAClB,IAAI,EACJ,WAAW,CAAC,EACZ,QAAQ,EACR,UAAU,EACV,MAAM,EACN,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAC3B,UAAU,EACX,GAAK;IACN,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,IAAM,IAAI,iLAAA,CAAA,OAAM;IACpD,MAAM,CAAC,OAAO,KAAK,GAAG,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAClC,MAAM,IAAI,EAAE;QACZ,IAAI,IAAI;QACR,qMAAA,CAAA,WAAc,CAAC,OAAO,CAAC,UAAU,CAAA;YAC/B,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;gBAC1D,KAAK;YACP,OAAO;gBACL,EAAE,IAAI,CAAC;YACT;QACF;QACA,OAAO;YAAC;YAAG;SAAE;IACf,GAAG;QAAC;KAAS;IACb,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,QAAQ,CAAA,MAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;gBAC3C;gBACA;YACF,GAAG,OAAO;QAAC;QAAe;QAAM;KAAW;IAC3C,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE,IAAM,KAAK,WAAW,IAAI,CAAC;YAC/C;YACA,IAAI,QAAQ,OAAO;QACrB;IACA,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO,IAAM,WAAW,OAAO;IACjC,GAAG;QAAC;KAAW;IACf,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,QAAQ;QACR,KAAK;QACL,MAAM;QACN,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,cAAc;IAChB,GAAG,QAAQ;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/Clone.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { SkeletonUtils } from 'three-stdlib';\n\nfunction createSpread(child, {\n  keys = ['near', 'far', 'color', 'distance', 'decay', 'penumbra', 'angle', 'intensity', 'skeleton', 'visible', 'castShadow', 'receiveShadow', 'morphTargetDictionary', 'morphTargetInfluences', 'name', 'geometry', 'material', 'position', 'rotation', 'scale', 'up', 'userData', 'bindMode', 'bindMatrix', 'bindMatrixInverse', 'skeleton'],\n  deep,\n  inject,\n  castShadow,\n  receiveShadow\n}) {\n  let spread = {};\n  for (const key of keys) {\n    spread[key] = child[key];\n  }\n  if (deep) {\n    if (spread.geometry && deep !== 'materialsOnly') spread.geometry = spread.geometry.clone();\n    if (spread.material && deep !== 'geometriesOnly') spread.material = spread.material.clone();\n  }\n  if (inject) {\n    if (typeof inject === 'function') spread = {\n      ...spread,\n      children: inject(child)\n    };else if (/*#__PURE__*/React.isValidElement(inject)) spread = {\n      ...spread,\n      children: inject\n    };else spread = {\n      ...spread,\n      ...inject\n    };\n  }\n  if (child instanceof THREE.Mesh) {\n    if (castShadow) spread.castShadow = true;\n    if (receiveShadow) spread.receiveShadow = true;\n  }\n  return spread;\n}\nconst Clone = /* @__PURE__ */React.forwardRef(({\n  isChild = false,\n  object,\n  children,\n  deep,\n  castShadow,\n  receiveShadow,\n  inject,\n  keys,\n  ...props\n}, forwardRef) => {\n  const config = {\n    keys,\n    deep,\n    inject,\n    castShadow,\n    receiveShadow\n  };\n  object = React.useMemo(() => {\n    if (isChild === false && !Array.isArray(object)) {\n      let isSkinned = false;\n      object.traverse(object => {\n        if (object.isSkinnedMesh) isSkinned = true;\n      });\n      if (isSkinned) return SkeletonUtils.clone(object);\n    }\n    return object;\n  }, [object, isChild]);\n\n  // Deal with arrayed clones\n  if (Array.isArray(object)) {\n    return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n      ref: forwardRef\n    }), object.map(o => /*#__PURE__*/React.createElement(Clone, _extends({\n      key: o.uuid,\n      object: o\n    }, config))), children);\n  }\n\n  // Singleton clones\n  const {\n    children: injectChildren,\n    ...spread\n  } = createSpread(object, config);\n  const Element = object.type[0].toLowerCase() + object.type.slice(1);\n  return /*#__PURE__*/React.createElement(Element, _extends({}, spread, props, {\n    ref: forwardRef\n  }), object.children.map(child => {\n    if (child.type === 'Bone') return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n      key: child.uuid,\n      object: child\n    }, config));\n    return /*#__PURE__*/React.createElement(Clone, _extends({\n      key: child.uuid,\n      object: child\n    }, config, {\n      isChild: true\n    }));\n  }), children, injectChildren);\n});\n\nexport { Clone };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,SAAS,aAAa,KAAK,EAAE,EAC3B,OAAO;IAAC;IAAQ;IAAO;IAAS;IAAY;IAAS;IAAY;IAAS;IAAa;IAAY;IAAW;IAAc;IAAiB;IAAyB;IAAyB;IAAQ;IAAY;IAAY;IAAY;IAAY;IAAS;IAAM;IAAY;IAAY;IAAc;IAAqB;CAAW,EAC5U,IAAI,EACJ,MAAM,EACN,UAAU,EACV,aAAa,EACd;IACC,IAAI,SAAS,CAAC;IACd,KAAK,MAAM,OAAO,KAAM;QACtB,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;IAC1B;IACA,IAAI,MAAM;QACR,IAAI,OAAO,QAAQ,IAAI,SAAS,iBAAiB,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,KAAK;QACxF,IAAI,OAAO,QAAQ,IAAI,SAAS,kBAAkB,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,KAAK;IAC3F;IACA,IAAI,QAAQ;QACV,IAAI,OAAO,WAAW,YAAY,SAAS;YACzC,GAAG,MAAM;YACT,UAAU,OAAO;QACnB;aAAO,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,iBAAoB,AAAD,EAAE,SAAS,SAAS;YAC7D,GAAG,MAAM;YACT,UAAU;QACZ;aAAO,SAAS;YACd,GAAG,MAAM;YACT,GAAG,MAAM;QACX;IACF;IACA,IAAI,iBAAiB,+IAAA,CAAA,OAAU,EAAE;QAC/B,IAAI,YAAY,OAAO,UAAU,GAAG;QACpC,IAAI,eAAe,OAAO,aAAa,GAAG;IAC5C;IACA,OAAO;AACT;AACA,MAAM,QAAQ,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC7C,UAAU,KAAK,EACf,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,aAAa,EACb,MAAM,EACN,IAAI,EACJ,GAAG,OACJ,EAAE;IACD,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;IACF;IACA,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACrB,IAAI,YAAY,SAAS,CAAC,MAAM,OAAO,CAAC,SAAS;YAC/C,IAAI,YAAY;YAChB,OAAO,QAAQ,CAAC,CAAA;gBACd,IAAI,OAAO,aAAa,EAAE,YAAY;YACxC;YACA,IAAI,WAAW,OAAO,yJAAA,CAAA,gBAAa,CAAC,KAAK,CAAC;QAC5C;QACA,OAAO;IACT,GAAG;QAAC;QAAQ;KAAQ;IAEpB,2BAA2B;IAC3B,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;YACnE,KAAK;QACP,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;gBACnE,KAAK,EAAE,IAAI;gBACX,QAAQ;YACV,GAAG,WAAW;IAChB;IAEA,mBAAmB;IACnB,MAAM,EACJ,UAAU,cAAc,EACxB,GAAG,QACJ,GAAG,aAAa,QAAQ;IACzB,MAAM,UAAU,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC;IACjE,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,QAAQ,OAAO;QAC3E,KAAK;IACP,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA;QACtB,IAAI,MAAM,IAAI,KAAK,QAAQ,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;YACvF,KAAK,MAAM,IAAI;YACf,QAAQ;QACV,GAAG;QACH,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;YACtD,KAAK,MAAM,IAAI;YACf,QAAQ;QACV,GAAG,QAAQ;YACT,SAAS;QACX;IACF,IAAI,UAAU;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/core/Gltf.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { GLTFLoader, DRACOLoader, MeshoptDecoder } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\nimport { Clone } from './Clone.js';\n\nlet dracoLoader = null;\nlet decoderPath = 'https://www.gstatic.com/draco/versioned/decoders/1.5.5/';\nfunction extensions(useDraco = true, useMeshopt = true, extendLoader) {\n  return loader => {\n    if (extendLoader) {\n      extendLoader(loader);\n    }\n    if (useDraco) {\n      if (!dracoLoader) {\n        dracoLoader = new DRACOLoader();\n      }\n      dracoLoader.setDecoderPath(typeof useDraco === 'string' ? useDraco : decoderPath);\n      loader.setDRACOLoader(dracoLoader);\n    }\n    if (useMeshopt) {\n      loader.setMeshoptDecoder(typeof MeshoptDecoder === 'function' ? MeshoptDecoder() : MeshoptDecoder);\n    }\n  };\n}\nconst useGLTF = (path, useDraco, useMeshopt, extendLoader) => useLoader(GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.preload = (path, useDraco, useMeshopt, extendLoader) => useLoader.preload(GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.clear = path => useLoader.clear(GLTFLoader, path);\nuseGLTF.setDecoderPath = path => {\n  decoderPath = path;\n};\n\n//\n\nconst Gltf = /* @__PURE__ */React.forwardRef(({\n  src,\n  useDraco,\n  useMeshOpt,\n  extendLoader,\n  ...props\n}, ref) => {\n  const {\n    scene\n  } = useGLTF(src, useDraco, useMeshOpt, extendLoader);\n  return /*#__PURE__*/React.createElement(Clone, _extends({\n    ref: ref\n  }, props, {\n    object: scene\n  }));\n});\n\nexport { Gltf, useGLTF };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAEA,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,SAAS,WAAW,WAAW,IAAI,EAAE,aAAa,IAAI,EAAE,YAAY;IAClE,OAAO,CAAA;QACL,IAAI,cAAc;YAChB,aAAa;QACf;QACA,IAAI,UAAU;YACZ,IAAI,CAAC,aAAa;gBAChB,cAAc,IAAI,yJAAA,CAAA,cAAW;YAC/B;YACA,YAAY,cAAc,CAAC,OAAO,aAAa,WAAW,WAAW;YACrE,OAAO,cAAc,CAAC;QACxB;QACA,IAAI,YAAY;YACd,OAAO,iBAAiB,CAAC,OAAO,yJAAA,CAAA,iBAAc,KAAK,aAAa,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,MAAM,yJAAA,CAAA,iBAAc;QACnG;IACF;AACF;AACA,MAAM,UAAU,CAAC,MAAM,UAAU,YAAY,eAAiB,CAAA,GAAA,gNAAA,CAAA,YAAS,AAAD,EAAE,wJAAA,CAAA,aAAU,EAAE,MAAM,WAAW,UAAU,YAAY;AAC3H,QAAQ,OAAO,GAAG,CAAC,MAAM,UAAU,YAAY,eAAiB,gNAAA,CAAA,YAAS,CAAC,OAAO,CAAC,wJAAA,CAAA,aAAU,EAAE,MAAM,WAAW,UAAU,YAAY;AACrI,QAAQ,KAAK,GAAG,CAAA,OAAQ,gNAAA,CAAA,YAAS,CAAC,KAAK,CAAC,wJAAA,CAAA,aAAU,EAAE;AACpD,QAAQ,cAAc,GAAG,CAAA;IACvB,cAAc;AAChB;AAEA,EAAE;AAEF,MAAM,OAAO,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC5C,GAAG,EACH,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,GAAG,OACJ,EAAE;IACD,MAAM,EACJ,KAAK,EACN,GAAG,QAAQ,KAAK,UAAU,YAAY;IACvC,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,yJAAA,CAAA,QAAK,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;QACtD,KAAK;IACP,GAAG,OAAO;QACR,QAAQ;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/%40react-three/drei/web/Html.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera, Vector2 } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nconst v1 = /* @__PURE__ */new Vector3();\nconst v2 = /* @__PURE__ */new Vector3();\nconst v3 = /* @__PURE__ */new Vector3();\nconst v4 = /* @__PURE__ */new Vector2();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  v4.set(screenPos.x, screenPos.y);\n  raycaster.setFromCamera(v4, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /* @__PURE__ */React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef(null);\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null);\n  // Append to the connected element, which makes HTML work with views\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null || _root$current.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null || _root$current2.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          } else if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */`\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */`\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\n\nexport { Html };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,KAAK,aAAa,GAAE,IAAI,+IAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,+IAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,+IAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,+IAAA,CAAA,UAAO;AACrC,SAAS,yBAAyB,EAAE,EAAE,MAAM,EAAE,IAAI;IAChD,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,UAAU,OAAO,CAAC;IAClB,MAAM,YAAY,KAAK,KAAK,GAAG;IAC/B,MAAM,aAAa,KAAK,MAAM,GAAG;IACjC,OAAO;QAAC,UAAU,CAAC,GAAG,YAAY;QAAW,CAAC,CAAC,UAAU,CAAC,GAAG,UAAU,IAAI;KAAW;AACxF;AACA,SAAS,qBAAqB,EAAE,EAAE,MAAM;IACtC,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;IAC7D,MAAM,cAAc,UAAU,GAAG,CAAC;IAClC,MAAM,SAAS,OAAO,iBAAiB,CAAC;IACxC,OAAO,YAAY,OAAO,CAAC,UAAU,KAAK,EAAE,GAAG;AACjD;AACA,SAAS,gBAAgB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO;IACrD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACrD,MAAM,YAAY,MAAM,KAAK;IAC7B,UAAU,OAAO,CAAC;IAClB,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;IAC/B,UAAU,aAAa,CAAC,IAAI;IAC5B,MAAM,aAAa,UAAU,gBAAgB,CAAC,SAAS;IACvD,IAAI,WAAW,MAAM,EAAE;QACrB,MAAM,uBAAuB,UAAU,CAAC,EAAE,CAAC,QAAQ;QACnD,MAAM,gBAAgB,MAAM,UAAU,CAAC,UAAU,GAAG,CAAC,MAAM;QAC3D,OAAO,gBAAgB;IACzB;IACA,OAAO;AACT;AACA,SAAS,YAAY,EAAE,EAAE,MAAM;IAC7B,IAAI,kBAAkB,+IAAA,CAAA,qBAAkB,EAAE;QACxC,OAAO,OAAO,IAAI;IACpB,OAAO,IAAI,kBAAkB,+IAAA,CAAA,oBAAiB,EAAE;QAC9C,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QACpC,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,WAAW,IAAI,KAAK,GAAG,CAAC,OAAO,KAAK;QAC1C,OAAO,IAAI;IACb,OAAO;QACL,OAAO;IACT;AACF;AACA,SAAS,aAAa,EAAE,EAAE,MAAM,EAAE,WAAW;IAC3C,IAAI,kBAAkB,+IAAA,CAAA,oBAAiB,IAAI,kBAAkB,+IAAA,CAAA,qBAAkB,EAAE;QAC/E,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,OAAO,IAAI;QACvE,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,IAAI,OAAO,GAAG;QACzC,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO;IAC/B;IACA,OAAO;AACT;AACA,MAAM,UAAU,CAAA,QAAS,KAAK,GAAG,CAAC,SAAS,QAAQ,IAAI;AACvD,SAAS,aAAa,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE;IACrD,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,IAAK;QAC7B,YAAY,QAAQ,WAAW,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK,MAAM,GAAG;IAClF;IACA,OAAO,UAAU;AACnB;AACA,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAA,SAAU,aAAa,QAAQ;AACxC,CAAC,EAAE;IAAC;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;CAAE;AACvD,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAC,QAAQ,SAAW,aAAa,QAAQ,iBAAiB,SAAS;AAC5E,CAAC,EAAE,CAAA,IAAK;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC;QAAG,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;AAChG,SAAS,YAAY,GAAG;IACtB,OAAO,OAAO,OAAO,QAAQ,YAAY,aAAa;AACxD;AACA,MAAM,OAAO,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC5C,QAAQ,EACR,MAAM,KAAK,EACX,KAAK,EACL,SAAS,EACT,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN,cAAc,EACd,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,UAAU,EACV,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,cAAc;IAAC;IAAU;CAAE,EAC3B,oBAAoB,wBAAwB,EAC5C,KAAK,KAAK,EACV,YAAY,EACZ,gBAAgB,MAAM,EACtB,GAAG,OACJ,EAAE;IACD,MAAM,EACJ,EAAE,EACF,MAAM,EACN,KAAK,EACL,IAAI,EACJ,SAAS,EACT,MAAM,EACN,QAAQ,EACT,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IACX,MAAM,CAAC,GAAG,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,IAAM,SAAS,aAAa,CAAC;IACzD,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC1B,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;QAAC;QAAG;KAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACvC,oEAAoE;IACpE,MAAM,SAAS,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,OAAO,SAAS,IAAI,GAAG,UAAU,CAAC,UAAU;IACzG,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACtC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACnC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACvC,OAAO,WAAW,YAAY,cAAc,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,IAAI,YAAY,OAAO,CAAC,EAAE;IAChH,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,MAAM,KAAK,GAAG,UAAU;QACxB,IAAI,WAAW,YAAY,YAAY;YACrC,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI;YACrD,GAAG,KAAK,CAAC,QAAQ,GAAG;YACpB,GAAG,KAAK,CAAC,aAAa,GAAG;QAC3B,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,GAAG;YAClB,GAAG,KAAK,CAAC,QAAQ,GAAG;YACpB,GAAG,KAAK,CAAC,aAAa,GAAG;QAC3B;IACF,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,cAAc,KAAK,OAAO,GAAG,CAAA,GAAA,kKAAA,CAAA,aAAmB,AAAD,EAAE;YACvD,MAAM,iBAAiB;YACvB,IAAI,WAAW;gBACb,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,mEAAmE,CAAC;YAC1F,OAAO;gBACL,MAAM,MAAM,kBAAkB,MAAM,OAAO,EAAE,QAAQ;gBACrD,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,qDAAqD,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;YAC5H;YACA,IAAI,QAAQ;gBACV,IAAI,SAAS,OAAO,OAAO,CAAC;qBAAS,OAAO,WAAW,CAAC;YAC1D;YACA,OAAO;gBACL,IAAI,QAAQ,OAAO,WAAW,CAAC;gBAC/B,YAAY,OAAO;YACrB;QACF;IACF,GAAG;QAAC;QAAQ;KAAU;IACtB,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,cAAc,GAAG,SAAS,GAAG;IACnC,GAAG;QAAC;KAAa;IACjB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC3B,IAAI,WAAW;YACb,OAAO;gBACL,UAAU;gBACV,KAAK;gBACL,MAAM;gBACN,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,gBAAgB;gBAChB,eAAe;YACjB;QACF,OAAO;YACL,OAAO;gBACL,UAAU;gBACV,WAAW,SAAS,6BAA6B;gBACjD,GAAI,cAAc;oBAChB,KAAK,CAAC,KAAK,MAAM,GAAG;oBACpB,MAAM,CAAC,KAAK,KAAK,GAAG;oBACpB,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;gBACrB,CAAC;gBACD,GAAG,KAAK;YACV;QACF;IACF,GAAG;QAAC;QAAO;QAAQ;QAAY;QAAM;KAAU;IAC/C,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAC;YAChD,UAAU;YACV;QACF,CAAC,GAAG;QAAC;KAAc;IACnB,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,cAAc,OAAO,GAAG;QACxB,IAAI,WAAW;YACb,IAAI;YACJ,CAAC,gBAAgB,KAAK,OAAO,KAAK,QAAQ,cAAc,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;gBACrG,KAAK;gBACL,OAAO;YACT,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;gBACzC,KAAK;gBACL,OAAO;YACT,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;gBACzC,KAAK;gBACL,WAAW;gBACX,OAAO;gBACP,UAAU;YACZ;QACF,OAAO;YACL,IAAI;YACJ,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;gBACvG,KAAK;gBACL,OAAO;gBACP,WAAW;gBACX,UAAU;YACZ;QACF;IACF;IACA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA;QACP,IAAI,MAAM,OAAO,EAAE;YACjB,OAAO,iBAAiB;YACxB,MAAM,OAAO,CAAC,iBAAiB,CAAC,MAAM;YACtC,MAAM,MAAM,YAAY,YAAY,OAAO,GAAG,kBAAkB,MAAM,OAAO,EAAE,QAAQ;YACvF,IAAI,aAAa,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,KAAK;gBACpK,MAAM,iBAAiB,qBAAqB,MAAM,OAAO,EAAE;gBAC3D,IAAI,iBAAiB;gBACrB,IAAI,oBAAoB;oBACtB,IAAI,MAAM,OAAO,CAAC,UAAU;wBAC1B,iBAAiB,QAAQ,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;oBACnD,OAAO,IAAI,YAAY,YAAY;wBACjC,iBAAiB;4BAAC;yBAAM;oBAC1B;gBACF;gBACA,MAAM,oBAAoB,QAAQ,OAAO;gBACzC,IAAI,gBAAgB;oBAClB,MAAM,YAAY,gBAAgB,MAAM,OAAO,EAAE,QAAQ,WAAW;oBACpE,QAAQ,OAAO,GAAG,aAAa,CAAC;gBAClC,OAAO;oBACL,QAAQ,OAAO,GAAG,CAAC;gBACrB;gBACA,IAAI,sBAAsB,QAAQ,OAAO,EAAE;oBACzC,IAAI,WAAW,UAAU,CAAC,QAAQ,OAAO;yBAAO,GAAG,KAAK,CAAC,OAAO,GAAG,QAAQ,OAAO,GAAG,UAAU;gBACjG;gBACA,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG;gBAC9C,MAAM,SAAS,UAAU,mBAAmB,EAAE;mBAC5C;oBAAC,WAAW,CAAC,EAAE;oBAAE;iBAAU,GAAG;oBAAC,YAAY;oBAAG;iBAAE,GAAG;gBACrD,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,aAAa,MAAM,OAAO,EAAE,QAAQ,SAAS;gBAClE,IAAI,WAAW;oBACb,MAAM,CAAC,WAAW,WAAW,GAAG;wBAAC,KAAK,KAAK,GAAG;wBAAG,KAAK,MAAM,GAAG;qBAAE;oBACjE,MAAM,MAAM,OAAO,gBAAgB,CAAC,QAAQ,CAAC,EAAE,GAAG;oBAClD,MAAM,EACJ,oBAAoB,EACpB,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;oBACJ,MAAM,eAAe,mBAAmB,OAAO,kBAAkB;oBACjE,MAAM,kBAAkB,uBAAuB,CAAC,MAAM,EAAE,IAAI,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,IAAI,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC;oBACpK,IAAI,SAAS,MAAM,OAAO,CAAC,WAAW;oBACtC,IAAI,QAAQ;wBACV,SAAS,OAAO,kBAAkB,CAAC,KAAK,GAAG,SAAS,GAAG,YAAY,CAAC,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,KAAK;wBACrG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,GAAG,GAAG;wBAChE,OAAO,QAAQ,CAAC,GAAG,GAAG;oBACxB;oBACA,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG;oBAC9B,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG;oBAChC,GAAG,KAAK,CAAC,WAAW,GAAG,uBAAuB,KAAK,GAAG,IAAI,EAAE,CAAC;oBAC7D,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,EAAE;wBAC1D,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,kBAAkB,aAAa,UAAU,EAAE,UAAU,GAAG,EAAE,WAAW,GAAG,CAAC;wBACxH,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,mBAAmB,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,GAAG;oBAC1G;gBACF,OAAO;oBACL,MAAM,QAAQ,mBAAmB,YAAY,IAAI,YAAY,MAAM,OAAO,EAAE,UAAU;oBACtF,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAC/E;gBACA,YAAY,OAAO,GAAG;gBACtB,QAAQ,OAAO,GAAG,OAAO,IAAI;YAC/B;QACF;QACA,IAAI,CAAC,sBAAsB,iBAAiB,OAAO,IAAI,CAAC,cAAc,OAAO,EAAE;YAC7E,IAAI,WAAW;gBACb,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,MAAM,KAAK,kBAAkB,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAChD,IAAI,MAAM,QAAQ,GAAG,WAAW,IAAI,MAAM,QAAQ,GAAG,YAAY,EAAE;wBACjE,MAAM,EACJ,oBAAoB,EACrB,GAAG;wBACJ,IAAI,wBAAwB,UAAU;4BACpC,IAAI,MAAM,KAAK,EAAE;gCACf,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG;oCAC/B,iBAAiB,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,MAAM,KAAK;gCAC1D,OAAO,IAAI,MAAM,KAAK,YAAY,+IAAA,CAAA,UAAO,EAAE;oCACzC,iBAAiB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC;gCACvE,OAAO;oCACL,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE;gCAC/F;4BACF;wBACF,OAAO;4BACL,MAAM,QAAQ,CAAC,kBAAkB,EAAE,IAAI;4BACvC,MAAM,IAAI,GAAG,WAAW,GAAG;4BAC3B,MAAM,IAAI,GAAG,YAAY,GAAG;4BAC5B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;wBAC3C;wBACA,cAAc,OAAO,GAAG;oBAC1B;gBACF;YACF,OAAO;gBACL,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE;gBAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,IAAI,OAAO,QAAQ,IAAI,YAAY,EAAE;oBACrE,MAAM,QAAQ,IAAI,SAAS,MAAM;oBACjC,MAAM,IAAI,IAAI,WAAW,GAAG;oBAC5B,MAAM,IAAI,IAAI,YAAY,GAAG;oBAC7B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;oBACzC,cAAc,OAAO,GAAG;gBAC1B;gBACA,iBAAiB,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,QAAQ;YACpD;QACF;IACF;IACA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAC;YACnC,cAAc,CAAC,YAAY,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAiCpC,CAAC,GAAG;YACN,gBAAgB,QAAQ,GAAE,CAAC;;;;MAIzB,CAAC;QACL,CAAC,GAAG;QAAC;KAAU;IACf,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACnE,KAAK;IACP,IAAI,WAAW,CAAC,sBAAsB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC7E,YAAY;QACZ,eAAe;QACf,KAAK;IACP,GAAG,YAAY,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB,OAAO,YAAY,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,kBAAkB;QACrI,MAAM,+IAAA,CAAA,aAAU;QAChB,cAAc,QAAQ,YAAY;QAClC,gBAAgB,QAAQ,cAAc;IACxC;AACF", "ignoreList": [0], "debugId": null}}]}
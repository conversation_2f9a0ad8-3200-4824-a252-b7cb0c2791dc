'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { useScreenReaderAnnouncements } from '@/hooks/useKeyboardNavigation';

interface ServiceWorkerProviderProps {
  children: React.ReactNode;
}

export function ServiceWorkerProvider({ children }: ServiceWorkerProviderProps) {
  const [swStatus, setSwStatus] = useState<'loading' | 'ready' | 'error' | 'update-available'>('loading');
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const { announce } = useScreenReaderAnnouncements();

  useEffect(() => {
    // Check if service workers are supported
    if ('serviceWorker' in navigator) {
      registerServiceWorker();
    } else {
      setSwStatus('error');
      console.warn('Service Workers not supported');
    }

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      announce('Connection restored');
    };

    const handleOffline = () => {
      setIsOnline(false);
      announce('Connection lost - offline mode active');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial online status
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [announce]);

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      console.log('Service Worker registered successfully:', registration);
      setSwStatus('ready');
      announce('Offline support enabled');

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setShowUpdatePrompt(true);
              setSwStatus('update-available');
              announce('App update available');
            }
          });
        }
      });

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'CACHE_UPDATED') {
          announce('Content updated in background');
        }
      });

    } catch (error) {
      console.error('Service Worker registration failed:', error);
      setSwStatus('error');
    }
  };

  const handleUpdate = async () => {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration && registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        window.location.reload();
      }
    }
  };

  const dismissUpdate = () => {
    setShowUpdatePrompt(false);
    setSwStatus('ready');
  };

  return (
    <>
      {children}
      
      {/* Connection Status Indicator */}
      <ConnectionStatus isOnline={isOnline} />
      
      {/* Update Prompt */}
      <AnimatePresence>
        {showUpdatePrompt && (
          <UpdatePrompt onUpdate={handleUpdate} onDismiss={dismissUpdate} />
        )}
      </AnimatePresence>
      
      {/* Service Worker Status */}
      {process.env.NODE_ENV === 'development' && (
        <ServiceWorkerStatus status={swStatus} />
      )}
    </>
  );
}

function ConnectionStatus({ isOnline }: { isOnline: boolean }) {
  const [showStatus, setShowStatus] = useState(false);

  useEffect(() => {
    if (!isOnline) {
      setShowStatus(true);
    } else {
      // Hide status after a delay when back online
      const timer = setTimeout(() => setShowStatus(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [isOnline]);

  return (
    <AnimatePresence>
      {showStatus && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-lg shadow-lg ${
            isOnline 
              ? 'bg-green-500 text-white' 
              : 'bg-orange-500 text-white'
          }`}
          role="status"
          aria-live="polite"
        >
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-white' : 'bg-white animate-pulse'}`} />
            <span className="text-sm font-medium">
              {isOnline ? 'Back Online' : 'Offline Mode'}
            </span>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

function UpdatePrompt({ 
  onUpdate, 
  onDismiss 
}: { 
  onUpdate: () => void; 
  onDismiss: () => void; 
}) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
      role="dialog"
      aria-labelledby="update-title"
      aria-describedby="update-description"
    >
      <div className="bg-bg-primary border border-primary/30 rounded-lg p-6 max-w-md mx-4 chip-border">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
            <span className="text-2xl">🚀</span>
          </div>
          
          <h3 id="update-title" className="text-xl font-bold text-primary mb-2">
            Update Available
          </h3>
          
          <p id="update-description" className="text-text-secondary mb-6">
            A new version of the portfolio is available with improvements and bug fixes. 
            Update now for the best experience.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="primary"
              onClick={onUpdate}
              className="flex-1"
            >
              🔄 Update Now
            </Button>
            <Button
              variant="ghost"
              onClick={onDismiss}
              className="flex-1"
            >
              Later
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

function ServiceWorkerStatus({ status }: { status: string }) {
  const getStatusColor = () => {
    switch (status) {
      case 'ready': return 'bg-green-500';
      case 'loading': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      case 'update-available': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'ready': return 'SW Ready';
      case 'loading': return 'SW Loading';
      case 'error': return 'SW Error';
      case 'update-available': return 'SW Update';
      default: return 'SW Unknown';
    }
  };

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <div className={`px-3 py-1 rounded-full text-xs text-white ${getStatusColor()}`}>
        {getStatusText()}
      </div>
    </div>
  );
}

// Hook for service worker utilities
export function useServiceWorker() {
  const [isOnline, setIsOnline] = useState(true);
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    setIsOnline(navigator.onLine);

    // Get service worker registration
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then(setSwRegistration);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const sendMessage = (message: any) => {
    if (swRegistration && swRegistration.active) {
      swRegistration.active.postMessage(message);
    }
  };

  const clearCache = async () => {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }
  };

  return {
    isOnline,
    swRegistration,
    sendMessage,
    clearCache,
  };
}

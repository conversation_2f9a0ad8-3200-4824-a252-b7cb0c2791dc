'use client';

import { Object3<PERSON>, LOD, Mesh, BufferGeometry, Material, InstancedMesh, Matrix4, Vector3, Quaternion } from 'three';
import { GLTF } from 'three/examples/jsm/loaders/GLTFLoader.js';

// Level of Detail (LOD) configuration
export interface LODConfig {
  distances: number[];
  geometries: BufferGeometry[];
  materials: Material[];
}

// Instance data for instanced rendering
export interface InstanceData {
  position: Vector3;
  rotation: Quaternion;
  scale: Vector3;
  color?: string;
  opacity?: number;
}

// Performance monitoring
export class PerformanceMonitor {
  private frameCount = 0;
  private lastTime = performance.now();
  private fps = 0;
  private memoryUsage = 0;
  private drawCalls = 0;
  private triangles = 0;

  update(renderer: any) {
    this.frameCount++;
    const currentTime = performance.now();
    
    if (currentTime - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
      this.frameCount = 0;
      this.lastTime = currentTime;
      
      // Memory usage (if available)
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      }
      
      // Renderer stats
      if (renderer && renderer.info) {
        this.drawCalls = renderer.info.render.calls;
        this.triangles = renderer.info.render.triangles;
      }
    }
  }

  getMetrics() {
    return {
      fps: this.fps,
      memoryUsage: this.memoryUsage,
      drawCalls: this.drawCalls,
      triangles: this.triangles,
    };
  }

  shouldReduceQuality(): boolean {
    return this.fps < 30 || this.memoryUsage > 100;
  }
}

// Model optimization utilities
export class ModelOptimizer {
  static createLOD(baseGeometry: BufferGeometry, baseMaterial: Material, distances: number[] = [5, 10, 20]): LOD {
    const lod = new LOD();
    
    // High detail (close)
    const highDetailMesh = new Mesh(baseGeometry, baseMaterial);
    lod.addLevel(highDetailMesh, distances[0]);
    
    // Medium detail
    const mediumGeometry = this.simplifyGeometry(baseGeometry, 0.7);
    const mediumMesh = new Mesh(mediumGeometry, baseMaterial);
    lod.addLevel(mediumMesh, distances[1]);
    
    // Low detail (far)
    const lowGeometry = this.simplifyGeometry(baseGeometry, 0.4);
    const lowMesh = new Mesh(lowGeometry, baseMaterial);
    lod.addLevel(lowMesh, distances[2]);
    
    return lod;
  }

  static simplifyGeometry(geometry: BufferGeometry, factor: number): BufferGeometry {
    // Simple geometry simplification by reducing vertices
    const simplified = geometry.clone();
    
    if (simplified.attributes.position) {
      const positions = simplified.attributes.position.array;
      const newPositions = new Float32Array(Math.floor(positions.length * factor));
      
      for (let i = 0; i < newPositions.length; i += 3) {
        const sourceIndex = Math.floor((i / newPositions.length) * positions.length);
        newPositions[i] = positions[sourceIndex];
        newPositions[i + 1] = positions[sourceIndex + 1];
        newPositions[i + 2] = positions[sourceIndex + 2];
      }
      
      simplified.setAttribute('position', new THREE.BufferAttribute(newPositions, 3));
    }
    
    return simplified;
  }

  static createInstancedMesh(
    geometry: BufferGeometry,
    material: Material,
    instances: InstanceData[],
    maxCount: number = 1000
  ): InstancedMesh {
    const instancedMesh = new InstancedMesh(geometry, material, Math.min(instances.length, maxCount));
    
    const matrix = new Matrix4();
    
    instances.slice(0, maxCount).forEach((instance, index) => {
      matrix.compose(instance.position, instance.rotation, instance.scale);
      instancedMesh.setMatrixAt(index, matrix);
      
      if (instance.color) {
        instancedMesh.setColorAt(index, new THREE.Color(instance.color));
      }
    });
    
    instancedMesh.instanceMatrix.needsUpdate = true;
    if (instancedMesh.instanceColor) {
      instancedMesh.instanceColor.needsUpdate = true;
    }
    
    return instancedMesh;
  }

  static optimizeGLTF(gltf: GLTF): GLTF {
    // Traverse and optimize the GLTF scene
    gltf.scene.traverse((child) => {
      if (child instanceof Mesh) {
        // Optimize geometry
        if (child.geometry) {
          child.geometry.computeBoundingSphere();
          child.geometry.computeBoundingBox();
          
          // Remove unnecessary attributes for performance
          if (child.geometry.attributes.uv2) {
            child.geometry.deleteAttribute('uv2');
          }
        }
        
        // Optimize materials
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => this.optimizeMaterial(mat));
          } else {
            this.optimizeMaterial(child.material);
          }
        }
        
        // Enable frustum culling
        child.frustumCulled = true;
      }
    });
    
    return gltf;
  }

  static optimizeMaterial(material: Material) {
    // Optimize material properties for performance
    if ('transparent' in material && !material.transparent) {
      material.alphaTest = 0;
    }
    
    // Reduce precision for mobile devices
    if (this.isMobileDevice()) {
      if ('roughness' in material) {
        (material as any).roughness = Math.round((material as any).roughness * 10) / 10;
      }
      if ('metalness' in material) {
        (material as any).metalness = Math.round((material as any).metalness * 10) / 10;
      }
    }
  }

  static isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }
}

// Texture optimization
export class TextureOptimizer {
  static async compressTexture(imageUrl: string, quality: number = 0.8): Promise<string> {
    return new Promise((resolve) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        
        // Resize for performance
        const maxSize = ModelOptimizer.isMobileDevice() ? 512 : 1024;
        const scale = Math.min(maxSize / img.width, maxSize / img.height, 1);
        
        canvas.width = img.width * scale;
        canvas.height = img.height * scale;
        
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        
        // Convert to compressed format
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
        resolve(compressedDataUrl);
      };
      
      img.src = imageUrl;
    });
  }

  static getOptimalTextureSize(originalSize: number): number {
    // Ensure power of 2 and reasonable size
    const maxSize = ModelOptimizer.isMobileDevice() ? 512 : 1024;
    let size = 1;
    
    while (size < originalSize && size < maxSize) {
      size *= 2;
    }
    
    return Math.min(size, maxSize);
  }
}

// Frustum culling optimization
export class FrustumCuller {
  private static visibleObjects = new Set<Object3D>();
  
  static updateVisibility(camera: any, scene: Object3D) {
    this.visibleObjects.clear();
    
    scene.traverse((object) => {
      if (object instanceof Mesh) {
        // Simple distance-based culling
        const distance = camera.position.distanceTo(object.position);
        const maxDistance = 50; // Adjust based on scene scale
        
        if (distance < maxDistance) {
          object.visible = true;
          this.visibleObjects.add(object);
        } else {
          object.visible = false;
        }
      }
    });
  }
  
  static getVisibleCount(): number {
    return this.visibleObjects.size;
  }
}

// Memory management
export class MemoryManager {
  private static disposedObjects = new WeakSet();
  
  static disposeObject(object: Object3D) {
    if (this.disposedObjects.has(object)) return;
    
    object.traverse((child) => {
      if (child instanceof Mesh) {
        if (child.geometry) {
          child.geometry.dispose();
        }
        
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => mat.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    });
    
    this.disposedObjects.add(object);
  }
  
  static cleanupScene(scene: Object3D) {
    const objectsToRemove: Object3D[] = [];
    
    scene.traverse((object) => {
      // Remove objects that are far away or not visible
      if (object.parent && !object.visible) {
        objectsToRemove.push(object);
      }
    });
    
    objectsToRemove.forEach(object => {
      if (object.parent) {
        object.parent.remove(object);
        this.disposeObject(object);
      }
    });
  }
}

// Performance presets
export const PerformancePresets = {
  high: {
    shadows: true,
    antialias: true,
    postProcessing: true,
    particleCount: 200,
    lodDistances: [5, 10, 20],
    textureQuality: 1.0,
    maxInstances: 1000,
  },
  medium: {
    shadows: true,
    antialias: false,
    postProcessing: false,
    particleCount: 100,
    lodDistances: [3, 8, 15],
    textureQuality: 0.8,
    maxInstances: 500,
  },
  low: {
    shadows: false,
    antialias: false,
    postProcessing: false,
    particleCount: 50,
    lodDistances: [2, 5, 10],
    textureQuality: 0.6,
    maxInstances: 250,
  },
  potato: {
    shadows: false,
    antialias: false,
    postProcessing: false,
    particleCount: 25,
    lodDistances: [1, 3, 6],
    textureQuality: 0.4,
    maxInstances: 100,
  },
};

'use client';

import { useRef, useState, Suspense } from 'react';
import { useFrame, useLoader } from '@react-three/fiber';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { useGLTF, Text, Html } from '@react-three/drei';
import { Mesh, Group } from 'three';

interface MicrochipModelProps {
  modelPath?: string;
  position?: [number, number, number];
  scale?: number;
  rotation?: [number, number, number];
  animated?: boolean;
  glowEffect?: boolean;
  text?: string;
  onClick?: () => void;
  onHover?: (hovered: boolean) => void;
  primaryColor?: string;
}

// Component for loading external GLTF models
function LoadedMicrochip({
  modelPath,
  position = [0, 0, 0],
  scale = 1,
  animated = true,
  glowEffect = true,
  onClick,
  onHover,
  primaryColor = '#00ff88'
}: MicrochipModelProps) {
  const groupRef = useRef<Group>(null);
  const [hovered, setHovered] = useState(false);
  
  // Load the GLTF model
  const { scene } = useGLTF(modelPath || '/models/microchip.gltf');
  
  // Animation loop
  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    
    // Gentle rotation animation
    groupRef.current.rotation.y = state.clock.elapsedTime * 0.3;
    
    // Floating animation
    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    
    // Hover effect
    if (hovered) {
      groupRef.current.scale.setScalar(scale * 1.1);
    } else {
      groupRef.current.scale.setScalar(scale);
    }
  });

  const handlePointerOver = () => {
    setHovered(true);
    onHover?.(true);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    setHovered(false);
    onHover?.(false);
    document.body.style.cursor = 'auto';
  };

  return (
    <group
      ref={groupRef}
      position={position}
      scale={scale}
      onClick={onClick}
      onPointerOver={handlePointerOver}
      onPointerOut={handlePointerOut}
    >
      <primitive 
        object={scene.clone()} 
        castShadow 
        receiveShadow
      />
      
      {/* Glow effect */}
      {glowEffect && hovered && (
        <mesh position={[0, 0, 0]}>
          <sphereGeometry args={[2, 16, 16]} />
          <meshBasicMaterial
            color={primaryColor}
            transparent
            opacity={0.1}
          />
        </mesh>
      )}
    </group>
  );
}

// Fallback component while model loads
function ModelLoader() {
  return (
    <Html center>
      <div className="flex flex-col items-center space-y-2 text-white">
        <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        <p className="text-sm">Loading 3D Model...</p>
      </div>
    </Html>
  );
}

// Error fallback component
function ModelError() {
  return (
    <mesh>
      <boxGeometry args={[2, 0.5, 2]} />
      <meshStandardMaterial color="#ff4444" />
    </mesh>
  );
}

// Main component with error boundary
export function MicrochipModel(props: MicrochipModelProps) {
  const [error, setError] = useState(false);

  if (error) {
    return <ModelError />;
  }

  return (
    <Suspense fallback={<ModelLoader />}>
      <ErrorBoundary onError={() => setError(true)}>
        <LoadedMicrochip {...props} />
      </ErrorBoundary>
    </Suspense>
  );
}

// Simple error boundary component
function ErrorBoundary({ 
  children, 
  onError 
}: { 
  children: React.ReactNode; 
  onError: () => void; 
}) {
  try {
    return <>{children}</>;
  } catch (error) {
    console.error('3D Model loading error:', error);
    onError();
    return <ModelError />;
  }
}

// Preload models for better performance
export function preloadMicrochipModels() {
  useGLTF.preload('/models/microchip.gltf');
  useGLTF.preload('/models/cpu.gltf');
  useGLTF.preload('/models/processor.gltf');
}

// Enhanced microchip with multiple variants
export function EnhancedMicrochip({
  variant = 'default',
  ...props
}: MicrochipModelProps & { variant?: 'default' | 'cpu' | 'processor' | 'memory' }) {
  const modelPaths = {
    default: '/models/microchip.gltf',
    cpu: '/models/cpu.gltf',
    processor: '/models/processor.gltf',
    memory: '/models/memory.gltf',
  };

  return (
    <MicrochipModel
      {...props}
      modelPath={modelPaths[variant]}
    />
  );
}

// Grid of microchips for background effects
export function MicrochipGrid({
  count = 20,
  spread = 10,
  animated = true,
  primaryColor = '#00ff88',
  secondaryColor = '#0088ff',
}: {
  count?: number;
  spread?: number;
  animated?: boolean;
  primaryColor?: string;
  secondaryColor?: string;
}) {
  const chips = Array.from({ length: count }, (_, i) => ({
    id: i,
    position: [
      (Math.random() - 0.5) * spread,
      (Math.random() - 0.5) * spread,
      (Math.random() - 0.5) * spread,
    ] as [number, number, number],
    scale: 0.3 + Math.random() * 0.4,
    rotation: [
      Math.random() * Math.PI,
      Math.random() * Math.PI,
      Math.random() * Math.PI,
    ] as [number, number, number],
  }));

  return (
    <group>
      {chips.map((chip) => (
        <MicrochipModel
          key={chip.id}
          position={chip.position}
          scale={chip.scale}
          rotation={chip.rotation}
          animated={animated}
          glowEffect={false}
          primaryColor={primaryColor}
        />
      ))}
    </group>
  );
}

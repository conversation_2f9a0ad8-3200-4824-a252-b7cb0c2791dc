'use client';

import { Project, Skill, Experience, Education, ContactFormData } from '@/types';

// Content storage interface
interface ContentStorage {
  projects: Project[];
  skills: Skill[];
  experiences: Experience[];
  education: Education[];
  personalInfo: {
    name: string;
    title: string;
    bio: string;
    location: string;
    email: string;
    phone: string;
    social: {
      github: string;
      linkedin: string;
      twitter: string;
    };
  };
}

// Default content data
const defaultContent: ContentStorage = {
  projects: [
    {
      id: '1',
      title: '3D Portfolio Website',
      description: 'An immersive 3D portfolio website built with React, Three.js, and Next.js featuring interactive microchip-themed design.',
      longDescription: 'This project showcases advanced 3D web development techniques using React Three Fiber and Three.js. Features include interactive camera controls, performance optimization, responsive design, and comprehensive accessibility support.',
      technologies: ['React', 'Three.js', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
      imageUrl: '/images/projects/3d-portfolio.jpg',
      demoUrl: 'https://portfolio.example.com',
      githubUrl: 'https://github.com/developer/3d-portfolio',
      featured: true,
      category: 'web-development',
      completedAt: new Date('2024-01-15')
    },
    {
      id: '2',
      title: 'AI-Powered Analytics Dashboard',
      description: 'A modern dashboard application with AI-driven analytics and real-time data visualization.',
      longDescription: 'Built with React and Python backend, this dashboard provides intelligent insights using machine learning algorithms. Features include real-time data processing, interactive charts, and predictive analytics.',
      technologies: ['React', 'Python', 'TensorFlow', 'D3.js', 'PostgreSQL', 'Redis'],
      imageUrl: '/images/projects/ai-dashboard.jpg',
      demoUrl: 'https://dashboard.example.com',
      githubUrl: 'https://github.com/developer/ai-dashboard',
      featured: true,
      category: 'ai-ml',
      completedAt: new Date('2023-11-20')
    },
    {
      id: '3',
      title: 'E-commerce Mobile App',
      description: 'Cross-platform mobile application for e-commerce with advanced features and smooth animations.',
      longDescription: 'React Native application with comprehensive e-commerce functionality including payment processing, inventory management, and user authentication. Features offline support and push notifications.',
      technologies: ['React Native', 'Node.js', 'MongoDB', 'Stripe', 'Firebase', 'Redux'],
      imageUrl: '/images/projects/ecommerce-app.jpg',
      demoUrl: 'https://app.example.com',
      githubUrl: 'https://github.com/developer/ecommerce-app',
      featured: false,
      category: 'mobile-development',
      completedAt: new Date('2023-09-10')
    }
  ],
  skills: [
    {
      id: '1',
      name: 'React',
      category: 'frontend',
      level: 'expert',
      yearsOfExperience: 5,
      description: 'Building complex user interfaces with React and its ecosystem including hooks, context, and state management'
    },
    {
      id: '2',
      name: 'Three.js',
      category: 'frontend',
      level: 'advanced',
      yearsOfExperience: 3,
      description: 'Creating immersive 3D web experiences with WebGL and Three.js'
    },
    {
      id: '3',
      name: 'TypeScript',
      category: 'frontend',
      level: 'expert',
      yearsOfExperience: 4,
      description: 'Type-safe JavaScript development for large-scale applications'
    },
    {
      id: '4',
      name: 'Node.js',
      category: 'backend',
      level: 'advanced',
      yearsOfExperience: 4,
      description: 'Server-side JavaScript development with Express, NestJS, and microservices'
    },
    {
      id: '5',
      name: 'Python',
      category: 'backend',
      level: 'advanced',
      yearsOfExperience: 3,
      description: 'Backend development, data science, and machine learning with Python'
    }
  ],
  experiences: [
    {
      id: '1',
      company: 'Tech Innovations Inc.',
      position: 'Senior Frontend Developer',
      startDate: new Date('2022-01-01'),
      endDate: undefined,
      description: 'Leading the development of interactive 3D web applications using React and Three.js. Responsible for architecture decisions, performance optimization, and team mentoring.',
      achievements: [
        'Increased user engagement by 40% with immersive 3D interfaces',
        'Led a team of 5 developers on multiple high-impact projects',
        'Implemented performance optimizations reducing load times by 60%',
        'Established coding standards and best practices for the team'
      ],
      technologies: ['React', 'Three.js', 'TypeScript', 'WebGL', 'Next.js', 'Node.js'],
      location: 'San Francisco, CA',
      type: 'full-time'
    },
    {
      id: '2',
      company: 'Digital Solutions Ltd.',
      position: 'Full Stack Developer',
      startDate: new Date('2020-06-01'),
      endDate: new Date('2021-12-31'),
      description: 'Developed and maintained web applications using modern JavaScript frameworks. Collaborated with design and product teams to deliver user-centric solutions.',
      achievements: [
        'Built 15+ responsive web applications from concept to deployment',
        'Improved application performance by 35% through optimization',
        'Mentored junior developers and conducted code reviews',
        'Implemented CI/CD pipelines reducing deployment time by 50%'
      ],
      technologies: ['React', 'Node.js', 'MongoDB', 'Express.js', 'AWS', 'Docker'],
      location: 'New York, NY',
      type: 'full-time'
    }
  ],
  education: [
    {
      id: '1',
      institution: 'University of Technology',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      startDate: new Date('2016-09-01'),
      endDate: new Date('2020-05-31'),
      gpa: 3.8,
      achievements: [
        'Magna Cum Laude',
        'Dean\'s List for 6 semesters',
        'Computer Science Society President',
        'Outstanding Senior Project Award'
      ],
      location: 'Boston, MA'
    }
  ],
  personalInfo: {
    name: 'Alex Developer',
    title: 'Senior Full Stack Developer',
    bio: 'Passionate developer with 5+ years of experience creating immersive web experiences. Specialized in 3D web development, performance optimization, and modern JavaScript frameworks.',
    location: 'San Francisco, CA',
    email: '<EMAIL>',
    phone: '+****************',
    social: {
      github: 'https://github.com/developer',
      linkedin: 'https://linkedin.com/in/developer',
      twitter: 'https://twitter.com/developer'
    }
  }
};

// Content Manager class
export class ContentManager {
  private static instance: ContentManager;
  private content: ContentStorage;
  private listeners: Set<() => void> = new Set();

  private constructor() {
    this.content = this.loadContent();
  }

  static getInstance(): ContentManager {
    if (!ContentManager.instance) {
      ContentManager.instance = new ContentManager();
    }
    return ContentManager.instance;
  }

  // Load content from localStorage or use defaults
  private loadContent(): ContentStorage {
    if (typeof window === 'undefined') return defaultContent;

    try {
      const saved = localStorage.getItem('portfolio-content');
      if (saved) {
        const parsed = JSON.parse(saved);
        // Ensure dates are properly parsed
        parsed.experiences?.forEach((exp: any) => {
          exp.startDate = new Date(exp.startDate);
          if (exp.endDate) exp.endDate = new Date(exp.endDate);
        });
        parsed.education?.forEach((edu: any) => {
          edu.startDate = new Date(edu.startDate);
          if (edu.endDate) edu.endDate = new Date(edu.endDate);
        });
        parsed.projects?.forEach((proj: any) => {
          proj.completedAt = new Date(proj.completedAt);
        });
        return { ...defaultContent, ...parsed };
      }
    } catch (error) {
      console.error('Failed to load content from localStorage:', error);
    }

    return defaultContent;
  }

  // Save content to localStorage
  private saveContent(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem('portfolio-content', JSON.stringify(this.content));
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to save content to localStorage:', error);
    }
  }

  // Subscribe to content changes
  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  // Getters
  getProjects(): Project[] {
    return [...this.content.projects];
  }

  getFeaturedProjects(): Project[] {
    return this.content.projects.filter(project => project.featured);
  }

  getProjectById(id: string): Project | undefined {
    return this.content.projects.find(project => project.id === id);
  }

  getSkills(): Skill[] {
    return [...this.content.skills];
  }

  getSkillsByCategory(category: string): Skill[] {
    return this.content.skills.filter(skill => skill.category === category);
  }

  getExperiences(): Experience[] {
    return [...this.content.experiences];
  }

  getEducation(): Education[] {
    return [...this.content.education];
  }

  getPersonalInfo() {
    return { ...this.content.personalInfo };
  }

  // Setters
  updateProject(id: string, updates: Partial<Project>): void {
    const index = this.content.projects.findIndex(p => p.id === id);
    if (index !== -1) {
      this.content.projects[index] = { ...this.content.projects[index], ...updates };
      this.saveContent();
    }
  }

  addProject(project: Omit<Project, 'id'>): void {
    const newProject: Project = {
      ...project,
      id: Date.now().toString(),
    };
    this.content.projects.push(newProject);
    this.saveContent();
  }

  deleteProject(id: string): void {
    this.content.projects = this.content.projects.filter(p => p.id !== id);
    this.saveContent();
  }

  updateSkill(id: string, updates: Partial<Skill>): void {
    const index = this.content.skills.findIndex(s => s.id === id);
    if (index !== -1) {
      this.content.skills[index] = { ...this.content.skills[index], ...updates };
      this.saveContent();
    }
  }

  updatePersonalInfo(updates: Partial<ContentStorage['personalInfo']>): void {
    this.content.personalInfo = { ...this.content.personalInfo, ...updates };
    this.saveContent();
  }

  // Reset to defaults
  resetContent(): void {
    this.content = { ...defaultContent };
    this.saveContent();
  }

  // Export/Import
  exportContent(): string {
    return JSON.stringify(this.content, null, 2);
  }

  importContent(jsonContent: string): boolean {
    try {
      const imported = JSON.parse(jsonContent);
      this.content = { ...defaultContent, ...imported };
      this.saveContent();
      return true;
    } catch (error) {
      console.error('Failed to import content:', error);
      return false;
    }
  }
}

// React hook for using content manager
export function useContentManager() {
  const [, forceUpdate] = useState({});
  const manager = ContentManager.getInstance();

  // Subscribe to changes
  useState(() => {
    const unsubscribe = manager.subscribe(() => {
      forceUpdate({});
    });
    return unsubscribe;
  });

  return manager;
}

// Contact form submission handler
export async function submitContactForm(data: ContactFormData): Promise<boolean> {
  try {
    // In a real application, this would send to your backend
    console.log('Contact form submitted:', data);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Store in localStorage for demo purposes
    const submissions = JSON.parse(localStorage.getItem('contact-submissions') || '[]');
    submissions.push({
      ...data,
      timestamp: new Date().toISOString(),
      id: Date.now().toString(),
    });
    localStorage.setItem('contact-submissions', JSON.stringify(submissions));
    
    return true;
  } catch (error) {
    console.error('Failed to submit contact form:', error);
    return false;
  }
}

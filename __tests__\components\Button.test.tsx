import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Button } from '@/components/ui/Button';
import { ThemeProvider } from '@/components/ui/ThemeProvider';

// Wrapper component for tests that need theme context
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
);

describe('Button Component', () => {
  it('renders with default props', () => {
    render(
      <TestWrapper>
        <Button>Click me</Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('btn');
  });

  it('applies variant classes correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <Button variant="primary">Primary</Button>
      </TestWrapper>
    );
    
    let button = screen.getByRole('button');
    expect(button).toHaveClass('btn-primary');

    rerender(
      <TestWrapper>
        <Button variant="secondary">Secondary</Button>
      </TestWrapper>
    );
    
    button = screen.getByRole('button');
    expect(button).toHaveClass('btn-secondary');
  });

  it('applies size classes correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <Button size="sm">Small</Button>
      </TestWrapper>
    );
    
    let button = screen.getByRole('button');
    expect(button).toHaveClass('btn-sm');

    rerender(
      <TestWrapper>
        <Button size="lg">Large</Button>
      </TestWrapper>
    );
    
    button = screen.getByRole('button');
    expect(button).toHaveClass('btn-lg');
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    
    render(
      <TestWrapper>
        <Button onClick={handleClick}>Click me</Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <Button loading>Loading</Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('aria-busy', 'true');
  });

  it('is disabled when disabled prop is true', () => {
    render(
      <TestWrapper>
        <Button disabled>Disabled</Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('does not call onClick when disabled', () => {
    const handleClick = jest.fn();
    
    render(
      <TestWrapper>
        <Button disabled onClick={handleClick}>Disabled</Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('applies fullWidth class when fullWidth is true', () => {
    render(
      <TestWrapper>
        <Button fullWidth>Full Width</Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('w-full');
  });

  it('forwards additional props', () => {
    render(
      <TestWrapper>
        <Button data-testid="custom-button" aria-label="Custom button">
          Custom
        </Button>
      </TestWrapper>
    );
    
    const button = screen.getByTestId('custom-button');
    expect(button).toHaveAttribute('aria-label', 'Custom button');
  });

  it('renders as different element when asChild is used', () => {
    render(
      <TestWrapper>
        <Button asChild>
          <a href="/test">Link Button</a>
        </Button>
      </TestWrapper>
    );
    
    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', '/test');
  });

  it('supports keyboard navigation', () => {
    const handleClick = jest.fn();
    
    render(
      <TestWrapper>
        <Button onClick={handleClick}>Keyboard</Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button');
    button.focus();
    
    expect(button).toHaveFocus();
    
    fireEvent.keyDown(button, { key: 'Enter' });
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    fireEvent.keyDown(button, { key: ' ' });
    expect(handleClick).toHaveBeenCalledTimes(2);
  });

  it('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <Button loading aria-describedby="help-text">
          Accessible Button
        </Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-busy', 'true');
    expect(button).toHaveAttribute('aria-describedby', 'help-text');
  });

  it('applies custom className', () => {
    render(
      <TestWrapper>
        <Button className="custom-class">Custom</Button>
      </TestWrapper>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
    expect(button).toHaveClass('btn'); // Should still have base classes
  });

  it('handles loading state transition', async () => {
    const { rerender } = render(
      <TestWrapper>
        <Button loading={false}>Not Loading</Button>
      </TestWrapper>
    );
    
    let button = screen.getByRole('button');
    expect(button).not.toBeDisabled();
    expect(button).not.toHaveAttribute('aria-busy');

    rerender(
      <TestWrapper>
        <Button loading={true}>Loading</Button>
      </TestWrapper>
    );
    
    button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('aria-busy', 'true');
  });
});

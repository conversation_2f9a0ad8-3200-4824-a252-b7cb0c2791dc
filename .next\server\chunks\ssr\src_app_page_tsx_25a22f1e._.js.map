{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { HeroSection } from '@/components/sections/HeroSection';\n\nexport default function Home() {\n  const { toggleTheme, theme } = useTheme();\n  const qualitySettings = useAdaptiveQuality();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary\">\n      {/* Hero Section */}\n      <section className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n        {/* Background Circuit Pattern */}\n        <div className=\"absolute inset-0 circuit-bg opacity-20\" />\n\n        {/* 3D Scene */}\n        <div className=\"absolute inset-0\">\n          <HeroScene\n            cameraPosition={[0, 0, 8]}\n            enableControls={true}\n            enableEnvironment={true}\n            shadows={qualitySettings.shadows}\n            antialias={qualitySettings.antialias}\n          >\n            {/* Main hero microchip */}\n            <Microchip\n              position={[0, 0, 0]}\n              scale={1.5}\n              animated={true}\n              glowEffect={true}\n              text=\"PORTFOLIO\"\n            />\n\n            {/* Background microchip grid for depth */}\n            <MicrochipGrid\n              count={qualitySettings.particleCount / 4}\n              spread={15}\n              animated={true}\n            />\n          </HeroScene>\n        </div>\n\n        {/* Content Overlay */}\n        <div className=\"relative z-10 text-center px-4 max-w-4xl mx-auto\">\n          <h1 className=\"text-6xl md:text-8xl font-bold mb-6 text-glow\">\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              3D Portfolio\n            </span>\n          </h1>\n\n          <p className=\"text-xl md:text-2xl text-text-secondary mb-8 max-w-2xl mx-auto\">\n            Welcome to an immersive 3D experience showcasing cutting-edge web development\n            with interactive microchip-themed design.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button\n              variant=\"primary\"\n              size=\"lg\"\n              className=\"animate-pulse-glow\"\n              onClick={() => {\n                document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' });\n              }}\n            >\n              Explore Portfolio\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              onClick={() => {\n                document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' });\n              }}\n            >\n              View Projects\n            </Button>\n          </div>\n        </div>\n\n        {/* Theme Toggle */}\n        <div className=\"absolute top-6 right-6 z-20\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={toggleTheme}\n            className=\"chip-border\"\n          >\n            {theme === 'dark' ? '☀️' : '🌙'}\n          </Button>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <div className=\"w-6 h-10 border-2 border-primary rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-primary rounded-full mt-2 animate-pulse\" />\n          </div>\n        </div>\n      </section>\n\n      {/* Placeholder sections for future development */}\n      <section id=\"about\" className=\"min-h-screen flex items-center justify-center bg-bg-secondary\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold text-primary mb-4\">About Section</h2>\n          <p className=\"text-text-secondary\">Coming soon in Phase 4...</p>\n        </div>\n      </section>\n\n      <section id=\"projects\" className=\"min-h-screen flex items-center justify-center bg-bg-tertiary\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold text-secondary mb-4\">Projects Section</h2>\n          <p className=\"text-text-secondary\">Coming soon in Phase 5...</p>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAIe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG;IAC/B,MAAM,kBAAkB;IAExB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,gBAAgB;gCAAC;gCAAG;gCAAG;6BAAE;4BACzB,gBAAgB;4BAChB,mBAAmB;4BACnB,SAAS,gBAAgB,OAAO;4BAChC,WAAW,gBAAgB,SAAS;;8CAGpC,8OAAC;oCACC,UAAU;wCAAC;wCAAG;wCAAG;qCAAE;oCACnB,OAAO;oCACP,UAAU;oCACV,YAAY;oCACZ,MAAK;;;;;;8CAIP,8OAAC;oCACC,OAAO,gBAAgB,aAAa,GAAG;oCACvC,QAAQ;oCACR,UAAU;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAsF;;;;;;;;;;;0CAKxG,8OAAC;gCAAE,WAAU;0CAAiE;;;;;;0CAK9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;4CACP,SAAS,cAAc,CAAC,UAAU,eAAe;gDAAE,UAAU;4CAAS;wCACxE;kDACD;;;;;;kDAID,8OAAC;wCACC,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,SAAS,cAAc,CAAC,aAAa,eAAe;gDAAE,UAAU;4CAAS;wCAC3E;kDACD;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAET,UAAU,SAAS,OAAO;;;;;;;;;;;kCAK/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;0BAIvC,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;;;;;;;AAK7C", "debugId": null}}]}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - 3D Portfolio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            position: relative;
            z-index: 10;
        }

        .circuit-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            background-image: 
                linear-gradient(90deg, #00ff88 1px, transparent 1px),
                linear-gradient(180deg, #00ff88 1px, transparent 1px);
            background-size: 50px 50px;
            animation: circuit-flow 20s linear infinite;
        }

        @keyframes circuit-flow {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .chip {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: linear-gradient(45deg, #00ff88, #0088ff);
            border-radius: 12px;
            position: relative;
            animation: chip-glow 2s ease-in-out infinite alternate;
        }

        .chip::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            background: #1a1a1a;
            border-radius: 8px;
        }

        .chip::after {
            content: '⚡';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            z-index: 1;
        }

        @keyframes chip-glow {
            0% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.3); }
            100% { box-shadow: 0 0 40px rgba(0, 255, 136, 0.6); }
        }

        h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00ff88, #0088ff, #ff6b00);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: text-glow 3s ease-in-out infinite;
        }

        @keyframes text-glow {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.2); }
        }

        p {
            font-size: 1.2rem;
            color: #cccccc;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .status-dot {
            width: 12px;
            height: 12px;
            background: #ff6b00;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .retry-btn {
            background: linear-gradient(45deg, #00ff88, #0088ff);
            color: #000;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 1rem;
        }

        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 255, 136, 0.3);
        }

        .home-btn {
            background: transparent;
            color: #00ff88;
            border: 2px solid #00ff88;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .home-btn:hover {
            background: #00ff88;
            color: #000;
            transform: translateY(-2px);
        }

        .features {
            margin-top: 3rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .feature {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(0, 255, 136, 0.2);
        }

        .feature h3 {
            color: #00ff88;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .feature p {
            font-size: 0.9rem;
            color: #aaa;
            margin: 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            p {
                font-size: 1rem;
            }
            
            .retry-btn, .home-btn {
                display: block;
                width: 100%;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="circuit-bg"></div>
    
    <div class="container">
        <div class="chip"></div>
        
        <h1>You're Offline</h1>
        
        <p>
            It looks like you've lost your internet connection. Don't worry - some features 
            of this 3D portfolio are still available offline thanks to advanced caching.
        </p>
        
        <div class="status">
            <div class="status-dot"></div>
            <span>Offline Mode Active</span>
        </div>
        
        <div>
            <button class="retry-btn" onclick="checkConnection()">
                🔄 Retry Connection
            </button>
            <a href="/" class="home-btn">🏠 Go Home</a>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🎨 Cached Content</h3>
                <p>Previously viewed pages and images are still available</p>
            </div>
            <div class="feature">
                <h3>⚡ Fast Loading</h3>
                <p>Cached resources load instantly without internet</p>
            </div>
            <div class="feature">
                <h3>🔄 Auto Sync</h3>
                <p>Content will sync automatically when connection returns</p>
            </div>
        </div>
    </div>

    <script>
        function checkConnection() {
            const button = document.querySelector('.retry-btn');
            const originalText = button.textContent;
            
            button.textContent = '🔄 Checking...';
            button.disabled = true;
            
            // Try to fetch a small resource to test connectivity
            fetch('/', { method: 'HEAD', cache: 'no-cache' })
                .then(response => {
                    if (response.ok) {
                        button.textContent = '✅ Connected!';
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        throw new Error('No connection');
                    }
                })
                .catch(() => {
                    button.textContent = '❌ Still Offline';
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.disabled = false;
                    }, 2000);
                });
        }

        // Auto-check connection every 30 seconds
        setInterval(() => {
            if (navigator.onLine) {
                fetch('/', { method: 'HEAD', cache: 'no-cache' })
                    .then(response => {
                        if (response.ok) {
                            // Show reconnection notification
                            const status = document.querySelector('.status');
                            status.innerHTML = '<div style="width: 12px; height: 12px; background: #00ff88; border-radius: 50%;"></div><span>Back Online!</span>';
                            
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        }
                    })
                    .catch(() => {
                        // Still offline
                    });
            }
        }, 30000);

        // Listen for online/offline events
        window.addEventListener('online', () => {
            checkConnection();
        });

        window.addEventListener('offline', () => {
            const status = document.querySelector('.status');
            status.innerHTML = '<div class="status-dot"></div><span>Connection Lost</span>';
        });
    </script>
</body>
</html>

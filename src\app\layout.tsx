import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ui/ThemeProvider";
import { Navigation, FloatingActionButton } from "@/components/ui/Navigation";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "3D Portfolio - Interactive Microchip Experience",
  description: "A cutting-edge 3D portfolio website featuring interactive microchip designs and immersive user experience.",
  keywords: ["portfolio", "3D", "microchip", "interactive", "web development", "three.js"],
  authors: [{ name: "Portfolio Developer" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#00ff88" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider>
          <Navigation />
          {children}
          <FloatingActionButton />
        </ThemeProvider>
      </body>
    </html>
  );
}

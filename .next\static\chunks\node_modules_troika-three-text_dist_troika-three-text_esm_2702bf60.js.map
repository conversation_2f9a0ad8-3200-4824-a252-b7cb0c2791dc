{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/troika-three-text/dist/troika-three-text.esm.js"], "sourcesContent": ["import { Texture, LinearFilter, Color, InstancedBufferGeometry, Sphere, Box3, InstancedBufferAttribute, PlaneGeometry, Vector2, Vector4, Matrix3, Mesh, MeshBasicMaterial, DoubleSide, Matrix4, Vector3, DataTexture, RGBAFormat, FloatType, DynamicDrawUsage } from 'three';\nimport { defineWorkerModule, terminateWorker } from 'troika-worker-utils';\nimport createSDFGenerator from 'webgl-sdf-generator';\nimport bidiFactory from 'bidi-js';\nimport { createDerivedMaterial, voidMainRegExp } from 'troika-three-utils';\n\n/*!\nCustom build of Typr.ts (https://github.com/fredli74/Typr.ts) for use in Troika text rendering.\nOriginal MIT license applies: https://github.com/fredli74/Typr.ts/blob/master/LICENSE\n*/\nfunction typrFactory(){return \"undefined\"==typeof window&&(self.window=self),function(r){var e={parse:function(r){var t=e._bin,a=new Uint8Array(r);if(\"ttcf\"==t.readASCII(a,0,4)){var n=4;t.readUshort(a,n),n+=2,t.readUshort(a,n),n+=2;var o=t.readUint(a,n);n+=4;for(var s=[],i=0;i<o;i++){var h=t.readUint(a,n);n+=4,s.push(e._readFont(a,h));}return s}return [e._readFont(a,0)]},_readFont:function(r,t){var a=e._bin,n=t;a.readFixed(r,t),t+=4;var o=a.readUshort(r,t);t+=2,a.readUshort(r,t),t+=2,a.readUshort(r,t),t+=2,a.readUshort(r,t),t+=2;for(var s=[\"cmap\",\"head\",\"hhea\",\"maxp\",\"hmtx\",\"name\",\"OS/2\",\"post\",\"loca\",\"glyf\",\"kern\",\"CFF \",\"GDEF\",\"GPOS\",\"GSUB\",\"SVG \"],i={_data:r,_offset:n},h={},d=0;d<o;d++){var f=a.readASCII(r,t,4);t+=4,a.readUint(r,t),t+=4;var u=a.readUint(r,t);t+=4;var l=a.readUint(r,t);t+=4,h[f]={offset:u,length:l};}for(d=0;d<s.length;d++){var v=s[d];h[v]&&(i[v.trim()]=e[v.trim()].parse(r,h[v].offset,h[v].length,i));}return i},_tabOffset:function(r,t,a){for(var n=e._bin,o=n.readUshort(r,a+4),s=a+12,i=0;i<o;i++){var h=n.readASCII(r,s,4);s+=4,n.readUint(r,s),s+=4;var d=n.readUint(r,s);if(s+=4,n.readUint(r,s),s+=4,h==t)return d}return 0}};e._bin={readFixed:function(r,e){return (r[e]<<8|r[e+1])+(r[e+2]<<8|r[e+3])/65540},readF2dot14:function(r,t){return e._bin.readShort(r,t)/16384},readInt:function(r,t){return e._bin._view(r).getInt32(t)},readInt8:function(r,t){return e._bin._view(r).getInt8(t)},readShort:function(r,t){return e._bin._view(r).getInt16(t)},readUshort:function(r,t){return e._bin._view(r).getUint16(t)},readUshorts:function(r,t,a){for(var n=[],o=0;o<a;o++)n.push(e._bin.readUshort(r,t+2*o));return n},readUint:function(r,t){return e._bin._view(r).getUint32(t)},readUint64:function(r,t){return 4294967296*e._bin.readUint(r,t)+e._bin.readUint(r,t+4)},readASCII:function(r,e,t){for(var a=\"\",n=0;n<t;n++)a+=String.fromCharCode(r[e+n]);return a},readUnicode:function(r,e,t){for(var a=\"\",n=0;n<t;n++){var o=r[e++]<<8|r[e++];a+=String.fromCharCode(o);}return a},_tdec:\"undefined\"!=typeof window&&window.TextDecoder?new window.TextDecoder:null,readUTF8:function(r,t,a){var n=e._bin._tdec;return n&&0==t&&a==r.length?n.decode(r):e._bin.readASCII(r,t,a)},readBytes:function(r,e,t){for(var a=[],n=0;n<t;n++)a.push(r[e+n]);return a},readASCIIArray:function(r,e,t){for(var a=[],n=0;n<t;n++)a.push(String.fromCharCode(r[e+n]));return a},_view:function(r){return r._dataView||(r._dataView=r.buffer?new DataView(r.buffer,r.byteOffset,r.byteLength):new DataView(new Uint8Array(r).buffer))}},e._lctf={},e._lctf.parse=function(r,t,a,n,o){var s=e._bin,i={},h=t;s.readFixed(r,t),t+=4;var d=s.readUshort(r,t);t+=2;var f=s.readUshort(r,t);t+=2;var u=s.readUshort(r,t);return t+=2,i.scriptList=e._lctf.readScriptList(r,h+d),i.featureList=e._lctf.readFeatureList(r,h+f),i.lookupList=e._lctf.readLookupList(r,h+u,o),i},e._lctf.readLookupList=function(r,t,a){var n=e._bin,o=t,s=[],i=n.readUshort(r,t);t+=2;for(var h=0;h<i;h++){var d=n.readUshort(r,t);t+=2;var f=e._lctf.readLookupTable(r,o+d,a);s.push(f);}return s},e._lctf.readLookupTable=function(r,t,a){var n=e._bin,o=t,s={tabs:[]};s.ltype=n.readUshort(r,t),t+=2,s.flag=n.readUshort(r,t),t+=2;var i=n.readUshort(r,t);t+=2;for(var h=s.ltype,d=0;d<i;d++){var f=n.readUshort(r,t);t+=2;var u=a(r,h,o+f,s);s.tabs.push(u);}return s},e._lctf.numOfOnes=function(r){for(var e=0,t=0;t<32;t++)0!=(r>>>t&1)&&e++;return e},e._lctf.readClassDef=function(r,t){var a=e._bin,n=[],o=a.readUshort(r,t);if(t+=2,1==o){var s=a.readUshort(r,t);t+=2;var i=a.readUshort(r,t);t+=2;for(var h=0;h<i;h++)n.push(s+h),n.push(s+h),n.push(a.readUshort(r,t)),t+=2;}if(2==o){var d=a.readUshort(r,t);t+=2;for(h=0;h<d;h++)n.push(a.readUshort(r,t)),t+=2,n.push(a.readUshort(r,t)),t+=2,n.push(a.readUshort(r,t)),t+=2;}return n},e._lctf.getInterval=function(r,e){for(var t=0;t<r.length;t+=3){var a=r[t],n=r[t+1];if(r[t+2],a<=e&&e<=n)return t}return -1},e._lctf.readCoverage=function(r,t){var a=e._bin,n={};n.fmt=a.readUshort(r,t),t+=2;var o=a.readUshort(r,t);return t+=2,1==n.fmt&&(n.tab=a.readUshorts(r,t,o)),2==n.fmt&&(n.tab=a.readUshorts(r,t,3*o)),n},e._lctf.coverageIndex=function(r,t){var a=r.tab;if(1==r.fmt)return a.indexOf(t);if(2==r.fmt){var n=e._lctf.getInterval(a,t);if(-1!=n)return a[n+2]+(t-a[n])}return -1},e._lctf.readFeatureList=function(r,t){var a=e._bin,n=t,o=[],s=a.readUshort(r,t);t+=2;for(var i=0;i<s;i++){var h=a.readASCII(r,t,4);t+=4;var d=a.readUshort(r,t);t+=2;var f=e._lctf.readFeatureTable(r,n+d);f.tag=h.trim(),o.push(f);}return o},e._lctf.readFeatureTable=function(r,t){var a=e._bin,n=t,o={},s=a.readUshort(r,t);t+=2,s>0&&(o.featureParams=n+s);var i=a.readUshort(r,t);t+=2,o.tab=[];for(var h=0;h<i;h++)o.tab.push(a.readUshort(r,t+2*h));return o},e._lctf.readScriptList=function(r,t){var a=e._bin,n=t,o={},s=a.readUshort(r,t);t+=2;for(var i=0;i<s;i++){var h=a.readASCII(r,t,4);t+=4;var d=a.readUshort(r,t);t+=2,o[h.trim()]=e._lctf.readScriptTable(r,n+d);}return o},e._lctf.readScriptTable=function(r,t){var a=e._bin,n=t,o={},s=a.readUshort(r,t);t+=2,s>0&&(o.default=e._lctf.readLangSysTable(r,n+s));var i=a.readUshort(r,t);t+=2;for(var h=0;h<i;h++){var d=a.readASCII(r,t,4);t+=4;var f=a.readUshort(r,t);t+=2,o[d.trim()]=e._lctf.readLangSysTable(r,n+f);}return o},e._lctf.readLangSysTable=function(r,t){var a=e._bin,n={};a.readUshort(r,t),t+=2,n.reqFeature=a.readUshort(r,t),t+=2;var o=a.readUshort(r,t);return t+=2,n.features=a.readUshorts(r,t,o),n},e.CFF={},e.CFF.parse=function(r,t,a){var n=e._bin;(r=new Uint8Array(r.buffer,t,a))[t=0],r[++t],r[++t],r[++t],t++;var o=[];t=e.CFF.readIndex(r,t,o);for(var s=[],i=0;i<o.length-1;i++)s.push(n.readASCII(r,t+o[i],o[i+1]-o[i]));t+=o[o.length-1];var h=[];t=e.CFF.readIndex(r,t,h);var d=[];for(i=0;i<h.length-1;i++)d.push(e.CFF.readDict(r,t+h[i],t+h[i+1]));t+=h[h.length-1];var f=d[0],u=[];t=e.CFF.readIndex(r,t,u);var l=[];for(i=0;i<u.length-1;i++)l.push(n.readASCII(r,t+u[i],u[i+1]-u[i]));if(t+=u[u.length-1],e.CFF.readSubrs(r,t,f),f.CharStrings){t=f.CharStrings;u=[];t=e.CFF.readIndex(r,t,u);var v=[];for(i=0;i<u.length-1;i++)v.push(n.readBytes(r,t+u[i],u[i+1]-u[i]));f.CharStrings=v;}if(f.ROS){t=f.FDArray;var c=[];t=e.CFF.readIndex(r,t,c),f.FDArray=[];for(i=0;i<c.length-1;i++){var p=e.CFF.readDict(r,t+c[i],t+c[i+1]);e.CFF._readFDict(r,p,l),f.FDArray.push(p);}t+=c[c.length-1],t=f.FDSelect,f.FDSelect=[];var U=r[t];if(t++,3!=U)throw U;var g=n.readUshort(r,t);t+=2;for(i=0;i<g+1;i++)f.FDSelect.push(n.readUshort(r,t),r[t+2]),t+=3;}return f.Encoding&&(f.Encoding=e.CFF.readEncoding(r,f.Encoding,f.CharStrings.length)),f.charset&&(f.charset=e.CFF.readCharset(r,f.charset,f.CharStrings.length)),e.CFF._readFDict(r,f,l),f},e.CFF._readFDict=function(r,t,a){var n;for(var o in t.Private&&(n=t.Private[1],t.Private=e.CFF.readDict(r,n,n+t.Private[0]),t.Private.Subrs&&e.CFF.readSubrs(r,n+t.Private.Subrs,t.Private)),t)-1!=[\"FamilyName\",\"FontName\",\"FullName\",\"Notice\",\"version\",\"Copyright\"].indexOf(o)&&(t[o]=a[t[o]-426+35]);},e.CFF.readSubrs=function(r,t,a){var n=e._bin,o=[];t=e.CFF.readIndex(r,t,o);var s,i=o.length;s=i<1240?107:i<33900?1131:32768,a.Bias=s,a.Subrs=[];for(var h=0;h<o.length-1;h++)a.Subrs.push(n.readBytes(r,t+o[h],o[h+1]-o[h]));},e.CFF.tableSE=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,0,111,112,113,114,0,115,116,117,118,119,120,121,122,0,123,0,124,125,126,127,128,129,130,131,0,132,133,0,134,135,136,137,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,138,0,139,0,0,0,0,140,141,142,143,0,0,0,0,0,144,0,0,0,145,0,0,146,147,148,149,0,0,0,0],e.CFF.glyphByUnicode=function(r,e){for(var t=0;t<r.charset.length;t++)if(r.charset[t]==e)return t;return -1},e.CFF.glyphBySE=function(r,t){return t<0||t>255?-1:e.CFF.glyphByUnicode(r,e.CFF.tableSE[t])},e.CFF.readEncoding=function(r,t,a){e._bin;var n=[\".notdef\"],o=r[t];if(t++,0!=o)throw \"error: unknown encoding format: \"+o;var s=r[t];t++;for(var i=0;i<s;i++)n.push(r[t+i]);return n},e.CFF.readCharset=function(r,t,a){var n=e._bin,o=[\".notdef\"],s=r[t];if(t++,0==s)for(var i=0;i<a;i++){var h=n.readUshort(r,t);t+=2,o.push(h);}else {if(1!=s&&2!=s)throw \"error: format: \"+s;for(;o.length<a;){h=n.readUshort(r,t);t+=2;var d=0;1==s?(d=r[t],t++):(d=n.readUshort(r,t),t+=2);for(i=0;i<=d;i++)o.push(h),h++;}}return o},e.CFF.readIndex=function(r,t,a){var n=e._bin,o=n.readUshort(r,t)+1,s=r[t+=2];if(t++,1==s)for(var i=0;i<o;i++)a.push(r[t+i]);else if(2==s)for(i=0;i<o;i++)a.push(n.readUshort(r,t+2*i));else if(3==s)for(i=0;i<o;i++)a.push(16777215&n.readUint(r,t+3*i-1));else if(1!=o)throw \"unsupported offset size: \"+s+\", count: \"+o;return (t+=o*s)-1},e.CFF.getCharString=function(r,t,a){var n=e._bin,o=r[t],s=r[t+1];r[t+2],r[t+3],r[t+4];var i=1,h=null,d=null;o<=20&&(h=o,i=1),12==o&&(h=100*o+s,i=2),21<=o&&o<=27&&(h=o,i=1),28==o&&(d=n.readShort(r,t+1),i=3),29<=o&&o<=31&&(h=o,i=1),32<=o&&o<=246&&(d=o-139,i=1),247<=o&&o<=250&&(d=256*(o-247)+s+108,i=2),251<=o&&o<=254&&(d=256*-(o-251)-s-108,i=2),255==o&&(d=n.readInt(r,t+1)/65535,i=5),a.val=null!=d?d:\"o\"+h,a.size=i;},e.CFF.readCharString=function(r,t,a){for(var n=t+a,o=e._bin,s=[];t<n;){var i=r[t],h=r[t+1];r[t+2],r[t+3],r[t+4];var d=1,f=null,u=null;i<=20&&(f=i,d=1),12==i&&(f=100*i+h,d=2),19!=i&&20!=i||(f=i,d=2),21<=i&&i<=27&&(f=i,d=1),28==i&&(u=o.readShort(r,t+1),d=3),29<=i&&i<=31&&(f=i,d=1),32<=i&&i<=246&&(u=i-139,d=1),247<=i&&i<=250&&(u=256*(i-247)+h+108,d=2),251<=i&&i<=254&&(u=256*-(i-251)-h-108,d=2),255==i&&(u=o.readInt(r,t+1)/65535,d=5),s.push(null!=u?u:\"o\"+f),t+=d;}return s},e.CFF.readDict=function(r,t,a){for(var n=e._bin,o={},s=[];t<a;){var i=r[t],h=r[t+1];r[t+2],r[t+3],r[t+4];var d=1,f=null,u=null;if(28==i&&(u=n.readShort(r,t+1),d=3),29==i&&(u=n.readInt(r,t+1),d=5),32<=i&&i<=246&&(u=i-139,d=1),247<=i&&i<=250&&(u=256*(i-247)+h+108,d=2),251<=i&&i<=254&&(u=256*-(i-251)-h-108,d=2),255==i)throw u=n.readInt(r,t+1)/65535,d=5,\"unknown number\";if(30==i){var l=[];for(d=1;;){var v=r[t+d];d++;var c=v>>4,p=15&v;if(15!=c&&l.push(c),15!=p&&l.push(p),15==p)break}for(var U=\"\",g=[0,1,2,3,4,5,6,7,8,9,\".\",\"e\",\"e-\",\"reserved\",\"-\",\"endOfNumber\"],S=0;S<l.length;S++)U+=g[l[S]];u=parseFloat(U);}if(i<=21)if(f=[\"version\",\"Notice\",\"FullName\",\"FamilyName\",\"Weight\",\"FontBBox\",\"BlueValues\",\"OtherBlues\",\"FamilyBlues\",\"FamilyOtherBlues\",\"StdHW\",\"StdVW\",\"escape\",\"UniqueID\",\"XUID\",\"charset\",\"Encoding\",\"CharStrings\",\"Private\",\"Subrs\",\"defaultWidthX\",\"nominalWidthX\"][i],d=1,12==i)f=[\"Copyright\",\"isFixedPitch\",\"ItalicAngle\",\"UnderlinePosition\",\"UnderlineThickness\",\"PaintType\",\"CharstringType\",\"FontMatrix\",\"StrokeWidth\",\"BlueScale\",\"BlueShift\",\"BlueFuzz\",\"StemSnapH\",\"StemSnapV\",\"ForceBold\",0,0,\"LanguageGroup\",\"ExpansionFactor\",\"initialRandomSeed\",\"SyntheticBase\",\"PostScript\",\"BaseFontName\",\"BaseFontBlend\",0,0,0,0,0,0,\"ROS\",\"CIDFontVersion\",\"CIDFontRevision\",\"CIDFontType\",\"CIDCount\",\"UIDBase\",\"FDArray\",\"FDSelect\",\"FontName\"][h],d=2;null!=f?(o[f]=1==s.length?s[0]:s,s=[]):s.push(u),t+=d;}return o},e.cmap={},e.cmap.parse=function(r,t,a){r=new Uint8Array(r.buffer,t,a),t=0;var n=e._bin,o={};n.readUshort(r,t),t+=2;var s=n.readUshort(r,t);t+=2;var i=[];o.tables=[];for(var h=0;h<s;h++){var d=n.readUshort(r,t);t+=2;var f=n.readUshort(r,t);t+=2;var u=n.readUint(r,t);t+=4;var l=\"p\"+d+\"e\"+f,v=i.indexOf(u);if(-1==v){var c;v=o.tables.length,i.push(u);var p=n.readUshort(r,u);0==p?c=e.cmap.parse0(r,u):4==p?c=e.cmap.parse4(r,u):6==p?c=e.cmap.parse6(r,u):12==p?c=e.cmap.parse12(r,u):console.debug(\"unknown format: \"+p,d,f,u),o.tables.push(c);}if(null!=o[l])throw \"multiple tables for one platform+encoding\";o[l]=v;}return o},e.cmap.parse0=function(r,t){var a=e._bin,n={};n.format=a.readUshort(r,t),t+=2;var o=a.readUshort(r,t);t+=2,a.readUshort(r,t),t+=2,n.map=[];for(var s=0;s<o-6;s++)n.map.push(r[t+s]);return n},e.cmap.parse4=function(r,t){var a=e._bin,n=t,o={};o.format=a.readUshort(r,t),t+=2;var s=a.readUshort(r,t);t+=2,a.readUshort(r,t),t+=2;var i=a.readUshort(r,t);t+=2;var h=i/2;o.searchRange=a.readUshort(r,t),t+=2,o.entrySelector=a.readUshort(r,t),t+=2,o.rangeShift=a.readUshort(r,t),t+=2,o.endCount=a.readUshorts(r,t,h),t+=2*h,t+=2,o.startCount=a.readUshorts(r,t,h),t+=2*h,o.idDelta=[];for(var d=0;d<h;d++)o.idDelta.push(a.readShort(r,t)),t+=2;for(o.idRangeOffset=a.readUshorts(r,t,h),t+=2*h,o.glyphIdArray=[];t<n+s;)o.glyphIdArray.push(a.readUshort(r,t)),t+=2;return o},e.cmap.parse6=function(r,t){var a=e._bin,n={};n.format=a.readUshort(r,t),t+=2,a.readUshort(r,t),t+=2,a.readUshort(r,t),t+=2,n.firstCode=a.readUshort(r,t),t+=2;var o=a.readUshort(r,t);t+=2,n.glyphIdArray=[];for(var s=0;s<o;s++)n.glyphIdArray.push(a.readUshort(r,t)),t+=2;return n},e.cmap.parse12=function(r,t){var a=e._bin,n={};n.format=a.readUshort(r,t),t+=2,t+=2,a.readUint(r,t),t+=4,a.readUint(r,t),t+=4;var o=a.readUint(r,t);t+=4,n.groups=[];for(var s=0;s<o;s++){var i=t+12*s,h=a.readUint(r,i+0),d=a.readUint(r,i+4),f=a.readUint(r,i+8);n.groups.push([h,d,f]);}return n},e.glyf={},e.glyf.parse=function(r,e,t,a){for(var n=[],o=0;o<a.maxp.numGlyphs;o++)n.push(null);return n},e.glyf._parseGlyf=function(r,t){var a=e._bin,n=r._data,o=e._tabOffset(n,\"glyf\",r._offset)+r.loca[t];if(r.loca[t]==r.loca[t+1])return null;var s={};if(s.noc=a.readShort(n,o),o+=2,s.xMin=a.readShort(n,o),o+=2,s.yMin=a.readShort(n,o),o+=2,s.xMax=a.readShort(n,o),o+=2,s.yMax=a.readShort(n,o),o+=2,s.xMin>=s.xMax||s.yMin>=s.yMax)return null;if(s.noc>0){s.endPts=[];for(var i=0;i<s.noc;i++)s.endPts.push(a.readUshort(n,o)),o+=2;var h=a.readUshort(n,o);if(o+=2,n.length-o<h)return null;s.instructions=a.readBytes(n,o,h),o+=h;var d=s.endPts[s.noc-1]+1;s.flags=[];for(i=0;i<d;i++){var f=n[o];if(o++,s.flags.push(f),0!=(8&f)){var u=n[o];o++;for(var l=0;l<u;l++)s.flags.push(f),i++;}}s.xs=[];for(i=0;i<d;i++){var v=0!=(2&s.flags[i]),c=0!=(16&s.flags[i]);v?(s.xs.push(c?n[o]:-n[o]),o++):c?s.xs.push(0):(s.xs.push(a.readShort(n,o)),o+=2);}s.ys=[];for(i=0;i<d;i++){v=0!=(4&s.flags[i]),c=0!=(32&s.flags[i]);v?(s.ys.push(c?n[o]:-n[o]),o++):c?s.ys.push(0):(s.ys.push(a.readShort(n,o)),o+=2);}var p=0,U=0;for(i=0;i<d;i++)p+=s.xs[i],U+=s.ys[i],s.xs[i]=p,s.ys[i]=U;}else {var g;s.parts=[];do{g=a.readUshort(n,o),o+=2;var S={m:{a:1,b:0,c:0,d:1,tx:0,ty:0},p1:-1,p2:-1};if(s.parts.push(S),S.glyphIndex=a.readUshort(n,o),o+=2,1&g){var m=a.readShort(n,o);o+=2;var b=a.readShort(n,o);o+=2;}else {m=a.readInt8(n,o);o++;b=a.readInt8(n,o);o++;}2&g?(S.m.tx=m,S.m.ty=b):(S.p1=m,S.p2=b),8&g?(S.m.a=S.m.d=a.readF2dot14(n,o),o+=2):64&g?(S.m.a=a.readF2dot14(n,o),o+=2,S.m.d=a.readF2dot14(n,o),o+=2):128&g&&(S.m.a=a.readF2dot14(n,o),o+=2,S.m.b=a.readF2dot14(n,o),o+=2,S.m.c=a.readF2dot14(n,o),o+=2,S.m.d=a.readF2dot14(n,o),o+=2);}while(32&g);if(256&g){var y=a.readUshort(n,o);o+=2,s.instr=[];for(i=0;i<y;i++)s.instr.push(n[o]),o++;}}return s},e.GDEF={},e.GDEF.parse=function(r,t,a,n){var o=t;t+=4;var s=e._bin.readUshort(r,t);return {glyphClassDef:0===s?null:e._lctf.readClassDef(r,o+s)}},e.GPOS={},e.GPOS.parse=function(r,t,a,n){return e._lctf.parse(r,t,a,n,e.GPOS.subt)},e.GPOS.subt=function(r,t,a,n){var o=e._bin,s=a,i={};if(i.fmt=o.readUshort(r,a),a+=2,1==t||2==t||3==t||7==t||8==t&&i.fmt<=2){var h=o.readUshort(r,a);a+=2,i.coverage=e._lctf.readCoverage(r,h+s);}if(1==t&&1==i.fmt){var d=o.readUshort(r,a);a+=2,0!=d&&(i.pos=e.GPOS.readValueRecord(r,a,d));}else if(2==t&&i.fmt>=1&&i.fmt<=2){d=o.readUshort(r,a);a+=2;var f=o.readUshort(r,a);a+=2;var u=e._lctf.numOfOnes(d),l=e._lctf.numOfOnes(f);if(1==i.fmt){i.pairsets=[];var v=o.readUshort(r,a);a+=2;for(var c=0;c<v;c++){var p=s+o.readUshort(r,a);a+=2;var U=o.readUshort(r,p);p+=2;for(var g=[],S=0;S<U;S++){var m=o.readUshort(r,p);p+=2,0!=d&&(P=e.GPOS.readValueRecord(r,p,d),p+=2*u),0!=f&&(x=e.GPOS.readValueRecord(r,p,f),p+=2*l),g.push({gid2:m,val1:P,val2:x});}i.pairsets.push(g);}}if(2==i.fmt){var b=o.readUshort(r,a);a+=2;var y=o.readUshort(r,a);a+=2;var F=o.readUshort(r,a);a+=2;var C=o.readUshort(r,a);a+=2,i.classDef1=e._lctf.readClassDef(r,s+b),i.classDef2=e._lctf.readClassDef(r,s+y),i.matrix=[];for(c=0;c<F;c++){var _=[];for(S=0;S<C;S++){var P=null,x=null;0!=d&&(P=e.GPOS.readValueRecord(r,a,d),a+=2*u),0!=f&&(x=e.GPOS.readValueRecord(r,a,f),a+=2*l),_.push({val1:P,val2:x});}i.matrix.push(_);}}}else if(4==t&&1==i.fmt)i.markCoverage=e._lctf.readCoverage(r,o.readUshort(r,a)+s),i.baseCoverage=e._lctf.readCoverage(r,o.readUshort(r,a+2)+s),i.markClassCount=o.readUshort(r,a+4),i.markArray=e.GPOS.readMarkArray(r,o.readUshort(r,a+6)+s),i.baseArray=e.GPOS.readBaseArray(r,o.readUshort(r,a+8)+s,i.markClassCount);else if(6==t&&1==i.fmt)i.mark1Coverage=e._lctf.readCoverage(r,o.readUshort(r,a)+s),i.mark2Coverage=e._lctf.readCoverage(r,o.readUshort(r,a+2)+s),i.markClassCount=o.readUshort(r,a+4),i.mark1Array=e.GPOS.readMarkArray(r,o.readUshort(r,a+6)+s),i.mark2Array=e.GPOS.readBaseArray(r,o.readUshort(r,a+8)+s,i.markClassCount);else {if(9==t&&1==i.fmt){var I=o.readUshort(r,a);a+=2;var w=o.readUint(r,a);if(a+=4,9==n.ltype)n.ltype=I;else if(n.ltype!=I)throw \"invalid extension substitution\";return e.GPOS.subt(r,n.ltype,s+w)}console.debug(\"unsupported GPOS table LookupType\",t,\"format\",i.fmt);}return i},e.GPOS.readValueRecord=function(r,t,a){var n=e._bin,o=[];return o.push(1&a?n.readShort(r,t):0),t+=1&a?2:0,o.push(2&a?n.readShort(r,t):0),t+=2&a?2:0,o.push(4&a?n.readShort(r,t):0),t+=4&a?2:0,o.push(8&a?n.readShort(r,t):0),t+=8&a?2:0,o},e.GPOS.readBaseArray=function(r,t,a){var n=e._bin,o=[],s=t,i=n.readUshort(r,t);t+=2;for(var h=0;h<i;h++){for(var d=[],f=0;f<a;f++)d.push(e.GPOS.readAnchorRecord(r,s+n.readUshort(r,t))),t+=2;o.push(d);}return o},e.GPOS.readMarkArray=function(r,t){var a=e._bin,n=[],o=t,s=a.readUshort(r,t);t+=2;for(var i=0;i<s;i++){var h=e.GPOS.readAnchorRecord(r,a.readUshort(r,t+2)+o);h.markClass=a.readUshort(r,t),n.push(h),t+=4;}return n},e.GPOS.readAnchorRecord=function(r,t){var a=e._bin,n={};return n.fmt=a.readUshort(r,t),n.x=a.readShort(r,t+2),n.y=a.readShort(r,t+4),n},e.GSUB={},e.GSUB.parse=function(r,t,a,n){return e._lctf.parse(r,t,a,n,e.GSUB.subt)},e.GSUB.subt=function(r,t,a,n){var o=e._bin,s=a,i={};if(i.fmt=o.readUshort(r,a),a+=2,1!=t&&2!=t&&4!=t&&5!=t&&6!=t)return null;if(1==t||2==t||4==t||5==t&&i.fmt<=2||6==t&&i.fmt<=2){var h=o.readUshort(r,a);a+=2,i.coverage=e._lctf.readCoverage(r,s+h);}if(1==t&&i.fmt>=1&&i.fmt<=2){if(1==i.fmt)i.delta=o.readShort(r,a),a+=2;else if(2==i.fmt){var d=o.readUshort(r,a);a+=2,i.newg=o.readUshorts(r,a,d),a+=2*i.newg.length;}}else if(2==t&&1==i.fmt){d=o.readUshort(r,a);a+=2,i.seqs=[];for(var f=0;f<d;f++){var u=o.readUshort(r,a)+s;a+=2;var l=o.readUshort(r,u);i.seqs.push(o.readUshorts(r,u+2,l));}}else if(4==t){i.vals=[];d=o.readUshort(r,a);a+=2;for(f=0;f<d;f++){var v=o.readUshort(r,a);a+=2,i.vals.push(e.GSUB.readLigatureSet(r,s+v));}}else if(5==t&&2==i.fmt){if(2==i.fmt){var c=o.readUshort(r,a);a+=2,i.cDef=e._lctf.readClassDef(r,s+c),i.scset=[];var p=o.readUshort(r,a);a+=2;for(f=0;f<p;f++){var U=o.readUshort(r,a);a+=2,i.scset.push(0==U?null:e.GSUB.readSubClassSet(r,s+U));}}}else if(6==t&&3==i.fmt){if(3==i.fmt){for(f=0;f<3;f++){d=o.readUshort(r,a);a+=2;for(var g=[],S=0;S<d;S++)g.push(e._lctf.readCoverage(r,s+o.readUshort(r,a+2*S)));a+=2*d,0==f&&(i.backCvg=g),1==f&&(i.inptCvg=g),2==f&&(i.ahedCvg=g);}d=o.readUshort(r,a);a+=2,i.lookupRec=e.GSUB.readSubstLookupRecords(r,a,d);}}else {if(7==t&&1==i.fmt){var m=o.readUshort(r,a);a+=2;var b=o.readUint(r,a);if(a+=4,9==n.ltype)n.ltype=m;else if(n.ltype!=m)throw \"invalid extension substitution\";return e.GSUB.subt(r,n.ltype,s+b)}console.debug(\"unsupported GSUB table LookupType\",t,\"format\",i.fmt);}return i},e.GSUB.readSubClassSet=function(r,t){var a=e._bin.readUshort,n=t,o=[],s=a(r,t);t+=2;for(var i=0;i<s;i++){var h=a(r,t);t+=2,o.push(e.GSUB.readSubClassRule(r,n+h));}return o},e.GSUB.readSubClassRule=function(r,t){var a=e._bin.readUshort,n={},o=a(r,t),s=a(r,t+=2);t+=2,n.input=[];for(var i=0;i<o-1;i++)n.input.push(a(r,t)),t+=2;return n.substLookupRecords=e.GSUB.readSubstLookupRecords(r,t,s),n},e.GSUB.readSubstLookupRecords=function(r,t,a){for(var n=e._bin.readUshort,o=[],s=0;s<a;s++)o.push(n(r,t),n(r,t+2)),t+=4;return o},e.GSUB.readChainSubClassSet=function(r,t){var a=e._bin,n=t,o=[],s=a.readUshort(r,t);t+=2;for(var i=0;i<s;i++){var h=a.readUshort(r,t);t+=2,o.push(e.GSUB.readChainSubClassRule(r,n+h));}return o},e.GSUB.readChainSubClassRule=function(r,t){for(var a=e._bin,n={},o=[\"backtrack\",\"input\",\"lookahead\"],s=0;s<o.length;s++){var i=a.readUshort(r,t);t+=2,1==s&&i--,n[o[s]]=a.readUshorts(r,t,i),t+=2*n[o[s]].length;}i=a.readUshort(r,t);return t+=2,n.subst=a.readUshorts(r,t,2*i),t+=2*n.subst.length,n},e.GSUB.readLigatureSet=function(r,t){var a=e._bin,n=t,o=[],s=a.readUshort(r,t);t+=2;for(var i=0;i<s;i++){var h=a.readUshort(r,t);t+=2,o.push(e.GSUB.readLigature(r,n+h));}return o},e.GSUB.readLigature=function(r,t){var a=e._bin,n={chain:[]};n.nglyph=a.readUshort(r,t),t+=2;var o=a.readUshort(r,t);t+=2;for(var s=0;s<o-1;s++)n.chain.push(a.readUshort(r,t)),t+=2;return n},e.head={},e.head.parse=function(r,t,a){var n=e._bin,o={};return n.readFixed(r,t),t+=4,o.fontRevision=n.readFixed(r,t),t+=4,n.readUint(r,t),t+=4,n.readUint(r,t),t+=4,o.flags=n.readUshort(r,t),t+=2,o.unitsPerEm=n.readUshort(r,t),t+=2,o.created=n.readUint64(r,t),t+=8,o.modified=n.readUint64(r,t),t+=8,o.xMin=n.readShort(r,t),t+=2,o.yMin=n.readShort(r,t),t+=2,o.xMax=n.readShort(r,t),t+=2,o.yMax=n.readShort(r,t),t+=2,o.macStyle=n.readUshort(r,t),t+=2,o.lowestRecPPEM=n.readUshort(r,t),t+=2,o.fontDirectionHint=n.readShort(r,t),t+=2,o.indexToLocFormat=n.readShort(r,t),t+=2,o.glyphDataFormat=n.readShort(r,t),t+=2,o},e.hhea={},e.hhea.parse=function(r,t,a){var n=e._bin,o={};return n.readFixed(r,t),t+=4,o.ascender=n.readShort(r,t),t+=2,o.descender=n.readShort(r,t),t+=2,o.lineGap=n.readShort(r,t),t+=2,o.advanceWidthMax=n.readUshort(r,t),t+=2,o.minLeftSideBearing=n.readShort(r,t),t+=2,o.minRightSideBearing=n.readShort(r,t),t+=2,o.xMaxExtent=n.readShort(r,t),t+=2,o.caretSlopeRise=n.readShort(r,t),t+=2,o.caretSlopeRun=n.readShort(r,t),t+=2,o.caretOffset=n.readShort(r,t),t+=2,t+=8,o.metricDataFormat=n.readShort(r,t),t+=2,o.numberOfHMetrics=n.readUshort(r,t),t+=2,o},e.hmtx={},e.hmtx.parse=function(r,t,a,n){for(var o=e._bin,s={aWidth:[],lsBearing:[]},i=0,h=0,d=0;d<n.maxp.numGlyphs;d++)d<n.hhea.numberOfHMetrics&&(i=o.readUshort(r,t),t+=2,h=o.readShort(r,t),t+=2),s.aWidth.push(i),s.lsBearing.push(h);return s},e.kern={},e.kern.parse=function(r,t,a,n){var o=e._bin,s=o.readUshort(r,t);if(t+=2,1==s)return e.kern.parseV1(r,t-2,a,n);var i=o.readUshort(r,t);t+=2;for(var h={glyph1:[],rval:[]},d=0;d<i;d++){t+=2;a=o.readUshort(r,t);t+=2;var f=o.readUshort(r,t);t+=2;var u=f>>>8;if(0!=(u&=15))throw \"unknown kern table format: \"+u;t=e.kern.readFormat0(r,t,h);}return h},e.kern.parseV1=function(r,t,a,n){var o=e._bin;o.readFixed(r,t),t+=4;var s=o.readUint(r,t);t+=4;for(var i={glyph1:[],rval:[]},h=0;h<s;h++){o.readUint(r,t),t+=4;var d=o.readUshort(r,t);t+=2,o.readUshort(r,t),t+=2;var f=d>>>8;if(0!=(f&=15))throw \"unknown kern table format: \"+f;t=e.kern.readFormat0(r,t,i);}return i},e.kern.readFormat0=function(r,t,a){var n=e._bin,o=-1,s=n.readUshort(r,t);t+=2,n.readUshort(r,t),t+=2,n.readUshort(r,t),t+=2,n.readUshort(r,t),t+=2;for(var i=0;i<s;i++){var h=n.readUshort(r,t);t+=2;var d=n.readUshort(r,t);t+=2;var f=n.readShort(r,t);t+=2,h!=o&&(a.glyph1.push(h),a.rval.push({glyph2:[],vals:[]}));var u=a.rval[a.rval.length-1];u.glyph2.push(d),u.vals.push(f),o=h;}return t},e.loca={},e.loca.parse=function(r,t,a,n){var o=e._bin,s=[],i=n.head.indexToLocFormat,h=n.maxp.numGlyphs+1;if(0==i)for(var d=0;d<h;d++)s.push(o.readUshort(r,t+(d<<1))<<1);if(1==i)for(d=0;d<h;d++)s.push(o.readUint(r,t+(d<<2)));return s},e.maxp={},e.maxp.parse=function(r,t,a){var n=e._bin,o={},s=n.readUint(r,t);return t+=4,o.numGlyphs=n.readUshort(r,t),t+=2,65536==s&&(o.maxPoints=n.readUshort(r,t),t+=2,o.maxContours=n.readUshort(r,t),t+=2,o.maxCompositePoints=n.readUshort(r,t),t+=2,o.maxCompositeContours=n.readUshort(r,t),t+=2,o.maxZones=n.readUshort(r,t),t+=2,o.maxTwilightPoints=n.readUshort(r,t),t+=2,o.maxStorage=n.readUshort(r,t),t+=2,o.maxFunctionDefs=n.readUshort(r,t),t+=2,o.maxInstructionDefs=n.readUshort(r,t),t+=2,o.maxStackElements=n.readUshort(r,t),t+=2,o.maxSizeOfInstructions=n.readUshort(r,t),t+=2,o.maxComponentElements=n.readUshort(r,t),t+=2,o.maxComponentDepth=n.readUshort(r,t),t+=2),o},e.name={},e.name.parse=function(r,t,a){var n=e._bin,o={};n.readUshort(r,t),t+=2;var s=n.readUshort(r,t);t+=2,n.readUshort(r,t);for(var i,h=[\"copyright\",\"fontFamily\",\"fontSubfamily\",\"ID\",\"fullName\",\"version\",\"postScriptName\",\"trademark\",\"manufacturer\",\"designer\",\"description\",\"urlVendor\",\"urlDesigner\",\"licence\",\"licenceURL\",\"---\",\"typoFamilyName\",\"typoSubfamilyName\",\"compatibleFull\",\"sampleText\",\"postScriptCID\",\"wwsFamilyName\",\"wwsSubfamilyName\",\"lightPalette\",\"darkPalette\"],d=t+=2,f=0;f<s;f++){var u=n.readUshort(r,t);t+=2;var l=n.readUshort(r,t);t+=2;var v=n.readUshort(r,t);t+=2;var c=n.readUshort(r,t);t+=2;var p=n.readUshort(r,t);t+=2;var U=n.readUshort(r,t);t+=2;var g,S=h[c],m=d+12*s+U;if(0==u)g=n.readUnicode(r,m,p/2);else if(3==u&&0==l)g=n.readUnicode(r,m,p/2);else if(0==l)g=n.readASCII(r,m,p);else if(1==l)g=n.readUnicode(r,m,p/2);else if(3==l)g=n.readUnicode(r,m,p/2);else {if(1!=u)throw \"unknown encoding \"+l+\", platformID: \"+u;g=n.readASCII(r,m,p),console.debug(\"reading unknown MAC encoding \"+l+\" as ASCII\");}var b=\"p\"+u+\",\"+v.toString(16);null==o[b]&&(o[b]={}),o[b][void 0!==S?S:c]=g,o[b]._lang=v;}for(var y in o)if(null!=o[y].postScriptName&&1033==o[y]._lang)return o[y];for(var y in o)if(null!=o[y].postScriptName&&0==o[y]._lang)return o[y];for(var y in o)if(null!=o[y].postScriptName&&3084==o[y]._lang)return o[y];for(var y in o)if(null!=o[y].postScriptName)return o[y];for(var y in o){i=y;break}return console.debug(\"returning name table with languageID \"+o[i]._lang),o[i]},e[\"OS/2\"]={},e[\"OS/2\"].parse=function(r,t,a){var n=e._bin.readUshort(r,t);t+=2;var o={};if(0==n)e[\"OS/2\"].version0(r,t,o);else if(1==n)e[\"OS/2\"].version1(r,t,o);else if(2==n||3==n||4==n)e[\"OS/2\"].version2(r,t,o);else {if(5!=n)throw \"unknown OS/2 table version: \"+n;e[\"OS/2\"].version5(r,t,o);}return o},e[\"OS/2\"].version0=function(r,t,a){var n=e._bin;return a.xAvgCharWidth=n.readShort(r,t),t+=2,a.usWeightClass=n.readUshort(r,t),t+=2,a.usWidthClass=n.readUshort(r,t),t+=2,a.fsType=n.readUshort(r,t),t+=2,a.ySubscriptXSize=n.readShort(r,t),t+=2,a.ySubscriptYSize=n.readShort(r,t),t+=2,a.ySubscriptXOffset=n.readShort(r,t),t+=2,a.ySubscriptYOffset=n.readShort(r,t),t+=2,a.ySuperscriptXSize=n.readShort(r,t),t+=2,a.ySuperscriptYSize=n.readShort(r,t),t+=2,a.ySuperscriptXOffset=n.readShort(r,t),t+=2,a.ySuperscriptYOffset=n.readShort(r,t),t+=2,a.yStrikeoutSize=n.readShort(r,t),t+=2,a.yStrikeoutPosition=n.readShort(r,t),t+=2,a.sFamilyClass=n.readShort(r,t),t+=2,a.panose=n.readBytes(r,t,10),t+=10,a.ulUnicodeRange1=n.readUint(r,t),t+=4,a.ulUnicodeRange2=n.readUint(r,t),t+=4,a.ulUnicodeRange3=n.readUint(r,t),t+=4,a.ulUnicodeRange4=n.readUint(r,t),t+=4,a.achVendID=[n.readInt8(r,t),n.readInt8(r,t+1),n.readInt8(r,t+2),n.readInt8(r,t+3)],t+=4,a.fsSelection=n.readUshort(r,t),t+=2,a.usFirstCharIndex=n.readUshort(r,t),t+=2,a.usLastCharIndex=n.readUshort(r,t),t+=2,a.sTypoAscender=n.readShort(r,t),t+=2,a.sTypoDescender=n.readShort(r,t),t+=2,a.sTypoLineGap=n.readShort(r,t),t+=2,a.usWinAscent=n.readUshort(r,t),t+=2,a.usWinDescent=n.readUshort(r,t),t+=2},e[\"OS/2\"].version1=function(r,t,a){var n=e._bin;return t=e[\"OS/2\"].version0(r,t,a),a.ulCodePageRange1=n.readUint(r,t),t+=4,a.ulCodePageRange2=n.readUint(r,t),t+=4},e[\"OS/2\"].version2=function(r,t,a){var n=e._bin;return t=e[\"OS/2\"].version1(r,t,a),a.sxHeight=n.readShort(r,t),t+=2,a.sCapHeight=n.readShort(r,t),t+=2,a.usDefault=n.readUshort(r,t),t+=2,a.usBreak=n.readUshort(r,t),t+=2,a.usMaxContext=n.readUshort(r,t),t+=2},e[\"OS/2\"].version5=function(r,t,a){var n=e._bin;return t=e[\"OS/2\"].version2(r,t,a),a.usLowerOpticalPointSize=n.readUshort(r,t),t+=2,a.usUpperOpticalPointSize=n.readUshort(r,t),t+=2},e.post={},e.post.parse=function(r,t,a){var n=e._bin,o={};return o.version=n.readFixed(r,t),t+=4,o.italicAngle=n.readFixed(r,t),t+=4,o.underlinePosition=n.readShort(r,t),t+=2,o.underlineThickness=n.readShort(r,t),t+=2,o},null==e&&(e={}),null==e.U&&(e.U={}),e.U.codeToGlyph=function(r,e){var t=r.cmap,a=-1;if(null!=t.p0e4?a=t.p0e4:null!=t.p3e1?a=t.p3e1:null!=t.p1e0?a=t.p1e0:null!=t.p0e3&&(a=t.p0e3),-1==a)throw \"no familiar platform and encoding!\";var n=t.tables[a];if(0==n.format)return e>=n.map.length?0:n.map[e];if(4==n.format){for(var o=-1,s=0;s<n.endCount.length;s++)if(e<=n.endCount[s]){o=s;break}if(-1==o)return 0;if(n.startCount[o]>e)return 0;return 65535&(0!=n.idRangeOffset[o]?n.glyphIdArray[e-n.startCount[o]+(n.idRangeOffset[o]>>1)-(n.idRangeOffset.length-o)]:e+n.idDelta[o])}if(12==n.format){if(e>n.groups[n.groups.length-1][1])return 0;for(s=0;s<n.groups.length;s++){var i=n.groups[s];if(i[0]<=e&&e<=i[1])return i[2]+(e-i[0])}return 0}throw \"unknown cmap table format \"+n.format},e.U.glyphToPath=function(r,t){var a={cmds:[],crds:[]};if(r.SVG&&r.SVG.entries[t]){var n=r.SVG.entries[t];return null==n?a:(\"string\"==typeof n&&(n=e.SVG.toPath(n),r.SVG.entries[t]=n),n)}if(r.CFF){var o={x:0,y:0,stack:[],nStems:0,haveWidth:!1,width:r.CFF.Private?r.CFF.Private.defaultWidthX:0,open:!1},s=r.CFF,i=r.CFF.Private;if(s.ROS){for(var h=0;s.FDSelect[h+2]<=t;)h+=2;i=s.FDArray[s.FDSelect[h+1]].Private;}e.U._drawCFF(r.CFF.CharStrings[t],o,s,i,a);}else r.glyf&&e.U._drawGlyf(t,r,a);return a},e.U._drawGlyf=function(r,t,a){var n=t.glyf[r];null==n&&(n=t.glyf[r]=e.glyf._parseGlyf(t,r)),null!=n&&(n.noc>-1?e.U._simpleGlyph(n,a):e.U._compoGlyph(n,t,a));},e.U._simpleGlyph=function(r,t){for(var a=0;a<r.noc;a++){for(var n=0==a?0:r.endPts[a-1]+1,o=r.endPts[a],s=n;s<=o;s++){var i=s==n?o:s-1,h=s==o?n:s+1,d=1&r.flags[s],f=1&r.flags[i],u=1&r.flags[h],l=r.xs[s],v=r.ys[s];if(s==n)if(d){if(!f){e.U.P.moveTo(t,l,v);continue}e.U.P.moveTo(t,r.xs[i],r.ys[i]);}else f?e.U.P.moveTo(t,r.xs[i],r.ys[i]):e.U.P.moveTo(t,(r.xs[i]+l)/2,(r.ys[i]+v)/2);d?f&&e.U.P.lineTo(t,l,v):u?e.U.P.qcurveTo(t,l,v,r.xs[h],r.ys[h]):e.U.P.qcurveTo(t,l,v,(l+r.xs[h])/2,(v+r.ys[h])/2);}e.U.P.closePath(t);}},e.U._compoGlyph=function(r,t,a){for(var n=0;n<r.parts.length;n++){var o={cmds:[],crds:[]},s=r.parts[n];e.U._drawGlyf(s.glyphIndex,t,o);for(var i=s.m,h=0;h<o.crds.length;h+=2){var d=o.crds[h],f=o.crds[h+1];a.crds.push(d*i.a+f*i.b+i.tx),a.crds.push(d*i.c+f*i.d+i.ty);}for(h=0;h<o.cmds.length;h++)a.cmds.push(o.cmds[h]);}},e.U._getGlyphClass=function(r,t){var a=e._lctf.getInterval(t,r);return -1==a?0:t[a+2]},e.U._applySubs=function(r,t,a,n){for(var o=r.length-t-1,s=0;s<a.tabs.length;s++)if(null!=a.tabs[s]){var i,h=a.tabs[s];if(!h.coverage||-1!=(i=e._lctf.coverageIndex(h.coverage,r[t])))if(1==a.ltype)r[t],1==h.fmt?r[t]=r[t]+h.delta:r[t]=h.newg[i];else if(4==a.ltype)for(var d=h.vals[i],f=0;f<d.length;f++){var u=d[f],l=u.chain.length;if(!(l>o)){for(var v=!0,c=0,p=0;p<l;p++){for(;-1==r[t+c+(1+p)];)c++;u.chain[p]!=r[t+c+(1+p)]&&(v=!1);}if(v){r[t]=u.nglyph;for(p=0;p<l+c;p++)r[t+p+1]=-1;break}}}else if(5==a.ltype&&2==h.fmt)for(var U=e._lctf.getInterval(h.cDef,r[t]),g=h.cDef[U+2],S=h.scset[g],m=0;m<S.length;m++){var b=S[m],y=b.input;if(!(y.length>o)){for(v=!0,p=0;p<y.length;p++){var F=e._lctf.getInterval(h.cDef,r[t+1+p]);if(-1==U&&h.cDef[F+2]!=y[p]){v=!1;break}}if(v){var C=b.substLookupRecords;for(f=0;f<C.length;f+=2)C[f],C[f+1];}}}else if(6==a.ltype&&3==h.fmt){if(!e.U._glsCovered(r,h.backCvg,t-h.backCvg.length))continue;if(!e.U._glsCovered(r,h.inptCvg,t))continue;if(!e.U._glsCovered(r,h.ahedCvg,t+h.inptCvg.length))continue;var _=h.lookupRec;for(m=0;m<_.length;m+=2){U=_[m];var P=n[_[m+1]];e.U._applySubs(r,t+U,P,n);}}}},e.U._glsCovered=function(r,t,a){for(var n=0;n<t.length;n++){if(-1==e._lctf.coverageIndex(t[n],r[a+n]))return !1}return !0},e.U.glyphsToPath=function(r,t,a){for(var n={cmds:[],crds:[]},o=0,s=0;s<t.length;s++){var i=t[s];if(-1!=i){for(var h=s<t.length-1&&-1!=t[s+1]?t[s+1]:0,d=e.U.glyphToPath(r,i),f=0;f<d.crds.length;f+=2)n.crds.push(d.crds[f]+o),n.crds.push(d.crds[f+1]);a&&n.cmds.push(a);for(f=0;f<d.cmds.length;f++)n.cmds.push(d.cmds[f]);a&&n.cmds.push(\"X\"),o+=r.hmtx.aWidth[i],s<t.length-1&&(o+=e.U.getPairAdjustment(r,i,h));}}return n},e.U.P={},e.U.P.moveTo=function(r,e,t){r.cmds.push(\"M\"),r.crds.push(e,t);},e.U.P.lineTo=function(r,e,t){r.cmds.push(\"L\"),r.crds.push(e,t);},e.U.P.curveTo=function(r,e,t,a,n,o,s){r.cmds.push(\"C\"),r.crds.push(e,t,a,n,o,s);},e.U.P.qcurveTo=function(r,e,t,a,n){r.cmds.push(\"Q\"),r.crds.push(e,t,a,n);},e.U.P.closePath=function(r){r.cmds.push(\"Z\");},e.U._drawCFF=function(r,t,a,n,o){for(var s=t.stack,i=t.nStems,h=t.haveWidth,d=t.width,f=t.open,u=0,l=t.x,v=t.y,c=0,p=0,U=0,g=0,S=0,m=0,b=0,y=0,F=0,C=0,_={val:0,size:0};u<r.length;){e.CFF.getCharString(r,u,_);var P=_.val;if(u+=_.size,\"o1\"==P||\"o18\"==P)s.length%2!=0&&!h&&(d=s.shift()+n.nominalWidthX),i+=s.length>>1,s.length=0,h=!0;else if(\"o3\"==P||\"o23\"==P){s.length%2!=0&&!h&&(d=s.shift()+n.nominalWidthX),i+=s.length>>1,s.length=0,h=!0;}else if(\"o4\"==P)s.length>1&&!h&&(d=s.shift()+n.nominalWidthX,h=!0),f&&e.U.P.closePath(o),v+=s.pop(),e.U.P.moveTo(o,l,v),f=!0;else if(\"o5\"==P)for(;s.length>0;)l+=s.shift(),v+=s.shift(),e.U.P.lineTo(o,l,v);else if(\"o6\"==P||\"o7\"==P)for(var x=s.length,I=\"o6\"==P,w=0;w<x;w++){var k=s.shift();I?l+=k:v+=k,I=!I,e.U.P.lineTo(o,l,v);}else if(\"o8\"==P||\"o24\"==P){x=s.length;for(var G=0;G+6<=x;)c=l+s.shift(),p=v+s.shift(),U=c+s.shift(),g=p+s.shift(),l=U+s.shift(),v=g+s.shift(),e.U.P.curveTo(o,c,p,U,g,l,v),G+=6;\"o24\"==P&&(l+=s.shift(),v+=s.shift(),e.U.P.lineTo(o,l,v));}else {if(\"o11\"==P)break;if(\"o1234\"==P||\"o1235\"==P||\"o1236\"==P||\"o1237\"==P)\"o1234\"==P&&(p=v,U=(c=l+s.shift())+s.shift(),C=g=p+s.shift(),m=g,y=v,l=(b=(S=(F=U+s.shift())+s.shift())+s.shift())+s.shift(),e.U.P.curveTo(o,c,p,U,g,F,C),e.U.P.curveTo(o,S,m,b,y,l,v)),\"o1235\"==P&&(c=l+s.shift(),p=v+s.shift(),U=c+s.shift(),g=p+s.shift(),F=U+s.shift(),C=g+s.shift(),S=F+s.shift(),m=C+s.shift(),b=S+s.shift(),y=m+s.shift(),l=b+s.shift(),v=y+s.shift(),s.shift(),e.U.P.curveTo(o,c,p,U,g,F,C),e.U.P.curveTo(o,S,m,b,y,l,v)),\"o1236\"==P&&(c=l+s.shift(),p=v+s.shift(),U=c+s.shift(),C=g=p+s.shift(),m=g,b=(S=(F=U+s.shift())+s.shift())+s.shift(),y=m+s.shift(),l=b+s.shift(),e.U.P.curveTo(o,c,p,U,g,F,C),e.U.P.curveTo(o,S,m,b,y,l,v)),\"o1237\"==P&&(c=l+s.shift(),p=v+s.shift(),U=c+s.shift(),g=p+s.shift(),F=U+s.shift(),C=g+s.shift(),S=F+s.shift(),m=C+s.shift(),b=S+s.shift(),y=m+s.shift(),Math.abs(b-l)>Math.abs(y-v)?l=b+s.shift():v=y+s.shift(),e.U.P.curveTo(o,c,p,U,g,F,C),e.U.P.curveTo(o,S,m,b,y,l,v));else if(\"o14\"==P){if(s.length>0&&!h&&(d=s.shift()+a.nominalWidthX,h=!0),4==s.length){var O=s.shift(),T=s.shift(),D=s.shift(),B=s.shift(),A=e.CFF.glyphBySE(a,D),R=e.CFF.glyphBySE(a,B);e.U._drawCFF(a.CharStrings[A],t,a,n,o),t.x=O,t.y=T,e.U._drawCFF(a.CharStrings[R],t,a,n,o);}f&&(e.U.P.closePath(o),f=!1);}else if(\"o19\"==P||\"o20\"==P){s.length%2!=0&&!h&&(d=s.shift()+n.nominalWidthX),i+=s.length>>1,s.length=0,h=!0,u+=i+7>>3;}else if(\"o21\"==P)s.length>2&&!h&&(d=s.shift()+n.nominalWidthX,h=!0),v+=s.pop(),l+=s.pop(),f&&e.U.P.closePath(o),e.U.P.moveTo(o,l,v),f=!0;else if(\"o22\"==P)s.length>1&&!h&&(d=s.shift()+n.nominalWidthX,h=!0),l+=s.pop(),f&&e.U.P.closePath(o),e.U.P.moveTo(o,l,v),f=!0;else if(\"o25\"==P){for(;s.length>6;)l+=s.shift(),v+=s.shift(),e.U.P.lineTo(o,l,v);c=l+s.shift(),p=v+s.shift(),U=c+s.shift(),g=p+s.shift(),l=U+s.shift(),v=g+s.shift(),e.U.P.curveTo(o,c,p,U,g,l,v);}else if(\"o26\"==P)for(s.length%2&&(l+=s.shift());s.length>0;)c=l,p=v+s.shift(),l=U=c+s.shift(),v=(g=p+s.shift())+s.shift(),e.U.P.curveTo(o,c,p,U,g,l,v);else if(\"o27\"==P)for(s.length%2&&(v+=s.shift());s.length>0;)p=v,U=(c=l+s.shift())+s.shift(),g=p+s.shift(),l=U+s.shift(),v=g,e.U.P.curveTo(o,c,p,U,g,l,v);else if(\"o10\"==P||\"o29\"==P){var L=\"o10\"==P?n:a;if(0==s.length)console.debug(\"error: empty stack\");else {var W=s.pop(),M=L.Subrs[W+L.Bias];t.x=l,t.y=v,t.nStems=i,t.haveWidth=h,t.width=d,t.open=f,e.U._drawCFF(M,t,a,n,o),l=t.x,v=t.y,i=t.nStems,h=t.haveWidth,d=t.width,f=t.open;}}else if(\"o30\"==P||\"o31\"==P){var V=s.length,E=(G=0,\"o31\"==P);for(G+=V-(x=-3&V);G<x;)E?(p=v,U=(c=l+s.shift())+s.shift(),v=(g=p+s.shift())+s.shift(),x-G==5?(l=U+s.shift(),G++):l=U,E=!1):(c=l,p=v+s.shift(),U=c+s.shift(),g=p+s.shift(),l=U+s.shift(),x-G==5?(v=g+s.shift(),G++):v=g,E=!0),e.U.P.curveTo(o,c,p,U,g,l,v),G+=4;}else {if(\"o\"==(P+\"\").charAt(0))throw console.debug(\"Unknown operation: \"+P,r),P;s.push(P);}}}t.x=l,t.y=v,t.nStems=i,t.haveWidth=h,t.width=d,t.open=f;};var t=e,a={Typr:t};return r.Typr=t,r.default=a,Object.defineProperty(r,\"__esModule\",{value:!0}),r}({}).Typr}\n\n/*!\nCustom bundle of woff2otf (https://github.com/arty-name/woff2otf) with fflate\n(https://github.com/101arrowz/fflate) for use in Troika text rendering. \nOriginal licenses apply: \n- fflate: https://github.com/101arrowz/fflate/blob/master/LICENSE (MIT)\n- woff2otf.js: https://github.com/arty-name/woff2otf/blob/master/woff2otf.js (Apache2)\n*/\nfunction woff2otfFactory(){return function(r){var e=Uint8Array,n=Uint16Array,t=Uint32Array,a=new e([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new e([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),o=new e([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),f=function(r,e){for(var a=new n(31),i=0;i<31;++i)a[i]=e+=1<<r[i-1];var o=new t(a[30]);for(i=1;i<30;++i)for(var f=a[i];f<a[i+1];++f)o[f]=f-a[i]<<5|i;return [a,o]},u=f(a,2),v=u[0],s=u[1];v[28]=258,s[258]=28;for(var l=f(i,0)[0],c=new n(32768),g=0;g<32768;++g){var h=(43690&g)>>>1|(21845&g)<<1;h=(61680&(h=(52428&h)>>>2|(13107&h)<<2))>>>4|(3855&h)<<4,c[g]=((65280&h)>>>8|(255&h)<<8)>>>1;}var w=function(r,e,t){for(var a=r.length,i=0,o=new n(e);i<a;++i)++o[r[i]-1];var f,u=new n(e);for(i=0;i<e;++i)u[i]=u[i-1]+o[i-1]<<1;if(t){f=new n(1<<e);var v=15-e;for(i=0;i<a;++i)if(r[i])for(var s=i<<4|r[i],l=e-r[i],g=u[r[i]-1]++<<l,h=g|(1<<l)-1;g<=h;++g)f[c[g]>>>v]=s;}else for(f=new n(a),i=0;i<a;++i)r[i]&&(f[i]=c[u[r[i]-1]++]>>>15-r[i]);return f},d=new e(288);for(g=0;g<144;++g)d[g]=8;for(g=144;g<256;++g)d[g]=9;for(g=256;g<280;++g)d[g]=7;for(g=280;g<288;++g)d[g]=8;var m=new e(32);for(g=0;g<32;++g)m[g]=5;var b=w(d,9,1),p=w(m,5,1),y=function(r){for(var e=r[0],n=1;n<r.length;++n)r[n]>e&&(e=r[n]);return e},L=function(r,e,n){var t=e/8|0;return (r[t]|r[t+1]<<8)>>(7&e)&n},U=function(r,e){var n=e/8|0;return (r[n]|r[n+1]<<8|r[n+2]<<16)>>(7&e)},k=[\"unexpected EOF\",\"invalid block type\",\"invalid length/literal\",\"invalid distance\",\"stream finished\",\"no stream handler\",,\"no callback\",\"invalid UTF-8 data\",\"extra field too long\",\"date not in range 1980-2099\",\"filename too long\",\"stream finishing\",\"invalid zip data\"],T=function(r,e,n){var t=new Error(e||k[r]);if(t.code=r,Error.captureStackTrace&&Error.captureStackTrace(t,T),!n)throw t;return t},O=function(r,f,u){var s=r.length;if(!s||u&&!u.l&&s<5)return f||new e(0);var c=!f||u,g=!u||u.i;u||(u={}),f||(f=new e(3*s));var h,d=function(r){var n=f.length;if(r>n){var t=new e(Math.max(2*n,r));t.set(f),f=t;}},m=u.f||0,k=u.p||0,O=u.b||0,A=u.l,x=u.d,E=u.m,D=u.n,M=8*s;do{if(!A){u.f=m=L(r,k,1);var S=L(r,k+1,3);if(k+=3,!S){var V=r[(I=((h=k)/8|0)+(7&h&&1)+4)-4]|r[I-3]<<8,_=I+V;if(_>s){g&&T(0);break}c&&d(O+V),f.set(r.subarray(I,_),O),u.b=O+=V,u.p=k=8*_;continue}if(1==S)A=b,x=p,E=9,D=5;else if(2==S){var j=L(r,k,31)+257,z=L(r,k+10,15)+4,C=j+L(r,k+5,31)+1;k+=14;for(var F=new e(C),P=new e(19),q=0;q<z;++q)P[o[q]]=L(r,k+3*q,7);k+=3*z;var B=y(P),G=(1<<B)-1,H=w(P,B,1);for(q=0;q<C;){var I,J=H[L(r,k,G)];if(k+=15&J,(I=J>>>4)<16)F[q++]=I;else {var K=0,N=0;for(16==I?(N=3+L(r,k,3),k+=2,K=F[q-1]):17==I?(N=3+L(r,k,7),k+=3):18==I&&(N=11+L(r,k,127),k+=7);N--;)F[q++]=K;}}var Q=F.subarray(0,j),R=F.subarray(j);E=y(Q),D=y(R),A=w(Q,E,1),x=w(R,D,1);}else T(1);if(k>M){g&&T(0);break}}c&&d(O+131072);for(var W=(1<<E)-1,X=(1<<D)-1,Y=k;;Y=k){var Z=(K=A[U(r,k)&W])>>>4;if((k+=15&K)>M){g&&T(0);break}if(K||T(2),Z<256)f[O++]=Z;else {if(256==Z){Y=k,A=null;break}var $=Z-254;if(Z>264){var rr=a[q=Z-257];$=L(r,k,(1<<rr)-1)+v[q],k+=rr;}var er=x[U(r,k)&X],nr=er>>>4;er||T(3),k+=15&er;R=l[nr];if(nr>3){rr=i[nr];R+=U(r,k)&(1<<rr)-1,k+=rr;}if(k>M){g&&T(0);break}c&&d(O+131072);for(var tr=O+$;O<tr;O+=4)f[O]=f[O-R],f[O+1]=f[O+1-R],f[O+2]=f[O+2-R],f[O+3]=f[O+3-R];O=tr;}}u.l=A,u.p=Y,u.b=O,A&&(m=1,u.m=E,u.d=x,u.n=D);}while(!m);return O==f.length?f:function(r,a,i){(null==a||a<0)&&(a=0),(null==i||i>r.length)&&(i=r.length);var o=new(r instanceof n?n:r instanceof t?t:e)(i-a);return o.set(r.subarray(a,i)),o}(f,0,O)},A=new e(0);var x=\"undefined\"!=typeof TextDecoder&&new TextDecoder;try{x.decode(A,{stream:!0}),1;}catch(r){}return r.convert_streams=function(r){var e=new DataView(r),n=0;function t(){var r=e.getUint16(n);return n+=2,r}function a(){var r=e.getUint32(n);return n+=4,r}function i(r){m.setUint16(b,r),b+=2;}function o(r){m.setUint32(b,r),b+=4;}for(var f={signature:a(),flavor:a(),length:a(),numTables:t(),reserved:t(),totalSfntSize:a(),majorVersion:t(),minorVersion:t(),metaOffset:a(),metaLength:a(),metaOrigLength:a(),privOffset:a(),privLength:a()},u=0;Math.pow(2,u)<=f.numTables;)u++;u--;for(var v=16*Math.pow(2,u),s=16*f.numTables-v,l=12,c=[],g=0;g<f.numTables;g++)c.push({tag:a(),offset:a(),compLength:a(),origLength:a(),origChecksum:a()}),l+=16;var h,w=new Uint8Array(12+16*c.length+c.reduce((function(r,e){return r+e.origLength+4}),0)),d=w.buffer,m=new DataView(d),b=0;return o(f.flavor),i(f.numTables),i(v),i(u),i(s),c.forEach((function(r){o(r.tag),o(r.origChecksum),o(l),o(r.origLength),r.outOffset=l,(l+=r.origLength)%4!=0&&(l+=4-l%4);})),c.forEach((function(e){var n,t=r.slice(e.offset,e.offset+e.compLength);if(e.compLength!=e.origLength){var a=new Uint8Array(e.origLength);n=new Uint8Array(t,2),O(n,a);}else a=new Uint8Array(t);w.set(a,e.outOffset);var i=0;(l=e.outOffset+e.origLength)%4!=0&&(i=4-l%4),w.set(new Uint8Array(i).buffer,e.outOffset+e.origLength),h=l+i;})),d.slice(0,h)},Object.defineProperty(r,\"__esModule\",{value:!0}),r}({}).convert_streams}\n\n/**\n * A factory wrapper parsing a font file using Typr.\n * Also adds support for WOFF files (not WOFF2).\n */\n\n/**\n * @typedef ParsedFont\n * @property {number} ascender\n * @property {number} descender\n * @property {number} xHeight\n * @property {(number) => boolean} supportsCodePoint\n * @property {(text:string, fontSize:number, letterSpacing:number, callback) => number} forEachGlyph\n * @property {number} lineGap\n * @property {number} capHeight\n * @property {number} unitsPerEm\n */\n\n/**\n * @typedef {(buffer: ArrayBuffer) => ParsedFont} FontParser\n */\n\n/**\n * @returns {FontParser}\n */\nfunction parserFactory(Typr, woff2otf) {\n  const cmdArgLengths = {\n    M: 2,\n    L: 2,\n    Q: 4,\n    C: 6,\n    Z: 0\n  };\n\n  // {joinType: \"skip+step,...\"}\n  const joiningTypeRawData = {\"C\":\"18g,ca,368,1kz\",\"D\":\"17k,6,2,2+4,5+c,2+6,2+1,10+1,9+f,j+11,2+1,a,2,2+1,15+2,3,j+2,6+3,2+8,2,2,2+1,w+a,4+e,3+3,2,3+2,3+5,23+w,2f+4,3,2+9,2,b,2+3,3,1k+9,6+1,3+1,2+2,2+d,30g,p+y,1,1+1g,f+x,2,sd2+1d,jf3+4,f+3,2+4,2+2,b+3,42,2,4+2,2+1,2,3,t+1,9f+w,2,el+2,2+g,d+2,2l,2+1,5,3+1,2+1,2,3,6,16wm+1v\",\"R\":\"17m+3,2,2,6+3,m,15+2,2+2,h+h,13,3+8,2,2,3+1,2,p+1,x,5+4,5,a,2,2,3,u,c+2,g+1,5,2+1,4+1,5j,6+1,2,b,2+2,f,2+1,1s+2,2,3+1,7,1ez0,2,2+1,4+4,b,4,3,b,42,2+2,4,3,2+1,2,o+3,ae,ep,x,2o+2,3+1,3,5+1,6\",\"L\":\"x9u,jff,a,fd,jv\",\"T\":\"4t,gj+33,7o+4,1+1,7c+18,2,2+1,2+1,2,21+a,2,1b+k,h,2u+6,3+5,3+1,2+3,y,2,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,3,7,6+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+d,1,1+1,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,ek,3+1,r+4,1e+4,6+5,2p+c,1+3,1,1+2,1+b,2db+2,3y,2p+v,ff+3,30+1,n9x,1+2,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,5s,6y+2,ea,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+9,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2,2b+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,470+8,at4+4,1o+6,t5,1s+3,2a,f5l+1,2+3,43o+2,a+7,1+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,1,gzau,v+2n,3l+6n\"};\n\n  const JT_LEFT = 1, //indicates that a character joins with the subsequent character, but does not join with the preceding character.\n    JT_RIGHT = 2, //indicates that a character joins with the preceding character, but does not join with the subsequent character.\n    JT_DUAL = 4, //indicates that a character joins with the preceding character and joins with the subsequent character.\n    JT_TRANSPARENT = 8, //indicates that the character does not join with adjacent characters and that the character must be skipped over when the shaping engine is evaluating the joining positions in a sequence of characters. When a JT_TRANSPARENT character is encountered in a sequence, the JOINING_TYPE of the preceding character passes through. Diacritical marks are frequently assigned this value.\n    JT_JOIN_CAUSING = 16, //indicates that the character forces the use of joining forms with the preceding and subsequent characters. Kashidas and the Zero Width Joiner (U+200D) are both JOIN_CAUSING characters.\n    JT_NON_JOINING = 32; //indicates that a character does not join with the preceding or with the subsequent character.,\n\n  let joiningTypeMap;\n  function getCharJoiningType(ch) {\n    if (!joiningTypeMap) {\n      const m = {\n        R: JT_RIGHT,\n        L: JT_LEFT,\n        D: JT_DUAL,\n        C: JT_JOIN_CAUSING,\n        U: JT_NON_JOINING,\n        T: JT_TRANSPARENT\n      };\n      joiningTypeMap = new Map();\n      for (let type in joiningTypeRawData) {\n        let lastCode = 0;\n        joiningTypeRawData[type].split(',').forEach(range => {\n          let [skip, step] = range.split('+');\n          skip = parseInt(skip,36);\n          step = step ? parseInt(step, 36) : 0;\n          joiningTypeMap.set(lastCode += skip, m[type]);\n          for (let i = step; i--;) {\n            joiningTypeMap.set(++lastCode, m[type]);\n          }\n        });\n      }\n    }\n    return joiningTypeMap.get(ch) || JT_NON_JOINING\n  }\n\n  const ISOL = 1, INIT = 2, FINA = 3, MEDI = 4;\n  const formsToFeatures = [null, 'isol', 'init', 'fina', 'medi'];\n\n  function detectJoiningForms(str) {\n    // This implements the algorithm described here:\n    // https://github.com/n8willis/opentype-shaping-documents/blob/master/opentype-shaping-arabic-general.md\n    const joiningForms = new Uint8Array(str.length);\n    let prevJoiningType = JT_NON_JOINING;\n    let prevForm = ISOL;\n    let prevIndex = -1;\n    for (let i = 0; i < str.length; i++) {\n      const code = str.codePointAt(i);\n      let joiningType = getCharJoiningType(code) | 0;\n      let form = ISOL;\n      if (joiningType & JT_TRANSPARENT) {\n        continue\n      }\n      if (prevJoiningType & (JT_LEFT | JT_DUAL | JT_JOIN_CAUSING)) {\n        if (joiningType & (JT_RIGHT | JT_DUAL | JT_JOIN_CAUSING)) {\n          form = FINA;\n          // isol->init, fina->medi\n          if (prevForm === ISOL || prevForm === FINA) {\n            joiningForms[prevIndex]++;\n          }\n        }\n        else if (joiningType & (JT_LEFT | JT_NON_JOINING)) {\n          // medi->fina, init->isol\n          if (prevForm === INIT || prevForm === MEDI) {\n            joiningForms[prevIndex]--;\n          }\n        }\n      }\n      else if (prevJoiningType & (JT_RIGHT | JT_NON_JOINING)) {\n        // medi->fina, init->isol\n        if (prevForm === INIT || prevForm === MEDI) {\n          joiningForms[prevIndex]--;\n        }\n      }\n      prevForm = joiningForms[i] = form;\n      prevJoiningType = joiningType;\n      prevIndex = i;\n      if (code > 0xffff) i++;\n    }\n    // console.log(str.split('').map(ch => ch.codePointAt(0).toString(16)))\n    // console.log(str.split('').map(ch => getCharJoiningType(ch.codePointAt(0))))\n    // console.log(Array.from(joiningForms).map(f => formsToFeatures[f] || 'none'))\n    return joiningForms\n  }\n\n  function stringToGlyphs (font, str) {\n    const glyphIds = [];\n    for (let i = 0; i < str.length; i++) {\n      const cc = str.codePointAt(i);\n      if (cc > 0xffff) i++;\n      glyphIds.push(Typr.U.codeToGlyph(font, cc));\n    }\n\n    const gsub = font['GSUB'];\n    if (gsub) {\n      const {lookupList, featureList} = gsub;\n      let joiningForms;\n      const supportedFeatures = /^(rlig|liga|mset|isol|init|fina|medi|half|pres|blws|ccmp)$/;\n      const usedLookups = [];\n      featureList.forEach(feature => {\n        if (supportedFeatures.test(feature.tag)) {\n          for (let ti = 0; ti < feature.tab.length; ti++) {\n            if (usedLookups[feature.tab[ti]]) continue\n            usedLookups[feature.tab[ti]] = true;\n            const tab = lookupList[feature.tab[ti]];\n            const isJoiningFeature = /^(isol|init|fina|medi)$/.test(feature.tag);\n            if (isJoiningFeature && !joiningForms) { //lazy\n              joiningForms = detectJoiningForms(str);\n            }\n            for (let ci = 0; ci < glyphIds.length; ci++) {\n              if (!joiningForms || !isJoiningFeature || formsToFeatures[joiningForms[ci]] === feature.tag) {\n                Typr.U._applySubs(glyphIds, ci, tab, lookupList);\n              }\n            }\n          }\n        }\n      });\n    }\n\n    return glyphIds\n  }\n\n  // Calculate advances and x/y offsets for each glyph, e.g. kerning and mark\n  // attachments. This is a more complete version of Typr.U.getPairAdjustment\n  // and should become an upstream replacement eventually.\n  function calcGlyphPositions(font, glyphIds) {\n    const positions = new Int16Array(glyphIds.length * 3); // [offsetX, offsetY, advanceX, ...]\n    let glyphIndex = 0;\n    for (; glyphIndex < glyphIds.length; glyphIndex++) {\n      const glyphId = glyphIds[glyphIndex];\n      if (glyphId === -1) continue;\n\n      positions[glyphIndex * 3 + 2] = font.hmtx.aWidth[glyphId]; // populate advanceX in...advance.\n\n      const gpos = font.GPOS;\n      if (gpos) {\n        const llist = gpos.lookupList;\n        for (let i = 0; i < llist.length; i++) {\n          const lookup = llist[i];\n          for (let j = 0; j < lookup.tabs.length; j++) {\n            const tab = lookup.tabs[j];\n            // Single char placement\n            if (lookup.ltype === 1) {\n              const ind = Typr._lctf.coverageIndex(tab.coverage, glyphId);\n              if (ind !== -1 && tab.pos) {\n                applyValueRecord(tab.pos, glyphIndex);\n                break\n              }\n            }\n            // Pairs (kerning)\n            else if (lookup.ltype === 2) {\n              let adj = null;\n              let prevGlyphIndex = getPrevGlyphIndex();\n              if (prevGlyphIndex !== -1) {\n                const coverageIndex = Typr._lctf.coverageIndex(tab.coverage, glyphIds[prevGlyphIndex]);\n                if (coverageIndex !== -1) {\n                  if (tab.fmt === 1) {\n                    const right = tab.pairsets[coverageIndex];\n                    for (let k = 0; k < right.length; k++) {\n                      if (right[k].gid2 === glyphId) adj = right[k];\n                    }\n                  } else if (tab.fmt === 2) {\n                    const c1 = Typr.U._getGlyphClass(glyphIds[prevGlyphIndex], tab.classDef1);\n                    const c2 = Typr.U._getGlyphClass(glyphId, tab.classDef2);\n                    adj = tab.matrix[c1][c2];\n                  }\n                  if (adj) {\n                    if (adj.val1) applyValueRecord(adj.val1, prevGlyphIndex);\n                    if (adj.val2) applyValueRecord(adj.val2, glyphIndex);\n                    break\n                  }\n                }\n              }\n            }\n            // Mark to base\n            else if (lookup.ltype === 4) {\n              const markArrIndex = Typr._lctf.coverageIndex(tab.markCoverage, glyphId);\n              if (markArrIndex !== -1) {\n                const baseGlyphIndex = getPrevGlyphIndex(isBaseGlyph);\n                const baseArrIndex = baseGlyphIndex === -1 ? -1 : Typr._lctf.coverageIndex(tab.baseCoverage, glyphIds[baseGlyphIndex]);\n                if (baseArrIndex !== -1) {\n                  const markRecord = tab.markArray[markArrIndex];\n                  const baseAnchor = tab.baseArray[baseArrIndex][markRecord.markClass];\n                  positions[glyphIndex * 3] = baseAnchor.x - markRecord.x + positions[baseGlyphIndex * 3] - positions[baseGlyphIndex * 3 + 2];\n                  positions[glyphIndex * 3 + 1] = baseAnchor.y - markRecord.y + positions[baseGlyphIndex * 3 + 1];\n                  break;\n                }\n              }\n            }\n            // Mark to mark\n            else if (lookup.ltype === 6) {\n              const mark1ArrIndex = Typr._lctf.coverageIndex(tab.mark1Coverage, glyphId);\n              if (mark1ArrIndex !== -1) {\n                const prevGlyphIndex = getPrevGlyphIndex();\n                if (prevGlyphIndex !== -1) {\n                  const prevGlyphId = glyphIds[prevGlyphIndex];\n                  if (getGlyphClass(font, prevGlyphId) === 3) { // only check mark glyphs\n                    const mark2ArrIndex = Typr._lctf.coverageIndex(tab.mark2Coverage, prevGlyphId);\n                    if (mark2ArrIndex !== -1) {\n                      const mark1Record = tab.mark1Array[mark1ArrIndex];\n                      const mark2Anchor = tab.mark2Array[mark2ArrIndex][mark1Record.markClass];\n                      positions[glyphIndex * 3] = mark2Anchor.x - mark1Record.x + positions[prevGlyphIndex * 3] - positions[prevGlyphIndex * 3 + 2];\n                      positions[glyphIndex * 3 + 1] = mark2Anchor.y - mark1Record.y + positions[prevGlyphIndex * 3 + 1];\n                      break;\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n      // Check kern table if no GPOS\n      else if (font.kern && !font.cff) {\n        const prevGlyphIndex = getPrevGlyphIndex();\n        if (prevGlyphIndex !== -1) {\n          const ind1 = font.kern.glyph1.indexOf(glyphIds[prevGlyphIndex]);\n          if (ind1 !== -1) {\n            const ind2 = font.kern.rval[ind1].glyph2.indexOf(glyphId);\n            if (ind2 !== -1) {\n              positions[prevGlyphIndex * 3 + 2] += font.kern.rval[ind1].vals[ind2];\n            }\n          }\n        }\n      }\n    }\n\n    return positions;\n\n    function getPrevGlyphIndex(filter) {\n      for (let i = glyphIndex - 1; i >=0; i--) {\n        if (glyphIds[i] !== -1 && (!filter || filter(glyphIds[i]))) {\n          return i\n        }\n      }\n      return -1;\n    }\n\n    function isBaseGlyph(glyphId) {\n      return getGlyphClass(font, glyphId) === 1;\n    }\n\n    function applyValueRecord(source, gi) {\n      for (let i = 0; i < 3; i++) {\n        positions[gi * 3 + i] += source[i] || 0;\n      }\n    }\n  }\n\n  function getGlyphClass(font, glyphId) {\n    const classDef = font.GDEF && font.GDEF.glyphClassDef;\n    return classDef ? Typr.U._getGlyphClass(glyphId, classDef) : 0;\n  }\n\n  function firstNum(...args) {\n    for (let i = 0; i < args.length; i++) {\n      if (typeof args[i] === 'number') {\n        return args[i]\n      }\n    }\n  }\n\n  /**\n   * @returns ParsedFont\n   */\n  function wrapFontObj(typrFont) {\n    const glyphMap = Object.create(null);\n\n    const os2 = typrFont['OS/2'];\n    const hhea = typrFont.hhea;\n    const unitsPerEm = typrFont.head.unitsPerEm;\n    const ascender = firstNum(os2 && os2.sTypoAscender, hhea && hhea.ascender, unitsPerEm);\n\n    /** @type ParsedFont */\n    const fontObj = {\n      unitsPerEm,\n      ascender,\n      descender: firstNum(os2 && os2.sTypoDescender, hhea && hhea.descender, 0),\n      capHeight: firstNum(os2 && os2.sCapHeight, ascender),\n      xHeight: firstNum(os2 && os2.sxHeight, ascender),\n      lineGap: firstNum(os2 && os2.sTypoLineGap, hhea && hhea.lineGap),\n      supportsCodePoint(code) {\n        return Typr.U.codeToGlyph(typrFont, code) > 0\n      },\n      forEachGlyph(text, fontSize, letterSpacing, callback) {\n        let penX = 0;\n        const fontScale = 1 / fontObj.unitsPerEm * fontSize;\n\n        const glyphIds = stringToGlyphs(typrFont, text);\n        let charIndex = 0;\n        const positions = calcGlyphPositions(typrFont, glyphIds);\n\n        glyphIds.forEach((glyphId, i) => {\n          // Typr returns a glyph index per string codepoint, with -1s in place of those that\n          // were omitted due to ligature substitution. So we can track original index in the\n          // string via simple increment, and skip everything else when seeing a -1.\n          if (glyphId !== -1) {\n            let glyphObj = glyphMap[glyphId];\n            if (!glyphObj) {\n              const {cmds, crds} = Typr.U.glyphToPath(typrFont, glyphId);\n\n              // Build path string\n              let path = '';\n              let crdsIdx = 0;\n              for (let i = 0, len = cmds.length; i < len; i++) {\n                const numArgs = cmdArgLengths[cmds[i]];\n                path += cmds[i];\n                for (let j = 1; j <= numArgs; j++) {\n                  path += (j > 1 ? ',' : '') + crds[crdsIdx++];\n                }\n              }\n\n              // Find extents - Glyf gives this in metadata but not CFF, and Typr doesn't\n              // normalize the two, so it's simplest just to iterate ourselves.\n              let xMin, yMin, xMax, yMax;\n              if (crds.length) {\n                xMin = yMin = Infinity;\n                xMax = yMax = -Infinity;\n                for (let i = 0, len = crds.length; i < len; i += 2) {\n                  let x = crds[i];\n                  let y = crds[i + 1];\n                  if (x < xMin) xMin = x;\n                  if (y < yMin) yMin = y;\n                  if (x > xMax) xMax = x;\n                  if (y > yMax) yMax = y;\n                }\n              } else {\n                xMin = xMax = yMin = yMax = 0;\n              }\n\n              glyphObj = glyphMap[glyphId] = {\n                index: glyphId,\n                advanceWidth: typrFont.hmtx.aWidth[glyphId],\n                xMin,\n                yMin,\n                xMax,\n                yMax,\n                path,\n              };\n            }\n\n            callback.call(\n              null,\n              glyphObj,\n              penX + positions[i * 3] * fontScale,\n              positions[i * 3 + 1] * fontScale,\n              charIndex\n            );\n\n            penX += positions[i * 3 + 2] * fontScale;\n            if (letterSpacing) {\n              penX += letterSpacing * fontSize;\n            }\n          }\n          charIndex += (text.codePointAt(charIndex) > 0xffff ? 2 : 1);\n        });\n\n        return penX\n      }\n    };\n\n    return fontObj\n  }\n\n  /**\n   * @type FontParser\n   */\n  return function parse(buffer) {\n    // Look to see if we have a WOFF file and convert it if so:\n    const peek = new Uint8Array(buffer, 0, 4);\n    const tag = Typr._bin.readASCII(peek, 0, 4);\n    if (tag === 'wOFF') {\n      buffer = woff2otf(buffer);\n    } else if (tag === 'wOF2') {\n      throw new Error('woff2 fonts not supported')\n    }\n    return wrapFontObj(Typr.parse(buffer)[0])\n  }\n}\n\n\nconst workerModule = /*#__PURE__*/defineWorkerModule({\n  name: 'Typr Font Parser',\n  dependencies: [typrFactory, woff2otfFactory, parserFactory],\n  init(typrFactory, woff2otfFactory, parserFactory) {\n    const Typr = typrFactory();\n    const woff2otf = woff2otfFactory();\n    return parserFactory(Typr, woff2otf)\n  }\n});\n\n/*!\nCustom bundle of @unicode-font-resolver/client v1.0.2 (https://github.com/lojjic/unicode-font-resolver)\nfor use in Troika text rendering. \nOriginal MIT license applies\n*/\nfunction unicodeFontResolverClientFactory(){return function(t){var n=function(){this.buckets=new Map;};n.prototype.add=function(t){var n=t>>5;this.buckets.set(n,(this.buckets.get(n)||0)|1<<(31&t));},n.prototype.has=function(t){var n=this.buckets.get(t>>5);return void 0!==n&&0!=(n&1<<(31&t))},n.prototype.serialize=function(){var t=[];return this.buckets.forEach((function(n,r){t.push((+r).toString(36)+\":\"+n.toString(36));})),t.join(\",\")},n.prototype.deserialize=function(t){var n=this;this.buckets.clear(),t.split(\",\").forEach((function(t){var r=t.split(\":\");n.buckets.set(parseInt(r[0],36),parseInt(r[1],36));}));};var r=Math.pow(2,8),e=r-1,o=~e;function a(t){var n=function(t){return t&o}(t).toString(16),e=function(t){return (t&o)+r-1}(t).toString(16);return \"codepoint-index/plane\"+(t>>16)+\"/\"+n+\"-\"+e+\".json\"}function i(t,n){var r=t&e,o=n.codePointAt(r/6|0);return 0!=((o=(o||48)-48)&1<<r%6)}function u(t,n){var r;(r=t,r.replace(/U\\+/gi,\"\").replace(/^,+|,+$/g,\"\").split(/,+/).map((function(t){return t.split(\"-\").map((function(t){return parseInt(t.trim(),16)}))}))).forEach((function(t){var r=t[0],e=t[1];void 0===e&&(e=r),n(r,e);}));}function c(t,n){u(t,(function(t,r){for(var e=t;e<=r;e++)n(e);}));}var s={},f={},l=new WeakMap,v=\"https://cdn.jsdelivr.net/gh/lojjic/unicode-font-resolver@v1.0.1/packages/data\";function d(t){var r=l.get(t);return r||(r=new n,c(t.ranges,(function(t){return r.add(t)})),l.set(t,r)),r}var h,p=new Map;function g(t,n,r){return t[n]?n:t[r]?r:function(t){for(var n in t)return n}(t)}function w(t,n){var r=n;if(!t.includes(r)){r=1/0;for(var e=0;e<t.length;e++)Math.abs(t[e]-n)<Math.abs(r-n)&&(r=t[e]);}return r}function k(t){return h||(h=new Set,c(\"9-D,20,85,A0,1680,2000-200A,2028-202F,205F,3000\",(function(t){h.add(t);}))),h.has(t)}return t.CodePointSet=n,t.clearCache=function(){s={},f={};},t.getFontsForString=function(t,n){void 0===n&&(n={});var r,e=n.lang;void 0===e&&(e=/\\p{Script=Hangul}/u.test(r=t)?\"ko\":/\\p{Script=Hiragana}|\\p{Script=Katakana}/u.test(r)?\"ja\":\"en\");var o=n.category;void 0===o&&(o=\"sans-serif\");var u=n.style;void 0===u&&(u=\"normal\");var c=n.weight;void 0===c&&(c=400);var l=(n.dataUrl||v).replace(/\\/$/g,\"\"),h=new Map,y=new Uint8Array(t.length),b={},m={},A=new Array(t.length),S=new Map,j=!1;function M(t){var n=p.get(t);return n||(n=fetch(l+\"/\"+t).then((function(t){if(!t.ok)throw new Error(t.statusText);return t.json().then((function(t){if(!Array.isArray(t)||1!==t[0])throw new Error(\"Incorrect schema version; need 1, got \"+t[0]);return t[1]}))})).catch((function(n){if(l!==v)return j||(console.error('unicode-font-resolver: Failed loading from dataUrl \"'+l+'\", trying default CDN. '+n.message),j=!0),l=v,p.delete(t),M(t);throw n})),p.set(t,n)),n}for(var P=function(n){var r=t.codePointAt(n),e=a(r);A[n]=e,s[e]||S.has(e)||S.set(e,M(e).then((function(t){s[e]=t;}))),r>65535&&(n++,E=n);},E=0;E<t.length;E++)P(E);return Promise.all(S.values()).then((function(){S.clear();for(var n=function(n){var o=t.codePointAt(n),a=null,u=s[A[n]],c=void 0;for(var l in u){var v=m[l];if(void 0===v&&(v=m[l]=new RegExp(l).test(e||\"en\")),v){for(var d in c=l,u[l])if(i(o,u[l][d])){a=d;break}break}}if(!a)t:for(var h in u)if(h!==c)for(var p in u[h])if(i(o,u[h][p])){a=p;break t}a||(console.debug(\"No font coverage for U+\"+o.toString(16)),a=\"latin\"),A[n]=a,f[a]||S.has(a)||S.set(a,M(\"font-meta/\"+a+\".json\").then((function(t){f[a]=t;}))),o>65535&&(n++,r=n);},r=0;r<t.length;r++)n(r);return Promise.all(S.values())})).then((function(){for(var n,r=null,e=0;e<t.length;e++){var a=t.codePointAt(e);if(r&&(k(a)||d(r).has(a)))y[e]=y[e-1];else {r=f[A[e]];var i=b[r.id];if(!i){var s=r.typeforms,v=g(s,o,\"sans-serif\"),p=g(s[v],u,\"normal\"),m=w(null===(n=s[v])||void 0===n?void 0:n[p],c);i=b[r.id]=l+\"/font-files/\"+r.id+\"/\"+v+\".\"+p+\".\"+m+\".woff\";}var S=h.get(i);null==S&&(S=h.size,h.set(i,S)),y[e]=S;}a>65535&&(e++,y[e]=y[e-1]);}return {fontUrls:Array.from(h.keys()),chars:y}}))},Object.defineProperty(t,\"__esModule\",{value:!0}),t}({})}\n\n/**\n * @typedef {string | {src:string, label?:string, unicodeRange?:string, lang?:string}} UserFont\n */\n\n/**\n * @typedef {ClientOptions} FontResolverOptions\n * @property {Array<UserFont>|UserFont} [fonts]\n * @property {'normal'|'italic'} [style]\n * @property {'normal'|'bold'|number} [style]\n * @property {string} [unicodeFontsURL]\n */\n\n/**\n * @typedef {Object} FontResolverResult\n * @property {Uint8Array} chars\n * @property {Array<ParsedFont & {src:string}>} fonts\n */\n\n/**\n * @typedef {function} FontResolver\n * @param {string} text\n * @param {(FontResolverResult) => void} callback\n * @param {FontResolverOptions} [options]\n */\n\n/**\n * Factory for the FontResolver function.\n * @param {FontParser} fontParser\n * @param {{getFontsForString: function, CodePointSet: function}} unicodeFontResolverClient\n * @return {FontResolver}\n */\nfunction createFontResolver(fontParser, unicodeFontResolverClient) {\n  /**\n   * @type {Record<string, ParsedFont>}\n   */\n  const parsedFonts = Object.create(null);\n\n  /**\n   * @type {Record<string, Array<(ParsedFont) => void>>}\n   */\n  const loadingFonts = Object.create(null);\n\n  /**\n   * Load a given font url\n   */\n  function doLoadFont(url, callback) {\n    const onError = err => {\n      console.error(`Failure loading font ${url}`, err);\n    };\n    try {\n      const request = new XMLHttpRequest();\n      request.open('get', url, true);\n      request.responseType = 'arraybuffer';\n      request.onload = function () {\n        if (request.status >= 400) {\n          onError(new Error(request.statusText));\n        }\n        else if (request.status > 0) {\n          try {\n            const fontObj = fontParser(request.response);\n            fontObj.src = url;\n            callback(fontObj);\n          } catch (e) {\n            onError(e);\n          }\n        }\n      };\n      request.onerror = onError;\n      request.send();\n    } catch(err) {\n      onError(err);\n    }\n  }\n\n\n  /**\n   * Load a given font url if needed, invoking a callback when it's loaded. If already\n   * loaded, the callback will be called synchronously.\n   * @param {string} fontUrl\n   * @param {(font: ParsedFont) => void} callback\n   */\n  function loadFont(fontUrl, callback) {\n    let font = parsedFonts[fontUrl];\n    if (font) {\n      callback(font);\n    } else if (loadingFonts[fontUrl]) {\n      loadingFonts[fontUrl].push(callback);\n    } else {\n      loadingFonts[fontUrl] = [callback];\n      doLoadFont(fontUrl, fontObj => {\n        fontObj.src = fontUrl;\n        parsedFonts[fontUrl] = fontObj;\n        loadingFonts[fontUrl].forEach(cb => cb(fontObj));\n        delete loadingFonts[fontUrl];\n      });\n    }\n  }\n\n  /**\n   * For a given string of text, determine which fonts are required to fully render it and\n   * ensure those fonts are loaded.\n   */\n  return function (text, callback, {\n    lang,\n    fonts: userFonts = [],\n    style = 'normal',\n    weight = 'normal',\n    unicodeFontsURL\n  } = {}) {\n    const charResolutions = new Uint8Array(text.length);\n    const fontResolutions = [];\n    if (!text.length) {\n      allDone();\n    }\n\n    const fontIndices = new Map();\n    const fallbackRanges = []; // [[start, end], ...]\n\n    if (style !== 'italic') style = 'normal';\n    if (typeof weight !== 'number') {\n      weight = weight === 'bold' ? 700 : 400;\n    }\n\n    if (userFonts && !Array.isArray(userFonts)) {\n      userFonts = [userFonts];\n    }\n    userFonts = userFonts.slice()\n      // filter by language\n      .filter(def => !def.lang || def.lang.test(lang))\n      // switch order for easier iteration\n      .reverse();\n    if (userFonts.length) {\n      const UNKNOWN = 0;\n      const RESOLVED = 1;\n      const NEEDS_FALLBACK = 2;\n      let prevCharResult = UNKNOWN\n\n      ;(function resolveUserFonts (startIndex = 0) {\n        for (let i = startIndex, iLen = text.length; i < iLen; i++) {\n          const codePoint = text.codePointAt(i);\n          // Carry previous character's result forward if:\n          // - it resolved to a font that also covers this character\n          // - this character is whitespace\n          if (\n            (prevCharResult === RESOLVED && fontResolutions[charResolutions[i - 1]].supportsCodePoint(codePoint)) ||\n            (i > 0 && /\\s/.test(text[i]))\n          ) {\n            charResolutions[i] = charResolutions[i - 1];\n            if (prevCharResult === NEEDS_FALLBACK) {\n              fallbackRanges[fallbackRanges.length - 1][1] = i;\n            }\n          } else {\n            for (let j = charResolutions[i], jLen = userFonts.length; j <= jLen; j++) {\n              if (j === jLen) {\n                // none of the user fonts matched; needs fallback\n                const range = prevCharResult === NEEDS_FALLBACK ?\n                  fallbackRanges[fallbackRanges.length - 1] :\n                  (fallbackRanges[fallbackRanges.length] = [i, i]);\n                range[1] = i;\n                prevCharResult = NEEDS_FALLBACK;\n              } else {\n                charResolutions[i] = j;\n                const { src, unicodeRange } = userFonts[j];\n                // filter by optional explicit unicode ranges\n                if (!unicodeRange || isCodeInRanges(codePoint, unicodeRange)) {\n                  const fontObj = parsedFonts[src];\n                  // font not yet loaded, load it and resume\n                  if (!fontObj) {\n                    loadFont(src, () => {\n                      resolveUserFonts(i);\n                    });\n                    return;\n                  }\n                  // if the font actually contains a glyph for this char, lock it in\n                  if (fontObj.supportsCodePoint(codePoint)) {\n                    let fontIndex = fontIndices.get(fontObj);\n                    if (typeof fontIndex !== 'number') {\n                      fontIndex = fontResolutions.length;\n                      fontResolutions.push(fontObj);\n                      fontIndices.set(fontObj, fontIndex);\n                    }\n                    charResolutions[i] = fontIndex;\n                    prevCharResult = RESOLVED;\n                    break;\n                  }\n                }\n              }\n            }\n          }\n\n          if (codePoint > 0xffff && i + 1 < iLen) {\n            charResolutions[i + 1] = charResolutions[i];\n            i++;\n            if (prevCharResult === NEEDS_FALLBACK) {\n              fallbackRanges[fallbackRanges.length - 1][1] = i;\n            }\n          }\n        }\n        resolveFallbacks();\n      })();\n    } else {\n      fallbackRanges.push([0, text.length - 1]);\n      resolveFallbacks();\n    }\n\n    function resolveFallbacks() {\n      if (fallbackRanges.length) {\n        // Combine all fallback substrings into a single string for querying\n        const fallbackString = fallbackRanges.map(range => text.substring(range[0], range[1] + 1)).join('\\n');\n        unicodeFontResolverClient.getFontsForString(fallbackString, {\n          lang: lang || undefined,\n          style,\n          weight,\n          dataUrl: unicodeFontsURL\n        }).then(({fontUrls, chars}) => {\n          // Extract results and put them back in the main array\n          const fontIndexOffset = fontResolutions.length;\n          let charIdx = 0;\n          fallbackRanges.forEach(range => {\n            for (let i = 0, endIdx = range[1] - range[0]; i <= endIdx; i++) {\n              charResolutions[range[0] + i] = chars[charIdx++] + fontIndexOffset;\n            }\n            charIdx++; //skip segment separator\n          });\n\n          // Load and parse the fallback fonts - avoiding Promise here to prevent polyfills in the worker\n          let loadedCount = 0;\n          fontUrls.forEach((url, i) => {\n            loadFont(url, fontObj => {\n              fontResolutions[i + fontIndexOffset] = fontObj;\n              if (++loadedCount === fontUrls.length) {\n                allDone();\n              }\n            });\n          });\n        });\n      } else {\n        allDone();\n      }\n    }\n\n    function allDone() {\n      callback({\n        chars: charResolutions,\n        fonts: fontResolutions\n      });\n    }\n\n    function isCodeInRanges(code, ranges) {\n      // todo optimize search - CodePointSet from unicode-font-resolver?\n      for (let k = 0; k < ranges.length; k++) {\n        const [start, end = start] = ranges[k];\n        if (start <= code && code <= end) {\n          return true\n        }\n      }\n      return false\n    }\n  }\n}\n\nconst fontResolverWorkerModule = /*#__PURE__*/defineWorkerModule({\n  name: 'FontResolver',\n  dependencies: [\n    createFontResolver,\n    workerModule,\n    unicodeFontResolverClientFactory,\n  ],\n  init(createFontResolver, fontParser, unicodeFontResolverClientFactory) {\n    return createFontResolver(fontParser, unicodeFontResolverClientFactory());\n  }\n});\n\n/**\n * @typedef {number|'left'|'center'|'right'} AnchorXValue\n */\n/**\n * @typedef {number|'top'|'top-baseline'|'top-cap'|'top-ex'|'middle'|'bottom-baseline'|'bottom'} AnchorYValue\n */\n\n/**\n * @typedef {object} TypesetParams\n * @property {string} text\n * @property {UserFont|UserFont[]} [font]\n * @property {string} [lang]\n * @property {number} [sdfGlyphSize=64]\n * @property {number} [fontSize=1]\n * @property {number|'normal'|'bold'} [fontWeight='normal']\n * @property {'normal'|'italic'} [fontStyle='normal']\n * @property {number} [letterSpacing=0]\n * @property {'normal'|number} [lineHeight='normal']\n * @property {number} [maxWidth]\n * @property {'ltr'|'rtl'} [direction='ltr']\n * @property {string} [textAlign='left']\n * @property {number} [textIndent=0]\n * @property {'normal'|'nowrap'} [whiteSpace='normal']\n * @property {'normal'|'break-word'} [overflowWrap='normal']\n * @property {AnchorXValue} [anchorX=0]\n * @property {AnchorYValue} [anchorY=0]\n * @property {boolean} [metricsOnly=false]\n * @property {string} [unicodeFontsURL]\n * @property {FontResolverResult} [preResolvedFonts]\n * @property {boolean} [includeCaretPositions=false]\n * @property {number} [chunkedBoundsSize=8192]\n * @property {{[rangeStartIndex]: number}} [colorRanges]\n */\n\n/**\n * @typedef {object} TypesetResult\n * @property {Uint16Array} glyphIds id for each glyph, specific to that glyph's font\n * @property {Uint8Array} glyphFontIndices index into fontData for each glyph\n * @property {Float32Array} glyphPositions x,y of each glyph's origin in layout\n * @property {{[font]: {[glyphId]: {path: string, pathBounds: number[]}}}} glyphData data about each glyph appearing in the text\n * @property {TypesetFontData[]} fontData data about each font used in the text\n * @property {Float32Array} [caretPositions] startX,endX,bottomY caret positions for each char\n * @property {Uint8Array} [glyphColors] color for each glyph, if color ranges supplied\n *         chunkedBounds, //total rects per (n=chunkedBoundsSize) consecutive glyphs\n *         fontSize, //calculated em height\n *         topBaseline: anchorYOffset + lines[0].baseline, //y coordinate of the top line's baseline\n *         blockBounds: [ //bounds for the whole block of text, including vertical padding for lineHeight\n *           anchorXOffset,\n *           anchorYOffset - totalHeight,\n *           anchorXOffset + maxLineWidth,\n *           anchorYOffset\n *         ],\n *         visibleBounds, //total bounds of visible text paths, may be larger or smaller than blockBounds\n *         timings\n */\n\n/**\n * @typedef {object} TypesetFontData\n * @property src\n * @property unitsPerEm\n * @property ascender\n * @property descender\n * @property lineHeight\n * @property capHeight\n * @property xHeight\n */\n\n/**\n * @typedef {function} TypesetterTypesetFunction - compute fonts and layout for some text.\n * @param {TypesetParams} params\n * @param {(TypesetResult) => void} callback - function called when typesetting is complete.\n *    If the params included `preResolvedFonts`, this will be called synchronously.\n */\n\n/**\n * @typedef {function} TypesetterMeasureFunction - compute width/height for some text.\n * @param {TypesetParams} params\n * @param {(width:number, height:number) => void} callback - function called when measurement is complete.\n *    If the params included `preResolvedFonts`, this will be called synchronously.\n */\n\n\n/**\n * Factory function that creates a self-contained environment for processing text typesetting requests.\n *\n * It is important that this function has no closure dependencies, so that it can be easily injected\n * into the source for a Worker without requiring a build step or complex dependency loading. All its\n * dependencies must be passed in at initialization.\n *\n * @param {FontResolver} resolveFonts - function to resolve a string to parsed fonts\n * @param {object} bidi - the bidi.js implementation object\n * @return {{typeset: TypesetterTypesetFunction, measure: TypesetterMeasureFunction}}\n */\nfunction createTypesetter(resolveFonts, bidi) {\n  const INF = Infinity;\n\n  // Set of Unicode Default_Ignorable_Code_Point characters, these will not produce visible glyphs\n  // eslint-disable-next-line no-misleading-character-class\n  const DEFAULT_IGNORABLE_CHARS = /[\\u00AD\\u034F\\u061C\\u115F-\\u1160\\u17B4-\\u17B5\\u180B-\\u180E\\u200B-\\u200F\\u202A-\\u202E\\u2060-\\u206F\\u3164\\uFE00-\\uFE0F\\uFEFF\\uFFA0\\uFFF0-\\uFFF8]/;\n\n  // This regex (instead of /\\s/) allows us to select all whitespace EXCEPT for non-breaking white spaces\n  const lineBreakingWhiteSpace = `[^\\\\S\\\\u00A0]`;\n\n  // Incomplete set of characters that allow line breaking after them\n  // In the future we may consider a full Unicode line breaking algorithm impl: https://www.unicode.org/reports/tr14\n  const BREAK_AFTER_CHARS = new RegExp(`${lineBreakingWhiteSpace}|[\\\\-\\\\u007C\\\\u00AD\\\\u2010\\\\u2012-\\\\u2014\\\\u2027\\\\u2056\\\\u2E17\\\\u2E40]`);\n\n  /**\n   * Load and parse all the necessary fonts to render a given string of text, then group\n   * them into consecutive runs of characters sharing a font.\n   */\n  function calculateFontRuns({text, lang, fonts, style, weight, preResolvedFonts, unicodeFontsURL}, onDone) {\n    const onResolved = ({chars, fonts: parsedFonts}) => {\n      let curRun, prevVal;\n      const runs = [];\n      for (let i = 0; i < chars.length; i++) {\n        if (chars[i] !== prevVal) {\n          prevVal = chars[i];\n          runs.push(curRun = { start: i, end: i, fontObj: parsedFonts[chars[i]]});\n        } else {\n          curRun.end = i;\n        }\n      }\n      onDone(runs);\n    };\n    if (preResolvedFonts) {\n      onResolved(preResolvedFonts);\n    } else {\n      resolveFonts(\n        text,\n        onResolved,\n        { lang, fonts, style, weight, unicodeFontsURL }\n      );\n    }\n  }\n\n  /**\n   * Main entry point.\n   * Process a text string with given font and formatting parameters, and return all info\n   * necessary to render all its glyphs.\n   * @type TypesetterTypesetFunction\n   */\n  function typeset(\n    {\n      text='',\n      font,\n      lang,\n      sdfGlyphSize=64,\n      fontSize=400,\n      fontWeight=1,\n      fontStyle='normal',\n      letterSpacing=0,\n      lineHeight='normal',\n      maxWidth=INF,\n      direction,\n      textAlign='left',\n      textIndent=0,\n      whiteSpace='normal',\n      overflowWrap='normal',\n      anchorX = 0,\n      anchorY = 0,\n      metricsOnly=false,\n      unicodeFontsURL,\n      preResolvedFonts=null,\n      includeCaretPositions=false,\n      chunkedBoundsSize=8192,\n      colorRanges=null\n    },\n    callback\n  ) {\n    const mainStart = now();\n    const timings = {fontLoad: 0, typesetting: 0};\n\n    // Ensure newlines are normalized\n    if (text.indexOf('\\r') > -1) {\n      console.info('Typesetter: got text with \\\\r chars; normalizing to \\\\n');\n      text = text.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n    }\n\n    // Ensure we've got numbers not strings\n    fontSize = +fontSize;\n    letterSpacing = +letterSpacing;\n    maxWidth = +maxWidth;\n    lineHeight = lineHeight || 'normal';\n    textIndent = +textIndent;\n\n    calculateFontRuns({\n      text,\n      lang,\n      style: fontStyle,\n      weight: fontWeight,\n      fonts: typeof font === 'string' ? [{src: font}] : font,\n      unicodeFontsURL,\n      preResolvedFonts\n    }, runs => {\n      timings.fontLoad = now() - mainStart;\n      const hasMaxWidth = isFinite(maxWidth);\n      let glyphIds = null;\n      let glyphFontIndices = null;\n      let glyphPositions = null;\n      let glyphData = null;\n      let glyphColors = null;\n      let caretPositions = null;\n      let visibleBounds = null;\n      let chunkedBounds = null;\n      let maxLineWidth = 0;\n      let renderableGlyphCount = 0;\n      let canWrap = whiteSpace !== 'nowrap';\n      const metricsByFont = new Map(); // fontObj -> metrics\n      const typesetStart = now();\n\n      // Distribute glyphs into lines based on wrapping\n      let lineXOffset = textIndent;\n      let prevRunEndX = 0;\n      let currentLine = new TextLine();\n      const lines = [currentLine];\n      runs.forEach(run => {\n        const { fontObj } = run;\n        const { ascender, descender, unitsPerEm, lineGap, capHeight, xHeight } = fontObj;\n\n        // Calculate metrics for each font used\n        let fontData = metricsByFont.get(fontObj);\n        if (!fontData) {\n          // Find conversion between native font units and fontSize units\n          const fontSizeMult = fontSize / unitsPerEm;\n\n          // Determine appropriate value for 'normal' line height based on the font's actual metrics\n          // This does not guarantee individual glyphs won't exceed the line height, e.g. Roboto; should we use yMin/Max instead?\n          const calcLineHeight = lineHeight === 'normal' ?\n            (ascender - descender + lineGap) * fontSizeMult : lineHeight * fontSize;\n\n          // Determine line height and leading adjustments\n          const halfLeading = (calcLineHeight - (ascender - descender) * fontSizeMult) / 2;\n          const caretHeight = Math.min(calcLineHeight, (ascender - descender) * fontSizeMult);\n          const caretTop = (ascender + descender) / 2 * fontSizeMult + caretHeight / 2;\n          fontData = {\n            index: metricsByFont.size,\n            src: fontObj.src,\n            fontObj,\n            fontSizeMult,\n            unitsPerEm,\n            ascender: ascender * fontSizeMult,\n            descender: descender * fontSizeMult,\n            capHeight: capHeight * fontSizeMult,\n            xHeight: xHeight * fontSizeMult,\n            lineHeight: calcLineHeight,\n            baseline: -halfLeading - ascender * fontSizeMult, // baseline offset from top of line height\n            // cap: -halfLeading - capHeight * fontSizeMult, // cap from top of line height\n            // ex: -halfLeading - xHeight * fontSizeMult, // ex from top of line height\n            caretTop,\n            caretBottom: caretTop - caretHeight\n          };\n          metricsByFont.set(fontObj, fontData);\n        }\n        const { fontSizeMult } = fontData;\n\n        const runText = text.slice(run.start, run.end + 1);\n        let prevGlyphX, prevGlyphObj;\n        fontObj.forEachGlyph(runText, fontSize, letterSpacing, (glyphObj, glyphX, glyphY, charIndex) => {\n          glyphX += prevRunEndX;\n          charIndex += run.start;\n          prevGlyphX = glyphX;\n          prevGlyphObj = glyphObj;\n          const char = text.charAt(charIndex);\n          const glyphWidth = glyphObj.advanceWidth * fontSizeMult;\n          const curLineCount = currentLine.count;\n          let nextLine;\n\n          // Calc isWhitespace and isEmpty once per glyphObj\n          if (!('isEmpty' in glyphObj)) {\n            glyphObj.isWhitespace = !!char && new RegExp(lineBreakingWhiteSpace).test(char);\n            glyphObj.canBreakAfter = !!char && BREAK_AFTER_CHARS.test(char);\n            glyphObj.isEmpty = glyphObj.xMin === glyphObj.xMax || glyphObj.yMin === glyphObj.yMax || DEFAULT_IGNORABLE_CHARS.test(char);\n          }\n          if (!glyphObj.isWhitespace && !glyphObj.isEmpty) {\n            renderableGlyphCount++;\n          }\n\n          // If a non-whitespace character overflows the max width, we need to soft-wrap\n          if (canWrap && hasMaxWidth && !glyphObj.isWhitespace && glyphX + glyphWidth + lineXOffset > maxWidth && curLineCount) {\n            // If it's the first char after a whitespace, start a new line\n            if (currentLine.glyphAt(curLineCount - 1).glyphObj.canBreakAfter) {\n              nextLine = new TextLine();\n              lineXOffset = -glyphX;\n            } else {\n              // Back up looking for a whitespace character to wrap at\n              for (let i = curLineCount; i--;) {\n                // If we got the start of the line there's no soft break point; make hard break if overflowWrap='break-word'\n                if (i === 0 && overflowWrap === 'break-word') {\n                  nextLine = new TextLine();\n                  lineXOffset = -glyphX;\n                  break\n                }\n                // Found a soft break point; move all chars since it to a new line\n                else if (currentLine.glyphAt(i).glyphObj.canBreakAfter) {\n                  nextLine = currentLine.splitAt(i + 1);\n                  const adjustX = nextLine.glyphAt(0).x;\n                  lineXOffset -= adjustX;\n                  for (let j = nextLine.count; j--;) {\n                    nextLine.glyphAt(j).x -= adjustX;\n                  }\n                  break\n                }\n              }\n            }\n            if (nextLine) {\n              currentLine.isSoftWrapped = true;\n              currentLine = nextLine;\n              lines.push(currentLine);\n              maxLineWidth = maxWidth; //after soft wrapping use maxWidth as calculated width\n            }\n          }\n\n          let fly = currentLine.glyphAt(currentLine.count);\n          fly.glyphObj = glyphObj;\n          fly.x = glyphX + lineXOffset;\n          fly.y = glyphY;\n          fly.width = glyphWidth;\n          fly.charIndex = charIndex;\n          fly.fontData = fontData;\n\n          // Handle hard line breaks\n          if (char === '\\n') {\n            currentLine = new TextLine();\n            lines.push(currentLine);\n            lineXOffset = -(glyphX + glyphWidth + (letterSpacing * fontSize)) + textIndent;\n          }\n        });\n        // At the end of a run we must capture the x position as the starting point for the next run\n        prevRunEndX = prevGlyphX + prevGlyphObj.advanceWidth * fontSizeMult + letterSpacing * fontSize;\n      });\n\n      // Calculate width/height/baseline of each line (excluding trailing whitespace) and maximum block width\n      let totalHeight = 0;\n      lines.forEach(line => {\n        let isTrailingWhitespace = true;\n        for (let i = line.count; i--;) {\n          const glyphInfo = line.glyphAt(i);\n          // omit trailing whitespace from width calculation\n          if (isTrailingWhitespace && !glyphInfo.glyphObj.isWhitespace) {\n            line.width = glyphInfo.x + glyphInfo.width;\n            if (line.width > maxLineWidth) {\n              maxLineWidth = line.width;\n            }\n            isTrailingWhitespace = false;\n          }\n          // use the tallest line height, lowest baseline, and highest cap/ex\n          let {lineHeight, capHeight, xHeight, baseline} = glyphInfo.fontData;\n          if (lineHeight > line.lineHeight) line.lineHeight = lineHeight;\n          const baselineDiff = baseline - line.baseline;\n          if (baselineDiff < 0) { //shift all metrics down\n            line.baseline += baselineDiff;\n            line.cap += baselineDiff;\n            line.ex += baselineDiff;\n          }\n          // compare cap/ex based on new lowest baseline\n          line.cap = Math.max(line.cap, line.baseline + capHeight);\n          line.ex = Math.max(line.ex, line.baseline + xHeight);\n        }\n        line.baseline -= totalHeight;\n        line.cap -= totalHeight;\n        line.ex -= totalHeight;\n        totalHeight += line.lineHeight;\n      });\n\n      // Find overall position adjustments for anchoring\n      let anchorXOffset = 0;\n      let anchorYOffset = 0;\n      if (anchorX) {\n        if (typeof anchorX === 'number') {\n          anchorXOffset = -anchorX;\n        }\n        else if (typeof anchorX === 'string') {\n          anchorXOffset = -maxLineWidth * (\n            anchorX === 'left' ? 0 :\n            anchorX === 'center' ? 0.5 :\n            anchorX === 'right' ? 1 :\n            parsePercent(anchorX)\n          );\n        }\n      }\n      if (anchorY) {\n        if (typeof anchorY === 'number') {\n          anchorYOffset = -anchorY;\n        }\n        else if (typeof anchorY === 'string') {\n          anchorYOffset = anchorY === 'top' ? 0 :\n            anchorY === 'top-baseline' ? -lines[0].baseline :\n            anchorY === 'top-cap' ? -lines[0].cap :\n            anchorY === 'top-ex' ? -lines[0].ex :\n            anchorY === 'middle' ? totalHeight / 2 :\n            anchorY === 'bottom' ? totalHeight :\n            anchorY === 'bottom-baseline' ? -lines[lines.length - 1].baseline :\n            parsePercent(anchorY) * totalHeight;\n        }\n      }\n\n      if (!metricsOnly) {\n        // Resolve bidi levels\n        const bidiLevelsResult = bidi.getEmbeddingLevels(text, direction);\n\n        // Process each line, applying alignment offsets, adding each glyph to the atlas, and\n        // collecting all renderable glyphs into a single collection.\n        glyphIds = new Uint16Array(renderableGlyphCount);\n        glyphFontIndices = new Uint8Array(renderableGlyphCount);\n        glyphPositions = new Float32Array(renderableGlyphCount * 2);\n        glyphData = {};\n        visibleBounds = [INF, INF, -INF, -INF];\n        chunkedBounds = [];\n        if (includeCaretPositions) {\n          caretPositions = new Float32Array(text.length * 4);\n        }\n        if (colorRanges) {\n          glyphColors = new Uint8Array(renderableGlyphCount * 3);\n        }\n        let renderableGlyphIndex = 0;\n        let prevCharIndex = -1;\n        let colorCharIndex = -1;\n        let chunk;\n        let currentColor;\n        lines.forEach((line, lineIndex) => {\n          let {count:lineGlyphCount, width:lineWidth} = line;\n\n          // Ignore empty lines\n          if (lineGlyphCount > 0) {\n            // Count trailing whitespaces, we want to ignore these for certain things\n            let trailingWhitespaceCount = 0;\n            for (let i = lineGlyphCount; i-- && line.glyphAt(i).glyphObj.isWhitespace;) {\n              trailingWhitespaceCount++;\n            }\n\n            // Apply horizontal alignment adjustments\n            let lineXOffset = 0;\n            let justifyAdjust = 0;\n            if (textAlign === 'center') {\n              lineXOffset = (maxLineWidth - lineWidth) / 2;\n            } else if (textAlign === 'right') {\n              lineXOffset = maxLineWidth - lineWidth;\n            } else if (textAlign === 'justify' && line.isSoftWrapped) {\n              // count non-trailing whitespace characters, and we'll adjust the offsets per character in the next loop\n              let whitespaceCount = 0;\n              for (let i = lineGlyphCount - trailingWhitespaceCount; i--;) {\n                if (line.glyphAt(i).glyphObj.isWhitespace) {\n                  whitespaceCount++;\n                }\n              }\n              justifyAdjust = (maxLineWidth - lineWidth) / whitespaceCount;\n            }\n            if (justifyAdjust || lineXOffset) {\n              let justifyOffset = 0;\n              for (let i = 0; i < lineGlyphCount; i++) {\n                let glyphInfo = line.glyphAt(i);\n                const glyphObj = glyphInfo.glyphObj;\n                glyphInfo.x += lineXOffset + justifyOffset;\n                // Expand non-trailing whitespaces for justify alignment\n                if (justifyAdjust !== 0 && glyphObj.isWhitespace && i < lineGlyphCount - trailingWhitespaceCount) {\n                  justifyOffset += justifyAdjust;\n                  glyphInfo.width += justifyAdjust;\n                }\n              }\n            }\n\n            // Perform bidi range flipping\n            const flips = bidi.getReorderSegments(\n              text, bidiLevelsResult, line.glyphAt(0).charIndex, line.glyphAt(line.count - 1).charIndex\n            );\n            for (let fi = 0; fi < flips.length; fi++) {\n              const [start, end] = flips[fi];\n              // Map start/end string indices to indices in the line\n              let left = Infinity, right = -Infinity;\n              for (let i = 0; i < lineGlyphCount; i++) {\n                if (line.glyphAt(i).charIndex >= start) { // gte to handle removed characters\n                  let startInLine = i, endInLine = i;\n                  for (; endInLine < lineGlyphCount; endInLine++) {\n                    let info = line.glyphAt(endInLine);\n                    if (info.charIndex > end) {\n                      break\n                    }\n                    if (endInLine < lineGlyphCount - trailingWhitespaceCount) { //don't include trailing ws in flip width\n                      left = Math.min(left, info.x);\n                      right = Math.max(right, info.x + info.width);\n                    }\n                  }\n                  for (let j = startInLine; j < endInLine; j++) {\n                    const glyphInfo = line.glyphAt(j);\n                    glyphInfo.x = right - (glyphInfo.x + glyphInfo.width - left);\n                  }\n                  break\n                }\n              }\n            }\n\n            // Assemble final data arrays\n            let glyphObj;\n            const setGlyphObj = g => glyphObj = g;\n            for (let i = 0; i < lineGlyphCount; i++) {\n              const glyphInfo = line.glyphAt(i);\n              glyphObj = glyphInfo.glyphObj;\n              const glyphId = glyphObj.index;\n\n              // Replace mirrored characters in rtl\n              const rtl = bidiLevelsResult.levels[glyphInfo.charIndex] & 1; //odd level means rtl\n              if (rtl) {\n                const mirrored = bidi.getMirroredCharacter(text[glyphInfo.charIndex]);\n                if (mirrored) {\n                  glyphInfo.fontData.fontObj.forEachGlyph(mirrored, 0, 0, setGlyphObj);\n                }\n              }\n\n              // Add caret positions\n              if (includeCaretPositions) {\n                const {charIndex, fontData} = glyphInfo;\n                const caretLeft = glyphInfo.x + anchorXOffset;\n                const caretRight = glyphInfo.x + glyphInfo.width + anchorXOffset;\n                caretPositions[charIndex * 4] = rtl ? caretRight : caretLeft; //start edge x\n                caretPositions[charIndex * 4 + 1] = rtl ? caretLeft : caretRight; //end edge x\n                caretPositions[charIndex * 4 + 2] = line.baseline + fontData.caretBottom + anchorYOffset; //common bottom y\n                caretPositions[charIndex * 4 + 3] = line.baseline + fontData.caretTop + anchorYOffset; //common top y\n\n                // If we skipped any chars from the previous glyph (due to ligature subs), fill in caret\n                // positions for those missing char indices; currently this uses a best-guess by dividing\n                // the ligature's width evenly. In the future we may try to use the font's LigatureCaretList\n                // table to get better interior caret positions.\n                const ligCount = charIndex - prevCharIndex;\n                if (ligCount > 1) {\n                  fillLigatureCaretPositions(caretPositions, prevCharIndex, ligCount);\n                }\n                prevCharIndex = charIndex;\n              }\n\n              // Track current color range\n              if (colorRanges) {\n                const {charIndex} = glyphInfo;\n                while(charIndex > colorCharIndex) {\n                  colorCharIndex++;\n                  if (colorRanges.hasOwnProperty(colorCharIndex)) {\n                    currentColor = colorRanges[colorCharIndex];\n                  }\n                }\n              }\n\n              // Get atlas data for renderable glyphs\n              if (!glyphObj.isWhitespace && !glyphObj.isEmpty) {\n                const idx = renderableGlyphIndex++;\n                const {fontSizeMult, src: fontSrc, index: fontIndex} = glyphInfo.fontData;\n\n                // Add this glyph's path data\n                const fontGlyphData = glyphData[fontSrc] || (glyphData[fontSrc] = {});\n                if (!fontGlyphData[glyphId]) {\n                  fontGlyphData[glyphId] = {\n                    path: glyphObj.path,\n                    pathBounds: [glyphObj.xMin, glyphObj.yMin, glyphObj.xMax, glyphObj.yMax]\n                  };\n                }\n\n                // Determine final glyph position and add to glyphPositions array\n                const glyphX = glyphInfo.x + anchorXOffset;\n                const glyphY = glyphInfo.y + line.baseline + anchorYOffset;\n                glyphPositions[idx * 2] = glyphX;\n                glyphPositions[idx * 2 + 1] = glyphY;\n\n                // Track total visible bounds\n                const visX0 = glyphX + glyphObj.xMin * fontSizeMult;\n                const visY0 = glyphY + glyphObj.yMin * fontSizeMult;\n                const visX1 = glyphX + glyphObj.xMax * fontSizeMult;\n                const visY1 = glyphY + glyphObj.yMax * fontSizeMult;\n                if (visX0 < visibleBounds[0]) visibleBounds[0] = visX0;\n                if (visY0 < visibleBounds[1]) visibleBounds[1] = visY0;\n                if (visX1 > visibleBounds[2]) visibleBounds[2] = visX1;\n                if (visY1 > visibleBounds[3]) visibleBounds[3] = visY1;\n\n                // Track bounding rects for each chunk of N glyphs\n                if (idx % chunkedBoundsSize === 0) {\n                  chunk = {start: idx, end: idx, rect: [INF, INF, -INF, -INF]};\n                  chunkedBounds.push(chunk);\n                }\n                chunk.end++;\n                const chunkRect = chunk.rect;\n                if (visX0 < chunkRect[0]) chunkRect[0] = visX0;\n                if (visY0 < chunkRect[1]) chunkRect[1] = visY0;\n                if (visX1 > chunkRect[2]) chunkRect[2] = visX1;\n                if (visY1 > chunkRect[3]) chunkRect[3] = visY1;\n\n                // Add to glyph ids and font indices arrays\n                glyphIds[idx] = glyphId;\n                glyphFontIndices[idx] = fontIndex;\n\n                // Add colors\n                if (colorRanges) {\n                  const start = idx * 3;\n                  glyphColors[start] = currentColor >> 16 & 255;\n                  glyphColors[start + 1] = currentColor >> 8 & 255;\n                  glyphColors[start + 2] = currentColor & 255;\n                }\n              }\n            }\n          }\n        });\n\n        // Fill in remaining caret positions in case the final character was a ligature\n        if (caretPositions) {\n          const ligCount = text.length - prevCharIndex;\n          if (ligCount > 1) {\n            fillLigatureCaretPositions(caretPositions, prevCharIndex, ligCount);\n          }\n        }\n      }\n\n      // Assemble final data about each font used\n      const fontData = [];\n      metricsByFont.forEach(({index, src, unitsPerEm, ascender, descender, lineHeight, capHeight, xHeight}) => {\n        fontData[index] = {src, unitsPerEm, ascender, descender, lineHeight, capHeight, xHeight};\n      });\n\n      // Timing stats\n      timings.typesetting = now() - typesetStart;\n\n      callback({\n        glyphIds, //id for each glyph, specific to that glyph's font\n        glyphFontIndices, //index into fontData for each glyph\n        glyphPositions, //x,y of each glyph's origin in layout\n        glyphData, //dict holding data about each glyph appearing in the text\n        fontData, //data about each font used in the text\n        caretPositions, //startX,endX,bottomY caret positions for each char\n        // caretHeight, //height of cursor from bottom to top - todo per glyph?\n        glyphColors, //color for each glyph, if color ranges supplied\n        chunkedBounds, //total rects per (n=chunkedBoundsSize) consecutive glyphs\n        fontSize, //calculated em height\n        topBaseline: anchorYOffset + lines[0].baseline, //y coordinate of the top line's baseline\n        blockBounds: [ //bounds for the whole block of text, including vertical padding for lineHeight\n          anchorXOffset,\n          anchorYOffset - totalHeight,\n          anchorXOffset + maxLineWidth,\n          anchorYOffset\n        ],\n        visibleBounds, //total bounds of visible text paths, may be larger or smaller than blockBounds\n        timings\n      });\n    });\n  }\n\n\n  /**\n   * For a given text string and font parameters, determine the resulting block dimensions\n   * after wrapping for the given maxWidth.\n   * @param args\n   * @param callback\n   */\n  function measure(args, callback) {\n    typeset({...args, metricsOnly: true}, (result) => {\n      const [x0, y0, x1, y1] = result.blockBounds;\n      callback({\n        width: x1 - x0,\n        height: y1 - y0\n      });\n    });\n  }\n\n  function parsePercent(str) {\n    let match = str.match(/^([\\d.]+)%$/);\n    let pct = match ? parseFloat(match[1]) : NaN;\n    return isNaN(pct) ? 0 : pct / 100\n  }\n\n  function fillLigatureCaretPositions(caretPositions, ligStartIndex, ligCount) {\n    const ligStartX = caretPositions[ligStartIndex * 4];\n    const ligEndX = caretPositions[ligStartIndex * 4 + 1];\n    const ligBottom = caretPositions[ligStartIndex * 4 + 2];\n    const ligTop = caretPositions[ligStartIndex * 4 + 3];\n    const guessedAdvanceX = (ligEndX - ligStartX) / ligCount;\n    for (let i = 0; i < ligCount; i++) {\n      const startIndex = (ligStartIndex + i) * 4;\n      caretPositions[startIndex] = ligStartX + guessedAdvanceX * i;\n      caretPositions[startIndex + 1] = ligStartX + guessedAdvanceX * (i + 1);\n      caretPositions[startIndex + 2] = ligBottom;\n      caretPositions[startIndex + 3] = ligTop;\n    }\n  }\n\n  function now() {\n    return (self.performance || Date).now()\n  }\n\n  // Array-backed structure for a single line's glyphs data\n  function TextLine() {\n    this.data = [];\n  }\n  const textLineProps = ['glyphObj', 'x', 'y', 'width', 'charIndex', 'fontData'];\n  TextLine.prototype = {\n    width: 0,\n    lineHeight: 0,\n    baseline: 0,\n    cap: 0,\n    ex: 0,\n    isSoftWrapped: false,\n    get count() {\n      return Math.ceil(this.data.length / textLineProps.length)\n    },\n    glyphAt(i) {\n      let fly = TextLine.flyweight;\n      fly.data = this.data;\n      fly.index = i;\n      return fly\n    },\n    splitAt(i) {\n      let newLine = new TextLine();\n      newLine.data = this.data.splice(i * textLineProps.length);\n      return newLine\n    }\n  };\n  TextLine.flyweight = textLineProps.reduce((obj, prop, i, all) => {\n    Object.defineProperty(obj, prop, {\n      get() {\n        return this.data[this.index * textLineProps.length + i]\n      },\n      set(val) {\n        this.data[this.index * textLineProps.length + i] = val;\n      }\n    });\n    return obj\n  }, {data: null, index: 0});\n\n\n  return {\n    typeset,\n    measure,\n  }\n}\n\nconst now = () => (self.performance || Date).now();\n\nconst mainThreadGenerator = /*#__PURE__*/ createSDFGenerator();\n\nlet warned;\n\n/**\n * Generate an SDF texture image for a single glyph path, placing the result into a webgl canvas at a\n * given location and channel. Utilizes the webgl-sdf-generator external package for GPU-accelerated SDF\n * generation when supported.\n */\nfunction generateSDF(width, height, path, viewBox, distance, exponent, canvas, x, y, channel, useWebGL = true) {\n  // Allow opt-out\n  if (!useWebGL) {\n    return generateSDF_JS_Worker(width, height, path, viewBox, distance, exponent, canvas, x, y, channel)\n  }\n\n  // Attempt GPU-accelerated generation first\n  return generateSDF_GL(width, height, path, viewBox, distance, exponent, canvas, x, y, channel).then(\n    null,\n    err => {\n      // WebGL failed either due to a hard error or unexpected results; fall back to JS in workers\n      if (!warned) {\n        console.warn(`WebGL SDF generation failed, falling back to JS`, err);\n        warned = true;\n      }\n      return generateSDF_JS_Worker(width, height, path, viewBox, distance, exponent, canvas, x, y, channel)\n    }\n  )\n}\n\nconst queue = [];\nconst chunkTimeBudget = 5; // ms\nlet timer = 0;\n\nfunction nextChunk() {\n  const start = now();\n  while (queue.length && now() - start < chunkTimeBudget) {\n    queue.shift()();\n  }\n  timer = queue.length ? setTimeout(nextChunk, 0) : 0;\n}\n\n/**\n * WebGL-based implementation executed on the main thread. Requests are executed in time-bounded\n * macrotask chunks to allow render frames to execute in between.\n */\nconst generateSDF_GL = (...args) => {\n  return new Promise((resolve, reject) => {\n    queue.push(() => {\n      const start = now();\n      try {\n        mainThreadGenerator.webgl.generateIntoCanvas(...args);\n        resolve({ timing: now() - start });\n      } catch (err) {\n        reject(err);\n      }\n    });\n    if (!timer) {\n      timer = setTimeout(nextChunk, 0);\n    }\n  })\n};\n\nconst threadCount = 4; // how many workers to spawn\nconst idleTimeout = 2000; // workers will be terminated after being idle this many milliseconds\nconst threads = {};\nlet callNum = 0;\n\n/**\n * Fallback JS-based implementation, fanned out to a number of worker threads for parallelism\n */\nfunction generateSDF_JS_Worker(width, height, path, viewBox, distance, exponent, canvas, x, y, channel) {\n  const workerId = 'TroikaTextSDFGenerator_JS_' + ((callNum++) % threadCount);\n  let thread = threads[workerId];\n  if (!thread) {\n    thread = threads[workerId] = {\n      workerModule: defineWorkerModule({\n        name: workerId,\n        workerId,\n        dependencies: [\n          createSDFGenerator,\n          now\n        ],\n        init(_createSDFGenerator, now) {\n          const generate = _createSDFGenerator().javascript.generate;\n          return function (...args) {\n            const start = now();\n            const textureData = generate(...args);\n            return {\n              textureData,\n              timing: now() - start\n            }\n          }\n        },\n        getTransferables(result) {\n          return [result.textureData.buffer]\n        }\n      }),\n      requests: 0,\n      idleTimer: null\n    };\n  }\n\n  thread.requests++;\n  clearTimeout(thread.idleTimer);\n  return thread.workerModule(width, height, path, viewBox, distance, exponent)\n    .then(({ textureData, timing }) => {\n      // copy result data into the canvas\n      const start = now();\n      // expand single-channel data into rgba\n      const imageData = new Uint8Array(textureData.length * 4);\n      for (let i = 0; i < textureData.length; i++) {\n        imageData[i * 4 + channel] = textureData[i];\n      }\n      mainThreadGenerator.webglUtils.renderImageData(canvas, imageData, x, y, width, height, 1 << (3 - channel));\n      timing += now() - start;\n\n      // clean up workers after a while\n      if (--thread.requests === 0) {\n        thread.idleTimer = setTimeout(() => { terminateWorker(workerId); }, idleTimeout);\n      }\n      return { timing }\n    })\n}\n\nfunction warmUpSDFCanvas(canvas) {\n  if (!canvas._warm) {\n    mainThreadGenerator.webgl.isSupported(canvas);\n    canvas._warm = true;\n  }\n}\n\nconst resizeWebGLCanvasWithoutClearing = mainThreadGenerator.webglUtils.resizeWebGLCanvasWithoutClearing;\n\nconst CONFIG = {\n  defaultFontURL: null,\n  unicodeFontsURL: null,\n  sdfGlyphSize: 64,\n  sdfMargin: 1 / 16,\n  sdfExponent: 9,\n  textureWidth: 2048,\n  useWorker: true,\n};\nconst tempColor = /*#__PURE__*/new Color();\nlet hasRequested = false;\n\nfunction now$1() {\n  return (self.performance || Date).now()\n}\n\n/**\n * Customizes the text builder configuration. This must be called prior to the first font processing\n * request, and applies to all fonts.\n *\n * @param {String} config.defaultFontURL - The URL of the default font to use for text processing\n *                 requests, in case none is specified or the specifiede font fails to load or parse.\n *                 Defaults to \"Roboto Regular\" from Google Fonts.\n * @param {String} config.unicodeFontsURL - A custom location for the fallback unicode-font-resolver\n *                 data and font files, if you don't want to use the default CDN. See\n *                 https://github.com/lojjic/unicode-font-resolver for details. It can also be\n *                 configured per text instance, but this lets you do it once globally.\n * @param {Number} config.sdfGlyphSize - The default size of each glyph's SDF (signed distance field)\n *                 texture used for rendering. Must be a power-of-two number, and applies to all fonts,\n *                 but note that this can also be overridden per call to `getTextRenderInfo()`.\n *                 Larger sizes can improve the quality of glyph rendering by increasing the sharpness\n *                 of corners and preventing loss of very thin lines, at the expense of memory. Defaults\n *                 to 64 which is generally a good balance of size and quality.\n * @param {Number} config.sdfExponent - The exponent used when encoding the SDF values. A higher exponent\n *                 shifts the encoded 8-bit values to achieve higher precision/accuracy at texels nearer\n *                 the glyph's path, with lower precision further away. Defaults to 9.\n * @param {Number} config.sdfMargin - How much space to reserve in the SDF as margin outside the glyph's\n *                 path, as a percentage of the SDF width. A larger margin increases the quality of\n *                 extruded glyph outlines, but decreases the precision available for the glyph itself.\n *                 Defaults to 1/16th of the glyph size.\n * @param {Number} config.textureWidth - The width of the SDF texture; must be a power of 2. Defaults to\n *                 2048 which is a safe maximum texture dimension according to the stats at\n *                 https://webglstats.com/webgl/parameter/MAX_TEXTURE_SIZE and should allow for a\n *                 reasonably large number of glyphs (default glyph size of 64^2 and safe texture size of\n *                 2048^2, times 4 channels, allows for 4096 glyphs.) This can be increased if you need to\n *                 increase the glyph size and/or have an extraordinary number of glyphs.\n * @param {Boolean} config.useWorker - Whether to run typesetting in a web worker. Defaults to true.\n */\nfunction configureTextBuilder(config) {\n  if (hasRequested) {\n    console.warn('configureTextBuilder called after first font request; will be ignored.');\n  } else {\n    assign(CONFIG, config);\n  }\n}\n\n/**\n * Repository for all font SDF atlas textures and their glyph mappings. There is a separate atlas for\n * each sdfGlyphSize. Each atlas has a single Texture that holds all glyphs for all fonts.\n *\n *   {\n *     [sdfGlyphSize]: {\n *       glyphCount: number,\n *       sdfGlyphSize: number,\n *       sdfTexture: Texture,\n *       sdfCanvas: HTMLCanvasElement,\n *       contextLost: boolean,\n *       glyphsByFont: Map<fontURL, Map<glyphID, {path, atlasIndex, sdfViewBox}>>\n *     }\n *   }\n */\nconst atlases = Object.create(null);\n\n/**\n * @typedef {object} TroikaTextRenderInfo - Format of the result from `getTextRenderInfo`.\n * @property {TypesetParams} parameters - The normalized input arguments to the render call.\n * @property {Texture} sdfTexture - The SDF atlas texture.\n * @property {number} sdfGlyphSize - The size of each glyph's SDF; see `configureTextBuilder`.\n * @property {number} sdfExponent - The exponent used in encoding the SDF's values; see `configureTextBuilder`.\n * @property {Float32Array} glyphBounds - List of [minX, minY, maxX, maxY] quad bounds for each glyph.\n * @property {Float32Array} glyphAtlasIndices - List holding each glyph's index in the SDF atlas.\n * @property {Uint8Array} [glyphColors] - List holding each glyph's [r, g, b] color, if `colorRanges` was supplied.\n * @property {Float32Array} [caretPositions] - A list of caret positions for all characters in the string; each is\n *           four elements: the starting X, the ending X, the bottom Y, and the top Y for the caret.\n * @property {number} [caretHeight] - An appropriate height for all selection carets.\n * @property {number} ascender - The font's ascender metric.\n * @property {number} descender - The font's descender metric.\n * @property {number} capHeight - The font's cap height metric, based on the height of Latin capital letters.\n * @property {number} xHeight - The font's x height metric, based on the height of Latin lowercase letters.\n * @property {number} lineHeight - The final computed lineHeight measurement.\n * @property {number} topBaseline - The y position of the top line's baseline.\n * @property {Array<number>} blockBounds - The total [minX, minY, maxX, maxY] rect of the whole text block;\n *           this can include extra vertical space beyond the visible glyphs due to lineHeight, and is\n *           equivalent to the dimensions of a block-level text element in CSS.\n * @property {Array<number>} visibleBounds - The total [minX, minY, maxX, maxY] rect of the whole text block;\n *           unlike `blockBounds` this is tightly wrapped to the visible glyph paths.\n * @property {Array<object>} chunkedBounds - List of bounding rects for each consecutive set of N glyphs,\n *           in the format `{start:N, end:N, rect:[minX, minY, maxX, maxY]}`.\n * @property {object} timings - Timing info for various parts of the rendering logic including SDF\n *           generation, typesetting, etc.\n * @frozen\n */\n\n/**\n * @callback getTextRenderInfo~callback\n * @param {TroikaTextRenderInfo} textRenderInfo\n */\n\n/**\n * Main entry point for requesting the data needed to render a text string with given font parameters.\n * This is an asynchronous call, performing most of the logic in a web worker thread.\n * @param {TypesetParams} args\n * @param {getTextRenderInfo~callback} callback\n */\nfunction getTextRenderInfo(args, callback) {\n  hasRequested = true;\n  args = assign({}, args);\n  const totalStart = now$1();\n\n  // Convert relative URL to absolute so it can be resolved in the worker, and add fallbacks.\n  // In the future we'll allow args.font to be a list with unicode ranges too.\n  const { defaultFontURL } = CONFIG;\n  const fonts = [];\n  if (defaultFontURL) {\n    fonts.push({label: 'default', src: toAbsoluteURL(defaultFontURL)});\n  }\n  if (args.font) {\n    fonts.push({label: 'user', src: toAbsoluteURL(args.font)});\n  }\n  args.font = fonts;\n\n  // Normalize text to a string\n  args.text = '' + args.text;\n\n  args.sdfGlyphSize = args.sdfGlyphSize || CONFIG.sdfGlyphSize;\n  args.unicodeFontsURL = args.unicodeFontsURL || CONFIG.unicodeFontsURL;\n\n  // Normalize colors\n  if (args.colorRanges != null) {\n    let colors = {};\n    for (let key in args.colorRanges) {\n      if (args.colorRanges.hasOwnProperty(key)) {\n        let val = args.colorRanges[key];\n        if (typeof val !== 'number') {\n          val = tempColor.set(val).getHex();\n        }\n        colors[key] = val;\n      }\n    }\n    args.colorRanges = colors;\n  }\n\n  Object.freeze(args);\n\n  // Init the atlas if needed\n  const {textureWidth, sdfExponent} = CONFIG;\n  const {sdfGlyphSize} = args;\n  const glyphsPerRow = (textureWidth / sdfGlyphSize * 4);\n  let atlas = atlases[sdfGlyphSize];\n  if (!atlas) {\n    const canvas = document.createElement('canvas');\n    canvas.width = textureWidth;\n    canvas.height = sdfGlyphSize * 256 / glyphsPerRow; // start tall enough to fit 256 glyphs\n    atlas = atlases[sdfGlyphSize] = {\n      glyphCount: 0,\n      sdfGlyphSize,\n      sdfCanvas: canvas,\n      sdfTexture: new Texture(\n        canvas,\n        undefined,\n        undefined,\n        undefined,\n        LinearFilter,\n        LinearFilter\n      ),\n      contextLost: false,\n      glyphsByFont: new Map()\n    };\n    atlas.sdfTexture.generateMipmaps = false;\n    initContextLossHandling(atlas);\n  }\n\n  const {sdfTexture, sdfCanvas} = atlas;\n\n  // Issue request to the typesetting engine in the worker\n  const typeset = CONFIG.useWorker ? typesetInWorker : typesetOnMainThread;\n  typeset(args).then(result => {\n    const {glyphIds, glyphFontIndices, fontData, glyphPositions, fontSize, timings} = result;\n    const neededSDFs = [];\n    const glyphBounds = new Float32Array(glyphIds.length * 4);\n    let boundsIdx = 0;\n    let positionsIdx = 0;\n    const quadsStart = now$1();\n\n    const fontGlyphMaps = fontData.map(font => {\n      let map = atlas.glyphsByFont.get(font.src);\n      if (!map) {\n        atlas.glyphsByFont.set(font.src, map = new Map());\n      }\n      return map\n    });\n\n    glyphIds.forEach((glyphId, i) => {\n      const fontIndex = glyphFontIndices[i];\n      const {src: fontSrc, unitsPerEm} = fontData[fontIndex];\n      let glyphInfo = fontGlyphMaps[fontIndex].get(glyphId);\n\n      // If this is a glyphId not seen before, add it to the atlas\n      if (!glyphInfo) {\n        const {path, pathBounds} = result.glyphData[fontSrc][glyphId];\n\n        // Margin around path edges in SDF, based on a percentage of the glyph's max dimension.\n        // Note we add an extra 0.5 px over the configured value because the outer 0.5 doesn't contain\n        // useful interpolated values and will be ignored anyway.\n        const fontUnitsMargin = Math.max(pathBounds[2] - pathBounds[0], pathBounds[3] - pathBounds[1])\n          / sdfGlyphSize * (CONFIG.sdfMargin * sdfGlyphSize + 0.5);\n\n        const atlasIndex = atlas.glyphCount++;\n        const sdfViewBox = [\n          pathBounds[0] - fontUnitsMargin,\n          pathBounds[1] - fontUnitsMargin,\n          pathBounds[2] + fontUnitsMargin,\n          pathBounds[3] + fontUnitsMargin,\n        ];\n        fontGlyphMaps[fontIndex].set(glyphId, (glyphInfo = { path, atlasIndex, sdfViewBox }));\n\n        // Collect those that need SDF generation\n        neededSDFs.push(glyphInfo);\n      }\n\n      // Calculate bounds for renderable quads\n      // TODO can we get this back off the main thread?\n      const {sdfViewBox} = glyphInfo;\n      const posX = glyphPositions[positionsIdx++];\n      const posY = glyphPositions[positionsIdx++];\n      const fontSizeMult = fontSize / unitsPerEm;\n      glyphBounds[boundsIdx++] = posX + sdfViewBox[0] * fontSizeMult;\n      glyphBounds[boundsIdx++] = posY + sdfViewBox[1] * fontSizeMult;\n      glyphBounds[boundsIdx++] = posX + sdfViewBox[2] * fontSizeMult;\n      glyphBounds[boundsIdx++] = posY + sdfViewBox[3] * fontSizeMult;\n\n      // Convert glyphId to SDF index for the shader\n      glyphIds[i] = glyphInfo.atlasIndex;\n    });\n    timings.quads = (timings.quads || 0) + (now$1() - quadsStart);\n\n    const sdfStart = now$1();\n    timings.sdf = {};\n\n    // Grow the texture height by power of 2 if needed\n    const currentHeight = sdfCanvas.height;\n    const neededRows = Math.ceil(atlas.glyphCount / glyphsPerRow);\n    const neededHeight = Math.pow(2, Math.ceil(Math.log2(neededRows * sdfGlyphSize)));\n    if (neededHeight > currentHeight) {\n      // Since resizing the canvas clears its render buffer, it needs special handling to copy the old contents over\n      console.info(`Increasing SDF texture size ${currentHeight}->${neededHeight}`);\n      resizeWebGLCanvasWithoutClearing(sdfCanvas, textureWidth, neededHeight);\n      // As of Three r136 textures cannot be resized once they're allocated on the GPU, we must dispose to reallocate it\n      sdfTexture.dispose();\n    }\n\n    Promise.all(neededSDFs.map(glyphInfo =>\n      generateGlyphSDF(glyphInfo, atlas, args.gpuAccelerateSDF).then(({timing}) => {\n        timings.sdf[glyphInfo.atlasIndex] = timing;\n      })\n    )).then(() => {\n      if (neededSDFs.length && !atlas.contextLost) {\n        safariPre15Workaround(atlas);\n        sdfTexture.needsUpdate = true;\n      }\n      timings.sdfTotal = now$1() - sdfStart;\n      timings.total = now$1() - totalStart;\n      // console.log(`SDF - ${timings.sdfTotal}, Total - ${timings.total - timings.fontLoad}`)\n\n      // Invoke callback with the text layout arrays and updated texture\n      callback(Object.freeze({\n        parameters: args,\n        sdfTexture,\n        sdfGlyphSize,\n        sdfExponent,\n        glyphBounds,\n        glyphAtlasIndices: glyphIds,\n        glyphColors: result.glyphColors,\n        caretPositions: result.caretPositions,\n        chunkedBounds: result.chunkedBounds,\n        ascender: result.ascender,\n        descender: result.descender,\n        lineHeight: result.lineHeight,\n        capHeight: result.capHeight,\n        xHeight: result.xHeight,\n        topBaseline: result.topBaseline,\n        blockBounds: result.blockBounds,\n        visibleBounds: result.visibleBounds,\n        timings: result.timings,\n      }));\n    });\n  });\n\n  // While the typesetting request is being handled, go ahead and make sure the atlas canvas context is\n  // \"warmed up\"; the first request will be the longest due to shader program compilation so this gets\n  // a head start on that process before SDFs actually start getting processed.\n  Promise.resolve().then(() => {\n    if (!atlas.contextLost) {\n      warmUpSDFCanvas(sdfCanvas);\n    }\n  });\n}\n\nfunction generateGlyphSDF({path, atlasIndex, sdfViewBox}, {sdfGlyphSize, sdfCanvas, contextLost}, useGPU) {\n  if (contextLost) {\n    // If the context is lost there's nothing we can do, just quit silently and let it\n    // get regenerated when the context is restored\n    return Promise.resolve({timing: -1})\n  }\n  const {textureWidth, sdfExponent} = CONFIG;\n  const maxDist = Math.max(sdfViewBox[2] - sdfViewBox[0], sdfViewBox[3] - sdfViewBox[1]);\n  const squareIndex = Math.floor(atlasIndex / 4);\n  const x = squareIndex % (textureWidth / sdfGlyphSize) * sdfGlyphSize;\n  const y = Math.floor(squareIndex / (textureWidth / sdfGlyphSize)) * sdfGlyphSize;\n  const channel = atlasIndex % 4;\n  return generateSDF(sdfGlyphSize, sdfGlyphSize, path, sdfViewBox, maxDist, sdfExponent, sdfCanvas, x, y, channel, useGPU)\n}\n\nfunction initContextLossHandling(atlas) {\n  const canvas = atlas.sdfCanvas;\n\n  /*\n  // Begin context loss simulation\n  if (!window.WebGLDebugUtils) {\n    let script = document.getElementById('WebGLDebugUtilsScript')\n    if (!script) {\n      script = document.createElement('script')\n      script.id = 'WebGLDebugUtils'\n      document.head.appendChild(script)\n      script.src = 'https://cdn.jsdelivr.net/gh/KhronosGroup/WebGLDeveloperTools@b42e702/src/debug/webgl-debug.js'\n    }\n    script.addEventListener('load', () => {\n      initContextLossHandling(atlas)\n    })\n    return\n  }\n  window.WebGLDebugUtils.makeLostContextSimulatingCanvas(canvas)\n  canvas.loseContextInNCalls(500)\n  canvas.addEventListener('webglcontextrestored', (event) => {\n    canvas.loseContextInNCalls(5000)\n  })\n  // End context loss simulation\n  */\n\n  canvas.addEventListener('webglcontextlost', (event) => {\n    console.log('Context Lost', event);\n    event.preventDefault();\n    atlas.contextLost = true;\n  });\n  canvas.addEventListener('webglcontextrestored', (event) => {\n    console.log('Context Restored', event);\n    atlas.contextLost = false;\n    // Regenerate all glyphs into the restored canvas:\n    const promises = [];\n    atlas.glyphsByFont.forEach(glyphMap => {\n      glyphMap.forEach(glyph => {\n        promises.push(generateGlyphSDF(glyph, atlas, true));\n      });\n    });\n    Promise.all(promises).then(() => {\n      safariPre15Workaround(atlas);\n      atlas.sdfTexture.needsUpdate = true;\n    });\n  });\n}\n\n/**\n * Preload a given font and optionally pre-generate glyph SDFs for one or more character sequences.\n * This can be useful to avoid long pauses when first showing text in a scene, by preloading the\n * needed fonts and glyphs up front along with other assets.\n *\n * @param {object} options\n * @param {string} options.font - URL of the font file to preload. If not given, the default font will\n *        be loaded.\n * @param {string|string[]} options.characters - One or more character sequences for which to pre-\n *        generate glyph SDFs. Note that this will honor ligature substitution, so you may need\n *        to specify ligature sequences in addition to their individual characters to get all\n *        possible glyphs, e.g. `[\"t\", \"h\", \"th\"]` to get the \"t\" and \"h\" glyphs plus the \"th\" ligature.\n * @param {number} options.sdfGlyphSize - The size at which to prerender the SDF textures for the\n *        specified `characters`.\n * @param {function} callback - A function that will be called when the preloading is complete.\n */\nfunction preloadFont({font, characters, sdfGlyphSize}, callback) {\n  let text = Array.isArray(characters) ? characters.join('\\n') : '' + characters;\n  getTextRenderInfo({ font, sdfGlyphSize, text }, callback);\n}\n\n\n// Local assign impl so we don't have to import troika-core\nfunction assign(toObj, fromObj) {\n  for (let key in fromObj) {\n    if (fromObj.hasOwnProperty(key)) {\n      toObj[key] = fromObj[key];\n    }\n  }\n  return toObj\n}\n\n// Utility for making URLs absolute\nlet linkEl;\nfunction toAbsoluteURL(path) {\n  if (!linkEl) {\n    linkEl = typeof document === 'undefined' ? {} : document.createElement('a');\n  }\n  linkEl.href = path;\n  return linkEl.href\n}\n\n/**\n * Safari < v15 seems unable to use the SDF webgl canvas as a texture. This applies a workaround\n * where it reads the pixels out of that canvas and uploads them as a data texture instead, at\n * a slight performance cost.\n */\nfunction safariPre15Workaround(atlas) {\n  // Use createImageBitmap support as a proxy for Safari<15, all other mainstream browsers\n  // have supported it for a long while so any false positives should be minimal.\n  if (typeof createImageBitmap !== 'function') {\n    console.info('Safari<15: applying SDF canvas workaround');\n    const {sdfCanvas, sdfTexture} = atlas;\n    const {width, height} = sdfCanvas;\n    const gl = atlas.sdfCanvas.getContext('webgl');\n    let pixels = sdfTexture.image.data;\n    if (!pixels || pixels.length !== width * height * 4) {\n      pixels = new Uint8Array(width * height * 4);\n      sdfTexture.image = {width, height, data: pixels};\n      sdfTexture.flipY = false;\n      sdfTexture.isDataTexture = true;\n    }\n    gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels);\n  }\n}\n\nconst typesetterWorkerModule = /*#__PURE__*/defineWorkerModule({\n  name: 'Typesetter',\n  dependencies: [\n    createTypesetter,\n    fontResolverWorkerModule,\n    bidiFactory,\n  ],\n  init(createTypesetter, fontResolver, bidiFactory) {\n    return createTypesetter(fontResolver, bidiFactory())\n  }\n});\n\nconst typesetInWorker = /*#__PURE__*/defineWorkerModule({\n  name: 'Typesetter',\n  dependencies: [\n    typesetterWorkerModule,\n  ],\n  init(typesetter) {\n    return function(args) {\n      return new Promise(resolve => {\n        typesetter.typeset(args, resolve);\n      })\n    }\n  },\n  getTransferables(result) {\n    // Mark array buffers as transferable to avoid cloning during postMessage\n    const transferables = [];\n    for (let p in result) {\n      if (result[p] && result[p].buffer) {\n        transferables.push(result[p].buffer);\n      }\n    }\n    return transferables\n  }\n});\n\nconst typesetOnMainThread = typesetInWorker.onMainThread;\n\nfunction dumpSDFTextures() {\n  Object.keys(atlases).forEach(size => {\n    const canvas = atlases[size].sdfCanvas;\n    const {width, height} = canvas;\n    console.log(\"%c.\", `\n      background: url(${canvas.toDataURL()});\n      background-size: ${width}px ${height}px;\n      color: transparent;\n      font-size: 0;\n      line-height: ${height}px;\n      padding-left: ${width}px;\n    `);\n  });\n}\n\nconst templateGeometries = {};\n\nfunction getTemplateGeometry(detail) {\n  let geom = templateGeometries[detail];\n  if (!geom) {\n    geom = templateGeometries[detail] = new PlaneGeometry(1, 1, detail, detail).translate(0.5, 0.5, 0);\n  }\n  return geom\n}\n\nconst glyphBoundsAttrName = 'aTroikaGlyphBounds';\nconst glyphIndexAttrName = 'aTroikaGlyphIndex';\nconst glyphColorAttrName = 'aTroikaGlyphColor';\n\n/**\n@class GlyphsGeometry\n\nA specialized Geometry for rendering a set of text glyphs. Uses InstancedBufferGeometry to\nrender the glyphs using GPU instancing of a single quad, rather than constructing a whole\ngeometry with vertices, for much smaller attribute arraybuffers according to this math:\n\n  Where N = number of glyphs...\n\n  Instanced:\n  - position: 4 * 3\n  - index: 2 * 3\n  - normal: 4 * 3\n  - uv: 4 * 2\n  - glyph x/y bounds: N * 4\n  - glyph indices: N * 1\n  = 5N + 38\n\n  Non-instanced:\n  - position: N * 4 * 3\n  - index: N * 2 * 3\n  - normal: N * 4 * 3\n  - uv: N * 4 * 2\n  - glyph indices: N * 1\n  = 39N\n\nA downside of this is the rare-but-possible lack of the instanced arrays extension,\nwhich we could potentially work around with a fallback non-instanced implementation.\n\n*/\nclass GlyphsGeometry extends InstancedBufferGeometry {\n  constructor() {\n    super();\n\n    this.detail = 1;\n    this.curveRadius = 0;\n\n    // Define groups for rendering text outline as a separate pass; these will only\n    // be used when the `material` getter returns an array, i.e. outlineWidth > 0.\n    this.groups = [\n      {start: 0, count: Infinity, materialIndex: 0},\n      {start: 0, count: Infinity, materialIndex: 1}\n    ];\n\n    // Preallocate empty bounding objects\n    this.boundingSphere = new Sphere();\n    this.boundingBox = new Box3();\n  }\n\n  computeBoundingSphere () {\n    // No-op; we'll sync the boundingSphere proactively when needed.\n  }\n\n  computeBoundingBox() {\n    // No-op; we'll sync the boundingBox proactively when needed.\n  }\n\n  set detail(detail) {\n    if (detail !== this._detail) {\n      this._detail = detail;\n      if (typeof detail !== 'number' || detail < 1) {\n        detail = 1;\n      }\n      let tpl = getTemplateGeometry(detail)\n      ;['position', 'normal', 'uv'].forEach(attr => {\n        this.attributes[attr] = tpl.attributes[attr].clone();\n      });\n      this.setIndex(tpl.getIndex().clone());\n    }\n  }\n  get detail() {\n    return this._detail\n  }\n\n  set curveRadius(r) {\n    if (r !== this._curveRadius) {\n      this._curveRadius = r;\n      this._updateBounds();\n    }\n  }\n  get curveRadius() {\n    return this._curveRadius\n  }\n\n  /**\n   * Update the geometry for a new set of glyphs.\n   * @param {Float32Array} glyphBounds - An array holding the planar bounds for all glyphs\n   *        to be rendered, 4 entries for each glyph: x1,x2,y1,y1\n   * @param {Float32Array} glyphAtlasIndices - An array holding the index of each glyph within\n   *        the SDF atlas texture.\n   * @param {Array} blockBounds - An array holding the [minX, minY, maxX, maxY] across all glyphs\n   * @param {Array} [chunkedBounds] - An array of objects describing bounds for each chunk of N\n   *        consecutive glyphs: `{start:N, end:N, rect:[minX, minY, maxX, maxY]}`. This can be\n   *        used with `applyClipRect` to choose an optimized `instanceCount`.\n   * @param {Uint8Array} [glyphColors] - An array holding r,g,b values for each glyph.\n   */\n  updateGlyphs(glyphBounds, glyphAtlasIndices, blockBounds, chunkedBounds, glyphColors) {\n    // Update the instance attributes\n    this.updateAttributeData(glyphBoundsAttrName, glyphBounds, 4);\n    this.updateAttributeData(glyphIndexAttrName, glyphAtlasIndices, 1);\n    this.updateAttributeData(glyphColorAttrName, glyphColors, 3);\n    this._blockBounds = blockBounds;\n    this._chunkedBounds = chunkedBounds;\n    this.instanceCount = glyphAtlasIndices.length;\n    this._updateBounds();\n  }\n\n  _updateBounds() {\n    const bounds = this._blockBounds;\n    if (bounds) {\n      const { curveRadius, boundingBox: bbox } = this;\n      if (curveRadius) {\n        const { PI, floor, min, max, sin, cos } = Math;\n        const halfPi = PI / 2;\n        const twoPi = PI * 2;\n        const absR = Math.abs(curveRadius);\n        const leftAngle = bounds[0] / absR;\n        const rightAngle = bounds[2] / absR;\n        const minX = floor((leftAngle + halfPi) / twoPi) !== floor((rightAngle + halfPi) / twoPi)\n          ? -absR : min(sin(leftAngle) * absR, sin(rightAngle) * absR);\n        const maxX = floor((leftAngle - halfPi) / twoPi) !== floor((rightAngle - halfPi) / twoPi)\n          ? absR : max(sin(leftAngle) * absR, sin(rightAngle) * absR);\n        const maxZ = floor((leftAngle + PI) / twoPi) !== floor((rightAngle + PI) / twoPi)\n          ? absR * 2 : max(absR - cos(leftAngle) * absR, absR - cos(rightAngle) * absR);\n        bbox.min.set(minX, bounds[1], curveRadius < 0 ? -maxZ : 0);\n        bbox.max.set(maxX, bounds[3], curveRadius < 0 ? 0 : maxZ);\n      } else {\n        bbox.min.set(bounds[0], bounds[1], 0);\n        bbox.max.set(bounds[2], bounds[3], 0);\n      }\n      bbox.getBoundingSphere(this.boundingSphere);\n    }\n  }\n\n  /**\n   * Given a clipping rect, and the chunkedBounds from the last updateGlyphs call, choose the lowest\n   * `instanceCount` that will show all glyphs within the clipped view. This is an optimization\n   * for long blocks of text that are clipped, to skip vertex shader evaluation for glyphs that would\n   * be clipped anyway.\n   *\n   * Note that since `drawElementsInstanced[ANGLE]` only accepts an instance count and not a starting\n   * offset, this optimization becomes less effective as the clipRect moves closer to the end of the\n   * text block. We could fix that by switching from instancing to a full geometry with a drawRange,\n   * but at the expense of much larger attribute buffers (see classdoc above.)\n   *\n   * @param {Vector4} clipRect\n   */\n  applyClipRect(clipRect) {\n    let count = this.getAttribute(glyphIndexAttrName).count;\n    let chunks = this._chunkedBounds;\n    if (chunks) {\n      for (let i = chunks.length; i--;) {\n        count = chunks[i].end;\n        let rect = chunks[i].rect;\n        // note: both rects are l-b-r-t\n        if (rect[1] < clipRect.w && rect[3] > clipRect.y && rect[0] < clipRect.z && rect[2] > clipRect.x) {\n          break\n        }\n      }\n    }\n    this.instanceCount = count;\n  }\n\n  /**\n   * Utility for updating instance attributes with automatic resizing\n   */\n  updateAttributeData(attrName, newArray, itemSize) {\n    const attr = this.getAttribute(attrName);\n    if (newArray) {\n      // If length isn't changing, just update the attribute's array data\n      if (attr && attr.array.length === newArray.length) {\n        attr.array.set(newArray);\n        attr.needsUpdate = true;\n      } else {\n        this.setAttribute(attrName, new InstancedBufferAttribute(newArray, itemSize));\n        // If the new attribute has a different size, we also have to (as of r117) manually clear the\n        // internal cached max instance count. See https://github.com/mrdoob/three.js/issues/19706\n        // It's unclear if this is a threejs bug or a truly unsupported scenario; discussion in\n        // that ticket is ambiguous as to whether replacing a BufferAttribute with one of a\n        // different size is supported, but https://github.com/mrdoob/three.js/pull/17418 strongly\n        // implies it should be supported. It's possible we need to\n        delete this._maxInstanceCount; //for r117+, could be fragile\n        this.dispose(); //for r118+, more robust feeling, but more heavy-handed than I'd like\n      }\n    } else if (attr) {\n      this.deleteAttribute(attrName);\n    }\n  }\n}\n\n// language=GLSL\nconst VERTEX_DEFS = `\nuniform vec2 uTroikaSDFTextureSize;\nuniform float uTroikaSDFGlyphSize;\nuniform vec4 uTroikaTotalBounds;\nuniform vec4 uTroikaClipRect;\nuniform mat3 uTroikaOrient;\nuniform bool uTroikaUseGlyphColors;\nuniform float uTroikaEdgeOffset;\nuniform float uTroikaBlurRadius;\nuniform vec2 uTroikaPositionOffset;\nuniform float uTroikaCurveRadius;\nattribute vec4 aTroikaGlyphBounds;\nattribute float aTroikaGlyphIndex;\nattribute vec3 aTroikaGlyphColor;\nvarying vec2 vTroikaGlyphUV;\nvarying vec4 vTroikaTextureUVBounds;\nvarying float vTroikaTextureChannel;\nvarying vec3 vTroikaGlyphColor;\nvarying vec2 vTroikaGlyphDimensions;\n`;\n\n// language=GLSL prefix=\"void main() {\" suffix=\"}\"\nconst VERTEX_TRANSFORM = `\nvec4 bounds = aTroikaGlyphBounds;\nbounds.xz += uTroikaPositionOffset.x;\nbounds.yw -= uTroikaPositionOffset.y;\n\nvec4 outlineBounds = vec4(\n  bounds.xy - uTroikaEdgeOffset - uTroikaBlurRadius,\n  bounds.zw + uTroikaEdgeOffset + uTroikaBlurRadius\n);\nvec4 clippedBounds = vec4(\n  clamp(outlineBounds.xy, uTroikaClipRect.xy, uTroikaClipRect.zw),\n  clamp(outlineBounds.zw, uTroikaClipRect.xy, uTroikaClipRect.zw)\n);\n\nvec2 clippedXY = (mix(clippedBounds.xy, clippedBounds.zw, position.xy) - bounds.xy) / (bounds.zw - bounds.xy);\n\nposition.xy = mix(bounds.xy, bounds.zw, clippedXY);\n\nuv = (position.xy - uTroikaTotalBounds.xy) / (uTroikaTotalBounds.zw - uTroikaTotalBounds.xy);\n\nfloat rad = uTroikaCurveRadius;\nif (rad != 0.0) {\n  float angle = position.x / rad;\n  position.xz = vec2(sin(angle) * rad, rad - cos(angle) * rad);\n  normal.xz = vec2(sin(angle), cos(angle));\n}\n  \nposition = uTroikaOrient * position;\nnormal = uTroikaOrient * normal;\n\nvTroikaGlyphUV = clippedXY.xy;\nvTroikaGlyphDimensions = vec2(bounds[2] - bounds[0], bounds[3] - bounds[1]);\n\n${''/* NOTE: it seems important to calculate the glyph's bounding texture UVs here in the\n  vertex shader, rather than in the fragment shader, as the latter gives strange artifacts\n  on some glyphs (those in the leftmost texture column) on some systems. The exact reason\n  isn't understood but doing this here, then mix()-ing in the fragment shader, seems to work. */}\nfloat txCols = uTroikaSDFTextureSize.x / uTroikaSDFGlyphSize;\nvec2 txUvPerSquare = uTroikaSDFGlyphSize / uTroikaSDFTextureSize;\nvec2 txStartUV = txUvPerSquare * vec2(\n  mod(floor(aTroikaGlyphIndex / 4.0), txCols),\n  floor(floor(aTroikaGlyphIndex / 4.0) / txCols)\n);\nvTroikaTextureUVBounds = vec4(txStartUV, vec2(txStartUV) + txUvPerSquare);\nvTroikaTextureChannel = mod(aTroikaGlyphIndex, 4.0);\n`;\n\n// language=GLSL\nconst FRAGMENT_DEFS = `\nuniform sampler2D uTroikaSDFTexture;\nuniform vec2 uTroikaSDFTextureSize;\nuniform float uTroikaSDFGlyphSize;\nuniform float uTroikaSDFExponent;\nuniform float uTroikaEdgeOffset;\nuniform float uTroikaFillOpacity;\nuniform float uTroikaBlurRadius;\nuniform vec3 uTroikaStrokeColor;\nuniform float uTroikaStrokeWidth;\nuniform float uTroikaStrokeOpacity;\nuniform bool uTroikaSDFDebug;\nvarying vec2 vTroikaGlyphUV;\nvarying vec4 vTroikaTextureUVBounds;\nvarying float vTroikaTextureChannel;\nvarying vec2 vTroikaGlyphDimensions;\n\nfloat troikaSdfValueToSignedDistance(float alpha) {\n  // Inverse of exponential encoding in webgl-sdf-generator\n  ${''/* TODO - there's some slight inaccuracy here when dealing with interpolated alpha values; those\n    are linearly interpolated where the encoding is exponential. Look into improving this by rounding\n    to nearest 2 whole texels, decoding those exponential values, and linearly interpolating the result.\n  */}\n  float maxDimension = max(vTroikaGlyphDimensions.x, vTroikaGlyphDimensions.y);\n  float absDist = (1.0 - pow(2.0 * (alpha > 0.5 ? 1.0 - alpha : alpha), 1.0 / uTroikaSDFExponent)) * maxDimension;\n  float signedDist = absDist * (alpha > 0.5 ? -1.0 : 1.0);\n  return signedDist;\n}\n\nfloat troikaGlyphUvToSdfValue(vec2 glyphUV) {\n  vec2 textureUV = mix(vTroikaTextureUVBounds.xy, vTroikaTextureUVBounds.zw, glyphUV);\n  vec4 rgba = texture2D(uTroikaSDFTexture, textureUV);\n  float ch = floor(vTroikaTextureChannel + 0.5); //NOTE: can't use round() in WebGL1\n  return ch == 0.0 ? rgba.r : ch == 1.0 ? rgba.g : ch == 2.0 ? rgba.b : rgba.a;\n}\n\nfloat troikaGlyphUvToDistance(vec2 uv) {\n  return troikaSdfValueToSignedDistance(troikaGlyphUvToSdfValue(uv));\n}\n\nfloat troikaGetAADist() {\n  ${''/*\n    When the standard derivatives extension is available, we choose an antialiasing alpha threshold based\n    on the potential change in the SDF's alpha from this fragment to its neighbor. This strategy maximizes \n    readability and edge crispness at all sizes and screen resolutions.\n  */}\n  #if defined(GL_OES_standard_derivatives) || __VERSION__ >= 300\n  return length(fwidth(vTroikaGlyphUV * vTroikaGlyphDimensions)) * 0.5;\n  #else\n  return vTroikaGlyphDimensions.x / 64.0;\n  #endif\n}\n\nfloat troikaGetFragDistValue() {\n  vec2 clampedGlyphUV = clamp(vTroikaGlyphUV, 0.5 / uTroikaSDFGlyphSize, 1.0 - 0.5 / uTroikaSDFGlyphSize);\n  float distance = troikaGlyphUvToDistance(clampedGlyphUV);\n \n  // Extrapolate distance when outside bounds:\n  distance += clampedGlyphUV == vTroikaGlyphUV ? 0.0 : \n    length((vTroikaGlyphUV - clampedGlyphUV) * vTroikaGlyphDimensions);\n\n  ${''/* \n  // TODO more refined extrapolated distance by adjusting for angle of gradient at edge...\n  // This has potential but currently gives very jagged extensions, maybe due to precision issues?\n  float uvStep = 1.0 / uTroikaSDFGlyphSize;\n  vec2 neighbor1UV = clampedGlyphUV + (\n    vTroikaGlyphUV.x != clampedGlyphUV.x ? vec2(0.0, uvStep * sign(0.5 - vTroikaGlyphUV.y)) :\n    vTroikaGlyphUV.y != clampedGlyphUV.y ? vec2(uvStep * sign(0.5 - vTroikaGlyphUV.x), 0.0) :\n    vec2(0.0)\n  );\n  vec2 neighbor2UV = clampedGlyphUV + (\n    vTroikaGlyphUV.x != clampedGlyphUV.x ? vec2(0.0, uvStep * -sign(0.5 - vTroikaGlyphUV.y)) :\n    vTroikaGlyphUV.y != clampedGlyphUV.y ? vec2(uvStep * -sign(0.5 - vTroikaGlyphUV.x), 0.0) :\n    vec2(0.0)\n  );\n  float neighbor1Distance = troikaGlyphUvToDistance(neighbor1UV);\n  float neighbor2Distance = troikaGlyphUvToDistance(neighbor2UV);\n  float distToUnclamped = length((vTroikaGlyphUV - clampedGlyphUV) * vTroikaGlyphDimensions);\n  float distToNeighbor = length((clampedGlyphUV - neighbor1UV) * vTroikaGlyphDimensions);\n  float gradientAngle1 = min(asin(abs(neighbor1Distance - distance) / distToNeighbor), PI / 2.0);\n  float gradientAngle2 = min(asin(abs(neighbor2Distance - distance) / distToNeighbor), PI / 2.0);\n  distance += (cos(gradientAngle1) + cos(gradientAngle2)) / 2.0 * distToUnclamped;\n  */}\n\n  return distance;\n}\n\nfloat troikaGetEdgeAlpha(float distance, float distanceOffset, float aaDist) {\n  #if defined(IS_DEPTH_MATERIAL) || defined(IS_DISTANCE_MATERIAL)\n  float alpha = step(-distanceOffset, -distance);\n  #else\n\n  float alpha = smoothstep(\n    distanceOffset + aaDist,\n    distanceOffset - aaDist,\n    distance\n  );\n  #endif\n\n  return alpha;\n}\n`;\n\n// language=GLSL prefix=\"void main() {\" suffix=\"}\"\nconst FRAGMENT_TRANSFORM = `\nfloat aaDist = troikaGetAADist();\nfloat fragDistance = troikaGetFragDistValue();\nfloat edgeAlpha = uTroikaSDFDebug ?\n  troikaGlyphUvToSdfValue(vTroikaGlyphUV) :\n  troikaGetEdgeAlpha(fragDistance, uTroikaEdgeOffset, max(aaDist, uTroikaBlurRadius));\n\n#if !defined(IS_DEPTH_MATERIAL) && !defined(IS_DISTANCE_MATERIAL)\nvec4 fillRGBA = gl_FragColor;\nfillRGBA.a *= uTroikaFillOpacity;\nvec4 strokeRGBA = uTroikaStrokeWidth == 0.0 ? fillRGBA : vec4(uTroikaStrokeColor, uTroikaStrokeOpacity);\nif (fillRGBA.a == 0.0) fillRGBA.rgb = strokeRGBA.rgb;\ngl_FragColor = mix(fillRGBA, strokeRGBA, smoothstep(\n  -uTroikaStrokeWidth - aaDist,\n  -uTroikaStrokeWidth + aaDist,\n  fragDistance\n));\ngl_FragColor.a *= edgeAlpha;\n#endif\n\nif (edgeAlpha == 0.0) {\n  discard;\n}\n`;\n\n\n/**\n * Create a material for rendering text, derived from a baseMaterial\n */\nfunction createTextDerivedMaterial(baseMaterial) {\n  const textMaterial = createDerivedMaterial(baseMaterial, {\n    chained: true,\n    extensions: {\n      derivatives: true\n    },\n    uniforms: {\n      uTroikaSDFTexture: {value: null},\n      uTroikaSDFTextureSize: {value: new Vector2()},\n      uTroikaSDFGlyphSize: {value: 0},\n      uTroikaSDFExponent: {value: 0},\n      uTroikaTotalBounds: {value: new Vector4(0,0,0,0)},\n      uTroikaClipRect: {value: new Vector4(0,0,0,0)},\n      uTroikaEdgeOffset: {value: 0},\n      uTroikaFillOpacity: {value: 1},\n      uTroikaPositionOffset: {value: new Vector2()},\n      uTroikaCurveRadius: {value: 0},\n      uTroikaBlurRadius: {value: 0},\n      uTroikaStrokeWidth: {value: 0},\n      uTroikaStrokeColor: {value: new Color()},\n      uTroikaStrokeOpacity: {value: 1},\n      uTroikaOrient: {value: new Matrix3()},\n      uTroikaUseGlyphColors: {value: true},\n      uTroikaSDFDebug: {value: false}\n    },\n    vertexDefs: VERTEX_DEFS,\n    vertexTransform: VERTEX_TRANSFORM,\n    fragmentDefs: FRAGMENT_DEFS,\n    fragmentColorTransform: FRAGMENT_TRANSFORM,\n    customRewriter({vertexShader, fragmentShader}) {\n      let uDiffuseRE = /\\buniform\\s+vec3\\s+diffuse\\b/;\n      if (uDiffuseRE.test(fragmentShader)) {\n        // Replace all instances of `diffuse` with our varying\n        fragmentShader = fragmentShader\n          .replace(uDiffuseRE, 'varying vec3 vTroikaGlyphColor')\n          .replace(/\\bdiffuse\\b/g, 'vTroikaGlyphColor');\n        // Make sure the vertex shader declares the uniform so we can grab it as a fallback\n        if (!uDiffuseRE.test(vertexShader)) {\n          vertexShader = vertexShader.replace(\n            voidMainRegExp,\n            'uniform vec3 diffuse;\\n$&\\nvTroikaGlyphColor = uTroikaUseGlyphColors ? aTroikaGlyphColor / 255.0 : diffuse;\\n'\n          );\n        }\n      }\n      return { vertexShader, fragmentShader }\n    }\n  });\n\n  // Force transparency - TODO is this reasonable?\n  textMaterial.transparent = true;\n\n  // Force single draw call when double-sided\n  textMaterial.forceSinglePass = true;\n\n  Object.defineProperties(textMaterial, {\n    isTroikaTextMaterial: {value: true},\n\n    // WebGLShadowMap reverses the side of the shadow material by default, which fails\n    // for planes, so here we force the `shadowSide` to always match the main side.\n    shadowSide: {\n      get() {\n        return this.side\n      },\n      set() {\n        //no-op\n      }\n    }\n  });\n\n  return textMaterial\n}\n\nconst defaultMaterial = /*#__PURE__*/ new MeshBasicMaterial({\n  color: 0xffffff,\n  side: DoubleSide,\n  transparent: true\n});\nconst defaultStrokeColor = 0x808080;\n\nconst tempMat4 = /*#__PURE__*/ new Matrix4();\nconst tempVec3a = /*#__PURE__*/ new Vector3();\nconst tempVec3b = /*#__PURE__*/ new Vector3();\nconst tempArray = [];\nconst origin = /*#__PURE__*/ new Vector3();\nconst defaultOrient = '+x+y';\n\nfunction first(o) {\n  return Array.isArray(o) ? o[0] : o\n}\n\nlet getFlatRaycastMesh = () => {\n  const mesh = new Mesh(\n    new PlaneGeometry(1, 1),\n    defaultMaterial\n  );\n  getFlatRaycastMesh = () => mesh;\n  return mesh\n};\nlet getCurvedRaycastMesh = () => {\n  const mesh = new Mesh(\n    new PlaneGeometry(1, 1, 32, 1),\n    defaultMaterial\n  );\n  getCurvedRaycastMesh = () => mesh;\n  return mesh\n};\n\nconst syncStartEvent = { type: 'syncstart' };\nconst syncCompleteEvent = { type: 'synccomplete' };\n\nconst SYNCABLE_PROPS = [\n  'font',\n  'fontSize',\n  'fontStyle',\n  'fontWeight',\n  'lang',\n  'letterSpacing',\n  'lineHeight',\n  'maxWidth',\n  'overflowWrap',\n  'text',\n  'direction',\n  'textAlign',\n  'textIndent',\n  'whiteSpace',\n  'anchorX',\n  'anchorY',\n  'colorRanges',\n  'sdfGlyphSize'\n];\n\nconst COPYABLE_PROPS = SYNCABLE_PROPS.concat(\n  'material',\n  'color',\n  'depthOffset',\n  'clipRect',\n  'curveRadius',\n  'orientation',\n  'glyphGeometryDetail'\n);\n\n/**\n * @class Text\n *\n * A ThreeJS Mesh that renders a string of text on a plane in 3D space using signed distance\n * fields (SDF).\n */\nclass Text extends Mesh {\n  constructor() {\n    const geometry = new GlyphsGeometry();\n    super(geometry, null);\n\n    // === Text layout properties: === //\n\n    /**\n     * @member {string} text\n     * The string of text to be rendered.\n     */\n    this.text = '';\n\n    /**\n     * @member {number|string} anchorX\n     * Defines the horizontal position in the text block that should line up with the local origin.\n     * Can be specified as a numeric x position in local units, a string percentage of the total\n     * text block width e.g. `'25%'`, or one of the following keyword strings: 'left', 'center',\n     * or 'right'.\n     */\n    this.anchorX = 0;\n\n    /**\n     * @member {number|string} anchorY\n     * Defines the vertical position in the text block that should line up with the local origin.\n     * Can be specified as a numeric y position in local units (note: down is negative y), a string\n     * percentage of the total text block height e.g. `'25%'`, or one of the following keyword strings:\n     * 'top', 'top-baseline', 'top-cap', 'top-ex', 'middle', 'bottom-baseline', or 'bottom'.\n     */\n    this.anchorY = 0;\n\n    /**\n     * @member {number} curveRadius\n     * Defines a cylindrical radius along which the text's plane will be curved. Positive numbers put\n     * the cylinder's centerline (oriented vertically) that distance in front of the text, for a concave\n     * curvature, while negative numbers put it behind the text for a convex curvature. The centerline\n     * will be aligned with the text's local origin; you can use `anchorX` to offset it.\n     *\n     * Since each glyph is by default rendered with a simple quad, each glyph remains a flat plane\n     * internally. You can use `glyphGeometryDetail` to add more vertices for curvature inside glyphs.\n     */\n    this.curveRadius = 0;\n\n    /**\n     * @member {string} direction\n     * Sets the base direction for the text. The default value of \"auto\" will choose a direction based\n     * on the text's content according to the bidi spec. A value of \"ltr\" or \"rtl\" will force the direction.\n     */\n    this.direction = 'auto';\n\n    /**\n     * @member {string|null} font\n     * URL of a custom font to be used. Font files can be in .ttf, .otf, or .woff (not .woff2) formats.\n     * Defaults to Noto Sans.\n     */\n    this.font = null; //will use default from TextBuilder\n\n    this.unicodeFontsURL = null; //defaults to CDN\n\n    /**\n     * @member {number} fontSize\n     * The size at which to render the font in local units; corresponds to the em-box height\n     * of the chosen `font`.\n     */\n    this.fontSize = 0.1;\n\n    /**\n     * @member {number|'normal'|'bold'}\n     * The weight of the font. Currently only used for fallback Noto fonts.\n     */\n    this.fontWeight = 'normal';\n\n    /**\n     * @member {'normal'|'italic'}\n     * The style of the font. Currently only used for fallback Noto fonts.\n     */\n    this.fontStyle = 'normal';\n\n    /**\n     * @member {string|null} lang\n     * The language code of this text; can be used for explicitly selecting certain CJK fonts.\n     */\n    this.lang = null;\n\n      /**\n     * @member {number} letterSpacing\n     * Sets a uniform adjustment to spacing between letters after kerning is applied. Positive\n     * numbers increase spacing and negative numbers decrease it.\n     */\n    this.letterSpacing = 0;\n\n    /**\n     * @member {number|string} lineHeight\n     * Sets the height of each line of text, as a multiple of the `fontSize`. Defaults to 'normal'\n     * which chooses a reasonable height based on the chosen font's ascender/descender metrics.\n     */\n    this.lineHeight = 'normal';\n\n    /**\n     * @member {number} maxWidth\n     * The maximum width of the text block, above which text may start wrapping according to the\n     * `whiteSpace` and `overflowWrap` properties.\n     */\n    this.maxWidth = Infinity;\n\n    /**\n     * @member {string} overflowWrap\n     * Defines how text wraps if the `whiteSpace` property is `normal`. Can be either `'normal'`\n     * to break at whitespace characters, or `'break-word'` to allow breaking within words.\n     * Defaults to `'normal'`.\n     */\n    this.overflowWrap = 'normal';\n\n    /**\n     * @member {string} textAlign\n     * The horizontal alignment of each line of text within the overall text bounding box.\n     */\n    this.textAlign = 'left';\n\n    /**\n     * @member {number} textIndent\n     * Indentation for the first character of a line; see CSS `text-indent`.\n     */\n    this.textIndent = 0;\n\n    /**\n     * @member {string} whiteSpace\n     * Defines whether text should wrap when a line reaches the `maxWidth`. Can\n     * be either `'normal'` (the default), to allow wrapping according to the `overflowWrap` property,\n     * or `'nowrap'` to prevent wrapping. Note that `'normal'` here honors newline characters to\n     * manually break lines, making it behave more like `'pre-wrap'` does in CSS.\n     */\n    this.whiteSpace = 'normal';\n\n\n    // === Presentation properties: === //\n\n    /**\n     * @member {THREE.Material} material\n     * Defines a _base_ material to be used when rendering the text. This material will be\n     * automatically replaced with a material derived from it, that adds shader code to\n     * decrease the alpha for each fragment (pixel) outside the text glyphs, with antialiasing.\n     * By default it will derive from a simple white MeshBasicMaterial, but you can use any\n     * of the other mesh materials to gain other features like lighting, texture maps, etc.\n     *\n     * Also see the `color` shortcut property.\n     */\n    this.material = null;\n\n    /**\n     * @member {string|number|THREE.Color} color\n     * This is a shortcut for setting the `color` of the text's material. You can use this\n     * if you don't want to specify a whole custom `material`. Also, if you do use a custom\n     * `material`, this color will only be used for this particuar Text instance, even if\n     * that same material instance is shared across multiple Text objects.\n     */\n    this.color = null;\n\n    /**\n     * @member {object|null} colorRanges\n     * WARNING: This API is experimental and may change.\n     * This allows more fine-grained control of colors for individual or ranges of characters,\n     * taking precedence over the material's `color`. Its format is an Object whose keys each\n     * define a starting character index for a range, and whose values are the color for each\n     * range. The color value can be a numeric hex color value, a `THREE.Color` object, or\n     * any of the strings accepted by `THREE.Color`.\n     */\n    this.colorRanges = null;\n\n    /**\n     * @member {number|string} outlineWidth\n     * WARNING: This API is experimental and may change.\n     * The width of an outline/halo to be drawn around each text glyph using the `outlineColor` and `outlineOpacity`.\n     * Can be specified as either an absolute number in local units, or as a percentage string e.g.\n     * `\"12%\"` which is treated as a percentage of the `fontSize`. Defaults to `0`, which means\n     * no outline will be drawn unless an `outlineOffsetX/Y` or `outlineBlur` is set.\n     */\n    this.outlineWidth = 0;\n\n    /**\n     * @member {string|number|THREE.Color} outlineColor\n     * WARNING: This API is experimental and may change.\n     * The color of the text outline, if `outlineWidth`/`outlineBlur`/`outlineOffsetX/Y` are set.\n     * Defaults to black.\n     */\n    this.outlineColor = 0x000000;\n\n    /**\n     * @member {number} outlineOpacity\n     * WARNING: This API is experimental and may change.\n     * The opacity of the outline, if `outlineWidth`/`outlineBlur`/`outlineOffsetX/Y` are set.\n     * Defaults to `1`.\n     */\n    this.outlineOpacity = 1;\n\n    /**\n     * @member {number|string} outlineBlur\n     * WARNING: This API is experimental and may change.\n     * A blur radius applied to the outer edge of the text's outline. If the `outlineWidth` is\n     * zero, the blur will be applied at the glyph edge, like CSS's `text-shadow` blur radius.\n     * Can be specified as either an absolute number in local units, or as a percentage string e.g.\n     * `\"12%\"` which is treated as a percentage of the `fontSize`. Defaults to `0`.\n     */\n    this.outlineBlur = 0;\n\n    /**\n     * @member {number|string} outlineOffsetX\n     * WARNING: This API is experimental and may change.\n     * A horizontal offset for the text outline.\n     * Can be specified as either an absolute number in local units, or as a percentage string e.g. `\"12%\"`\n     * which is treated as a percentage of the `fontSize`. Defaults to `0`.\n     */\n    this.outlineOffsetX = 0;\n\n    /**\n     * @member {number|string} outlineOffsetY\n     * WARNING: This API is experimental and may change.\n     * A vertical offset for the text outline.\n     * Can be specified as either an absolute number in local units, or as a percentage string e.g. `\"12%\"`\n     * which is treated as a percentage of the `fontSize`. Defaults to `0`.\n     */\n    this.outlineOffsetY = 0;\n\n    /**\n     * @member {number|string} strokeWidth\n     * WARNING: This API is experimental and may change.\n     * The width of an inner stroke drawn inside each text glyph using the `strokeColor` and `strokeOpacity`.\n     * Can be specified as either an absolute number in local units, or as a percentage string e.g. `\"12%\"`\n     * which is treated as a percentage of the `fontSize`. Defaults to `0`.\n     */\n    this.strokeWidth = 0;\n\n    /**\n     * @member {string|number|THREE.Color} strokeColor\n     * WARNING: This API is experimental and may change.\n     * The color of the text stroke, if `strokeWidth` is greater than zero. Defaults to gray.\n     */\n    this.strokeColor = defaultStrokeColor;\n\n    /**\n     * @member {number} strokeOpacity\n     * WARNING: This API is experimental and may change.\n     * The opacity of the stroke, if `strokeWidth` is greater than zero. Defaults to `1`.\n     */\n    this.strokeOpacity = 1;\n\n    /**\n     * @member {number} fillOpacity\n     * WARNING: This API is experimental and may change.\n     * The opacity of the glyph's fill from 0 to 1. This behaves like the material's `opacity` but allows\n     * giving the fill a different opacity than the `strokeOpacity`. A fillOpacity of `0` makes the\n     * interior of the glyph invisible, leaving just the `strokeWidth`. Defaults to `1`.\n     */\n    this.fillOpacity = 1;\n\n    /**\n     * @member {number} depthOffset\n     * This is a shortcut for setting the material's `polygonOffset` and related properties,\n     * which can be useful in preventing z-fighting when this text is laid on top of another\n     * plane in the scene. Positive numbers are further from the camera, negatives closer.\n     */\n    this.depthOffset = 0;\n\n    /**\n     * @member {Array<number>} clipRect\n     * If specified, defines a `[minX, minY, maxX, maxY]` of a rectangle outside of which all\n     * pixels will be discarded. This can be used for example to clip overflowing text when\n     * `whiteSpace='nowrap'`.\n     */\n    this.clipRect = null;\n\n    /**\n     * @member {string} orientation\n     * Defines the axis plane on which the text should be laid out when the mesh has no extra\n     * rotation transform. It is specified as a string with two axes: the horizontal axis with\n     * positive pointing right, and the vertical axis with positive pointing up. By default this\n     * is '+x+y', meaning the text sits on the xy plane with the text's top toward positive y\n     * and facing positive z. A value of '+x-z' would place it on the xz plane with the text's\n     * top toward negative z and facing positive y.\n     */\n    this.orientation = defaultOrient;\n\n    /**\n     * @member {number} glyphGeometryDetail\n     * Controls number of vertical/horizontal segments that make up each glyph's rectangular\n     * plane. Defaults to 1. This can be increased to provide more geometrical detail for custom\n     * vertex shader effects, for example.\n     */\n    this.glyphGeometryDetail = 1;\n\n    /**\n     * @member {number|null} sdfGlyphSize\n     * The size of each glyph's SDF (signed distance field) used for rendering. This must be a\n     * power-of-two number. Defaults to 64 which is generally a good balance of size and quality\n     * for most fonts. Larger sizes can improve the quality of glyph rendering by increasing\n     * the sharpness of corners and preventing loss of very thin lines, at the expense of\n     * increased memory footprint and longer SDF generation time.\n     */\n    this.sdfGlyphSize = null;\n\n    /**\n     * @member {boolean} gpuAccelerateSDF\n     * When `true`, the SDF generation process will be GPU-accelerated with WebGL when possible,\n     * making it much faster especially for complex glyphs, and falling back to a JavaScript version\n     * executed in web workers when support isn't available. It should automatically detect support,\n     * but it's still somewhat experimental, so you can set it to `false` to force it to use the JS\n     * version if you encounter issues with it.\n     */\n    this.gpuAccelerateSDF = true;\n\n    this.debugSDF = false;\n  }\n\n  /**\n   * Updates the text rendering according to the current text-related configuration properties.\n   * This is an async process, so you can pass in a callback function to be executed when it\n   * finishes.\n   * @param {function} [callback]\n   */\n  sync(callback) {\n    if (this._needsSync) {\n      this._needsSync = false;\n\n      // If there's another sync still in progress, queue\n      if (this._isSyncing) {\n        (this._queuedSyncs || (this._queuedSyncs = [])).push(callback);\n      } else {\n        this._isSyncing = true;\n        this.dispatchEvent(syncStartEvent);\n\n        getTextRenderInfo({\n          text: this.text,\n          font: this.font,\n          lang: this.lang,\n          fontSize: this.fontSize || 0.1,\n          fontWeight: this.fontWeight || 'normal',\n          fontStyle: this.fontStyle || 'normal',\n          letterSpacing: this.letterSpacing || 0,\n          lineHeight: this.lineHeight || 'normal',\n          maxWidth: this.maxWidth,\n          direction: this.direction || 'auto',\n          textAlign: this.textAlign,\n          textIndent: this.textIndent,\n          whiteSpace: this.whiteSpace,\n          overflowWrap: this.overflowWrap,\n          anchorX: this.anchorX,\n          anchorY: this.anchorY,\n          colorRanges: this.colorRanges,\n          includeCaretPositions: true, //TODO parameterize\n          sdfGlyphSize: this.sdfGlyphSize,\n          gpuAccelerateSDF: this.gpuAccelerateSDF,\n          unicodeFontsURL: this.unicodeFontsURL,\n        }, textRenderInfo => {\n          this._isSyncing = false;\n\n          // Save result for later use in onBeforeRender\n          this._textRenderInfo = textRenderInfo;\n\n          // Update the geometry attributes\n          this.geometry.updateGlyphs(\n            textRenderInfo.glyphBounds,\n            textRenderInfo.glyphAtlasIndices,\n            textRenderInfo.blockBounds,\n            textRenderInfo.chunkedBounds,\n            textRenderInfo.glyphColors\n          );\n\n          // If we had extra sync requests queued up, kick it off\n          const queued = this._queuedSyncs;\n          if (queued) {\n            this._queuedSyncs = null;\n            this._needsSync = true;\n            this.sync(() => {\n              queued.forEach(fn => fn && fn());\n            });\n          }\n\n          this.dispatchEvent(syncCompleteEvent);\n          if (callback) {\n            callback();\n          }\n        });\n      }\n    }\n  }\n\n  /**\n   * Initiate a sync if needed - note it won't complete until next frame at the\n   * earliest so if possible it's a good idea to call sync() manually as soon as\n   * all the properties have been set.\n   * @override\n   */\n  onBeforeRender(renderer, scene, camera, geometry, material, group) {\n    this.sync();\n\n    // This may not always be a text material, e.g. if there's a scene.overrideMaterial present\n    if (material.isTroikaTextMaterial) {\n      this._prepareForRender(material);\n    }\n  }\n\n  /**\n   * Shortcut to dispose the geometry specific to this instance.\n   * Note: we don't also dispose the derived material here because if anything else is\n   * sharing the same base material it will result in a pause next frame as the program\n   * is recompiled. Instead users can dispose the base material manually, like normal,\n   * and we'll also dispose the derived material at that time.\n   */\n  dispose() {\n    this.geometry.dispose();\n  }\n\n  /**\n   * @property {TroikaTextRenderInfo|null} textRenderInfo\n   * @readonly\n   * The current processed rendering data for this TextMesh, returned by the TextBuilder after\n   * a `sync()` call. This will be `null` initially, and may be stale for a short period until\n   * the asynchrous `sync()` process completes.\n   */\n  get textRenderInfo() {\n    return this._textRenderInfo || null\n  }\n\n  /**\n   * Create the text derived material from the base material. Can be overridden to use a custom\n   * derived material.\n   */\n  createDerivedMaterial(baseMaterial) {\n    return createTextDerivedMaterial(baseMaterial)\n  }\n\n  // Handler for automatically wrapping the base material with our upgrades. We do the wrapping\n  // lazily on _read_ rather than write to avoid unnecessary wrapping on transient values.\n  get material() {\n    let derivedMaterial = this._derivedMaterial;\n    const baseMaterial = this._baseMaterial || this._defaultMaterial || (this._defaultMaterial = defaultMaterial.clone());\n    if (!derivedMaterial || !derivedMaterial.isDerivedFrom(baseMaterial)) {\n      derivedMaterial = this._derivedMaterial = this.createDerivedMaterial(baseMaterial);\n      // dispose the derived material when its base material is disposed:\n      baseMaterial.addEventListener('dispose', function onDispose() {\n        baseMaterial.removeEventListener('dispose', onDispose);\n        derivedMaterial.dispose();\n      });\n    }\n    // If text outline is configured, render it as a preliminary draw using Three's multi-material\n    // feature (see GlyphsGeometry which sets up `groups` for this purpose) Doing it with multi\n    // materials ensures the layers are always rendered consecutively in a consistent order.\n    // Each layer will trigger onBeforeRender with the appropriate material.\n    if (this.hasOutline()) {\n      let outlineMaterial = derivedMaterial._outlineMtl;\n      if (!outlineMaterial) {\n        outlineMaterial = derivedMaterial._outlineMtl = Object.create(derivedMaterial, {\n          id: {value: derivedMaterial.id + 0.1}\n        });\n        outlineMaterial.isTextOutlineMaterial = true;\n        outlineMaterial.depthWrite = false;\n        outlineMaterial.map = null; //???\n        derivedMaterial.addEventListener('dispose', function onDispose() {\n          derivedMaterial.removeEventListener('dispose', onDispose);\n          outlineMaterial.dispose();\n        });\n      }\n      return [\n        outlineMaterial,\n        derivedMaterial\n      ]\n    } else {\n      return derivedMaterial\n    }\n  }\n  set material(baseMaterial) {\n    if (baseMaterial && baseMaterial.isTroikaTextMaterial) { //prevent double-derivation\n      this._derivedMaterial = baseMaterial;\n      this._baseMaterial = baseMaterial.baseMaterial;\n    } else {\n      this._baseMaterial = baseMaterial;\n    }\n  }\n\n  hasOutline() {\n    return !!(this.outlineWidth || this.outlineBlur || this.outlineOffsetX || this.outlineOffsetY)\n  }\n\n  get glyphGeometryDetail() {\n    return this.geometry.detail\n  }\n  set glyphGeometryDetail(detail) {\n    this.geometry.detail = detail;\n  }\n\n  get curveRadius() {\n    return this.geometry.curveRadius\n  }\n  set curveRadius(r) {\n    this.geometry.curveRadius = r;\n  }\n\n  // Create and update material for shadows upon request:\n  get customDepthMaterial() {\n    return first(this.material).getDepthMaterial()\n  }\n  set customDepthMaterial(m) {\n    // future: let the user override with their own?\n  }\n  get customDistanceMaterial() {\n    return first(this.material).getDistanceMaterial()\n  }\n  set customDistanceMaterial(m) {\n    // future: let the user override with their own?\n  }\n\n  _prepareForRender(material) {\n    const isOutline = material.isTextOutlineMaterial;\n    const uniforms = material.uniforms;\n    const textInfo = this.textRenderInfo;\n    if (textInfo) {\n      const {sdfTexture, blockBounds} = textInfo;\n      uniforms.uTroikaSDFTexture.value = sdfTexture;\n      uniforms.uTroikaSDFTextureSize.value.set(sdfTexture.image.width, sdfTexture.image.height);\n      uniforms.uTroikaSDFGlyphSize.value = textInfo.sdfGlyphSize;\n      uniforms.uTroikaSDFExponent.value = textInfo.sdfExponent;\n      uniforms.uTroikaTotalBounds.value.fromArray(blockBounds);\n      uniforms.uTroikaUseGlyphColors.value = !isOutline && !!textInfo.glyphColors;\n\n      let distanceOffset = 0;\n      let blurRadius = 0;\n      let strokeWidth = 0;\n      let fillOpacity;\n      let strokeOpacity;\n      let strokeColor;\n      let offsetX = 0;\n      let offsetY = 0;\n\n      if (isOutline) {\n        let {outlineWidth, outlineOffsetX, outlineOffsetY, outlineBlur, outlineOpacity} = this;\n        distanceOffset = this._parsePercent(outlineWidth) || 0;\n        blurRadius = Math.max(0, this._parsePercent(outlineBlur) || 0);\n        fillOpacity = outlineOpacity;\n        offsetX = this._parsePercent(outlineOffsetX) || 0;\n        offsetY = this._parsePercent(outlineOffsetY) || 0;\n      } else {\n        strokeWidth = Math.max(0, this._parsePercent(this.strokeWidth) || 0);\n        if (strokeWidth) {\n          strokeColor = this.strokeColor;\n          uniforms.uTroikaStrokeColor.value.set(strokeColor == null ? defaultStrokeColor : strokeColor);\n          strokeOpacity = this.strokeOpacity;\n          if (strokeOpacity == null) strokeOpacity = 1;\n        }\n        fillOpacity = this.fillOpacity;\n      }\n\n      uniforms.uTroikaEdgeOffset.value = distanceOffset;\n      uniforms.uTroikaPositionOffset.value.set(offsetX, offsetY);\n      uniforms.uTroikaBlurRadius.value = blurRadius;\n      uniforms.uTroikaStrokeWidth.value = strokeWidth;\n      uniforms.uTroikaStrokeOpacity.value = strokeOpacity;\n      uniforms.uTroikaFillOpacity.value = fillOpacity == null ? 1 : fillOpacity;\n      uniforms.uTroikaCurveRadius.value = this.curveRadius || 0;\n\n      let clipRect = this.clipRect;\n      if (clipRect && Array.isArray(clipRect) && clipRect.length === 4) {\n        uniforms.uTroikaClipRect.value.fromArray(clipRect);\n      } else {\n        // no clipping - choose a finite rect that shouldn't ever be reached by overflowing glyphs or outlines\n        const pad = (this.fontSize || 0.1) * 100;\n        uniforms.uTroikaClipRect.value.set(\n          blockBounds[0] - pad,\n          blockBounds[1] - pad,\n          blockBounds[2] + pad,\n          blockBounds[3] + pad\n        );\n      }\n      this.geometry.applyClipRect(uniforms.uTroikaClipRect.value);\n    }\n    uniforms.uTroikaSDFDebug.value = !!this.debugSDF;\n    material.polygonOffset = !!this.depthOffset;\n    material.polygonOffsetFactor = material.polygonOffsetUnits = this.depthOffset || 0;\n\n    // Shortcut for setting material color via `color` prop on the mesh; this is\n    // applied only to the derived material to avoid mutating a shared base material.\n    const color = isOutline ? (this.outlineColor || 0) : this.color;\n\n    if (color == null) {\n      delete material.color; //inherit from base\n    } else {\n      const colorObj = material.hasOwnProperty('color') ? material.color : (material.color = new Color());\n      if (color !== colorObj._input || typeof color === 'object') {\n        colorObj.set(colorObj._input = color);\n      }\n    }\n\n    // base orientation\n    let orient = this.orientation || defaultOrient;\n    if (orient !== material._orientation) {\n      let rotMat = uniforms.uTroikaOrient.value;\n      orient = orient.replace(/[^-+xyz]/g, '');\n      let match = orient !== defaultOrient && orient.match(/^([-+])([xyz])([-+])([xyz])$/);\n      if (match) {\n        let [, hSign, hAxis, vSign, vAxis] = match;\n        tempVec3a.set(0, 0, 0)[hAxis] = hSign === '-' ? 1 : -1;\n        tempVec3b.set(0, 0, 0)[vAxis] = vSign === '-' ? -1 : 1;\n        tempMat4.lookAt(origin, tempVec3a.cross(tempVec3b), tempVec3b);\n        rotMat.setFromMatrix4(tempMat4);\n      } else {\n        rotMat.identity();\n      }\n      material._orientation = orient;\n    }\n  }\n\n  _parsePercent(value) {\n    if (typeof value === 'string') {\n      let match = value.match(/^(-?[\\d.]+)%$/);\n      let pct = match ? parseFloat(match[1]) : NaN;\n      value = (isNaN(pct) ? 0 : pct / 100) * this.fontSize;\n    }\n    return value\n  }\n\n  /**\n   * Translate a point in local space to an x/y in the text plane.\n   */\n  localPositionToTextCoords(position, target = new Vector2()) {\n    target.copy(position); //simple non-curved case is 1:1\n    const r = this.curveRadius;\n    if (r) { //flatten the curve\n      target.x = Math.atan2(position.x, Math.abs(r) - Math.abs(position.z)) * Math.abs(r);\n    }\n    return target\n  }\n\n  /**\n   * Translate a point in world space to an x/y in the text plane.\n   */\n  worldPositionToTextCoords(position, target = new Vector2()) {\n    tempVec3a.copy(position);\n    return this.localPositionToTextCoords(this.worldToLocal(tempVec3a), target)\n  }\n\n  /**\n   * @override Custom raycasting to test against the whole text block's max rectangular bounds\n   * TODO is there any reason to make this more granular, like within individual line or glyph rects?\n   */\n  raycast(raycaster, intersects) {\n    const {textRenderInfo, curveRadius} = this;\n    if (textRenderInfo) {\n      const bounds = textRenderInfo.blockBounds;\n      const raycastMesh = curveRadius ? getCurvedRaycastMesh() : getFlatRaycastMesh();\n      const geom = raycastMesh.geometry;\n      const {position, uv} = geom.attributes;\n      for (let i = 0; i < uv.count; i++) {\n        let x = bounds[0] + (uv.getX(i) * (bounds[2] - bounds[0]));\n        const y = bounds[1] + (uv.getY(i) * (bounds[3] - bounds[1]));\n        let z = 0;\n        if (curveRadius) {\n          z = curveRadius - Math.cos(x / curveRadius) * curveRadius;\n          x = Math.sin(x / curveRadius) * curveRadius;\n        }\n        position.setXYZ(i, x, y, z);\n      }\n      geom.boundingSphere = this.geometry.boundingSphere;\n      geom.boundingBox = this.geometry.boundingBox;\n      raycastMesh.matrixWorld = this.matrixWorld;\n      raycastMesh.material.side = this.material.side;\n      tempArray.length = 0;\n      raycastMesh.raycast(raycaster, tempArray);\n      for (let i = 0; i < tempArray.length; i++) {\n        tempArray[i].object = this;\n        intersects.push(tempArray[i]);\n      }\n    }\n  }\n\n  copy(source) {\n    // Prevent copying the geometry reference so we don't end up sharing attributes between instances\n    const geom = this.geometry;\n    super.copy(source);\n    this.geometry = geom;\n\n    COPYABLE_PROPS.forEach(prop => {\n      this[prop] = source[prop];\n    });\n    return this\n  }\n\n  clone() {\n    return new this.constructor().copy(this)\n  }\n}\n\n\n// Create setters for properties that affect text layout:\nSYNCABLE_PROPS.forEach(prop => {\n  const privateKey = '_private_' + prop;\n  Object.defineProperty(Text.prototype, prop, {\n    get() {\n      return this[privateKey]\n    },\n    set(value) {\n      if (value !== this[privateKey]) {\n        this[privateKey] = value;\n        this._needsSync = true;\n      }\n    }\n  });\n});\n\nconst syncStartEvent$1 = { type: \"syncstart\" };\nconst syncCompleteEvent$1 = { type: \"synccomplete\" };\nconst memberIndexAttrName = \"aTroikaTextBatchMemberIndex\";\n\n\n/*\nData texture packing strategy:\n\n# Common:\n0-15: matrix\n16-19: uTroikaTotalBounds\n20-23: uTroikaClipRect\n24: diffuse (color/outlineColor)\n25: uTroikaFillOpacity (fillOpacity/outlineOpacity)\n26: uTroikaCurveRadius\n27: <blank>\n\n# Main:\n28: uTroikaStrokeWidth\n29: uTroikaStrokeColor\n30: uTroikaStrokeOpacity\n\n# Outline:\n28-29: uTroikaPositionOffset\n30: uTroikaEdgeOffset\n31: uTroikaBlurRadius\n*/\nconst floatsPerMember = 32;\n\nconst tempBox3 = new Box3();\nconst tempColor$1 = new Color();\n\n/**\n * @experimental\n *\n * A specialized `Text` implementation that accepts any number of `Text` children\n * and automatically batches them together to render in a single draw call.\n *\n * The `material` of each child `Text` will be ignored, and the `material` of the\n * `BatchedText` will be used for all of them instead.\n *\n * NOTE: This only works in WebGL2 or where the OES_texture_float extension is available.\n */\nclass BatchedText extends Text {\n  constructor () {\n    super();\n\n    /**\n     * @typedef {Object} PackingInfo\n     * @property {number} index - the packing order index when last packed, or -1\n     * @property {boolean} dirty - whether it has synced since last pack\n     */\n\n    /**\n     * @type {Map<Text, PackingInfo>}\n     */\n    this._members = new Map();\n    this._dataTextures = {};\n\n    this._onMemberSynced = (e) => {\n      this._members.get(e.target).dirty = true;\n    };\n  }\n\n  /**\n   * @override\n   * Batch any Text objects added as children\n   */\n  add (...objects) {\n    for (let i = 0; i < objects.length; i++) {\n      if (objects[i] instanceof Text) {\n        this.addText(objects[i]);\n      } else {\n        super.add(objects[i]);\n      }\n    }\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  remove (...objects) {\n    for (let i = 0; i < objects.length; i++) {\n      if (objects[i] instanceof Text) {\n        this.removeText(objects[i]);\n      } else {\n        super.remove(objects[i]);\n      }\n    }\n    return this;\n  }\n\n  /**\n   * @param {Text} text\n   */\n  addText (text) {\n    if (!this._members.has(text)) {\n      this._members.set(text, {\n        index: -1,\n        glyphCount: -1,\n        dirty: true\n      });\n      text.addEventListener(\"synccomplete\", this._onMemberSynced);\n    }\n  }\n\n  /**\n   * @param {Text} text\n   */\n  removeText (text) {\n    this._needsRepack = true;\n    text.removeEventListener(\"synccomplete\", this._onMemberSynced);\n    this._members.delete(text);\n  }\n\n  /**\n   * Use the custom derivation with extra batching logic\n   */\n  createDerivedMaterial (baseMaterial) {\n    return createBatchedTextMaterial(baseMaterial);\n  }\n\n  updateMatrixWorld (force) {\n    super.updateMatrixWorld(force);\n    this.updateBounds();\n  }\n\n  /**\n   * Update the batched geometry bounds to hold all members\n   */\n  updateBounds () {\n    // Update member local matrices and the overall bounds\n    const bbox = this.geometry.boundingBox.makeEmpty();\n    this._members.forEach((_, text) => {\n      if (text.matrixAutoUpdate) text.updateMatrix(); // ignore world matrix\n      tempBox3.copy(text.geometry.boundingBox).applyMatrix4(text.matrix);\n      bbox.union(tempBox3);\n    });\n    bbox.getBoundingSphere(this.geometry.boundingSphere);\n  }\n\n  /** @override */\n  hasOutline() {\n    // Iterator.some() not supported in Safari\n    for (let member of this._members.keys()) {\n      if (member.hasOutline()) return true;\n    }\n    return false;\n  }\n\n  /**\n   * @override\n   * Copy member matrices and uniform values into the data texture\n   */\n  _prepareForRender (material) {\n    const isOutline = material.isTextOutlineMaterial;\n    material.uniforms.uTroikaIsOutline.value = isOutline;\n\n    // Resize the texture to fit in powers of 2\n    let texture = this._dataTextures[isOutline ? 'outline' : 'main'];\n    const dataLength = Math.pow(2, Math.ceil(Math.log2(this._members.size * floatsPerMember)));\n    if (!texture || dataLength !== texture.image.data.length) {\n      // console.log(`resizing: ${dataLength}`);\n      if (texture) texture.dispose();\n      const width = Math.min(dataLength / 4, 1024);\n      texture = this._dataTextures[isOutline ? 'outline' : 'main'] = new DataTexture(\n        new Float32Array(dataLength),\n        width,\n        dataLength / 4 / width,\n        RGBAFormat,\n        FloatType\n      );\n    }\n\n    const texData = texture.image.data;\n    const setTexData = (index, value) => {\n      if (value !== texData[index]) {\n        texData[index] = value;\n        texture.needsUpdate = true;\n      }\n    };\n    this._members.forEach(({ index, dirty }, text) => {\n      if (index > -1) {\n        const startIndex = index * floatsPerMember;\n\n        // Matrix\n        const matrix = text.matrix.elements;\n        for (let i = 0; i < 16; i++) {\n          setTexData(startIndex + i, matrix[i]);\n        }\n\n        // Let the member populate the uniforms, since that does all the appropriate\n        // logic and handling of defaults, and we'll just grab the results from there\n        text._prepareForRender(material);\n        const {\n          uTroikaTotalBounds,\n          uTroikaClipRect,\n          uTroikaPositionOffset,\n          uTroikaEdgeOffset,\n          uTroikaBlurRadius,\n          uTroikaStrokeWidth,\n          uTroikaStrokeColor,\n          uTroikaStrokeOpacity,\n          uTroikaFillOpacity,\n          uTroikaCurveRadius,\n        } = material.uniforms;\n\n        // Total bounds for uv\n        for (let i = 0; i < 4; i++) {\n          setTexData(startIndex + 16 + i, uTroikaTotalBounds.value.getComponent(i));\n        }\n\n        // Clip rect\n        for (let i = 0; i < 4; i++) {\n          setTexData(startIndex + 20 + i, uTroikaClipRect.value.getComponent(i));\n        }\n\n        // Color\n        let color = isOutline ? (text.outlineColor || 0) : text.color;\n        if (color == null) color = this.color;\n        if (color == null) color = this.material.color;\n        if (color == null) color = 0xffffff;\n        setTexData(startIndex + 24, tempColor$1.set(color).getHex());\n\n        // Fill opacity / outline opacity\n        setTexData(startIndex + 25, uTroikaFillOpacity.value);\n\n        // Curve radius\n        setTexData(startIndex + 26, uTroikaCurveRadius.value);\n\n        if (isOutline) {\n          // Outline properties\n          setTexData(startIndex + 28, uTroikaPositionOffset.value.x);\n          setTexData(startIndex + 29, uTroikaPositionOffset.value.y);\n          setTexData(startIndex + 30, uTroikaEdgeOffset.value);\n          setTexData(startIndex + 31, uTroikaBlurRadius.value);\n        } else {\n          // Stroke properties\n          setTexData(startIndex + 28, uTroikaStrokeWidth.value);\n          setTexData(startIndex + 29, tempColor$1.set(uTroikaStrokeColor.value).getHex());\n          setTexData(startIndex + 30, uTroikaStrokeOpacity.value);\n        }\n      }\n    });\n    material.setMatrixTexture(texture);\n\n    // For the non-member-specific uniforms:\n    super._prepareForRender(material);\n  }\n\n  sync (callback) {\n    // TODO: skip members updating their geometries, just use textRenderInfo directly\n\n    // Trigger sync on all members that need it\n    let syncPromises = this._needsRepack ? [] : null;\n    this._needsRepack = false;\n    this._members.forEach((packingInfo, text) => {\n      if (packingInfo.dirty || text._needsSync) {\n        packingInfo.dirty = false;\n        (syncPromises || (syncPromises = [])).push(new Promise(resolve => {\n          if (text._needsSync) {\n            text.sync(resolve);\n          } else {\n            resolve();\n          }\n        }));\n      }\n    });\n\n    // If any needed syncing, wait for them and then repack the batched geometry\n    if (syncPromises) {\n      this.dispatchEvent(syncStartEvent$1);\n\n      Promise.all(syncPromises).then(() => {\n        const { geometry } = this;\n        const batchedAttributes = geometry.attributes;\n        let memberIndexes = batchedAttributes[memberIndexAttrName] && batchedAttributes[memberIndexAttrName].array || new Uint16Array(0);\n        let batchedGlyphIndexes = batchedAttributes[glyphIndexAttrName] && batchedAttributes[glyphIndexAttrName].array || new Float32Array(0);\n        let batchedGlyphBounds = batchedAttributes[glyphBoundsAttrName] && batchedAttributes[glyphBoundsAttrName].array || new Float32Array(0);\n\n        // Initial pass to collect total glyph count and resize the arrays if needed\n        let totalGlyphCount = 0;\n        this._members.forEach((packingInfo, { textRenderInfo }) => {\n          if (textRenderInfo) {\n            totalGlyphCount += textRenderInfo.glyphAtlasIndices.length;\n            this._textRenderInfo = textRenderInfo; // TODO - need this, but be smarter\n          }\n        });\n        if (totalGlyphCount !== memberIndexes.length) {\n          memberIndexes = cloneAndResize(memberIndexes, totalGlyphCount);\n          batchedGlyphIndexes = cloneAndResize(batchedGlyphIndexes, totalGlyphCount);\n          batchedGlyphBounds = cloneAndResize(batchedGlyphBounds, totalGlyphCount * 4);\n        }\n\n        // Populate batch arrays\n        let memberIndex = 0;\n        let glyphIndex = 0;\n        this._members.forEach((packingInfo, { textRenderInfo }) => {\n          if (textRenderInfo) {\n            const glyphCount = textRenderInfo.glyphAtlasIndices.length;\n            memberIndexes.fill(memberIndex, glyphIndex, glyphIndex + glyphCount);\n\n            // TODO can skip these for members that are not dirty or shifting overall position:\n            batchedGlyphIndexes.set(textRenderInfo.glyphAtlasIndices, glyphIndex, glyphIndex + glyphCount);\n            batchedGlyphBounds.set(textRenderInfo.glyphBounds, glyphIndex * 4, (glyphIndex + glyphCount) * 4);\n\n            glyphIndex += glyphCount;\n            packingInfo.index = memberIndex++;\n          }\n        });\n\n        // Update the geometry attributes\n        geometry.updateAttributeData(memberIndexAttrName, memberIndexes, 1);\n        geometry.getAttribute(memberIndexAttrName).setUsage(DynamicDrawUsage);\n        geometry.updateAttributeData(glyphIndexAttrName, batchedGlyphIndexes, 1);\n        geometry.updateAttributeData(glyphBoundsAttrName, batchedGlyphBounds, 4);\n\n        this.updateBounds();\n\n        this.dispatchEvent(syncCompleteEvent$1);\n        if (callback) {\n          callback();\n        }\n      });\n    }\n  }\n\n  copy (source) {\n    if (source instanceof BatchedText) {\n      super.copy(source);\n      this._members.forEach((_, text) => this.removeText(text));\n      source._members.forEach((_, text) => this.addText(text));\n    }\n    return this;\n  }\n\n  dispose () {\n    super.dispose();\n    Object.values(this._dataTextures).forEach(tex => tex.dispose());\n  }\n}\n\nfunction cloneAndResize (source, newLength) {\n  const copy = new source.constructor(newLength);\n  copy.set(source.subarray(0, newLength));\n  return copy;\n}\n\nfunction createBatchedTextMaterial (baseMaterial) {\n  const texUniformName = \"uTroikaMatricesTexture\";\n  const texSizeUniformName = \"uTroikaMatricesTextureSize\";\n\n  // Due to how vertexTransform gets injected, the matrix transforms must happen\n  // in the base material of TextDerivedMaterial, but other transforms to its\n  // shader must come after, so we sandwich it between two derivations.\n\n  // Transform the vertex position\n  let batchMaterial = createDerivedMaterial(baseMaterial, {\n    chained: true,\n    uniforms: {\n      [texSizeUniformName]: { value: new Vector2() },\n      [texUniformName]: { value: null }\n    },\n    // language=GLSL\n    vertexDefs: `\n      uniform highp sampler2D ${texUniformName};\n      uniform vec2 ${texSizeUniformName};\n      attribute float ${memberIndexAttrName};\n\n      vec4 troikaBatchTexel(float offset) {\n        offset += ${memberIndexAttrName} * ${floatsPerMember.toFixed(1)} / 4.0;\n        float w = ${texSizeUniformName}.x;\n        vec2 uv = (vec2(mod(offset, w), floor(offset / w)) + 0.5) / ${texSizeUniformName};\n        return texture2D(${texUniformName}, uv);\n      }\n    `,\n    // language=GLSL prefix=\"void main() {\" suffix=\"}\"\n    vertexTransform: `\n      mat4 matrix = mat4(\n        troikaBatchTexel(0.0),\n        troikaBatchTexel(1.0),\n        troikaBatchTexel(2.0),\n        troikaBatchTexel(3.0)\n      );\n      position.xyz = (matrix * vec4(position, 1.0)).xyz;\n    `,\n  });\n\n  // Add the text shaders\n  batchMaterial = createTextDerivedMaterial(batchMaterial);\n\n  // Now make other changes to the derived text shader code\n  batchMaterial = createDerivedMaterial(batchMaterial, {\n    chained: true,\n    uniforms: {\n      uTroikaIsOutline: {value: false},\n    },\n    customRewriter(shaders) {\n      // Convert some text shader uniforms to varyings\n      const varyingUniforms = [\n        'uTroikaTotalBounds',\n        'uTroikaClipRect',\n        'uTroikaPositionOffset',\n        'uTroikaEdgeOffset',\n        'uTroikaBlurRadius',\n        'uTroikaStrokeWidth',\n        'uTroikaStrokeColor',\n        'uTroikaStrokeOpacity',\n        'uTroikaFillOpacity',\n        'uTroikaCurveRadius',\n        'diffuse'\n      ];\n      varyingUniforms.forEach(uniformName => {\n        shaders = uniformToVarying(shaders, uniformName);\n      });\n      return shaders\n    },\n    // language=GLSL\n    vertexDefs: `\n      uniform bool uTroikaIsOutline;\n      vec3 troikaFloatToColor(float v) {\n        return mod(floor(vec3(v / 65536.0, v / 256.0, v)), 256.0) / 256.0;\n      }\n    `,\n    // language=GLSL prefix=\"void main() {\" suffix=\"}\"\n    vertexTransform: `\n      uTroikaTotalBounds = troikaBatchTexel(4.0);\n      uTroikaClipRect = troikaBatchTexel(5.0);\n      \n      vec4 data = troikaBatchTexel(6.0);\n      diffuse = troikaFloatToColor(data.x);\n      uTroikaFillOpacity = data.y;\n      uTroikaCurveRadius = data.z;\n      \n      data = troikaBatchTexel(7.0);\n      if (uTroikaIsOutline) {\n        if (data == vec4(0.0)) { // degenerate if zero outline\n          position = vec3(0.0);\n        } else {\n          uTroikaPositionOffset = data.xy;\n          uTroikaEdgeOffset = data.z;\n          uTroikaBlurRadius = data.w;\n        }\n      } else {\n        uTroikaStrokeWidth = data.x;\n        uTroikaStrokeColor = troikaFloatToColor(data.y);\n        uTroikaStrokeOpacity = data.z;\n      }\n    `,\n  });\n\n  batchMaterial.setMatrixTexture = (texture) => {\n    batchMaterial.uniforms[texUniformName].value = texture;\n    batchMaterial.uniforms[texSizeUniformName].value.set(texture.image.width, texture.image.height);\n  };\n  return batchMaterial;\n}\n\n/**\n * Turn a uniform into a varying/writeable value.\n * - If the uniform was used in the fragment shader, it will become a varying in both shaders.\n * - If the uniform was only used in the vertex shader, it will become a writeable var.\n */\nfunction uniformToVarying({vertexShader, fragmentShader}, uniformName, varyingName = uniformName) {\n  const uniformRE = new RegExp(`uniform\\\\s+(bool|float|vec[234]|mat[34])\\\\s+${uniformName}\\\\b`);\n\n  let type;\n  let hadFragmentUniform = false;\n  fragmentShader = fragmentShader.replace(uniformRE, ($0, $1) => {\n    hadFragmentUniform = true;\n    return `varying ${type = $1} ${varyingName}`\n  });\n\n  let hadVertexUniform = false;\n  vertexShader = vertexShader.replace(uniformRE, (_, $1) => {\n    hadVertexUniform = true;\n    return `${hadFragmentUniform ? 'varying' : ''} ${type = $1} ${varyingName}`\n  });\n  if (!hadVertexUniform) {\n    vertexShader = `${hadFragmentUniform ? 'varying' : ''} ${type} ${varyingName};\\n${vertexShader}`;\n  }\n  return {vertexShader, fragmentShader}\n}\n\n//=== Utility functions for dealing with carets and selection ranges ===//\n\n/**\n * @typedef {object} TextCaret\n * @property {number} x - x position of the caret\n * @property {number} y - y position of the caret's bottom\n * @property {number} height - height of the caret\n * @property {number} charIndex - the index in the original input string of this caret's target\n *   character; the caret will be for the position _before_ that character.\n */\n\n/**\n * Given a local x/y coordinate in the text block plane, find the nearest caret position.\n * @param {TroikaTextRenderInfo} textRenderInfo - a result object from TextBuilder#getTextRenderInfo\n * @param {number} x\n * @param {number} y\n * @return {TextCaret | null}\n */\nfunction getCaretAtPoint(textRenderInfo, x, y) {\n  let closestCaret = null;\n  const rows = groupCaretsByRow(textRenderInfo);\n\n  // Find nearest row by y first\n  let closestRow = null;\n  rows.forEach(row => {\n    if (!closestRow || Math.abs(y - (row.top + row.bottom) / 2) < Math.abs(y - (closestRow.top + closestRow.bottom) / 2)) {\n      closestRow = row;\n    }\n  });\n\n  // Then find closest caret by x within that row\n  closestRow.carets.forEach(caret => {\n    if (!closestCaret || Math.abs(x - caret.x) < Math.abs(x - closestCaret.x)) {\n      closestCaret = caret;\n    }\n  });\n  return closestCaret\n}\n\n\nconst _rectsCache = new WeakMap();\n\n/**\n * Given start and end character indexes, return a list of rectangles covering all the\n * characters within that selection.\n * @param {TroikaTextRenderInfo} textRenderInfo\n * @param {number} start - index of the first char in the selection\n * @param {number} end - index of the first char after the selection\n * @return {Array<{left, top, right, bottom}> | null}\n */\nfunction getSelectionRects(textRenderInfo, start, end) {\n  let rects;\n  if (textRenderInfo) {\n    // Check cache - textRenderInfo is frozen so it's safe to cache based on it\n    let prevResult = _rectsCache.get(textRenderInfo);\n    if (prevResult && prevResult.start === start && prevResult.end === end) {\n      return prevResult.rects\n    }\n\n    const {caretPositions} = textRenderInfo;\n\n    // Normalize\n    if (end < start) {\n      const s = start;\n      start = end;\n      end = s;\n    }\n    start = Math.max(start, 0);\n    end = Math.min(end, caretPositions.length + 1);\n\n    // Build list of rects, expanding the current rect for all characters in a run and starting\n    // a new rect whenever reaching a new line or a new bidi direction\n    rects = [];\n    let currentRect = null;\n    for (let i = start; i < end; i++) {\n      const x1 = caretPositions[i * 4];\n      const x2 = caretPositions[i * 4 + 1];\n      const left = Math.min(x1, x2);\n      const right = Math.max(x1, x2);\n      const bottom = caretPositions[i * 4 + 2];\n      const top = caretPositions[i * 4 + 3];\n      if (!currentRect || bottom !== currentRect.bottom || top !== currentRect.top || left > currentRect.right || right < currentRect.left) {\n        currentRect = {\n          left: Infinity,\n          right: -Infinity,\n          bottom,\n          top,\n        };\n        rects.push(currentRect);\n      }\n      currentRect.left = Math.min(left, currentRect.left);\n      currentRect.right = Math.max(right, currentRect.right);\n    }\n\n    // Merge any overlapping rects, e.g. those formed by adjacent bidi runs\n    rects.sort((a, b) => b.bottom - a.bottom || a.left - b.left);\n    for (let i = rects.length - 1; i-- > 0;) {\n      const rectA = rects[i];\n      const rectB = rects[i + 1];\n      if (rectA.bottom === rectB.bottom && rectA.top === rectB.top && rectA.left <= rectB.right && rectA.right >= rectB.left) {\n        rectB.left = Math.min(rectB.left, rectA.left);\n        rectB.right = Math.max(rectB.right, rectA.right);\n        rects.splice(i, 1);\n      }\n    }\n\n    _rectsCache.set(textRenderInfo, {start, end, rects});\n  }\n  return rects\n}\n\nconst _caretsByRowCache = new WeakMap();\n\n/**\n * Group a set of carets by row of text, caching the result. A single row of text may contain carets of\n * differing positions/heights if it has multiple fonts, and they may overlap slightly across rows, so this\n * uses an assumption of \"at least overlapping by half\" to put them in the same row.\n * @return Array<{bottom: number, top: number, carets: TextCaret[]}>\n */\nfunction groupCaretsByRow(textRenderInfo) {\n  // textRenderInfo is frozen so it's safe to cache based on it\n  let rows = _caretsByRowCache.get(textRenderInfo);\n  if (!rows) {\n    rows = [];\n    const {caretPositions} = textRenderInfo;\n    let curRow;\n\n    const visitCaret = (x, bottom, top, charIndex) => {\n      // new row if not overlapping by at least half\n      if (!curRow || (top < (curRow.top + curRow.bottom) / 2)) {\n        rows.push(curRow = {bottom, top, carets: []});\n      }\n      // expand vertical limits if necessary\n      if (top > curRow.top) curRow.top = top;\n      if (bottom < curRow.bottom) curRow.bottom = bottom;\n      curRow.carets.push({\n        x,\n        y: bottom,\n        height: top - bottom,\n        charIndex,\n      });\n    };\n\n    let i = 0;\n    for (; i < caretPositions.length; i += 4) {\n      visitCaret(caretPositions[i], caretPositions[i + 2], caretPositions[i + 3], i / 4);\n    }\n    // Add one more caret after the final char\n    visitCaret(caretPositions[i - 3], caretPositions[i - 2], caretPositions[i - 1], i / 4);\n  }\n  _caretsByRowCache.set(textRenderInfo, rows);\n  return rows\n}\n\nexport { BatchedText, GlyphsGeometry, Text, configureTextBuilder, createTextDerivedMaterial, dumpSDFTextures, fontResolverWorkerModule, getCaretAtPoint, getSelectionRects, getTextRenderInfo, preloadFont, typesetterWorkerModule };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA;;;AAGA,GACA,SAAS;IAAc,OAAO,eAAa,OAAO,UAAQ,CAAC,KAAK,MAAM,GAAC,IAAI,GAAE,SAAS,CAAC;QAAE,IAAI,IAAE;YAAC,OAAM,SAAS,CAAC;gBAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,IAAI,WAAW;gBAAG,IAAG,UAAQ,EAAE,SAAS,CAAC,GAAE,GAAE,IAAG;oBAAC,IAAI,IAAE;oBAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI;wBAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;wBAAG,KAAG,GAAE,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE;oBAAI;oBAAC,OAAO;gBAAC;gBAAC,OAAO;oBAAC,EAAE,SAAS,CAAC,GAAE;iBAAG;YAAA;YAAE,WAAU,SAAS,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE;gBAAE,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;gBAAE,IAAI,IAAI,IAAE;oBAAC;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;iBAAO,EAAC,IAAE;oBAAC,OAAM;oBAAE,SAAQ;gBAAC,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAI,IAAE,EAAE,SAAS,CAAC,GAAE,GAAE;oBAAG,KAAG,GAAE,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;oBAAG,KAAG,GAAE,CAAC,CAAC,EAAE,GAAC;wBAAC,QAAO;wBAAE,QAAO;oBAAC;gBAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;oBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;oBAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,GAAC,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,GAAE,CAAC,CAAC,EAAE,CAAC,MAAM,EAAC,CAAC,CAAC,EAAE,CAAC,MAAM,EAAC,EAAE;gBAAE;gBAAC,OAAO;YAAC;YAAE,YAAW,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE,IAAE,IAAG,IAAE,IAAE,IAAG,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAI,IAAE,EAAE,SAAS,CAAC,GAAE,GAAE;oBAAG,KAAG,GAAE,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;oBAAG,IAAG,KAAG,GAAE,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,KAAG,GAAE,OAAO;gBAAC;gBAAC,OAAO;YAAC;QAAC;QAAE,EAAE,IAAI,GAAC;YAAC,WAAU,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAE,IAAE,CAAC,CAAC,IAAE,EAAE,IAAE,CAAC,CAAC,CAAC,IAAE,EAAE,IAAE,IAAE,CAAC,CAAC,IAAE,EAAE,IAAE;YAAK;YAAE,aAAY,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,GAAE,KAAG;YAAK;YAAE,SAAQ,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;YAAE;YAAE,UAAS,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;YAAE;YAAE,WAAU,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;YAAE;YAAE,YAAW,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;YAAE;YAAE,aAAY,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,GAAE,IAAE,IAAE;gBAAI,OAAO;YAAC;YAAE,UAAS,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;YAAE;YAAE,YAAW,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,aAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAE,KAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAE,IAAE;YAAE;YAAE,WAAU,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAI,IAAE,IAAG,IAAE,GAAE,IAAE,GAAE,IAAI,KAAG,OAAO,YAAY,CAAC,CAAC,CAAC,IAAE,EAAE;gBAAE,OAAO;YAAC;YAAE,aAAY,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAI,IAAE,IAAG,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAI,IAAE,CAAC,CAAC,IAAI,IAAE,IAAE,CAAC,CAAC,IAAI;oBAAC,KAAG,OAAO,YAAY,CAAC;gBAAG;gBAAC,OAAO;YAAC;YAAE,OAAM,eAAa,OAAO,UAAQ,OAAO,WAAW,GAAC,IAAI,OAAO,WAAW,GAAC;YAAK,UAAS,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAE,EAAE,IAAI,CAAC,KAAK;gBAAC,OAAO,KAAG,KAAG,KAAG,KAAG,EAAE,MAAM,GAAC,EAAE,MAAM,CAAC,KAAG,EAAE,IAAI,CAAC,SAAS,CAAC,GAAE,GAAE;YAAE;YAAE,WAAU,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAE,EAAE;gBAAE,OAAO;YAAC;YAAE,gBAAe,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,OAAO,YAAY,CAAC,CAAC,CAAC,IAAE,EAAE;gBAAG,OAAO;YAAC;YAAE,OAAM,SAAS,CAAC;gBAAE,OAAO,EAAE,SAAS,IAAE,CAAC,EAAE,SAAS,GAAC,EAAE,MAAM,GAAC,IAAI,SAAS,EAAE,MAAM,EAAC,EAAE,UAAU,EAAC,EAAE,UAAU,IAAE,IAAI,SAAS,IAAI,WAAW,GAAG,MAAM,CAAC;YAAC;QAAC,GAAE,EAAE,KAAK,GAAC,CAAC,GAAE,EAAE,KAAK,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC,GAAE,IAAE;YAAE,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,OAAO,KAAG,GAAE,EAAE,UAAU,GAAC,EAAE,KAAK,CAAC,cAAc,CAAC,GAAE,IAAE,IAAG,EAAE,WAAW,GAAC,EAAE,KAAK,CAAC,eAAe,CAAC,GAAE,IAAE,IAAG,EAAE,UAAU,GAAC,EAAE,KAAK,CAAC,cAAc,CAAC,GAAE,IAAE,GAAE,IAAG;QAAC,GAAE,EAAE,KAAK,CAAC,cAAc,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,EAAE,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,KAAK,CAAC,eAAe,CAAC,GAAE,IAAE,GAAE;gBAAG,EAAE,IAAI,CAAC;YAAG;YAAC,OAAO;QAAC,GAAE,EAAE,KAAK,CAAC,eAAe,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE;gBAAC,MAAK,EAAE;YAAA;YAAE,EAAE,KAAK,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,EAAE,KAAK,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,GAAE,GAAE,IAAE,GAAE;gBAAG,EAAE,IAAI,CAAC,IAAI,CAAC;YAAG;YAAC,OAAO;QAAC,GAAE,EAAE,KAAK,CAAC,SAAS,GAAC,SAAS,CAAC;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAI,KAAG,CAAC,MAAI,IAAE,CAAC,KAAG;YAAI,OAAO;QAAC,GAAE,EAAE,KAAK,CAAC,YAAY,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,IAAG,KAAG,GAAE,KAAG,GAAE;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,IAAE,IAAG,EAAE,IAAI,CAAC,IAAE,IAAG,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,KAAI,KAAG;YAAE;YAAC,IAAG,KAAG,GAAE;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,KAAI,KAAG,GAAE,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,KAAI,KAAG,GAAE,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,KAAI,KAAG;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,KAAK,CAAC,WAAW,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE;gBAAC,IAAG,CAAC,CAAC,IAAE,EAAE,EAAC,KAAG,KAAG,KAAG,GAAE,OAAO;YAAC;YAAC,OAAO,CAAC;QAAC,GAAE,EAAE,KAAK,CAAC,YAAY,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,EAAE,GAAG,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,OAAO,KAAG,GAAE,KAAG,EAAE,GAAG,IAAE,CAAC,EAAE,GAAG,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,EAAE,GAAE,KAAG,EAAE,GAAG,IAAE,CAAC,EAAE,GAAG,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,IAAE,EAAE,GAAE;QAAC,GAAE,EAAE,KAAK,CAAC,aAAa,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,GAAG;YAAC,IAAG,KAAG,EAAE,GAAG,EAAC,OAAO,EAAE,OAAO,CAAC;YAAG,IAAG,KAAG,EAAE,GAAG,EAAC;gBAAC,IAAI,IAAE,EAAE,KAAK,CAAC,WAAW,CAAC,GAAE;gBAAG,IAAG,CAAC,KAAG,GAAE,OAAO,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,IAAE,CAAC,CAAC,EAAE;YAAC;YAAC,OAAO,CAAC;QAAC,GAAE,EAAE,KAAK,CAAC,eAAe,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,EAAE,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,SAAS,CAAC,GAAE,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,KAAK,CAAC,gBAAgB,CAAC,GAAE,IAAE;gBAAG,EAAE,GAAG,GAAC,EAAE,IAAI,IAAG,EAAE,IAAI,CAAC;YAAG;YAAC,OAAO;QAAC,GAAE,EAAE,KAAK,CAAC,gBAAgB,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG,GAAE,IAAE,KAAG,CAAC,EAAE,aAAa,GAAC,IAAE,CAAC;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG,GAAE,EAAE,GAAG,GAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,IAAE,IAAE;YAAI,OAAO;QAAC,GAAE,EAAE,KAAK,CAAC,cAAc,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,SAAS,CAAC,GAAE,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,CAAC,CAAC,EAAE,IAAI,GAAG,GAAC,EAAE,KAAK,CAAC,eAAe,CAAC,GAAE,IAAE;YAAG;YAAC,OAAO;QAAC,GAAE,EAAE,KAAK,CAAC,eAAe,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG,GAAE,IAAE,KAAG,CAAC,EAAE,OAAO,GAAC,EAAE,KAAK,CAAC,gBAAgB,CAAC,GAAE,IAAE,EAAE;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,SAAS,CAAC,GAAE,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,CAAC,CAAC,EAAE,IAAI,GAAG,GAAC,EAAE,KAAK,CAAC,gBAAgB,CAAC,GAAE,IAAE;YAAG;YAAC,OAAO;QAAC,GAAE,EAAE,KAAK,CAAC,gBAAgB,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,OAAO,KAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,IAAG;QAAC,GAAE,EAAE,GAAG,GAAC,CAAC,GAAE,EAAE,GAAG,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI;YAAC,CAAC,IAAE,IAAI,WAAW,EAAE,MAAM,EAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,EAAE,EAAE,EAAC,CAAC,CAAC,EAAE,EAAE,EAAC,CAAC,CAAC,EAAE,EAAE,EAAC;YAAI,IAAI,IAAE,EAAE;YAAC,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,GAAE;YAAG,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,GAAC,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE;YAAG,KAAG,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;YAAC,IAAI,IAAE,EAAE;YAAC,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,GAAE;YAAG,IAAI,IAAE,EAAE;YAAC,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,GAAC,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE;YAAG,KAAG,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,EAAE;YAAC,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,GAAE;YAAG,IAAI,IAAE,EAAE;YAAC,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,GAAC,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE;YAAG,IAAG,KAAG,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,EAAC,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,GAAE,IAAG,EAAE,WAAW,EAAC;gBAAC,IAAE,EAAE,WAAW;gBAAC,IAAE,EAAE;gBAAC,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,GAAE;gBAAG,IAAI,IAAE,EAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,GAAC,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE;gBAAG,EAAE,WAAW,GAAC;YAAE;YAAC,IAAG,EAAE,GAAG,EAAC;gBAAC,IAAE,EAAE,OAAO;gBAAC,IAAI,IAAE,EAAE;gBAAC,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,GAAE,IAAG,EAAE,OAAO,GAAC,EAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,GAAC,GAAE,IAAI;oBAAC,IAAI,IAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE;oBAAE,EAAE,GAAG,CAAC,UAAU,CAAC,GAAE,GAAE,IAAG,EAAE,OAAO,CAAC,IAAI,CAAC;gBAAG;gBAAC,KAAG,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,QAAQ,GAAC,EAAE;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;gBAAC,IAAG,KAAI,KAAG,GAAE,MAAM;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,GAAE,IAAE,IAAE,GAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,IAAG,CAAC,CAAC,IAAE,EAAE,GAAE,KAAG;YAAE;YAAC,OAAO,EAAE,QAAQ,IAAE,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAG,CAAC,YAAY,CAAC,GAAE,EAAE,QAAQ,EAAC,EAAE,WAAW,CAAC,MAAM,CAAC,GAAE,EAAE,OAAO,IAAE,CAAC,EAAE,OAAO,GAAC,EAAE,GAAG,CAAC,WAAW,CAAC,GAAE,EAAE,OAAO,EAAC,EAAE,WAAW,CAAC,MAAM,CAAC,GAAE,EAAE,GAAG,CAAC,UAAU,CAAC,GAAE,GAAE,IAAG;QAAC,GAAE,EAAE,GAAG,CAAC,UAAU,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI;YAAE,IAAI,IAAI,KAAK,EAAE,OAAO,IAAE,CAAC,IAAE,EAAE,OAAO,CAAC,EAAE,EAAC,EAAE,OAAO,GAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAE,GAAE,IAAE,EAAE,OAAO,CAAC,EAAE,GAAE,EAAE,OAAO,CAAC,KAAK,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,IAAE,EAAE,OAAO,CAAC,KAAK,EAAC,EAAE,OAAO,CAAC,GAAE,EAAE,CAAC,KAAG;gBAAC;gBAAa;gBAAW;gBAAW;gBAAS;gBAAU;aAAY,CAAC,OAAO,CAAC,MAAI,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAC,MAAI,GAAG;QAAE,GAAE,EAAE,GAAG,CAAC,SAAS,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE;YAAC,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,GAAE;YAAG,IAAI,GAAE,IAAE,EAAE,MAAM;YAAC,IAAE,IAAE,OAAK,MAAI,IAAE,QAAM,OAAK,OAAM,EAAE,IAAI,GAAC,GAAE,EAAE,KAAK,GAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,GAAC,GAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE;QAAG,GAAE,EAAE,GAAG,CAAC,OAAO,GAAC;YAAC;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAE;YAAI;YAAI;YAAI;YAAI;YAAE;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAE;YAAI;YAAE;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAE;YAAI;YAAI;YAAE;YAAI;YAAI;YAAI;YAAI;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAI;YAAE;YAAI;YAAE;YAAE;YAAE;YAAE;YAAI;YAAI;YAAI;YAAI;YAAE;YAAE;YAAE;YAAE;YAAE;YAAI;YAAE;YAAE;YAAE;YAAI;YAAE;YAAE;YAAI;YAAI;YAAI;YAAI;YAAE;YAAE;YAAE;SAAE,EAAC,EAAE,GAAG,CAAC,cAAc,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,OAAO,CAAC,MAAM,EAAC,IAAI,IAAG,EAAE,OAAO,CAAC,EAAE,IAAE,GAAE,OAAO;YAAE,OAAO,CAAC;QAAC,GAAE,EAAE,GAAG,CAAC,SAAS,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,OAAO,IAAE,KAAG,IAAE,MAAI,CAAC,IAAE,EAAE,GAAG,CAAC,cAAc,CAAC,GAAE,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE;QAAC,GAAE,EAAE,GAAG,CAAC,YAAY,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,EAAE,IAAI;YAAC,IAAI,IAAE;gBAAC;aAAU,EAAC,IAAE,CAAC,CAAC,EAAE;YAAC,IAAG,KAAI,KAAG,GAAE,MAAM,qCAAmC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC;YAAI,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAE,EAAE;YAAE,OAAO;QAAC,GAAE,EAAE,GAAG,CAAC,WAAW,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE;gBAAC;aAAU,EAAC,IAAE,CAAC,CAAC,EAAE;YAAC,IAAG,KAAI,KAAG,GAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,EAAE,IAAI,CAAC;YAAG;iBAAM;gBAAC,IAAG,KAAG,KAAG,KAAG,GAAE,MAAM,oBAAkB;gBAAE,MAAK,EAAE,MAAM,GAAC,GAAG;oBAAC,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAE;oBAAE,KAAG,IAAE,CAAC,IAAE,CAAC,CAAC,EAAE,EAAC,GAAG,IAAE,CAAC,IAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,CAAC;oBAAE,IAAI,IAAE,GAAE,KAAG,GAAE,IAAI,EAAE,IAAI,CAAC,IAAG;gBAAI;YAAC;YAAC,OAAO;QAAC,GAAE,EAAE,GAAG,CAAC,SAAS,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE,KAAG,GAAE,IAAE,CAAC,CAAC,KAAG,EAAE;YAAC,IAAG,KAAI,KAAG,GAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAE,EAAE;iBAAO,IAAG,KAAG,GAAE,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,IAAE,IAAE;iBAAS,IAAG,KAAG,GAAE,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,WAAS,EAAE,QAAQ,CAAC,GAAE,IAAE,IAAE,IAAE;iBAAS,IAAG,KAAG,GAAE,MAAM,8BAA4B,IAAE,cAAY;YAAE,OAAO,CAAC,KAAG,IAAE,CAAC,IAAE;QAAC,GAAE,EAAE,GAAG,CAAC,aAAa,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE;YAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE;YAAC,IAAI,IAAE,GAAE,IAAE,MAAK,IAAE;YAAK,KAAG,MAAI,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,CAAC,IAAE,MAAI,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,KAAG,MAAI,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,CAAC,IAAE,EAAE,SAAS,CAAC,GAAE,IAAE,IAAG,IAAE,CAAC,GAAE,MAAI,KAAG,KAAG,MAAI,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,KAAG,OAAK,CAAC,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,KAAG,KAAG,OAAK,CAAC,IAAE,MAAI,CAAC,IAAE,GAAG,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,KAAG,KAAG,OAAK,CAAC,IAAE,MAAI,CAAC,CAAC,IAAE,GAAG,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,KAAG,CAAC,IAAE,EAAE,OAAO,CAAC,GAAE,IAAE,KAAG,OAAM,IAAE,CAAC,GAAE,EAAE,GAAG,GAAC,QAAM,IAAE,IAAE,MAAI,GAAE,EAAE,IAAI,GAAC;QAAE,GAAE,EAAE,GAAG,CAAC,cAAc,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,IAAE,GAAE,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,EAAC,IAAE,GAAG;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE;gBAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,MAAK,IAAE;gBAAK,KAAG,MAAI,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,CAAC,IAAE,MAAI,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,MAAI,KAAG,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,KAAG,MAAI,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,CAAC,IAAE,EAAE,SAAS,CAAC,GAAE,IAAE,IAAG,IAAE,CAAC,GAAE,MAAI,KAAG,KAAG,MAAI,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,MAAI,KAAG,KAAG,OAAK,CAAC,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,KAAG,KAAG,OAAK,CAAC,IAAE,MAAI,CAAC,IAAE,GAAG,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,KAAG,KAAG,OAAK,CAAC,IAAE,MAAI,CAAC,CAAC,IAAE,GAAG,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,KAAG,CAAC,IAAE,EAAE,OAAO,CAAC,GAAE,IAAE,KAAG,OAAM,IAAE,CAAC,GAAE,EAAE,IAAI,CAAC,QAAM,IAAE,IAAE,MAAI,IAAG,KAAG;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,GAAG,CAAC,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC,GAAE,IAAE,EAAE,EAAC,IAAE,GAAG;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE;gBAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,MAAK,IAAE;gBAAK,IAAG,MAAI,KAAG,CAAC,IAAE,EAAE,SAAS,CAAC,GAAE,IAAE,IAAG,IAAE,CAAC,GAAE,MAAI,KAAG,CAAC,IAAE,EAAE,OAAO,CAAC,GAAE,IAAE,IAAG,IAAE,CAAC,GAAE,MAAI,KAAG,KAAG,OAAK,CAAC,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,KAAG,KAAG,OAAK,CAAC,IAAE,MAAI,CAAC,IAAE,GAAG,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,KAAG,KAAG,OAAK,CAAC,IAAE,MAAI,CAAC,CAAC,IAAE,GAAG,IAAE,IAAE,KAAI,IAAE,CAAC,GAAE,OAAK,GAAE,MAAM,IAAE,EAAE,OAAO,CAAC,GAAE,IAAE,KAAG,OAAM,IAAE,GAAE;gBAAiB,IAAG,MAAI,GAAE;oBAAC,IAAI,IAAE,EAAE;oBAAC,IAAI,IAAE,IAAI;wBAAC,IAAI,IAAE,CAAC,CAAC,IAAE,EAAE;wBAAC;wBAAI,IAAI,IAAE,KAAG,GAAE,IAAE,KAAG;wBAAE,IAAG,MAAI,KAAG,EAAE,IAAI,CAAC,IAAG,MAAI,KAAG,EAAE,IAAI,CAAC,IAAG,MAAI,GAAE;oBAAK;oBAAC,IAAI,IAAI,IAAE,IAAG,IAAE;wBAAC;wBAAE;wBAAE;wBAAE;wBAAE;wBAAE;wBAAE;wBAAE;wBAAE;wBAAE;wBAAE;wBAAI;wBAAI;wBAAK;wBAAW;wBAAI;qBAAc,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,KAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAAC,IAAE,WAAW;gBAAG;gBAAC,IAAG,KAAG,IAAG;oBAAA,IAAG,IAAE;wBAAC;wBAAU;wBAAS;wBAAW;wBAAa;wBAAS;wBAAW;wBAAa;wBAAa;wBAAc;wBAAmB;wBAAQ;wBAAQ;wBAAS;wBAAW;wBAAO;wBAAU;wBAAW;wBAAc;wBAAU;wBAAQ;wBAAgB;qBAAgB,CAAC,EAAE,EAAC,IAAE,GAAE,MAAI,GAAE,IAAE;wBAAC;wBAAY;wBAAe;wBAAc;wBAAoB;wBAAqB;wBAAY;wBAAiB;wBAAa;wBAAc;wBAAY;wBAAY;wBAAW;wBAAY;wBAAY;wBAAY;wBAAE;wBAAE;wBAAgB;wBAAkB;wBAAoB;wBAAgB;wBAAa;wBAAe;wBAAgB;wBAAE;wBAAE;wBAAE;wBAAE;wBAAE;wBAAE;wBAAM;wBAAiB;wBAAkB;wBAAc;wBAAW;wBAAU;wBAAU;wBAAW;qBAAW,CAAC,EAAE,EAAC,IAAE;gBAAC;gBAAC,QAAM,IAAE,CAAC,CAAC,CAAC,EAAE,GAAC,KAAG,EAAE,MAAM,GAAC,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,EAAE,IAAE,EAAE,IAAI,CAAC,IAAG,KAAG;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAE,IAAI,WAAW,EAAE,MAAM,EAAC,GAAE,IAAG,IAAE;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAE,EAAE;YAAC,EAAE,MAAM,GAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,MAAI,IAAE,MAAI,GAAE,IAAE,EAAE,OAAO,CAAC;gBAAG,IAAG,CAAC,KAAG,GAAE;oBAAC,IAAI;oBAAE,IAAE,EAAE,MAAM,CAAC,MAAM,EAAC,EAAE,IAAI,CAAC;oBAAG,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG,IAAE,IAAE,EAAE,IAAI,CAAC,MAAM,CAAC,GAAE,KAAG,KAAG,IAAE,IAAE,EAAE,IAAI,CAAC,MAAM,CAAC,GAAE,KAAG,KAAG,IAAE,IAAE,EAAE,IAAI,CAAC,MAAM,CAAC,GAAE,KAAG,MAAI,IAAE,IAAE,EAAE,IAAI,CAAC,OAAO,CAAC,GAAE,KAAG,QAAQ,KAAK,CAAC,qBAAmB,GAAE,GAAE,GAAE,IAAG,EAAE,MAAM,CAAC,IAAI,CAAC;gBAAG;gBAAC,IAAG,QAAM,CAAC,CAAC,EAAE,EAAC,MAAM;gBAA4C,CAAC,CAAC,EAAE,GAAC;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,GAAG,GAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,IAAE,GAAE,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAE,EAAE;YAAE,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,CAAC;YAAE,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAE,IAAE;YAAE,EAAE,WAAW,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,aAAa,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,GAAE,KAAG,GAAE,EAAE,UAAU,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,GAAE,EAAE,OAAO,GAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE,KAAI,KAAG;YAAE,IAAI,EAAE,aAAa,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,GAAE,EAAE,YAAY,GAAC,EAAE,EAAC,IAAE,IAAE,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,KAAI,KAAG;YAAE,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,SAAS,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG,GAAE,EAAE,YAAY,GAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,KAAI,KAAG;YAAE,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,OAAO,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,KAAG,GAAE,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;YAAG,KAAG,GAAE,EAAE,MAAM,GAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,IAAE,KAAG,GAAE,IAAE,EAAE,QAAQ,CAAC,GAAE,IAAE,IAAG,IAAE,EAAE,QAAQ,CAAC,GAAE,IAAE,IAAG,IAAE,EAAE,QAAQ,CAAC,GAAE,IAAE;gBAAG,EAAE,MAAM,CAAC,IAAI,CAAC;oBAAC;oBAAE;oBAAE;iBAAE;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC;YAAM,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,UAAU,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE,QAAO,EAAE,OAAO,IAAE,EAAE,IAAI,CAAC,EAAE;YAAC,IAAG,EAAE,IAAI,CAAC,EAAE,IAAE,EAAE,IAAI,CAAC,IAAE,EAAE,EAAC,OAAO;YAAK,IAAI,IAAE,CAAC;YAAE,IAAG,EAAE,GAAG,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,IAAE,EAAE,IAAI,IAAE,EAAE,IAAI,IAAE,EAAE,IAAI,EAAC,OAAO;YAAK,IAAG,EAAE,GAAG,GAAC,GAAE;gBAAC,EAAE,MAAM,GAAC,EAAE;gBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,GAAG,EAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,KAAI,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,IAAG,KAAG,GAAE,EAAE,MAAM,GAAC,IAAE,GAAE,OAAO;gBAAK,EAAE,YAAY,GAAC,EAAE,SAAS,CAAC,GAAE,GAAE,IAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,MAAM,CAAC,EAAE,GAAG,GAAC,EAAE,GAAC;gBAAE,EAAE,KAAK,GAAC,EAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;oBAAC,IAAG,KAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAG,KAAG,CAAC,IAAE,CAAC,GAAE;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;wBAAC;wBAAI,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAG;oBAAI;gBAAC;gBAAC,EAAE,EAAE,GAAC,EAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAI,IAAE,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,IAAE,KAAG,CAAC,KAAG,EAAE,KAAK,CAAC,EAAE;oBAAE,IAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC,EAAE,GAAE,GAAG,IAAE,IAAE,EAAE,EAAE,CAAC,IAAI,CAAC,KAAG,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE,KAAI,KAAG,CAAC;gBAAE;gBAAC,EAAE,EAAE,GAAC,EAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAE,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,IAAE,KAAG,CAAC,KAAG,EAAE,KAAK,CAAC,EAAE;oBAAE,IAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC,EAAE,GAAE,GAAG,IAAE,IAAE,EAAE,EAAE,CAAC,IAAI,CAAC,KAAG,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE,KAAI,KAAG,CAAC;gBAAE;gBAAC,IAAI,IAAE,GAAE,IAAE;gBAAE,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,KAAG,EAAE,EAAE,CAAC,EAAE,EAAC,KAAG,EAAE,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,CAAC,EAAE,GAAC,GAAE,EAAE,EAAE,CAAC,EAAE,GAAC;YAAE,OAAM;gBAAC,IAAI;gBAAE,EAAE,KAAK,GAAC,EAAE;gBAAC,GAAE;oBAAC,IAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;oBAAE,IAAI,IAAE;wBAAC,GAAE;4BAAC,GAAE;4BAAE,GAAE;4BAAE,GAAE;4BAAE,GAAE;4BAAE,IAAG;4BAAE,IAAG;wBAAC;wBAAE,IAAG,CAAC;wBAAE,IAAG,CAAC;oBAAC;oBAAE,IAAG,EAAE,KAAK,CAAC,IAAI,CAAC,IAAG,EAAE,UAAU,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,IAAE,GAAE;wBAAC,IAAI,IAAE,EAAE,SAAS,CAAC,GAAE;wBAAG,KAAG;wBAAE,IAAI,IAAE,EAAE,SAAS,CAAC,GAAE;wBAAG,KAAG;oBAAE,OAAM;wBAAC,IAAE,EAAE,QAAQ,CAAC,GAAE;wBAAG;wBAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;wBAAG;oBAAI;oBAAC,IAAE,IAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAC,GAAE,EAAE,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,CAAC,EAAE,EAAE,GAAC,GAAE,EAAE,EAAE,GAAC,CAAC,GAAE,IAAE,IAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,CAAC,CAAC,GAAC,EAAE,WAAW,CAAC,GAAE,IAAG,KAAG,CAAC,IAAE,KAAG,IAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAC,EAAE,WAAW,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,CAAC,CAAC,CAAC,GAAC,EAAE,WAAW,CAAC,GAAE,IAAG,KAAG,CAAC,IAAE,MAAI,KAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAC,EAAE,WAAW,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,CAAC,CAAC,CAAC,GAAC,EAAE,WAAW,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,CAAC,CAAC,CAAC,GAAC,EAAE,WAAW,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,CAAC,CAAC,CAAC,GAAC,EAAE,WAAW,CAAC,GAAE,IAAG,KAAG,CAAC;gBAAE,QAAO,KAAG,EAAG;gBAAA,IAAG,MAAI,GAAE;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG,GAAE,EAAE,KAAK,GAAC,EAAE;oBAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAE;gBAAI;YAAC;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE;YAAE,KAAG;YAAE,IAAI,IAAE,EAAE,IAAI,CAAC,UAAU,CAAC,GAAE;YAAG,OAAO;gBAAC,eAAc,MAAI,IAAE,OAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,IAAE;YAAE;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,GAAE,GAAE,GAAE,GAAE,EAAE,IAAI,CAAC,IAAI;QAAC,GAAE,EAAE,IAAI,CAAC,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,CAAC;YAAE,IAAG,EAAE,GAAG,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,EAAE,GAAG,IAAE,GAAE;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,IAAE;YAAG;YAAC,IAAG,KAAG,KAAG,KAAG,EAAE,GAAG,EAAC;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,KAAG,KAAG,CAAC,EAAE,GAAG,GAAC,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE,GAAE,EAAE;YAAE,OAAM,IAAG,KAAG,KAAG,EAAE,GAAG,IAAE,KAAG,EAAE,GAAG,IAAE,GAAE;gBAAC,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,KAAK,CAAC,SAAS,CAAC,IAAG,IAAE,EAAE,KAAK,CAAC,SAAS,CAAC;gBAAG,IAAG,KAAG,EAAE,GAAG,EAAC;oBAAC,EAAE,QAAQ,GAAC,EAAE;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;wBAAC,IAAI,IAAE,IAAE,EAAE,UAAU,CAAC,GAAE;wBAAG,KAAG;wBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;wBAAG,KAAG;wBAAE,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI;4BAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;4BAAG,KAAG,GAAE,KAAG,KAAG,CAAC,IAAE,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,CAAC,GAAE,KAAG,KAAG,CAAC,IAAE,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,CAAC,GAAE,EAAE,IAAI,CAAC;gCAAC,MAAK;gCAAE,MAAK;gCAAE,MAAK;4BAAC;wBAAG;wBAAC,EAAE,QAAQ,CAAC,IAAI,CAAC;oBAAG;gBAAC;gBAAC,IAAG,KAAG,EAAE,GAAG,EAAC;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG,GAAE,EAAE,SAAS,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,IAAE,IAAG,EAAE,SAAS,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,IAAE,IAAG,EAAE,MAAM,GAAC,EAAE;oBAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;wBAAC,IAAI,IAAE,EAAE;wBAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;4BAAC,IAAI,IAAE,MAAK,IAAE;4BAAK,KAAG,KAAG,CAAC,IAAE,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,CAAC,GAAE,KAAG,KAAG,CAAC,IAAE,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,CAAC,GAAE,EAAE,IAAI,CAAC;gCAAC,MAAK;gCAAE,MAAK;4BAAC;wBAAG;wBAAC,EAAE,MAAM,CAAC,IAAI,CAAC;oBAAG;gBAAC;YAAC,OAAM,IAAG,KAAG,KAAG,KAAG,EAAE,GAAG,EAAC,EAAE,YAAY,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,KAAG,IAAG,EAAE,YAAY,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,IAAE,KAAG,IAAG,EAAE,cAAc,GAAC,EAAE,UAAU,CAAC,GAAE,IAAE,IAAG,EAAE,SAAS,GAAC,EAAE,IAAI,CAAC,aAAa,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,IAAE,KAAG,IAAG,EAAE,SAAS,GAAC,EAAE,IAAI,CAAC,aAAa,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,IAAE,KAAG,GAAE,EAAE,cAAc;iBAAO,IAAG,KAAG,KAAG,KAAG,EAAE,GAAG,EAAC,EAAE,aAAa,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,KAAG,IAAG,EAAE,aAAa,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,IAAE,KAAG,IAAG,EAAE,cAAc,GAAC,EAAE,UAAU,CAAC,GAAE,IAAE,IAAG,EAAE,UAAU,GAAC,EAAE,IAAI,CAAC,aAAa,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,IAAE,KAAG,IAAG,EAAE,UAAU,GAAC,EAAE,IAAI,CAAC,aAAa,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,IAAE,KAAG,GAAE,EAAE,cAAc;iBAAO;gBAAC,IAAG,KAAG,KAAG,KAAG,EAAE,GAAG,EAAC;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;oBAAG,IAAG,KAAG,GAAE,KAAG,EAAE,KAAK,EAAC,EAAE,KAAK,GAAC;yBAAO,IAAG,EAAE,KAAK,IAAE,GAAE,MAAM;oBAAiC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,GAAE,EAAE,KAAK,EAAC,IAAE;gBAAE;gBAAC,QAAQ,KAAK,CAAC,qCAAoC,GAAE,UAAS,EAAE,GAAG;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,eAAe,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE;YAAC,OAAO,EAAE,IAAI,CAAC,IAAE,IAAE,EAAE,SAAS,CAAC,GAAE,KAAG,IAAG,KAAG,IAAE,IAAE,IAAE,GAAE,EAAE,IAAI,CAAC,IAAE,IAAE,EAAE,SAAS,CAAC,GAAE,KAAG,IAAG,KAAG,IAAE,IAAE,IAAE,GAAE,EAAE,IAAI,CAAC,IAAE,IAAE,EAAE,SAAS,CAAC,GAAE,KAAG,IAAG,KAAG,IAAE,IAAE,IAAE,GAAE,EAAE,IAAI,CAAC,IAAE,IAAE,EAAE,SAAS,CAAC,GAAE,KAAG,IAAG,KAAG,IAAE,IAAE,IAAE,GAAE;QAAC,GAAE,EAAE,IAAI,CAAC,aAAa,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAE,IAAE,EAAE,UAAU,CAAC,GAAE,MAAK,KAAG;gBAAE,EAAE,IAAI,CAAC;YAAG;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,aAAa,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAE,EAAE,UAAU,CAAC,GAAE,IAAE,KAAG;gBAAG,EAAE,SAAS,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,EAAE,IAAI,CAAC,IAAG,KAAG;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,gBAAgB,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,OAAO,EAAE,GAAG,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,EAAE,CAAC,GAAC,EAAE,SAAS,CAAC,GAAE,IAAE,IAAG,EAAE,CAAC,GAAC,EAAE,SAAS,CAAC,GAAE,IAAE,IAAG;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,GAAE,GAAE,GAAE,GAAE,EAAE,IAAI,CAAC,IAAI;QAAC,GAAE,EAAE,IAAI,CAAC,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,CAAC;YAAE,IAAG,EAAE,GAAG,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,GAAE,OAAO;YAAK,IAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,EAAE,GAAG,IAAE,KAAG,KAAG,KAAG,EAAE,GAAG,IAAE,GAAE;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,IAAE;YAAG;YAAC,IAAG,KAAG,KAAG,EAAE,GAAG,IAAE,KAAG,EAAE,GAAG,IAAE,GAAE;gBAAC,IAAG,KAAG,EAAE,GAAG,EAAC,EAAE,KAAK,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG;qBAAO,IAAG,KAAG,EAAE,GAAG,EAAC;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,EAAE,IAAI,CAAC,MAAM;gBAAC;YAAC,OAAM,IAAG,KAAG,KAAG,KAAG,EAAE,GAAG,EAAC;gBAAC,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE;gBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE,KAAG;oBAAE,KAAG;oBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,GAAE,IAAE,GAAE;gBAAI;YAAC,OAAM,IAAG,KAAG,GAAE;gBAAC,EAAE,IAAI,GAAC,EAAE;gBAAC,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG,GAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE,IAAE;gBAAI;YAAC,OAAM,IAAG,KAAG,KAAG,KAAG,EAAE,GAAG,EAAC;gBAAC,IAAG,KAAG,EAAE,GAAG,EAAC;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,IAAE,IAAG,EAAE,KAAK,GAAC,EAAE;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;wBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;wBAAG,KAAG,GAAE,EAAE,KAAK,CAAC,IAAI,CAAC,KAAG,IAAE,OAAK,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE,IAAE;oBAAI;gBAAC;YAAC,OAAM,IAAG,KAAG,KAAG,KAAG,EAAE,GAAG,EAAC;gBAAC,IAAG,KAAG,EAAE,GAAG,EAAC;oBAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;wBAAC,IAAE,EAAE,UAAU,CAAC,GAAE;wBAAG,KAAG;wBAAE,IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,GAAE,IAAE,EAAE,UAAU,CAAC,GAAE,IAAE,IAAE;wBAAK,KAAG,IAAE,GAAE,KAAG,KAAG,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,KAAG,KAAG,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,KAAG,KAAG,CAAC,EAAE,OAAO,GAAC,CAAC;oBAAE;oBAAC,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG,GAAE,EAAE,SAAS,GAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAE,GAAE;gBAAG;YAAC,OAAM;gBAAC,IAAG,KAAG,KAAG,KAAG,EAAE,GAAG,EAAC;oBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;oBAAG,KAAG;oBAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;oBAAG,IAAG,KAAG,GAAE,KAAG,EAAE,KAAK,EAAC,EAAE,KAAK,GAAC;yBAAO,IAAG,EAAE,KAAK,IAAE,GAAE,MAAM;oBAAiC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,GAAE,EAAE,KAAK,EAAC,IAAE;gBAAE;gBAAC,QAAQ,KAAK,CAAC,qCAAoC,GAAE,UAAS,EAAE,GAAG;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,eAAe,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,CAAC,UAAU,EAAC,IAAE,GAAE,IAAE,EAAE,EAAC,IAAE,EAAE,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,GAAE;gBAAG,KAAG,GAAE,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAE,IAAE;YAAI;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,gBAAgB,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,CAAC,UAAU,EAAC,IAAE,CAAC,GAAE,IAAE,EAAE,GAAE,IAAG,IAAE,EAAE,GAAE,KAAG;YAAG,KAAG,GAAE,EAAE,KAAK,GAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,IAAE,GAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,GAAE,KAAI,KAAG;YAAE,OAAO,EAAE,kBAAkB,GAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAE,GAAE,IAAG;QAAC,GAAE,EAAE,IAAI,CAAC,sBAAsB,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,EAAE,IAAI,CAAC,UAAU,EAAC,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,GAAE,IAAG,EAAE,GAAE,IAAE,KAAI,KAAG;YAAE,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,oBAAoB,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,EAAE,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAE,IAAE;YAAI;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,qBAAqB,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC,GAAE,IAAE;gBAAC;gBAAY;gBAAQ;aAAY,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,KAAG,KAAG,KAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,IAAG,KAAG,IAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM;YAAC;YAAC,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,OAAO,KAAG,GAAE,EAAE,KAAK,GAAC,EAAE,WAAW,CAAC,GAAE,GAAE,IAAE,IAAG,KAAG,IAAE,EAAE,KAAK,CAAC,MAAM,EAAC;QAAC,GAAE,EAAE,IAAI,CAAC,eAAe,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,EAAE,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,GAAE,IAAE;YAAI;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,YAAY,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE;gBAAC,OAAM,EAAE;YAAA;YAAE,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,IAAE,GAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,KAAI,KAAG;YAAE,OAAO;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,OAAO,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,YAAY,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,KAAK,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,OAAO,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,IAAI,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,aAAa,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,iBAAiB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,gBAAgB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,OAAO,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,SAAS,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,OAAO,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,kBAAkB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,mBAAmB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,cAAc,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,aAAa,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,WAAW,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,KAAG,GAAE,EAAE,gBAAgB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,gBAAgB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE;gBAAC,QAAO,EAAE;gBAAC,WAAU,EAAE;YAAA,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,SAAS,EAAC,IAAI,IAAE,EAAE,IAAI,CAAC,gBAAgB,IAAE,CAAC,IAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,IAAE,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,CAAC,GAAE,EAAE,MAAM,CAAC,IAAI,CAAC,IAAG,EAAE,SAAS,CAAC,IAAI,CAAC;YAAG,OAAO;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,IAAG,KAAG,GAAE,KAAG,GAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAE,IAAE,GAAE,GAAE;YAAG,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE;gBAAC,QAAO,EAAE;gBAAC,MAAK,EAAE;YAAA,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,KAAG;gBAAE,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,MAAI;gBAAE,IAAG,KAAG,CAAC,KAAG,EAAE,GAAE,MAAM,gCAA8B;gBAAE,IAAE,EAAE,IAAI,CAAC,WAAW,CAAC,GAAE,GAAE;YAAG;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,OAAO,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI;YAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAI,IAAE;gBAAC,QAAO,EAAE;gBAAC,MAAK,EAAE;YAAA,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;gBAAE,IAAI,IAAE,MAAI;gBAAE,IAAG,KAAG,CAAC,KAAG,EAAE,GAAE,MAAM,gCAA8B;gBAAE,IAAE,EAAE,IAAI,CAAC,WAAW,CAAC,GAAE,GAAE;YAAG;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,CAAC,WAAW,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC,GAAE,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,SAAS,CAAC,GAAE;gBAAG,KAAG,GAAE,KAAG,KAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAG,EAAE,IAAI,CAAC,IAAI,CAAC;oBAAC,QAAO,EAAE;oBAAC,MAAK,EAAE;gBAAA,EAAE;gBAAE,IAAI,IAAE,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,GAAC,EAAE;gBAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAG,IAAE;YAAE;YAAC,OAAO;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,EAAE,EAAC,IAAE,EAAE,IAAI,CAAC,gBAAgB,EAAC,IAAE,EAAE,IAAI,CAAC,SAAS,GAAC;YAAE,IAAG,KAAG,GAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,GAAE,IAAE,CAAC,KAAG,CAAC,MAAI;YAAG,IAAG,KAAG,GAAE,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,GAAE,IAAE,CAAC,KAAG,CAAC;YAAI,OAAO;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC,GAAE,IAAE,EAAE,QAAQ,CAAC,GAAE;YAAG,OAAO,KAAG,GAAE,EAAE,SAAS,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,SAAO,KAAG,CAAC,EAAE,SAAS,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,WAAW,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,kBAAkB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,oBAAoB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,iBAAiB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,kBAAkB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,gBAAgB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,qBAAqB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,oBAAoB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,iBAAiB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,CAAC,GAAE;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;YAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;YAAG,KAAG,GAAE,EAAE,UAAU,CAAC,GAAE;YAAG,IAAI,IAAI,GAAE,IAAE;gBAAC;gBAAY;gBAAa;gBAAgB;gBAAK;gBAAW;gBAAU;gBAAiB;gBAAY;gBAAe;gBAAW;gBAAc;gBAAY;gBAAc;gBAAU;gBAAa;gBAAM;gBAAiB;gBAAoB;gBAAiB;gBAAa;gBAAgB;gBAAgB;gBAAmB;gBAAe;aAAc,EAAC,IAAE,KAAG,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,IAAE,EAAE,UAAU,CAAC,GAAE;gBAAG,KAAG;gBAAE,IAAI,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,IAAE,KAAG,IAAE;gBAAE,IAAG,KAAG,GAAE,IAAE,EAAE,WAAW,CAAC,GAAE,GAAE,IAAE;qBAAQ,IAAG,KAAG,KAAG,KAAG,GAAE,IAAE,EAAE,WAAW,CAAC,GAAE,GAAE,IAAE;qBAAQ,IAAG,KAAG,GAAE,IAAE,EAAE,SAAS,CAAC,GAAE,GAAE;qBAAQ,IAAG,KAAG,GAAE,IAAE,EAAE,WAAW,CAAC,GAAE,GAAE,IAAE;qBAAQ,IAAG,KAAG,GAAE,IAAE,EAAE,WAAW,CAAC,GAAE,GAAE,IAAE;qBAAQ;oBAAC,IAAG,KAAG,GAAE,MAAM,sBAAoB,IAAE,mBAAiB;oBAAE,IAAE,EAAE,SAAS,CAAC,GAAE,GAAE,IAAG,QAAQ,KAAK,CAAC,kCAAgC,IAAE;gBAAa;gBAAC,IAAI,IAAE,MAAI,IAAE,MAAI,EAAE,QAAQ,CAAC;gBAAI,QAAM,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,GAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAI,IAAE,IAAE,EAAE,GAAC,GAAE,CAAC,CAAC,EAAE,CAAC,KAAK,GAAC;YAAE;YAAC,IAAI,IAAI,KAAK,EAAE,IAAG,QAAM,CAAC,CAAC,EAAE,CAAC,cAAc,IAAE,QAAM,CAAC,CAAC,EAAE,CAAC,KAAK,EAAC,OAAO,CAAC,CAAC,EAAE;YAAC,IAAI,IAAI,KAAK,EAAE,IAAG,QAAM,CAAC,CAAC,EAAE,CAAC,cAAc,IAAE,KAAG,CAAC,CAAC,EAAE,CAAC,KAAK,EAAC,OAAO,CAAC,CAAC,EAAE;YAAC,IAAI,IAAI,KAAK,EAAE,IAAG,QAAM,CAAC,CAAC,EAAE,CAAC,cAAc,IAAE,QAAM,CAAC,CAAC,EAAE,CAAC,KAAK,EAAC,OAAO,CAAC,CAAC,EAAE;YAAC,IAAI,IAAI,KAAK,EAAE,IAAG,QAAM,CAAC,CAAC,EAAE,CAAC,cAAc,EAAC,OAAO,CAAC,CAAC,EAAE;YAAC,IAAI,IAAI,KAAK,EAAE;gBAAC,IAAE;gBAAE;YAAK;YAAC,OAAO,QAAQ,KAAK,CAAC,0CAAwC,CAAC,CAAC,EAAE,CAAC,KAAK,GAAE,CAAC,CAAC,EAAE;QAAA,GAAE,CAAC,CAAC,OAAO,GAAC,CAAC,GAAE,CAAC,CAAC,OAAO,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,CAAC,UAAU,CAAC,GAAE;YAAG,KAAG;YAAE,IAAI,IAAE,CAAC;YAAE,IAAG,KAAG,GAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAE,GAAE;iBAAQ,IAAG,KAAG,GAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAE,GAAE;iBAAQ,IAAG,KAAG,KAAG,KAAG,KAAG,KAAG,GAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAE,GAAE;iBAAQ;gBAAC,IAAG,KAAG,GAAE,MAAM,iCAA+B;gBAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAE,GAAE;YAAG;YAAC,OAAO;QAAC,GAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI;YAAC,OAAO,EAAE,aAAa,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,aAAa,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,YAAY,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,MAAM,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,iBAAiB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,iBAAiB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,iBAAiB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,iBAAiB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,mBAAmB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,mBAAmB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,cAAc,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,kBAAkB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,YAAY,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,MAAM,GAAC,EAAE,SAAS,CAAC,GAAE,GAAE,KAAI,KAAG,IAAG,EAAE,eAAe,GAAC,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,SAAS,GAAC;gBAAC,EAAE,QAAQ,CAAC,GAAE;gBAAG,EAAE,QAAQ,CAAC,GAAE,IAAE;gBAAG,EAAE,QAAQ,CAAC,GAAE,IAAE;gBAAG,EAAE,QAAQ,CAAC,GAAE,IAAE;aAAG,EAAC,KAAG,GAAE,EAAE,WAAW,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,gBAAgB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,eAAe,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,aAAa,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,cAAc,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,YAAY,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,WAAW,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,YAAY,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;QAAC,GAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI;YAAC,OAAO,IAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAE,GAAE,IAAG,EAAE,gBAAgB,GAAC,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,gBAAgB,GAAC,EAAE,QAAQ,CAAC,GAAE,IAAG,KAAG;QAAC,GAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI;YAAC,OAAO,IAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAE,GAAE,IAAG,EAAE,QAAQ,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,UAAU,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,SAAS,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,OAAO,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,YAAY,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;QAAC,GAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI;YAAC,OAAO,IAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAE,GAAE,IAAG,EAAE,uBAAuB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,uBAAuB,GAAC,EAAE,UAAU,CAAC,GAAE,IAAG,KAAG;QAAC,GAAE,EAAE,IAAI,GAAC,CAAC,GAAE,EAAE,IAAI,CAAC,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,OAAO,EAAE,OAAO,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,WAAW,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,iBAAiB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE,EAAE,kBAAkB,GAAC,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG,GAAE;QAAC,GAAE,QAAM,KAAG,CAAC,IAAE,CAAC,CAAC,GAAE,QAAM,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,GAAE,EAAE,CAAC,CAAC,WAAW,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,EAAC,IAAE,CAAC;YAAE,IAAG,QAAM,EAAE,IAAI,GAAC,IAAE,EAAE,IAAI,GAAC,QAAM,EAAE,IAAI,GAAC,IAAE,EAAE,IAAI,GAAC,QAAM,EAAE,IAAI,GAAC,IAAE,EAAE,IAAI,GAAC,QAAM,EAAE,IAAI,IAAE,CAAC,IAAE,EAAE,IAAI,GAAE,CAAC,KAAG,GAAE,MAAM;YAAqC,IAAI,IAAE,EAAE,MAAM,CAAC,EAAE;YAAC,IAAG,KAAG,EAAE,MAAM,EAAC,OAAO,KAAG,EAAE,GAAG,CAAC,MAAM,GAAC,IAAE,EAAE,GAAG,CAAC,EAAE;YAAC,IAAG,KAAG,EAAE,MAAM,EAAC;gBAAC,IAAI,IAAI,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,QAAQ,CAAC,MAAM,EAAC,IAAI,IAAG,KAAG,EAAE,QAAQ,CAAC,EAAE,EAAC;oBAAC,IAAE;oBAAE;gBAAK;gBAAC,IAAG,CAAC,KAAG,GAAE,OAAO;gBAAE,IAAG,EAAE,UAAU,CAAC,EAAE,GAAC,GAAE,OAAO;gBAAE,OAAO,QAAM,CAAC,KAAG,EAAE,aAAa,CAAC,EAAE,GAAC,EAAE,YAAY,CAAC,IAAE,EAAE,UAAU,CAAC,EAAE,GAAC,CAAC,EAAE,aAAa,CAAC,EAAE,IAAE,CAAC,IAAE,CAAC,EAAE,aAAa,CAAC,MAAM,GAAC,CAAC,EAAE,GAAC,IAAE,EAAE,OAAO,CAAC,EAAE;YAAC;YAAC,IAAG,MAAI,EAAE,MAAM,EAAC;gBAAC,IAAG,IAAE,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,GAAC,EAAE,CAAC,EAAE,EAAC,OAAO;gBAAE,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,CAAC,MAAM,EAAC,IAAI;oBAAC,IAAI,IAAE,EAAE,MAAM,CAAC,EAAE;oBAAC,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG,KAAG,CAAC,CAAC,EAAE,EAAC,OAAO,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,CAAC,CAAC,EAAE;gBAAC;gBAAC,OAAO;YAAC;YAAC,MAAM,+BAA6B,EAAE,MAAM;QAAA,GAAE,EAAE,CAAC,CAAC,WAAW,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE;gBAAC,MAAK,EAAE;gBAAC,MAAK,EAAE;YAAA;YAAE,IAAG,EAAE,GAAG,IAAE,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,EAAC;gBAAC,IAAI,IAAE,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE;gBAAC,OAAO,QAAM,IAAE,IAAE,CAAC,YAAU,OAAO,KAAG,CAAC,IAAE,EAAE,GAAG,CAAC,MAAM,CAAC,IAAG,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,GAAC,CAAC,GAAE,CAAC;YAAC;YAAC,IAAG,EAAE,GAAG,EAAC;gBAAC,IAAI,IAAE;oBAAC,GAAE;oBAAE,GAAE;oBAAE,OAAM,EAAE;oBAAC,QAAO;oBAAE,WAAU,CAAC;oBAAE,OAAM,EAAE,GAAG,CAAC,OAAO,GAAC,EAAE,GAAG,CAAC,OAAO,CAAC,aAAa,GAAC;oBAAE,MAAK,CAAC;gBAAC,GAAE,IAAE,EAAE,GAAG,EAAC,IAAE,EAAE,GAAG,CAAC,OAAO;gBAAC,IAAG,EAAE,GAAG,EAAC;oBAAC,IAAI,IAAI,IAAE,GAAE,EAAE,QAAQ,CAAC,IAAE,EAAE,IAAE,GAAG,KAAG;oBAAE,IAAE,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,IAAE,EAAE,CAAC,CAAC,OAAO;gBAAC;gBAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,EAAE,EAAC,GAAE,GAAE,GAAE;YAAG,OAAM,EAAE,IAAI,IAAE,EAAE,CAAC,CAAC,SAAS,CAAC,GAAE,GAAE;YAAG,OAAO;QAAC,GAAE,EAAE,CAAC,CAAC,SAAS,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,CAAC,EAAE;YAAC,QAAM,KAAG,CAAC,IAAE,EAAE,IAAI,CAAC,EAAE,GAAC,EAAE,IAAI,CAAC,UAAU,CAAC,GAAE,EAAE,GAAE,QAAM,KAAG,CAAC,EAAE,GAAG,GAAC,CAAC,IAAE,EAAE,CAAC,CAAC,YAAY,CAAC,GAAE,KAAG,EAAE,CAAC,CAAC,WAAW,CAAC,GAAE,GAAE,EAAE;QAAE,GAAE,EAAE,CAAC,CAAC,YAAY,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,GAAG,EAAC,IAAI;gBAAC,IAAI,IAAI,IAAE,KAAG,IAAE,IAAE,EAAE,MAAM,CAAC,IAAE,EAAE,GAAC,GAAE,IAAE,EAAE,MAAM,CAAC,EAAE,EAAC,IAAE,GAAE,KAAG,GAAE,IAAI;oBAAC,IAAI,IAAE,KAAG,IAAE,IAAE,IAAE,GAAE,IAAE,KAAG,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE,EAAE,KAAK,CAAC,EAAE,EAAC,IAAE,IAAE,EAAE,KAAK,CAAC,EAAE,EAAC,IAAE,IAAE,EAAE,KAAK,CAAC,EAAE,EAAC,IAAE,EAAE,EAAE,CAAC,EAAE,EAAC,IAAE,EAAE,EAAE,CAAC,EAAE;oBAAC,IAAG,KAAG,GAAE,IAAG,GAAE;wBAAC,IAAG,CAAC,GAAE;4BAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE;4BAAG;wBAAQ;wBAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,EAAE,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,CAAC,EAAE;oBAAE,OAAM,IAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,EAAE,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,CAAC,EAAE,IAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAC,CAAC,IAAE,GAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAC,CAAC,IAAE;oBAAG,IAAE,KAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE,KAAG,IAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAE,GAAE,GAAE,EAAE,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,CAAC,EAAE,IAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAE,GAAE,GAAE,CAAC,IAAE,EAAE,EAAE,CAAC,EAAE,IAAE,GAAE,CAAC,IAAE,EAAE,EAAE,CAAC,EAAE,IAAE;gBAAG;gBAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAAG;QAAC,GAAE,EAAE,CAAC,CAAC,WAAW,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,MAAM,EAAC,IAAI;gBAAC,IAAI,IAAE;oBAAC,MAAK,EAAE;oBAAC,MAAK,EAAE;gBAAA,GAAE,IAAE,EAAE,KAAK,CAAC,EAAE;gBAAC,EAAE,CAAC,CAAC,SAAS,CAAC,EAAE,UAAU,EAAC,GAAE;gBAAG,IAAI,IAAI,IAAE,EAAE,CAAC,EAAC,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,MAAM,EAAC,KAAG,EAAE;oBAAC,IAAI,IAAE,EAAE,IAAI,CAAC,EAAE,EAAC,IAAE,EAAE,IAAI,CAAC,IAAE,EAAE;oBAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAE,EAAE,CAAC,GAAC,IAAE,EAAE,CAAC,GAAC,EAAE,EAAE,GAAE,EAAE,IAAI,CAAC,IAAI,CAAC,IAAE,EAAE,CAAC,GAAC,IAAE,EAAE,CAAC,GAAC,EAAE,EAAE;gBAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,MAAM,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE;YAAE;QAAC,GAAE,EAAE,CAAC,CAAC,cAAc,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,KAAK,CAAC,WAAW,CAAC,GAAE;YAAG,OAAO,CAAC,KAAG,IAAE,IAAE,CAAC,CAAC,IAAE,EAAE;QAAA,GAAE,EAAE,CAAC,CAAC,UAAU,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,EAAE,MAAM,GAAC,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,MAAM,EAAC,IAAI,IAAG,QAAM,EAAE,IAAI,CAAC,EAAE,EAAC;gBAAC,IAAI,GAAE,IAAE,EAAE,IAAI,CAAC,EAAE;gBAAC,IAAG,CAAC,EAAE,QAAQ,IAAE,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC,CAAC,CAAC,EAAE,CAAC,GAAE;oBAAA,IAAG,KAAG,EAAE,KAAK,EAAC,CAAC,CAAC,EAAE,EAAC,KAAG,EAAE,GAAG,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,KAAK,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAI,CAAC,EAAE;yBAAM,IAAG,KAAG,EAAE,KAAK,EAAC,IAAI,IAAI,IAAE,EAAE,IAAI,CAAC,EAAE,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,EAAE,KAAK,CAAC,MAAM;wBAAC,IAAG,CAAC,CAAC,IAAE,CAAC,GAAE;4BAAC,IAAI,IAAI,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI;gCAAC,MAAK,CAAC,KAAG,CAAC,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,EAAE,EAAE;gCAAI,EAAE,KAAK,CAAC,EAAE,IAAE,CAAC,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,EAAE,IAAE,CAAC,IAAE,CAAC,CAAC;4BAAE;4BAAC,IAAG,GAAE;gCAAC,CAAC,CAAC,EAAE,GAAC,EAAE,MAAM;gCAAC,IAAI,IAAE,GAAE,IAAE,IAAE,GAAE,IAAI,CAAC,CAAC,IAAE,IAAE,EAAE,GAAC,CAAC;gCAAE;4BAAK;wBAAC;oBAAC;yBAAM,IAAG,KAAG,EAAE,KAAK,IAAE,KAAG,EAAE,GAAG,EAAC,IAAI,IAAI,IAAE,EAAE,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI,EAAC,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,IAAI,CAAC,IAAE,EAAE,EAAC,IAAE,EAAE,KAAK,CAAC,EAAE,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,EAAE,KAAK;wBAAC,IAAG,CAAC,CAAC,EAAE,MAAM,GAAC,CAAC,GAAE;4BAAC,IAAI,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gCAAC,IAAI,IAAE,EAAE,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI,EAAC,CAAC,CAAC,IAAE,IAAE,EAAE;gCAAE,IAAG,CAAC,KAAG,KAAG,EAAE,IAAI,CAAC,IAAE,EAAE,IAAE,CAAC,CAAC,EAAE,EAAC;oCAAC,IAAE,CAAC;oCAAE;gCAAK;4BAAC;4BAAC,IAAG,GAAE;gCAAC,IAAI,IAAE,EAAE,kBAAkB;gCAAC,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE;4BAAC;wBAAC;oBAAC;yBAAM,IAAG,KAAG,EAAE,KAAK,IAAE,KAAG,EAAE,GAAG,EAAC;wBAAC,IAAG,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAE,EAAE,OAAO,EAAC,IAAE,EAAE,OAAO,CAAC,MAAM,GAAE;wBAAS,IAAG,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAE,EAAE,OAAO,EAAC,IAAG;wBAAS,IAAG,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAE,EAAE,OAAO,EAAC,IAAE,EAAE,OAAO,CAAC,MAAM,GAAE;wBAAS,IAAI,IAAE,EAAE,SAAS;wBAAC,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE;4BAAC,IAAE,CAAC,CAAC,EAAE;4BAAC,IAAI,IAAE,CAAC,CAAC,CAAC,CAAC,IAAE,EAAE,CAAC;4BAAC,EAAE,CAAC,CAAC,UAAU,CAAC,GAAE,IAAE,GAAE,GAAE;wBAAG;oBAAC;gBAAA;YAAC;QAAC,GAAE,EAAE,CAAC,CAAC,WAAW,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gBAAC,IAAG,CAAC,KAAG,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAE,OAAO,CAAC;YAAC;YAAC,OAAO,CAAC;QAAC,GAAE,EAAE,CAAC,CAAC,YAAY,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE;gBAAC,MAAK,EAAE;gBAAC,MAAK,EAAE;YAAA,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;gBAAC,IAAG,CAAC,KAAG,GAAE;oBAAC,IAAI,IAAI,IAAE,IAAE,EAAE,MAAM,GAAC,KAAG,CAAC,KAAG,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,GAAC,GAAE,IAAE,EAAE,CAAC,CAAC,WAAW,CAAC,GAAE,IAAG,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,MAAM,EAAC,KAAG,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,GAAC,IAAG,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAE,EAAE;oBAAE,KAAG,EAAE,IAAI,CAAC,IAAI,CAAC;oBAAG,IAAI,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,MAAM,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE;oBAAE,KAAG,EAAE,IAAI,CAAC,IAAI,CAAC,MAAK,KAAG,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,EAAC,IAAE,EAAE,MAAM,GAAC,KAAG,CAAC,KAAG,EAAE,CAAC,CAAC,iBAAiB,CAAC,GAAE,GAAE,EAAE;gBAAE;YAAC;YAAC,OAAO;QAAC,GAAE,EAAE,CAAC,CAAC,CAAC,GAAC,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,EAAE,IAAI,CAAC,IAAI,CAAC,MAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAE;QAAG,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,EAAE,IAAI,CAAC,IAAI,CAAC,MAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAE;QAAG,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,EAAE,IAAI,CAAC,IAAI,CAAC,MAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE;QAAG,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,EAAE,IAAI,CAAC,IAAI,CAAC,MAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAE,GAAE,GAAE;QAAG,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,GAAC,SAAS,CAAC;YAAE,EAAE,IAAI,CAAC,IAAI,CAAC;QAAK,GAAE,EAAE,CAAC,CAAC,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,IAAI,EAAC,IAAE,GAAE,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE;gBAAC,KAAI;gBAAE,MAAK;YAAC,GAAE,IAAE,EAAE,MAAM,EAAE;gBAAC,EAAE,GAAG,CAAC,aAAa,CAAC,GAAE,GAAE;gBAAG,IAAI,IAAE,EAAE,GAAG;gBAAC,IAAG,KAAG,EAAE,IAAI,EAAC,QAAM,KAAG,SAAO,GAAE,EAAE,MAAM,GAAC,KAAG,KAAG,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,KAAG,EAAE,aAAa,GAAE,KAAG,EAAE,MAAM,IAAE,GAAE,EAAE,MAAM,GAAC,GAAE,IAAE,CAAC;qBAAO,IAAG,QAAM,KAAG,SAAO,GAAE;oBAAC,EAAE,MAAM,GAAC,KAAG,KAAG,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,KAAG,EAAE,aAAa,GAAE,KAAG,EAAE,MAAM,IAAE,GAAE,EAAE,MAAM,GAAC,GAAE,IAAE,CAAC;gBAAE,OAAM,IAAG,QAAM,GAAE,EAAE,MAAM,GAAC,KAAG,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,KAAG,EAAE,aAAa,EAAC,IAAE,CAAC,CAAC,GAAE,KAAG,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAG,KAAG,EAAE,GAAG,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE,IAAG,IAAE,CAAC;qBAAO,IAAG,QAAM,GAAE,MAAK,EAAE,MAAM,GAAC,GAAG,KAAG,EAAE,KAAK,IAAG,KAAG,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE;qBAAQ,IAAG,QAAM,KAAG,QAAM,GAAE,IAAI,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,QAAM,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI;oBAAC,IAAI,IAAE,EAAE,KAAK;oBAAG,IAAE,KAAG,IAAE,KAAG,GAAE,IAAE,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE;gBAAG;qBAAM,IAAG,QAAM,KAAG,SAAO,GAAE;oBAAC,IAAE,EAAE,MAAM;oBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,KAAG,GAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,KAAG;oBAAE,SAAO,KAAG,CAAC,KAAG,EAAE,KAAK,IAAG,KAAG,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE,EAAE;gBAAE,OAAM;oBAAC,IAAG,SAAO,GAAE;oBAAM,IAAG,WAAS,KAAG,WAAS,KAAG,WAAS,KAAG,WAAS,GAAE,WAAS,KAAG,CAAC,IAAE,GAAE,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,GAAE,IAAE,GAAE,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE,GAAE,WAAS,KAAG,CAAC,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE,GAAE,WAAS,KAAG,CAAC,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,GAAE,IAAE,CAAC,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE,GAAE,WAAS,KAAG,CAAC,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,KAAK,GAAG,CAAC,IAAE,KAAG,KAAK,GAAG,CAAC,IAAE,KAAG,IAAE,IAAE,EAAE,KAAK,KAAG,IAAE,IAAE,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE;yBAAO,IAAG,SAAO,GAAE;wBAAC,IAAG,EAAE,MAAM,GAAC,KAAG,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,KAAG,EAAE,aAAa,EAAC,IAAE,CAAC,CAAC,GAAE,KAAG,EAAE,MAAM,EAAC;4BAAC,IAAI,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE,IAAG,IAAE,EAAE,GAAG,CAAC,SAAS,CAAC,GAAE;4BAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,EAAE,EAAC,GAAE,GAAE,GAAE,IAAG,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,EAAE,EAAC,GAAE,GAAE,GAAE;wBAAG;wBAAC,KAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAG,IAAE,CAAC,CAAC;oBAAE,OAAM,IAAG,SAAO,KAAG,SAAO,GAAE;wBAAC,EAAE,MAAM,GAAC,KAAG,KAAG,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,KAAG,EAAE,aAAa,GAAE,KAAG,EAAE,MAAM,IAAE,GAAE,EAAE,MAAM,GAAC,GAAE,IAAE,CAAC,GAAE,KAAG,IAAE,KAAG;oBAAE,OAAM,IAAG,SAAO,GAAE,EAAE,MAAM,GAAC,KAAG,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,KAAG,EAAE,aAAa,EAAC,IAAE,CAAC,CAAC,GAAE,KAAG,EAAE,GAAG,IAAG,KAAG,EAAE,GAAG,IAAG,KAAG,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE,IAAG,IAAE,CAAC;yBAAO,IAAG,SAAO,GAAE,EAAE,MAAM,GAAC,KAAG,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,KAAG,EAAE,aAAa,EAAC,IAAE,CAAC,CAAC,GAAE,KAAG,EAAE,GAAG,IAAG,KAAG,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE,IAAG,IAAE,CAAC;yBAAO,IAAG,SAAO,GAAE;wBAAC,MAAK,EAAE,MAAM,GAAC,GAAG,KAAG,EAAE,KAAK,IAAG,KAAG,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAE,GAAE;wBAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;oBAAG,OAAM,IAAG,SAAO,GAAE,IAAI,EAAE,MAAM,GAAC,KAAG,CAAC,KAAG,EAAE,KAAK,EAAE,GAAE,EAAE,MAAM,GAAC,GAAG,IAAE,GAAE,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,IAAG,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;yBAAQ,IAAG,SAAO,GAAE,IAAI,EAAE,MAAM,GAAC,KAAG,CAAC,KAAG,EAAE,KAAK,EAAE,GAAE,EAAE,MAAM,GAAC,GAAG,IAAE,GAAE,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;yBAAQ,IAAG,SAAO,KAAG,SAAO,GAAE;wBAAC,IAAI,IAAE,SAAO,IAAE,IAAE;wBAAE,IAAG,KAAG,EAAE,MAAM,EAAC,QAAQ,KAAK,CAAC;6BAA2B;4BAAC,IAAI,IAAE,EAAE,GAAG,IAAG,IAAE,EAAE,KAAK,CAAC,IAAE,EAAE,IAAI,CAAC;4BAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAE,EAAE,MAAM,GAAC,GAAE,EAAE,SAAS,GAAC,GAAE,EAAE,KAAK,GAAC,GAAE,EAAE,IAAI,GAAC,GAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,IAAG,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,IAAI;wBAAC;oBAAC,OAAM,IAAG,SAAO,KAAG,SAAO,GAAE;wBAAC,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,CAAC,IAAE,GAAE,SAAO,CAAC;wBAAE,IAAI,KAAG,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC,GAAE,IAAE,GAAG,IAAE,CAAC,IAAE,GAAE,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,IAAG,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,EAAE,IAAE,EAAE,KAAK,IAAG,IAAE,KAAG,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,IAAG,GAAG,IAAE,IAAE,GAAE,IAAE,CAAC,CAAC,IAAE,CAAC,IAAE,GAAE,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,EAAE,KAAK,IAAG,IAAE,KAAG,IAAE,CAAC,IAAE,IAAE,EAAE,KAAK,IAAG,GAAG,IAAE,IAAE,GAAE,IAAE,CAAC,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,KAAG;oBAAE,OAAM;wBAAC,IAAG,OAAK,CAAC,IAAE,EAAE,EAAE,MAAM,CAAC,IAAG,MAAM,QAAQ,KAAK,CAAC,wBAAsB,GAAE,IAAG;wBAAE,EAAE,IAAI,CAAC;oBAAG;gBAAC;YAAC;YAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAE,EAAE,MAAM,GAAC,GAAE,EAAE,SAAS,GAAC,GAAE,EAAE,KAAK,GAAC,GAAE,EAAE,IAAI,GAAC;QAAE;QAAE,IAAI,IAAE,GAAE,IAAE;YAAC,MAAK;QAAC;QAAE,OAAO,EAAE,IAAI,GAAC,GAAE,EAAE,OAAO,GAAC,GAAE,OAAO,cAAc,CAAC,GAAE,cAAa;YAAC,OAAM,CAAC;QAAC,IAAG;IAAC,EAAE,CAAC,GAAG,IAAI;AAAA;AAE/2nC;;;;;;AAMA,GACA,SAAS;IAAkB,OAAO,SAAS,CAAC;QAAE,IAAI,IAAE,YAAW,IAAE,aAAY,IAAE,aAAY,IAAE,IAAI,EAAE;YAAC;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;SAAE,GAAE,IAAE,IAAI,EAAE;YAAC;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAE;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAE;SAAE,GAAE,IAAE,IAAI,EAAE;YAAC;YAAG;YAAG;YAAG;YAAE;YAAE;YAAE;YAAE;YAAE;YAAG;YAAE;YAAG;YAAE;YAAG;YAAE;YAAG;YAAE;YAAG;YAAE;SAAG,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,IAAI,EAAE,KAAI,IAAE,GAAE,IAAE,IAAG,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC,KAAG,KAAG,CAAC,CAAC,IAAE,EAAE;YAAC,IAAI,IAAE,IAAI,EAAE,CAAC,CAAC,GAAG;YAAE,IAAI,IAAE,GAAE,IAAE,IAAG,EAAE,EAAE,IAAI,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC,IAAE,CAAC,CAAC,EAAE,IAAE,IAAE;YAAE,OAAO;gBAAC;gBAAE;aAAE;QAAA,GAAE,IAAE,EAAE,GAAE,IAAG,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE;QAAC,CAAC,CAAC,GAAG,GAAC,KAAI,CAAC,CAAC,IAAI,GAAC;QAAG,IAAI,IAAI,IAAE,EAAE,GAAE,EAAE,CAAC,EAAE,EAAC,IAAE,IAAI,EAAE,QAAO,IAAE,GAAE,IAAE,OAAM,EAAE,EAAE;YAAC,IAAI,IAAE,CAAC,QAAM,CAAC,MAAI,IAAE,CAAC,QAAM,CAAC,KAAG;YAAE,IAAE,CAAC,QAAM,CAAC,IAAE,CAAC,QAAM,CAAC,MAAI,IAAE,CAAC,QAAM,CAAC,KAAG,CAAC,CAAC,MAAI,IAAE,CAAC,OAAK,CAAC,KAAG,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,QAAM,CAAC,MAAI,IAAE,CAAC,MAAI,CAAC,KAAG,CAAC,MAAI;QAAE;QAAC,IAAI,IAAE,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,GAAE,IAAE,IAAI,EAAE,IAAG,IAAE,GAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE;YAAC,IAAI,GAAE,IAAE,IAAI,EAAE;YAAG,IAAI,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,IAAE;YAAE,IAAG,GAAE;gBAAC,IAAE,IAAI,EAAE,KAAG;gBAAG,IAAI,IAAE,KAAG;gBAAE,IAAI,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,IAAG,CAAC,CAAC,EAAE,EAAC,IAAI,IAAI,IAAE,KAAG,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,MAAI,GAAE,IAAE,IAAE,CAAC,KAAG,CAAC,IAAE,GAAE,KAAG,GAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAG,EAAE,GAAC;YAAE,OAAM,IAAI,IAAE,IAAI,EAAE,IAAG,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,GAAG,KAAG,KAAG,CAAC,CAAC,EAAE;YAAE,OAAO;QAAC,GAAE,IAAE,IAAI,EAAE;QAAK,IAAI,IAAE,GAAE,IAAE,KAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC;QAAE,IAAI,IAAE,KAAI,IAAE,KAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC;QAAE,IAAI,IAAE,KAAI,IAAE,KAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC;QAAE,IAAI,IAAE,KAAI,IAAE,KAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC;QAAE,IAAI,IAAE,IAAI,EAAE;QAAI,IAAI,IAAE,GAAE,IAAE,IAAG,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC;QAAE,IAAI,IAAE,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,GAAE,GAAE,IAAG,IAAE,SAAS,CAAC;YAAE,IAAI,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAC,KAAG,CAAC,IAAE,CAAC,CAAC,EAAE;YAAE,OAAO;QAAC,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,IAAE,IAAE;YAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,IAAE,CAAC,KAAG,CAAC,IAAE,CAAC,IAAE;QAAC,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,IAAE,IAAE;YAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,IAAE,IAAE,CAAC,CAAC,IAAE,EAAE,IAAE,EAAE,KAAG,CAAC,IAAE,CAAC;QAAC,GAAE,IAAE;YAAC;YAAiB;YAAqB;YAAyB;YAAmB;YAAkB;;YAAqB;YAAc;YAAqB;YAAuB;YAA8B;YAAoB;YAAmB;SAAmB,EAAC,IAAE,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,IAAI,MAAM,KAAG,CAAC,CAAC,EAAE;YAAE,IAAG,EAAE,IAAI,GAAC,GAAE,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG,CAAC,GAAE,MAAM;YAAE,OAAO;QAAC,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,MAAM;YAAC,IAAG,CAAC,KAAG,KAAG,CAAC,EAAE,CAAC,IAAE,IAAE,GAAE,OAAO,KAAG,IAAI,EAAE;YAAG,IAAI,IAAE,CAAC,KAAG,GAAE,IAAE,CAAC,KAAG,EAAE,CAAC;YAAC,KAAG,CAAC,IAAE,CAAC,CAAC,GAAE,KAAG,CAAC,IAAE,IAAI,EAAE,IAAE,EAAE;YAAE,IAAI,GAAE,IAAE,SAAS,CAAC;gBAAE,IAAI,IAAE,EAAE,MAAM;gBAAC,IAAG,IAAE,GAAE;oBAAC,IAAI,IAAE,IAAI,EAAE,KAAK,GAAG,CAAC,IAAE,GAAE;oBAAI,EAAE,GAAG,CAAC,IAAG,IAAE;gBAAE;YAAC,GAAE,IAAE,EAAE,CAAC,IAAE,GAAE,IAAE,EAAE,CAAC,IAAE,GAAE,IAAE,EAAE,CAAC,IAAE,GAAE,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,IAAE;YAAE,GAAE;gBAAC,IAAG,CAAC,GAAE;oBAAC,EAAE,CAAC,GAAC,IAAE,EAAE,GAAE,GAAE;oBAAG,IAAI,IAAE,EAAE,GAAE,IAAE,GAAE;oBAAG,IAAG,KAAG,GAAE,CAAC,GAAE;wBAAC,IAAI,IAAE,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC,IAAE,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,IAAE,KAAG,CAAC,IAAE,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,IAAE,GAAE,IAAE,IAAE;wBAAE,IAAG,IAAE,GAAE;4BAAC,KAAG,EAAE;4BAAG;wBAAK;wBAAC,KAAG,EAAE,IAAE,IAAG,EAAE,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAE,IAAG,IAAG,EAAE,CAAC,GAAC,KAAG,GAAE,EAAE,CAAC,GAAC,IAAE,IAAE;wBAAE;oBAAQ;oBAAC,IAAG,KAAG,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE;yBAAO,IAAG,KAAG,GAAE;wBAAC,IAAI,IAAE,EAAE,GAAE,GAAE,MAAI,KAAI,IAAE,EAAE,GAAE,IAAE,IAAG,MAAI,GAAE,IAAE,IAAE,EAAE,GAAE,IAAE,GAAE,MAAI;wBAAE,KAAG;wBAAG,IAAI,IAAI,IAAE,IAAI,EAAE,IAAG,IAAE,IAAI,EAAE,KAAI,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAC,EAAE,GAAE,IAAE,IAAE,GAAE;wBAAG,KAAG,IAAE;wBAAE,IAAI,IAAE,EAAE,IAAG,IAAE,CAAC,KAAG,CAAC,IAAE,GAAE,IAAE,EAAE,GAAE,GAAE;wBAAG,IAAI,IAAE,GAAE,IAAE,GAAG;4BAAC,IAAI,GAAE,IAAE,CAAC,CAAC,EAAE,GAAE,GAAE,GAAG;4BAAC,IAAG,KAAG,KAAG,GAAE,CAAC,IAAE,MAAI,CAAC,IAAE,IAAG,CAAC,CAAC,IAAI,GAAC;iCAAO;gCAAC,IAAI,IAAE,GAAE,IAAE;gCAAE,IAAI,MAAI,IAAE,CAAC,IAAE,IAAE,EAAE,GAAE,GAAE,IAAG,KAAG,GAAE,IAAE,CAAC,CAAC,IAAE,EAAE,IAAE,MAAI,IAAE,CAAC,IAAE,IAAE,EAAE,GAAE,GAAE,IAAG,KAAG,CAAC,IAAE,MAAI,KAAG,CAAC,IAAE,KAAG,EAAE,GAAE,GAAE,MAAK,KAAG,CAAC,GAAE,KAAK,CAAC,CAAC,IAAI,GAAC;4BAAE;wBAAC;wBAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,GAAE,IAAG,IAAE,EAAE,QAAQ,CAAC;wBAAG,IAAE,EAAE,IAAG,IAAE,EAAE,IAAG,IAAE,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,GAAE,GAAE;oBAAG,OAAM,EAAE;oBAAG,IAAG,IAAE,GAAE;wBAAC,KAAG,EAAE;wBAAG;oBAAK;gBAAC;gBAAC,KAAG,EAAE,IAAE;gBAAQ,IAAI,IAAI,IAAE,CAAC,KAAG,CAAC,IAAE,GAAE,IAAE,CAAC,KAAG,CAAC,IAAE,GAAE,IAAE,IAAG,IAAE,EAAE;oBAAC,IAAI,IAAE,CAAC,IAAE,CAAC,CAAC,EAAE,GAAE,KAAG,EAAE,MAAI;oBAAE,IAAG,CAAC,KAAG,KAAG,CAAC,IAAE,GAAE;wBAAC,KAAG,EAAE;wBAAG;oBAAK;oBAAC,IAAG,KAAG,EAAE,IAAG,IAAE,KAAI,CAAC,CAAC,IAAI,GAAC;yBAAO;wBAAC,IAAG,OAAK,GAAE;4BAAC,IAAE,GAAE,IAAE;4BAAK;wBAAK;wBAAC,IAAI,IAAE,IAAE;wBAAI,IAAG,IAAE,KAAI;4BAAC,IAAI,KAAG,CAAC,CAAC,IAAE,IAAE,IAAI;4BAAC,IAAE,EAAE,GAAE,GAAE,CAAC,KAAG,EAAE,IAAE,KAAG,CAAC,CAAC,EAAE,EAAC,KAAG;wBAAG;wBAAC,IAAI,KAAG,CAAC,CAAC,EAAE,GAAE,KAAG,EAAE,EAAC,KAAG,OAAK;wBAAE,MAAI,EAAE,IAAG,KAAG,KAAG;wBAAG,IAAE,CAAC,CAAC,GAAG;wBAAC,IAAG,KAAG,GAAE;4BAAC,KAAG,CAAC,CAAC,GAAG;4BAAC,KAAG,EAAE,GAAE,KAAG,CAAC,KAAG,EAAE,IAAE,GAAE,KAAG;wBAAG;wBAAC,IAAG,IAAE,GAAE;4BAAC,KAAG,EAAE;4BAAG;wBAAK;wBAAC,KAAG,EAAE,IAAE;wBAAQ,IAAI,IAAI,KAAG,IAAE,GAAE,IAAE,IAAG,KAAG,EAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,IAAE,EAAE;wBAAC,IAAE;oBAAG;gBAAC;gBAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAE,KAAG,CAAC,IAAE,GAAE,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,CAAC;YAAE,QAAO,CAAC,EAAG;YAAA,OAAO,KAAG,EAAE,MAAM,GAAC,IAAE,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,CAAC,QAAM,KAAG,IAAE,CAAC,KAAG,CAAC,IAAE,CAAC,GAAE,CAAC,QAAM,KAAG,IAAE,EAAE,MAAM,KAAG,CAAC,IAAE,EAAE,MAAM;gBAAE,IAAI,IAAE,IAAG,CAAC,aAAa,IAAE,IAAE,aAAa,IAAE,IAAE,CAAC,EAAE,IAAE;gBAAG,OAAO,EAAE,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAE,KAAI;YAAC,EAAE,GAAE,GAAE;QAAE,GAAE,IAAE,IAAI,EAAE;QAAG,IAAI,IAAE,eAAa,OAAO,eAAa,IAAI;QAAY,IAAG;YAAC,EAAE,MAAM,CAAC,GAAE;gBAAC,QAAO,CAAC;YAAC,IAAG;QAAE,EAAC,OAAM,GAAE,CAAC;QAAC,OAAO,EAAE,eAAe,GAAC,SAAS,CAAC;YAAE,IAAI,IAAE,IAAI,SAAS,IAAG,IAAE;YAAE,SAAS;gBAAI,IAAI,IAAE,EAAE,SAAS,CAAC;gBAAG,OAAO,KAAG,GAAE;YAAC;YAAC,SAAS;gBAAI,IAAI,IAAE,EAAE,SAAS,CAAC;gBAAG,OAAO,KAAG,GAAE;YAAC;YAAC,SAAS,EAAE,CAAC;gBAAE,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG;YAAE;YAAC,SAAS,EAAE,CAAC;gBAAE,EAAE,SAAS,CAAC,GAAE,IAAG,KAAG;YAAE;YAAC,IAAI,IAAI,IAAE;gBAAC,WAAU;gBAAI,QAAO;gBAAI,QAAO;gBAAI,WAAU;gBAAI,UAAS;gBAAI,eAAc;gBAAI,cAAa;gBAAI,cAAa;gBAAI,YAAW;gBAAI,YAAW;gBAAI,gBAAe;gBAAI,YAAW;gBAAI,YAAW;YAAG,GAAE,IAAE,GAAE,KAAK,GAAG,CAAC,GAAE,MAAI,EAAE,SAAS,EAAE;YAAI;YAAI,IAAI,IAAI,IAAE,KAAG,KAAK,GAAG,CAAC,GAAE,IAAG,IAAE,KAAG,EAAE,SAAS,GAAC,GAAE,IAAE,IAAG,IAAE,EAAE,EAAC,IAAE,GAAE,IAAE,EAAE,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC;gBAAC,KAAI;gBAAI,QAAO;gBAAI,YAAW;gBAAI,YAAW;gBAAI,cAAa;YAAG,IAAG,KAAG;YAAG,IAAI,GAAE,IAAE,IAAI,WAAW,KAAG,KAAG,EAAE,MAAM,GAAC,EAAE,MAAM,CAAE,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,IAAE,EAAE,UAAU,GAAC;YAAC,GAAG,KAAI,IAAE,EAAE,MAAM,EAAC,IAAE,IAAI,SAAS,IAAG,IAAE;YAAE,OAAO,EAAE,EAAE,MAAM,GAAE,EAAE,EAAE,SAAS,GAAE,EAAE,IAAG,EAAE,IAAG,EAAE,IAAG,EAAE,OAAO,CAAE,SAAS,CAAC;gBAAE,EAAE,EAAE,GAAG,GAAE,EAAE,EAAE,YAAY,GAAE,EAAE,IAAG,EAAE,EAAE,UAAU,GAAE,EAAE,SAAS,GAAC,GAAE,CAAC,KAAG,EAAE,UAAU,IAAE,KAAG,KAAG,CAAC,KAAG,IAAE,IAAE,CAAC;YAAE,IAAI,EAAE,OAAO,CAAE,SAAS,CAAC;gBAAE,IAAI,GAAE,IAAE,EAAE,KAAK,CAAC,EAAE,MAAM,EAAC,EAAE,MAAM,GAAC,EAAE,UAAU;gBAAE,IAAG,EAAE,UAAU,IAAE,EAAE,UAAU,EAAC;oBAAC,IAAI,IAAE,IAAI,WAAW,EAAE,UAAU;oBAAE,IAAE,IAAI,WAAW,GAAE,IAAG,EAAE,GAAE;gBAAG,OAAM,IAAE,IAAI,WAAW;gBAAG,EAAE,GAAG,CAAC,GAAE,EAAE,SAAS;gBAAE,IAAI,IAAE;gBAAE,CAAC,IAAE,EAAE,SAAS,GAAC,EAAE,UAAU,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,IAAE,CAAC,GAAE,EAAE,GAAG,CAAC,IAAI,WAAW,GAAG,MAAM,EAAC,EAAE,SAAS,GAAC,EAAE,UAAU,GAAE,IAAE,IAAE;YAAE,IAAI,EAAE,KAAK,CAAC,GAAE;QAAE,GAAE,OAAO,cAAc,CAAC,GAAE,cAAa;YAAC,OAAM,CAAC;QAAC,IAAG;IAAC,EAAE,CAAC,GAAG,eAAe;AAAA;AAEp3J;;;CAGC,GAED;;;;;;;;;;CAUC,GAED;;CAEC,GAED;;CAEC,GACD,SAAS,cAAc,IAAI,EAAE,QAAQ;IACnC,MAAM,gBAAgB;QACpB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB;QAAC,KAAI;QAAiB,KAAI;QAA8Q,KAAI;QAA+L,KAAI;QAAkB,KAAI;IAAqxC;IAErzD,MAAM,UAAU,GACd,WAAW,GACX,UAAU,GACV,iBAAiB,GACjB,kBAAkB,IAClB,iBAAiB,IAAI,gGAAgG;IAEvH,IAAI;IACJ,SAAS,mBAAmB,EAAE;QAC5B,IAAI,CAAC,gBAAgB;YACnB,MAAM,IAAI;gBACR,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG;YACL;YACA,iBAAiB,IAAI;YACrB,IAAK,IAAI,QAAQ,mBAAoB;gBACnC,IAAI,WAAW;gBACf,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;oBAC1C,IAAI,CAAC,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC;oBAC/B,OAAO,SAAS,MAAK;oBACrB,OAAO,OAAO,SAAS,MAAM,MAAM;oBACnC,eAAe,GAAG,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK;oBAC5C,IAAK,IAAI,IAAI,MAAM,KAAM;wBACvB,eAAe,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,KAAK;oBACxC;gBACF;YACF;QACF;QACA,OAAO,eAAe,GAAG,CAAC,OAAO;IACnC;IAEA,MAAM,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO;IAC3C,MAAM,kBAAkB;QAAC;QAAM;QAAQ;QAAQ;QAAQ;KAAO;IAE9D,SAAS,mBAAmB,GAAG;QAC7B,gDAAgD;QAChD,wGAAwG;QACxG,MAAM,eAAe,IAAI,WAAW,IAAI,MAAM;QAC9C,IAAI,kBAAkB;QACtB,IAAI,WAAW;QACf,IAAI,YAAY,CAAC;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,MAAM,OAAO,IAAI,WAAW,CAAC;YAC7B,IAAI,cAAc,mBAAmB,QAAQ;YAC7C,IAAI,OAAO;YACX,IAAI,cAAc,gBAAgB;gBAChC;YACF;YACA,IAAI,kBAAkB,CAAC,UAAU,UAAU,eAAe,GAAG;gBAC3D,IAAI,cAAc,CAAC,WAAW,UAAU,eAAe,GAAG;oBACxD,OAAO;oBACP,yBAAyB;oBACzB,IAAI,aAAa,QAAQ,aAAa,MAAM;wBAC1C,YAAY,CAAC,UAAU;oBACzB;gBACF,OACK,IAAI,cAAc,CAAC,UAAU,cAAc,GAAG;oBACjD,yBAAyB;oBACzB,IAAI,aAAa,QAAQ,aAAa,MAAM;wBAC1C,YAAY,CAAC,UAAU;oBACzB;gBACF;YACF,OACK,IAAI,kBAAkB,CAAC,WAAW,cAAc,GAAG;gBACtD,yBAAyB;gBACzB,IAAI,aAAa,QAAQ,aAAa,MAAM;oBAC1C,YAAY,CAAC,UAAU;gBACzB;YACF;YACA,WAAW,YAAY,CAAC,EAAE,GAAG;YAC7B,kBAAkB;YAClB,YAAY;YACZ,IAAI,OAAO,QAAQ;QACrB;QACA,uEAAuE;QACvE,8EAA8E;QAC9E,+EAA+E;QAC/E,OAAO;IACT;IAEA,SAAS,eAAgB,IAAI,EAAE,GAAG;QAChC,MAAM,WAAW,EAAE;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,MAAM,KAAK,IAAI,WAAW,CAAC;YAC3B,IAAI,KAAK,QAAQ;YACjB,SAAS,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM;QACzC;QAEA,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,IAAI,MAAM;YACR,MAAM,EAAC,UAAU,EAAE,WAAW,EAAC,GAAG;YAClC,IAAI;YACJ,MAAM,oBAAoB;YAC1B,MAAM,cAAc,EAAE;YACtB,YAAY,OAAO,CAAC,CAAA;gBAClB,IAAI,kBAAkB,IAAI,CAAC,QAAQ,GAAG,GAAG;oBACvC,IAAK,IAAI,KAAK,GAAG,KAAK,QAAQ,GAAG,CAAC,MAAM,EAAE,KAAM;wBAC9C,IAAI,WAAW,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,EAAE;wBAClC,WAAW,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG;wBAC/B,MAAM,MAAM,UAAU,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC;wBACvC,MAAM,mBAAmB,0BAA0B,IAAI,CAAC,QAAQ,GAAG;wBACnE,IAAI,oBAAoB,CAAC,cAAc;4BACrC,eAAe,mBAAmB;wBACpC;wBACA,IAAK,IAAI,KAAK,GAAG,KAAK,SAAS,MAAM,EAAE,KAAM;4BAC3C,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,eAAe,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,QAAQ,GAAG,EAAE;gCAC3F,KAAK,CAAC,CAAC,UAAU,CAAC,UAAU,IAAI,KAAK;4BACvC;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,2EAA2E;IAC3E,2EAA2E;IAC3E,wDAAwD;IACxD,SAAS,mBAAmB,IAAI,EAAE,QAAQ;QACxC,MAAM,YAAY,IAAI,WAAW,SAAS,MAAM,GAAG,IAAI,oCAAoC;QAC3F,IAAI,aAAa;QACjB,MAAO,aAAa,SAAS,MAAM,EAAE,aAAc;YACjD,MAAM,UAAU,QAAQ,CAAC,WAAW;YACpC,IAAI,YAAY,CAAC,GAAG;YAEpB,SAAS,CAAC,aAAa,IAAI,EAAE,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,kCAAkC;YAE7F,MAAM,OAAO,KAAK,IAAI;YACtB,IAAI,MAAM;gBACR,MAAM,QAAQ,KAAK,UAAU;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACrC,MAAM,SAAS,KAAK,CAAC,EAAE;oBACvB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,IAAK;wBAC3C,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE;wBAC1B,wBAAwB;wBACxB,IAAI,OAAO,KAAK,KAAK,GAAG;4BACtB,MAAM,MAAM,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI,QAAQ,EAAE;4BACnD,IAAI,QAAQ,CAAC,KAAK,IAAI,GAAG,EAAE;gCACzB,iBAAiB,IAAI,GAAG,EAAE;gCAC1B;4BACF;wBACF,OAEK,IAAI,OAAO,KAAK,KAAK,GAAG;4BAC3B,IAAI,MAAM;4BACV,IAAI,iBAAiB;4BACrB,IAAI,mBAAmB,CAAC,GAAG;gCACzB,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI,QAAQ,EAAE,QAAQ,CAAC,eAAe;gCACrF,IAAI,kBAAkB,CAAC,GAAG;oCACxB,IAAI,IAAI,GAAG,KAAK,GAAG;wCACjB,MAAM,QAAQ,IAAI,QAAQ,CAAC,cAAc;wCACzC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;4CACrC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,MAAM,KAAK,CAAC,EAAE;wCAC/C;oCACF,OAAO,IAAI,IAAI,GAAG,KAAK,GAAG;wCACxB,MAAM,KAAK,KAAK,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,SAAS;wCACxE,MAAM,KAAK,KAAK,CAAC,CAAC,cAAc,CAAC,SAAS,IAAI,SAAS;wCACvD,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG;oCAC1B;oCACA,IAAI,KAAK;wCACP,IAAI,IAAI,IAAI,EAAE,iBAAiB,IAAI,IAAI,EAAE;wCACzC,IAAI,IAAI,IAAI,EAAE,iBAAiB,IAAI,IAAI,EAAE;wCACzC;oCACF;gCACF;4BACF;wBACF,OAEK,IAAI,OAAO,KAAK,KAAK,GAAG;4BAC3B,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI,YAAY,EAAE;4BAChE,IAAI,iBAAiB,CAAC,GAAG;gCACvB,MAAM,iBAAiB,kBAAkB;gCACzC,MAAM,eAAe,mBAAmB,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI,YAAY,EAAE,QAAQ,CAAC,eAAe;gCACrH,IAAI,iBAAiB,CAAC,GAAG;oCACvB,MAAM,aAAa,IAAI,SAAS,CAAC,aAAa;oCAC9C,MAAM,aAAa,IAAI,SAAS,CAAC,aAAa,CAAC,WAAW,SAAS,CAAC;oCACpE,SAAS,CAAC,aAAa,EAAE,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,SAAS,CAAC,iBAAiB,EAAE,GAAG,SAAS,CAAC,iBAAiB,IAAI,EAAE;oCAC3H,SAAS,CAAC,aAAa,IAAI,EAAE,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,SAAS,CAAC,iBAAiB,IAAI,EAAE;oCAC/F;gCACF;4BACF;wBACF,OAEK,IAAI,OAAO,KAAK,KAAK,GAAG;4BAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI,aAAa,EAAE;4BAClE,IAAI,kBAAkB,CAAC,GAAG;gCACxB,MAAM,iBAAiB;gCACvB,IAAI,mBAAmB,CAAC,GAAG;oCACzB,MAAM,cAAc,QAAQ,CAAC,eAAe;oCAC5C,IAAI,cAAc,MAAM,iBAAiB,GAAG;wCAC1C,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI,aAAa,EAAE;wCAClE,IAAI,kBAAkB,CAAC,GAAG;4CACxB,MAAM,cAAc,IAAI,UAAU,CAAC,cAAc;4CACjD,MAAM,cAAc,IAAI,UAAU,CAAC,cAAc,CAAC,YAAY,SAAS,CAAC;4CACxE,SAAS,CAAC,aAAa,EAAE,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,SAAS,CAAC,iBAAiB,EAAE,GAAG,SAAS,CAAC,iBAAiB,IAAI,EAAE;4CAC7H,SAAS,CAAC,aAAa,IAAI,EAAE,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,SAAS,CAAC,iBAAiB,IAAI,EAAE;4CACjG;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,OAEK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE;gBAC/B,MAAM,iBAAiB;gBACvB,IAAI,mBAAmB,CAAC,GAAG;oBACzB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe;oBAC9D,IAAI,SAAS,CAAC,GAAG;wBACf,MAAM,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC;wBACjD,IAAI,SAAS,CAAC,GAAG;4BACf,SAAS,CAAC,iBAAiB,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;wBACtE;oBACF;gBACF;YACF;QACF;QAEA,OAAO;;QAEP,SAAS,kBAAkB,MAAM;YAC/B,IAAK,IAAI,IAAI,aAAa,GAAG,KAAI,GAAG,IAAK;gBACvC,IAAI,QAAQ,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG;oBAC1D,OAAO;gBACT;YACF;YACA,OAAO,CAAC;QACV;QAEA,SAAS,YAAY,OAAO;YAC1B,OAAO,cAAc,MAAM,aAAa;QAC1C;QAEA,SAAS,iBAAiB,MAAM,EAAE,EAAE;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,SAAS,CAAC,KAAK,IAAI,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI;YACxC;QACF;IACF;IAEA,SAAS,cAAc,IAAI,EAAE,OAAO;QAClC,MAAM,WAAW,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,aAAa;QACrD,OAAO,WAAW,KAAK,CAAC,CAAC,cAAc,CAAC,SAAS,YAAY;IAC/D;IAEA,SAAS,SAAS,GAAG,IAAI;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC/B,OAAO,IAAI,CAAC,EAAE;YAChB;QACF;IACF;IAEA;;GAEC,GACD,SAAS,YAAY,QAAQ;QAC3B,MAAM,WAAW,OAAO,MAAM,CAAC;QAE/B,MAAM,MAAM,QAAQ,CAAC,OAAO;QAC5B,MAAM,OAAO,SAAS,IAAI;QAC1B,MAAM,aAAa,SAAS,IAAI,CAAC,UAAU;QAC3C,MAAM,WAAW,SAAS,OAAO,IAAI,aAAa,EAAE,QAAQ,KAAK,QAAQ,EAAE;QAE3E,qBAAqB,GACrB,MAAM,UAAU;YACd;YACA;YACA,WAAW,SAAS,OAAO,IAAI,cAAc,EAAE,QAAQ,KAAK,SAAS,EAAE;YACvE,WAAW,SAAS,OAAO,IAAI,UAAU,EAAE;YAC3C,SAAS,SAAS,OAAO,IAAI,QAAQ,EAAE;YACvC,SAAS,SAAS,OAAO,IAAI,YAAY,EAAE,QAAQ,KAAK,OAAO;YAC/D,mBAAkB,IAAI;gBACpB,OAAO,KAAK,CAAC,CAAC,WAAW,CAAC,UAAU,QAAQ;YAC9C;YACA,cAAa,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ;gBAClD,IAAI,OAAO;gBACX,MAAM,YAAY,IAAI,QAAQ,UAAU,GAAG;gBAE3C,MAAM,WAAW,eAAe,UAAU;gBAC1C,IAAI,YAAY;gBAChB,MAAM,YAAY,mBAAmB,UAAU;gBAE/C,SAAS,OAAO,CAAC,CAAC,SAAS;oBACzB,mFAAmF;oBACnF,mFAAmF;oBACnF,0EAA0E;oBAC1E,IAAI,YAAY,CAAC,GAAG;wBAClB,IAAI,WAAW,QAAQ,CAAC,QAAQ;wBAChC,IAAI,CAAC,UAAU;4BACb,MAAM,EAAC,IAAI,EAAE,IAAI,EAAC,GAAG,KAAK,CAAC,CAAC,WAAW,CAAC,UAAU;4BAElD,oBAAoB;4BACpB,IAAI,OAAO;4BACX,IAAI,UAAU;4BACd,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAK;gCAC/C,MAAM,UAAU,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;gCACtC,QAAQ,IAAI,CAAC,EAAE;gCACf,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,IAAK;oCACjC,QAAQ,CAAC,IAAI,IAAI,MAAM,EAAE,IAAI,IAAI,CAAC,UAAU;gCAC9C;4BACF;4BAEA,2EAA2E;4BAC3E,iEAAiE;4BACjE,IAAI,MAAM,MAAM,MAAM;4BACtB,IAAI,KAAK,MAAM,EAAE;gCACf,OAAO,OAAO;gCACd,OAAO,OAAO,CAAC;gCACf,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,KAAK,EAAG;oCAClD,IAAI,IAAI,IAAI,CAAC,EAAE;oCACf,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;oCACnB,IAAI,IAAI,MAAM,OAAO;oCACrB,IAAI,IAAI,MAAM,OAAO;oCACrB,IAAI,IAAI,MAAM,OAAO;oCACrB,IAAI,IAAI,MAAM,OAAO;gCACvB;4BACF,OAAO;gCACL,OAAO,OAAO,OAAO,OAAO;4BAC9B;4BAEA,WAAW,QAAQ,CAAC,QAAQ,GAAG;gCAC7B,OAAO;gCACP,cAAc,SAAS,IAAI,CAAC,MAAM,CAAC,QAAQ;gCAC3C;gCACA;gCACA;gCACA;gCACA;4BACF;wBACF;wBAEA,SAAS,IAAI,CACX,MACA,UACA,OAAO,SAAS,CAAC,IAAI,EAAE,GAAG,WAC1B,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,WACvB;wBAGF,QAAQ,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG;wBAC/B,IAAI,eAAe;4BACjB,QAAQ,gBAAgB;wBAC1B;oBACF;oBACA,aAAc,KAAK,WAAW,CAAC,aAAa,SAAS,IAAI;gBAC3D;gBAEA,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,SAAS,MAAM,MAAM;QAC1B,2DAA2D;QAC3D,MAAM,OAAO,IAAI,WAAW,QAAQ,GAAG;QACvC,MAAM,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;QACzC,IAAI,QAAQ,QAAQ;YAClB,SAAS,SAAS;QACpB,OAAO,IAAI,QAAQ,QAAQ;YACzB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,YAAY,KAAK,KAAK,CAAC,OAAO,CAAC,EAAE;IAC1C;AACF;AAGA,MAAM,eAAe,WAAW,GAAE,CAAA,GAAA,wLAAA,CAAA,qBAAkB,AAAD,EAAE;IACnD,MAAM;IACN,cAAc;QAAC;QAAa;QAAiB;KAAc;IAC3D,MAAK,WAAW,EAAE,eAAe,EAAE,aAAa;QAC9C,MAAM,OAAO;QACb,MAAM,WAAW;QACjB,OAAO,cAAc,MAAM;IAC7B;AACF;AAEA;;;;AAIA,GACA,SAAS;IAAmC,OAAO,SAAS,CAAC;QAAE,IAAI,IAAE;YAAW,IAAI,CAAC,OAAO,GAAC,IAAI;QAAI;QAAE,EAAE,SAAS,CAAC,GAAG,GAAC,SAAS,CAAC;YAAE,IAAI,IAAE,KAAG;YAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAI,CAAC,IAAE,KAAG,CAAC,KAAG,CAAC;QAAG,GAAE,EAAE,SAAS,CAAC,GAAG,GAAC,SAAS,CAAC;YAAE,IAAI,IAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAG;YAAG,OAAO,KAAK,MAAI,KAAG,KAAG,CAAC,IAAE,KAAG,CAAC,KAAG,CAAC,CAAC;QAAC,GAAE,EAAE,SAAS,CAAC,SAAS,GAAC;YAAW,IAAI,IAAE,EAAE;YAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE,SAAS,CAAC,EAAC,CAAC;gBAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAI,MAAI,EAAE,QAAQ,CAAC;YAAK,IAAI,EAAE,IAAI,CAAC;QAAI,GAAE,EAAE,SAAS,CAAC,WAAW,GAAC,SAAS,CAAC;YAAE,IAAI,IAAE,IAAI;YAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAG,EAAE,KAAK,CAAC,KAAK,OAAO,CAAE,SAAS,CAAC;gBAAE,IAAI,IAAE,EAAE,KAAK,CAAC;gBAAK,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,EAAC,KAAI,SAAS,CAAC,CAAC,EAAE,EAAC;YAAK;QAAI;QAAE,IAAI,IAAE,KAAK,GAAG,CAAC,GAAE,IAAG,IAAE,IAAE,GAAE,IAAE,CAAC;QAAE,SAAS,EAAE,CAAC;YAAE,IAAI,IAAE,CAAA,SAAS,CAAC;gBAAE,OAAO,IAAE;YAAC,CAAA,EAAE,GAAG,QAAQ,CAAC,KAAI,IAAE,CAAA,SAAS,CAAC;gBAAE,OAAO,CAAC,IAAE,CAAC,IAAE,IAAE;YAAC,CAAA,EAAE,GAAG,QAAQ,CAAC;YAAI,OAAO,0BAAwB,CAAC,KAAG,EAAE,IAAE,MAAI,IAAE,MAAI,IAAE;QAAO;QAAC,SAAS,EAAE,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,IAAE,GAAE,IAAE,EAAE,WAAW,CAAC,IAAE,IAAE;YAAG,OAAO,KAAG,CAAC,CAAC,IAAE,CAAC,KAAG,EAAE,IAAE,EAAE,IAAE,KAAG,IAAE,CAAC;QAAC;QAAC,SAAS,EAAE,CAAC,EAAC,CAAC;YAAE,IAAI;YAAE,CAAC,IAAE,GAAE,EAAE,OAAO,CAAC,SAAQ,IAAI,OAAO,CAAC,YAAW,IAAI,KAAK,CAAC,MAAM,GAAG,CAAE,SAAS,CAAC;gBAAE,OAAO,EAAE,KAAK,CAAC,KAAK,GAAG,CAAE,SAAS,CAAC;oBAAE,OAAO,SAAS,EAAE,IAAI,IAAG;gBAAG;YAAG,EAAG,EAAE,OAAO,CAAE,SAAS,CAAC;gBAAE,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE;gBAAC,KAAK,MAAI,KAAG,CAAC,IAAE,CAAC,GAAE,EAAE,GAAE;YAAG;QAAI;QAAC,SAAS,EAAE,CAAC,EAAC,CAAC;YAAE,EAAE,GAAG,SAAS,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAI,IAAE,GAAE,KAAG,GAAE,IAAI,EAAE;YAAG;QAAI;QAAC,IAAI,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,IAAI,SAAQ,IAAE;QAAgF,SAAS,EAAE,CAAC;YAAE,IAAI,IAAE,EAAE,GAAG,CAAC;YAAG,OAAO,KAAG,CAAC,IAAE,IAAI,GAAE,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC;gBAAE,OAAO,EAAE,GAAG,CAAC;YAAE,IAAI,EAAE,GAAG,CAAC,GAAE,EAAE,GAAE;QAAC;QAAC,IAAI,GAAE,IAAE,IAAI;QAAI,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,OAAO,CAAC,CAAC,EAAE,GAAC,IAAE,CAAC,CAAC,EAAE,GAAC,IAAE,SAAS,CAAC;gBAAE,IAAI,IAAI,KAAK,EAAE,OAAO;YAAC,EAAE;QAAE;QAAC,SAAS,EAAE,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE;YAAE,IAAG,CAAC,EAAE,QAAQ,CAAC,IAAG;gBAAC,IAAE,IAAE;gBAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,GAAC,KAAG,KAAK,GAAG,CAAC,IAAE,MAAI,CAAC,IAAE,CAAC,CAAC,EAAE;YAAE;YAAC,OAAO;QAAC;QAAC,SAAS,EAAE,CAAC;YAAE,OAAO,KAAG,CAAC,IAAE,IAAI,KAAI,EAAE,mDAAmD,SAAS,CAAC;gBAAE,EAAE,GAAG,CAAC;YAAG,EAAG,GAAE,EAAE,GAAG,CAAC;QAAE;QAAC,OAAO,EAAE,YAAY,GAAC,GAAE,EAAE,UAAU,GAAC;YAAW,IAAE,CAAC,GAAE,IAAE,CAAC;QAAE,GAAE,EAAE,iBAAiB,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,KAAK,MAAI,KAAG,CAAC,IAAE,CAAC,CAAC;YAAE,IAAI,GAAE,IAAE,EAAE,IAAI;YAAC,KAAK,MAAI,KAAG,CAAC,IAAE,qBAAqB,IAAI,CAAC,IAAE,KAAG,OAAK,2CAA2C,IAAI,CAAC,KAAG,OAAK,IAAI;YAAE,IAAI,IAAE,EAAE,QAAQ;YAAC,KAAK,MAAI,KAAG,CAAC,IAAE,YAAY;YAAE,IAAI,IAAE,EAAE,KAAK;YAAC,KAAK,MAAI,KAAG,CAAC,IAAE,QAAQ;YAAE,IAAI,IAAE,EAAE,MAAM;YAAC,KAAK,MAAI,KAAG,CAAC,IAAE,GAAG;YAAE,IAAI,IAAE,CAAC,EAAE,OAAO,IAAE,CAAC,EAAE,OAAO,CAAC,QAAO,KAAI,IAAE,IAAI,KAAI,IAAE,IAAI,WAAW,EAAE,MAAM,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,IAAI,MAAM,EAAE,MAAM,GAAE,IAAE,IAAI,KAAI,IAAE,CAAC;YAAE,SAAS,EAAE,CAAC;gBAAE,IAAI,IAAE,EAAE,GAAG,CAAC;gBAAG,OAAO,KAAG,CAAC,IAAE,MAAM,IAAE,MAAI,GAAG,IAAI,CAAE,SAAS,CAAC;oBAAE,IAAG,CAAC,EAAE,EAAE,EAAC,MAAM,IAAI,MAAM,EAAE,UAAU;oBAAE,OAAO,EAAE,IAAI,GAAG,IAAI,CAAE,SAAS,CAAC;wBAAE,IAAG,CAAC,MAAM,OAAO,CAAC,MAAI,MAAI,CAAC,CAAC,EAAE,EAAC,MAAM,IAAI,MAAM,2CAAyC,CAAC,CAAC,EAAE;wBAAE,OAAO,CAAC,CAAC,EAAE;oBAAA;gBAAG,GAAI,KAAK,CAAE,SAAS,CAAC;oBAAE,IAAG,MAAI,GAAE,OAAO,KAAG,CAAC,QAAQ,KAAK,CAAC,yDAAuD,IAAE,4BAA0B,EAAE,OAAO,GAAE,IAAE,CAAC,CAAC,GAAE,IAAE,GAAE,EAAE,MAAM,CAAC,IAAG,EAAE;oBAAG,MAAM;gBAAC,IAAI,EAAE,GAAG,CAAC,GAAE,EAAE,GAAE;YAAC;YAAC,IAAI,IAAI,IAAE,SAAS,CAAC;gBAAE,IAAI,IAAE,EAAE,WAAW,CAAC,IAAG,IAAE,EAAE;gBAAG,CAAC,CAAC,EAAE,GAAC,GAAE,CAAC,CAAC,EAAE,IAAE,EAAE,GAAG,CAAC,MAAI,EAAE,GAAG,CAAC,GAAE,EAAE,GAAG,IAAI,CAAE,SAAS,CAAC;oBAAE,CAAC,CAAC,EAAE,GAAC;gBAAE,KAAK,IAAE,SAAO,CAAC,KAAI,IAAE,CAAC;YAAE,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,EAAE;YAAG,OAAO,QAAQ,GAAG,CAAC,EAAE,MAAM,IAAI,IAAI,CAAE;gBAAW,EAAE,KAAK;gBAAG,IAAI,IAAI,IAAE,SAAS,CAAC;oBAAE,IAAI,IAAE,EAAE,WAAW,CAAC,IAAG,IAAE,MAAK,IAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAC,IAAE,KAAK;oBAAE,IAAI,IAAI,KAAK,EAAE;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;wBAAC,IAAG,KAAK,MAAI,KAAG,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,IAAI,OAAO,GAAG,IAAI,CAAC,KAAG,KAAK,GAAE,GAAE;4BAAC,IAAI,IAAI,KAAK,IAAE,GAAE,CAAC,CAAC,EAAE,CAAC,IAAG,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAE;gCAAC,IAAE;gCAAE;4BAAK;4BAAC;wBAAK;oBAAC;oBAAC,IAAG,CAAC,GAAE;wBAAA,GAAE,IAAI,IAAI,KAAK,EAAE,IAAG,MAAI,GAAE;4BAAA,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,IAAG,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAE;gCAAC,IAAE;gCAAE,MAAM;4BAAC;wBAAA;oBAAA;oBAAC,KAAG,CAAC,QAAQ,KAAK,CAAC,4BAA0B,EAAE,QAAQ,CAAC,MAAK,IAAE,OAAO,GAAE,CAAC,CAAC,EAAE,GAAC,GAAE,CAAC,CAAC,EAAE,IAAE,EAAE,GAAG,CAAC,MAAI,EAAE,GAAG,CAAC,GAAE,EAAE,eAAa,IAAE,SAAS,IAAI,CAAE,SAAS,CAAC;wBAAE,CAAC,CAAC,EAAE,GAAC;oBAAE,KAAK,IAAE,SAAO,CAAC,KAAI,IAAE,CAAC;gBAAE,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,EAAE;gBAAG,OAAO,QAAQ,GAAG,CAAC,EAAE,MAAM;YAAG,GAAI,IAAI,CAAE;gBAAW,IAAI,IAAI,GAAE,IAAE,MAAK,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;oBAAC,IAAI,IAAE,EAAE,WAAW,CAAC;oBAAG,IAAG,KAAG,CAAC,EAAE,MAAI,EAAE,GAAG,GAAG,CAAC,EAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE;yBAAM;wBAAC,IAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAE,CAAC;wBAAC,IAAG,CAAC,GAAE;4BAAC,IAAI,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,GAAE,GAAE,eAAc,IAAE,EAAE,CAAC,CAAC,EAAE,EAAC,GAAE,WAAU,IAAE,EAAE,SAAO,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,KAAK,MAAI,IAAE,KAAK,IAAE,CAAC,CAAC,EAAE,EAAC;4BAAG,IAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAC,IAAE,iBAAe,EAAE,EAAE,GAAC,MAAI,IAAE,MAAI,IAAE,MAAI,IAAE;wBAAQ;wBAAC,IAAI,IAAE,EAAE,GAAG,CAAC;wBAAG,QAAM,KAAG,CAAC,IAAE,EAAE,IAAI,EAAC,EAAE,GAAG,CAAC,GAAE,EAAE,GAAE,CAAC,CAAC,EAAE,GAAC;oBAAE;oBAAC,IAAE,SAAO,CAAC,KAAI,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE;gBAAE;gBAAC,OAAO;oBAAC,UAAS,MAAM,IAAI,CAAC,EAAE,IAAI;oBAAI,OAAM;gBAAC;YAAC;QAAG,GAAE,OAAO,cAAc,CAAC,GAAE,cAAa;YAAC,OAAM,CAAC;QAAC,IAAG;IAAC,EAAE,CAAC;AAAE;AAE73H;;CAEC,GAED;;;;;;CAMC,GAED;;;;CAIC,GAED;;;;;CAKC,GAED;;;;;CAKC,GACD,SAAS,mBAAmB,UAAU,EAAE,yBAAyB;IAC/D;;GAEC,GACD,MAAM,cAAc,OAAO,MAAM,CAAC;IAElC;;GAEC,GACD,MAAM,eAAe,OAAO,MAAM,CAAC;IAEnC;;GAEC,GACD,SAAS,WAAW,GAAG,EAAE,QAAQ;QAC/B,MAAM,UAAU,CAAA;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,EAAE;QAC/C;QACA,IAAI;YACF,MAAM,UAAU,IAAI;YACpB,QAAQ,IAAI,CAAC,OAAO,KAAK;YACzB,QAAQ,YAAY,GAAG;YACvB,QAAQ,MAAM,GAAG;gBACf,IAAI,QAAQ,MAAM,IAAI,KAAK;oBACzB,QAAQ,IAAI,MAAM,QAAQ,UAAU;gBACtC,OACK,IAAI,QAAQ,MAAM,GAAG,GAAG;oBAC3B,IAAI;wBACF,MAAM,UAAU,WAAW,QAAQ,QAAQ;wBAC3C,QAAQ,GAAG,GAAG;wBACd,SAAS;oBACX,EAAE,OAAO,GAAG;wBACV,QAAQ;oBACV;gBACF;YACF;YACA,QAAQ,OAAO,GAAG;YAClB,QAAQ,IAAI;QACd,EAAE,OAAM,KAAK;YACX,QAAQ;QACV;IACF;IAGA;;;;;GAKC,GACD,SAAS,SAAS,OAAO,EAAE,QAAQ;QACjC,IAAI,OAAO,WAAW,CAAC,QAAQ;QAC/B,IAAI,MAAM;YACR,SAAS;QACX,OAAO,IAAI,YAAY,CAAC,QAAQ,EAAE;YAChC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC7B,OAAO;YACL,YAAY,CAAC,QAAQ,GAAG;gBAAC;aAAS;YAClC,WAAW,SAAS,CAAA;gBAClB,QAAQ,GAAG,GAAG;gBACd,WAAW,CAAC,QAAQ,GAAG;gBACvB,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA,KAAM,GAAG;gBACvC,OAAO,YAAY,CAAC,QAAQ;YAC9B;QACF;IACF;IAEA;;;GAGC,GACD,OAAO,SAAU,IAAI,EAAE,QAAQ,EAAE,EAC/B,IAAI,EACJ,OAAO,YAAY,EAAE,EACrB,QAAQ,QAAQ,EAChB,SAAS,QAAQ,EACjB,eAAe,EAChB,GAAG,CAAC,CAAC;QACJ,MAAM,kBAAkB,IAAI,WAAW,KAAK,MAAM;QAClD,MAAM,kBAAkB,EAAE;QAC1B,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB;QACF;QAEA,MAAM,cAAc,IAAI;QACxB,MAAM,iBAAiB,EAAE,EAAE,sBAAsB;QAEjD,IAAI,UAAU,UAAU,QAAQ;QAChC,IAAI,OAAO,WAAW,UAAU;YAC9B,SAAS,WAAW,SAAS,MAAM;QACrC;QAEA,IAAI,aAAa,CAAC,MAAM,OAAO,CAAC,YAAY;YAC1C,YAAY;gBAAC;aAAU;QACzB;QACA,YAAY,UAAU,KAAK,EACzB,qBAAqB;SACpB,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAC1C,oCAAoC;SACnC,OAAO;QACV,IAAI,UAAU,MAAM,EAAE;YACpB,MAAM,UAAU;YAChB,MAAM,WAAW;YACjB,MAAM,iBAAiB;YACvB,IAAI,iBAAiB;YAEpB,CAAC,SAAS,iBAAkB,aAAa,CAAC;gBACzC,IAAK,IAAI,IAAI,YAAY,OAAO,KAAK,MAAM,EAAE,IAAI,MAAM,IAAK;oBAC1D,MAAM,YAAY,KAAK,WAAW,CAAC;oBACnC,gDAAgD;oBAChD,0DAA0D;oBAC1D,iCAAiC;oBACjC,IACE,AAAC,mBAAmB,YAAY,eAAe,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,iBAAiB,CAAC,cACzF,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,GAC3B;wBACA,eAAe,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,EAAE;wBAC3C,IAAI,mBAAmB,gBAAgB;4BACrC,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,EAAE,GAAG;wBACjD;oBACF,OAAO;wBACL,IAAK,IAAI,IAAI,eAAe,CAAC,EAAE,EAAE,OAAO,UAAU,MAAM,EAAE,KAAK,MAAM,IAAK;4BACxE,IAAI,MAAM,MAAM;gCACd,iDAAiD;gCACjD,MAAM,QAAQ,mBAAmB,iBAC/B,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,GACxC,cAAc,CAAC,eAAe,MAAM,CAAC,GAAG;oCAAC;oCAAG;iCAAE;gCACjD,KAAK,CAAC,EAAE,GAAG;gCACX,iBAAiB;4BACnB,OAAO;gCACL,eAAe,CAAC,EAAE,GAAG;gCACrB,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,SAAS,CAAC,EAAE;gCAC1C,6CAA6C;gCAC7C,IAAI,CAAC,gBAAgB,eAAe,WAAW,eAAe;oCAC5D,MAAM,UAAU,WAAW,CAAC,IAAI;oCAChC,0CAA0C;oCAC1C,IAAI,CAAC,SAAS;wCACZ,SAAS,KAAK;4CACZ,iBAAiB;wCACnB;wCACA;oCACF;oCACA,kEAAkE;oCAClE,IAAI,QAAQ,iBAAiB,CAAC,YAAY;wCACxC,IAAI,YAAY,YAAY,GAAG,CAAC;wCAChC,IAAI,OAAO,cAAc,UAAU;4CACjC,YAAY,gBAAgB,MAAM;4CAClC,gBAAgB,IAAI,CAAC;4CACrB,YAAY,GAAG,CAAC,SAAS;wCAC3B;wCACA,eAAe,CAAC,EAAE,GAAG;wCACrB,iBAAiB;wCACjB;oCACF;gCACF;4BACF;wBACF;oBACF;oBAEA,IAAI,YAAY,UAAU,IAAI,IAAI,MAAM;wBACtC,eAAe,CAAC,IAAI,EAAE,GAAG,eAAe,CAAC,EAAE;wBAC3C;wBACA,IAAI,mBAAmB,gBAAgB;4BACrC,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,EAAE,GAAG;wBACjD;oBACF;gBACF;gBACA;YACF,CAAC;QACH,OAAO;YACL,eAAe,IAAI,CAAC;gBAAC;gBAAG,KAAK,MAAM,GAAG;aAAE;YACxC;QACF;QAEA,SAAS;YACP,IAAI,eAAe,MAAM,EAAE;gBACzB,oEAAoE;gBACpE,MAAM,iBAAiB,eAAe,GAAG,CAAC,CAAA,QAAS,KAAK,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC;gBAChG,0BAA0B,iBAAiB,CAAC,gBAAgB;oBAC1D,MAAM,QAAQ;oBACd;oBACA;oBACA,SAAS;gBACX,GAAG,IAAI,CAAC,CAAC,EAAC,QAAQ,EAAE,KAAK,EAAC;oBACxB,sDAAsD;oBACtD,MAAM,kBAAkB,gBAAgB,MAAM;oBAC9C,IAAI,UAAU;oBACd,eAAe,OAAO,CAAC,CAAA;wBACrB,IAAK,IAAI,IAAI,GAAG,SAAS,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE,KAAK,QAAQ,IAAK;4BAC9D,eAAe,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,UAAU,GAAG;wBACrD;wBACA,WAAW,wBAAwB;oBACrC;oBAEA,+FAA+F;oBAC/F,IAAI,cAAc;oBAClB,SAAS,OAAO,CAAC,CAAC,KAAK;wBACrB,SAAS,KAAK,CAAA;4BACZ,eAAe,CAAC,IAAI,gBAAgB,GAAG;4BACvC,IAAI,EAAE,gBAAgB,SAAS,MAAM,EAAE;gCACrC;4BACF;wBACF;oBACF;gBACF;YACF,OAAO;gBACL;YACF;QACF;QAEA,SAAS;YACP,SAAS;gBACP,OAAO;gBACP,OAAO;YACT;QACF;QAEA,SAAS,eAAe,IAAI,EAAE,MAAM;YAClC,kEAAkE;YAClE,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,MAAM,CAAC,OAAO,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,EAAE;gBACtC,IAAI,SAAS,QAAQ,QAAQ,KAAK;oBAChC,OAAO;gBACT;YACF;YACA,OAAO;QACT;IACF;AACF;AAEA,MAAM,2BAA2B,WAAW,GAAE,CAAA,GAAA,wLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/D,MAAM;IACN,cAAc;QACZ;QACA;QACA;KACD;IACD,MAAK,kBAAkB,EAAE,UAAU,EAAE,gCAAgC;QACnE,OAAO,mBAAmB,YAAY;IACxC;AACF;AAEA;;CAEC,GACD;;CAEC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GAED;;;;;;;;;;;;;;;;;;;;CAoBC,GAED;;;;;;;;;CASC,GAED;;;;;CAKC,GAED;;;;;CAKC,GAGD;;;;;;;;;;CAUC,GACD,SAAS,iBAAiB,YAAY,EAAE,IAAI;IAC1C,MAAM,MAAM;IAEZ,gGAAgG;IAChG,yDAAyD;IACzD,MAAM,0BAA0B;IAEhC,uGAAuG;IACvG,MAAM,yBAAyB,CAAC,aAAa,CAAC;IAE9C,mEAAmE;IACnE,kHAAkH;IAClH,MAAM,oBAAoB,IAAI,OAAO,GAAG,uBAAuB,sEAAsE,CAAC;IAEtI;;;GAGC,GACD,SAAS,kBAAkB,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAC,EAAE,MAAM;QACtG,MAAM,aAAa,CAAC,EAAC,KAAK,EAAE,OAAO,WAAW,EAAC;YAC7C,IAAI,QAAQ;YACZ,MAAM,OAAO,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS;oBACxB,UAAU,KAAK,CAAC,EAAE;oBAClB,KAAK,IAAI,CAAC,SAAS;wBAAE,OAAO;wBAAG,KAAK;wBAAG,SAAS,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;oBAAA;gBACvE,OAAO;oBACL,OAAO,GAAG,GAAG;gBACf;YACF;YACA,OAAO;QACT;QACA,IAAI,kBAAkB;YACpB,WAAW;QACb,OAAO;YACL,aACE,MACA,YACA;gBAAE;gBAAM;gBAAO;gBAAO;gBAAQ;YAAgB;QAElD;IACF;IAEA;;;;;GAKC,GACD,SAAS,QACP,EACE,OAAK,EAAE,EACP,IAAI,EACJ,IAAI,EACJ,eAAa,EAAE,EACf,WAAS,GAAG,EACZ,aAAW,CAAC,EACZ,YAAU,QAAQ,EAClB,gBAAc,CAAC,EACf,aAAW,QAAQ,EACnB,WAAS,GAAG,EACZ,SAAS,EACT,YAAU,MAAM,EAChB,aAAW,CAAC,EACZ,aAAW,QAAQ,EACnB,eAAa,QAAQ,EACrB,UAAU,CAAC,EACX,UAAU,CAAC,EACX,cAAY,KAAK,EACjB,eAAe,EACf,mBAAiB,IAAI,EACrB,wBAAsB,KAAK,EAC3B,oBAAkB,IAAI,EACtB,cAAY,IAAI,EACjB,EACD,QAAQ;QAER,MAAM,YAAY;QAClB,MAAM,UAAU;YAAC,UAAU;YAAG,aAAa;QAAC;QAE5C,iCAAiC;QACjC,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,GAAG;YAC3B,QAAQ,IAAI,CAAC;YACb,OAAO,KAAK,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,OAAO;QACpD;QAEA,uCAAuC;QACvC,WAAW,CAAC;QACZ,gBAAgB,CAAC;QACjB,WAAW,CAAC;QACZ,aAAa,cAAc;QAC3B,aAAa,CAAC;QAEd,kBAAkB;YAChB;YACA;YACA,OAAO;YACP,QAAQ;YACR,OAAO,OAAO,SAAS,WAAW;gBAAC;oBAAC,KAAK;gBAAI;aAAE,GAAG;YAClD;YACA;QACF,GAAG,CAAA;YACD,QAAQ,QAAQ,GAAG,QAAQ;YAC3B,MAAM,cAAc,SAAS;YAC7B,IAAI,WAAW;YACf,IAAI,mBAAmB;YACvB,IAAI,iBAAiB;YACrB,IAAI,YAAY;YAChB,IAAI,cAAc;YAClB,IAAI,iBAAiB;YACrB,IAAI,gBAAgB;YACpB,IAAI,gBAAgB;YACpB,IAAI,eAAe;YACnB,IAAI,uBAAuB;YAC3B,IAAI,UAAU,eAAe;YAC7B,MAAM,gBAAgB,IAAI,OAAO,qBAAqB;YACtD,MAAM,eAAe;YAErB,iDAAiD;YACjD,IAAI,cAAc;YAClB,IAAI,cAAc;YAClB,IAAI,cAAc,IAAI;YACtB,MAAM,QAAQ;gBAAC;aAAY;YAC3B,KAAK,OAAO,CAAC,CAAA;gBACX,MAAM,EAAE,OAAO,EAAE,GAAG;gBACpB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;gBAEzE,uCAAuC;gBACvC,IAAI,WAAW,cAAc,GAAG,CAAC;gBACjC,IAAI,CAAC,UAAU;oBACb,+DAA+D;oBAC/D,MAAM,eAAe,WAAW;oBAEhC,0FAA0F;oBAC1F,uHAAuH;oBACvH,MAAM,iBAAiB,eAAe,WACpC,CAAC,WAAW,YAAY,OAAO,IAAI,eAAe,aAAa;oBAEjE,gDAAgD;oBAChD,MAAM,cAAc,CAAC,iBAAiB,CAAC,WAAW,SAAS,IAAI,YAAY,IAAI;oBAC/E,MAAM,cAAc,KAAK,GAAG,CAAC,gBAAgB,CAAC,WAAW,SAAS,IAAI;oBACtE,MAAM,WAAW,CAAC,WAAW,SAAS,IAAI,IAAI,eAAe,cAAc;oBAC3E,WAAW;wBACT,OAAO,cAAc,IAAI;wBACzB,KAAK,QAAQ,GAAG;wBAChB;wBACA;wBACA;wBACA,UAAU,WAAW;wBACrB,WAAW,YAAY;wBACvB,WAAW,YAAY;wBACvB,SAAS,UAAU;wBACnB,YAAY;wBACZ,UAAU,CAAC,cAAc,WAAW;wBACpC,+EAA+E;wBAC/E,2EAA2E;wBAC3E;wBACA,aAAa,WAAW;oBAC1B;oBACA,cAAc,GAAG,CAAC,SAAS;gBAC7B;gBACA,MAAM,EAAE,YAAY,EAAE,GAAG;gBAEzB,MAAM,UAAU,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI,GAAG,GAAG;gBAChD,IAAI,YAAY;gBAChB,QAAQ,YAAY,CAAC,SAAS,UAAU,eAAe,CAAC,UAAU,QAAQ,QAAQ;oBAChF,UAAU;oBACV,aAAa,IAAI,KAAK;oBACtB,aAAa;oBACb,eAAe;oBACf,MAAM,OAAO,KAAK,MAAM,CAAC;oBACzB,MAAM,aAAa,SAAS,YAAY,GAAG;oBAC3C,MAAM,eAAe,YAAY,KAAK;oBACtC,IAAI;oBAEJ,kDAAkD;oBAClD,IAAI,CAAC,CAAC,aAAa,QAAQ,GAAG;wBAC5B,SAAS,YAAY,GAAG,CAAC,CAAC,QAAQ,IAAI,OAAO,wBAAwB,IAAI,CAAC;wBAC1E,SAAS,aAAa,GAAG,CAAC,CAAC,QAAQ,kBAAkB,IAAI,CAAC;wBAC1D,SAAS,OAAO,GAAG,SAAS,IAAI,KAAK,SAAS,IAAI,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI,IAAI,wBAAwB,IAAI,CAAC;oBACxH;oBACA,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,OAAO,EAAE;wBAC/C;oBACF;oBAEA,8EAA8E;oBAC9E,IAAI,WAAW,eAAe,CAAC,SAAS,YAAY,IAAI,SAAS,aAAa,cAAc,YAAY,cAAc;wBACpH,8DAA8D;wBAC9D,IAAI,YAAY,OAAO,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,EAAE;4BAChE,WAAW,IAAI;4BACf,cAAc,CAAC;wBACjB,OAAO;4BACL,wDAAwD;4BACxD,IAAK,IAAI,IAAI,cAAc,KAAM;gCAC/B,4GAA4G;gCAC5G,IAAI,MAAM,KAAK,iBAAiB,cAAc;oCAC5C,WAAW,IAAI;oCACf,cAAc,CAAC;oCACf;gCACF,OAEK,IAAI,YAAY,OAAO,CAAC,GAAG,QAAQ,CAAC,aAAa,EAAE;oCACtD,WAAW,YAAY,OAAO,CAAC,IAAI;oCACnC,MAAM,UAAU,SAAS,OAAO,CAAC,GAAG,CAAC;oCACrC,eAAe;oCACf,IAAK,IAAI,IAAI,SAAS,KAAK,EAAE,KAAM;wCACjC,SAAS,OAAO,CAAC,GAAG,CAAC,IAAI;oCAC3B;oCACA;gCACF;4BACF;wBACF;wBACA,IAAI,UAAU;4BACZ,YAAY,aAAa,GAAG;4BAC5B,cAAc;4BACd,MAAM,IAAI,CAAC;4BACX,eAAe,UAAU,sDAAsD;wBACjF;oBACF;oBAEA,IAAI,MAAM,YAAY,OAAO,CAAC,YAAY,KAAK;oBAC/C,IAAI,QAAQ,GAAG;oBACf,IAAI,CAAC,GAAG,SAAS;oBACjB,IAAI,CAAC,GAAG;oBACR,IAAI,KAAK,GAAG;oBACZ,IAAI,SAAS,GAAG;oBAChB,IAAI,QAAQ,GAAG;oBAEf,0BAA0B;oBAC1B,IAAI,SAAS,MAAM;wBACjB,cAAc,IAAI;wBAClB,MAAM,IAAI,CAAC;wBACX,cAAc,CAAC,CAAC,SAAS,aAAc,gBAAgB,QAAS,IAAI;oBACtE;gBACF;gBACA,4FAA4F;gBAC5F,cAAc,aAAa,aAAa,YAAY,GAAG,eAAe,gBAAgB;YACxF;YAEA,uGAAuG;YACvG,IAAI,cAAc;YAClB,MAAM,OAAO,CAAC,CAAA;gBACZ,IAAI,uBAAuB;gBAC3B,IAAK,IAAI,IAAI,KAAK,KAAK,EAAE,KAAM;oBAC7B,MAAM,YAAY,KAAK,OAAO,CAAC;oBAC/B,kDAAkD;oBAClD,IAAI,wBAAwB,CAAC,UAAU,QAAQ,CAAC,YAAY,EAAE;wBAC5D,KAAK,KAAK,GAAG,UAAU,CAAC,GAAG,UAAU,KAAK;wBAC1C,IAAI,KAAK,KAAK,GAAG,cAAc;4BAC7B,eAAe,KAAK,KAAK;wBAC3B;wBACA,uBAAuB;oBACzB;oBACA,mEAAmE;oBACnE,IAAI,EAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAC,GAAG,UAAU,QAAQ;oBACnE,IAAI,aAAa,KAAK,UAAU,EAAE,KAAK,UAAU,GAAG;oBACpD,MAAM,eAAe,WAAW,KAAK,QAAQ;oBAC7C,IAAI,eAAe,GAAG;wBACpB,KAAK,QAAQ,IAAI;wBACjB,KAAK,GAAG,IAAI;wBACZ,KAAK,EAAE,IAAI;oBACb;oBACA,8CAA8C;oBAC9C,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,EAAE,KAAK,QAAQ,GAAG;oBAC9C,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;gBAC9C;gBACA,KAAK,QAAQ,IAAI;gBACjB,KAAK,GAAG,IAAI;gBACZ,KAAK,EAAE,IAAI;gBACX,eAAe,KAAK,UAAU;YAChC;YAEA,kDAAkD;YAClD,IAAI,gBAAgB;YACpB,IAAI,gBAAgB;YACpB,IAAI,SAAS;gBACX,IAAI,OAAO,YAAY,UAAU;oBAC/B,gBAAgB,CAAC;gBACnB,OACK,IAAI,OAAO,YAAY,UAAU;oBACpC,gBAAgB,CAAC,eAAe,CAC9B,YAAY,SAAS,IACrB,YAAY,WAAW,MACvB,YAAY,UAAU,IACtB,aAAa,QACf;gBACF;YACF;YACA,IAAI,SAAS;gBACX,IAAI,OAAO,YAAY,UAAU;oBAC/B,gBAAgB,CAAC;gBACnB,OACK,IAAI,OAAO,YAAY,UAAU;oBACpC,gBAAgB,YAAY,QAAQ,IAClC,YAAY,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,GAC/C,YAAY,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,GACrC,YAAY,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,GACnC,YAAY,WAAW,cAAc,IACrC,YAAY,WAAW,cACvB,YAAY,oBAAoB,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,GACjE,aAAa,WAAW;gBAC5B;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,sBAAsB;gBACtB,MAAM,mBAAmB,KAAK,kBAAkB,CAAC,MAAM;gBAEvD,qFAAqF;gBACrF,6DAA6D;gBAC7D,WAAW,IAAI,YAAY;gBAC3B,mBAAmB,IAAI,WAAW;gBAClC,iBAAiB,IAAI,aAAa,uBAAuB;gBACzD,YAAY,CAAC;gBACb,gBAAgB;oBAAC;oBAAK;oBAAK,CAAC;oBAAK,CAAC;iBAAI;gBACtC,gBAAgB,EAAE;gBAClB,IAAI,uBAAuB;oBACzB,iBAAiB,IAAI,aAAa,KAAK,MAAM,GAAG;gBAClD;gBACA,IAAI,aAAa;oBACf,cAAc,IAAI,WAAW,uBAAuB;gBACtD;gBACA,IAAI,uBAAuB;gBAC3B,IAAI,gBAAgB,CAAC;gBACrB,IAAI,iBAAiB,CAAC;gBACtB,IAAI;gBACJ,IAAI;gBACJ,MAAM,OAAO,CAAC,CAAC,MAAM;oBACnB,IAAI,EAAC,OAAM,cAAc,EAAE,OAAM,SAAS,EAAC,GAAG;oBAE9C,qBAAqB;oBACrB,IAAI,iBAAiB,GAAG;wBACtB,yEAAyE;wBACzE,IAAI,0BAA0B;wBAC9B,IAAK,IAAI,IAAI,gBAAgB,OAAO,KAAK,OAAO,CAAC,GAAG,QAAQ,CAAC,YAAY,EAAG;4BAC1E;wBACF;wBAEA,yCAAyC;wBACzC,IAAI,cAAc;wBAClB,IAAI,gBAAgB;wBACpB,IAAI,cAAc,UAAU;4BAC1B,cAAc,CAAC,eAAe,SAAS,IAAI;wBAC7C,OAAO,IAAI,cAAc,SAAS;4BAChC,cAAc,eAAe;wBAC/B,OAAO,IAAI,cAAc,aAAa,KAAK,aAAa,EAAE;4BACxD,wGAAwG;4BACxG,IAAI,kBAAkB;4BACtB,IAAK,IAAI,IAAI,iBAAiB,yBAAyB,KAAM;gCAC3D,IAAI,KAAK,OAAO,CAAC,GAAG,QAAQ,CAAC,YAAY,EAAE;oCACzC;gCACF;4BACF;4BACA,gBAAgB,CAAC,eAAe,SAAS,IAAI;wBAC/C;wBACA,IAAI,iBAAiB,aAAa;4BAChC,IAAI,gBAAgB;4BACpB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;gCACvC,IAAI,YAAY,KAAK,OAAO,CAAC;gCAC7B,MAAM,WAAW,UAAU,QAAQ;gCACnC,UAAU,CAAC,IAAI,cAAc;gCAC7B,wDAAwD;gCACxD,IAAI,kBAAkB,KAAK,SAAS,YAAY,IAAI,IAAI,iBAAiB,yBAAyB;oCAChG,iBAAiB;oCACjB,UAAU,KAAK,IAAI;gCACrB;4BACF;wBACF;wBAEA,8BAA8B;wBAC9B,MAAM,QAAQ,KAAK,kBAAkB,CACnC,MAAM,kBAAkB,KAAK,OAAO,CAAC,GAAG,SAAS,EAAE,KAAK,OAAO,CAAC,KAAK,KAAK,GAAG,GAAG,SAAS;wBAE3F,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,MAAM,EAAE,KAAM;4BACxC,MAAM,CAAC,OAAO,IAAI,GAAG,KAAK,CAAC,GAAG;4BAC9B,sDAAsD;4BACtD,IAAI,OAAO,UAAU,QAAQ,CAAC;4BAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;gCACvC,IAAI,KAAK,OAAO,CAAC,GAAG,SAAS,IAAI,OAAO;oCACtC,IAAI,cAAc,GAAG,YAAY;oCACjC,MAAO,YAAY,gBAAgB,YAAa;wCAC9C,IAAI,OAAO,KAAK,OAAO,CAAC;wCACxB,IAAI,KAAK,SAAS,GAAG,KAAK;4CACxB;wCACF;wCACA,IAAI,YAAY,iBAAiB,yBAAyB;4CACxD,OAAO,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC;4CAC5B,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK;wCAC7C;oCACF;oCACA,IAAK,IAAI,IAAI,aAAa,IAAI,WAAW,IAAK;wCAC5C,MAAM,YAAY,KAAK,OAAO,CAAC;wCAC/B,UAAU,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI;oCAC7D;oCACA;gCACF;4BACF;wBACF;wBAEA,6BAA6B;wBAC7B,IAAI;wBACJ,MAAM,cAAc,CAAA,IAAK,WAAW;wBACpC,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;4BACvC,MAAM,YAAY,KAAK,OAAO,CAAC;4BAC/B,WAAW,UAAU,QAAQ;4BAC7B,MAAM,UAAU,SAAS,KAAK;4BAE9B,qCAAqC;4BACrC,MAAM,MAAM,iBAAiB,MAAM,CAAC,UAAU,SAAS,CAAC,GAAG,GAAG,qBAAqB;4BACnF,IAAI,KAAK;gCACP,MAAM,WAAW,KAAK,oBAAoB,CAAC,IAAI,CAAC,UAAU,SAAS,CAAC;gCACpE,IAAI,UAAU;oCACZ,UAAU,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,GAAG,GAAG;gCAC1D;4BACF;4BAEA,sBAAsB;4BACtB,IAAI,uBAAuB;gCACzB,MAAM,EAAC,SAAS,EAAE,QAAQ,EAAC,GAAG;gCAC9B,MAAM,YAAY,UAAU,CAAC,GAAG;gCAChC,MAAM,aAAa,UAAU,CAAC,GAAG,UAAU,KAAK,GAAG;gCACnD,cAAc,CAAC,YAAY,EAAE,GAAG,MAAM,aAAa,WAAW,cAAc;gCAC5E,cAAc,CAAC,YAAY,IAAI,EAAE,GAAG,MAAM,YAAY,YAAY,YAAY;gCAC9E,cAAc,CAAC,YAAY,IAAI,EAAE,GAAG,KAAK,QAAQ,GAAG,SAAS,WAAW,GAAG,eAAe,iBAAiB;gCAC3G,cAAc,CAAC,YAAY,IAAI,EAAE,GAAG,KAAK,QAAQ,GAAG,SAAS,QAAQ,GAAG,eAAe,cAAc;gCAErG,wFAAwF;gCACxF,yFAAyF;gCACzF,4FAA4F;gCAC5F,gDAAgD;gCAChD,MAAM,WAAW,YAAY;gCAC7B,IAAI,WAAW,GAAG;oCAChB,2BAA2B,gBAAgB,eAAe;gCAC5D;gCACA,gBAAgB;4BAClB;4BAEA,4BAA4B;4BAC5B,IAAI,aAAa;gCACf,MAAM,EAAC,SAAS,EAAC,GAAG;gCACpB,MAAM,YAAY,eAAgB;oCAChC;oCACA,IAAI,YAAY,cAAc,CAAC,iBAAiB;wCAC9C,eAAe,WAAW,CAAC,eAAe;oCAC5C;gCACF;4BACF;4BAEA,uCAAuC;4BACvC,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,OAAO,EAAE;gCAC/C,MAAM,MAAM;gCACZ,MAAM,EAAC,YAAY,EAAE,KAAK,OAAO,EAAE,OAAO,SAAS,EAAC,GAAG,UAAU,QAAQ;gCAEzE,6BAA6B;gCAC7B,MAAM,gBAAgB,SAAS,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;gCACpE,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;oCAC3B,aAAa,CAAC,QAAQ,GAAG;wCACvB,MAAM,SAAS,IAAI;wCACnB,YAAY;4CAAC,SAAS,IAAI;4CAAE,SAAS,IAAI;4CAAE,SAAS,IAAI;4CAAE,SAAS,IAAI;yCAAC;oCAC1E;gCACF;gCAEA,iEAAiE;gCACjE,MAAM,SAAS,UAAU,CAAC,GAAG;gCAC7B,MAAM,SAAS,UAAU,CAAC,GAAG,KAAK,QAAQ,GAAG;gCAC7C,cAAc,CAAC,MAAM,EAAE,GAAG;gCAC1B,cAAc,CAAC,MAAM,IAAI,EAAE,GAAG;gCAE9B,6BAA6B;gCAC7B,MAAM,QAAQ,SAAS,SAAS,IAAI,GAAG;gCACvC,MAAM,QAAQ,SAAS,SAAS,IAAI,GAAG;gCACvC,MAAM,QAAQ,SAAS,SAAS,IAAI,GAAG;gCACvC,MAAM,QAAQ,SAAS,SAAS,IAAI,GAAG;gCACvC,IAAI,QAAQ,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,GAAG;gCACjD,IAAI,QAAQ,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,GAAG;gCACjD,IAAI,QAAQ,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,GAAG;gCACjD,IAAI,QAAQ,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,GAAG;gCAEjD,kDAAkD;gCAClD,IAAI,MAAM,sBAAsB,GAAG;oCACjC,QAAQ;wCAAC,OAAO;wCAAK,KAAK;wCAAK,MAAM;4CAAC;4CAAK;4CAAK,CAAC;4CAAK,CAAC;yCAAI;oCAAA;oCAC3D,cAAc,IAAI,CAAC;gCACrB;gCACA,MAAM,GAAG;gCACT,MAAM,YAAY,MAAM,IAAI;gCAC5B,IAAI,QAAQ,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,GAAG;gCACzC,IAAI,QAAQ,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,GAAG;gCACzC,IAAI,QAAQ,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,GAAG;gCACzC,IAAI,QAAQ,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,GAAG;gCAEzC,2CAA2C;gCAC3C,QAAQ,CAAC,IAAI,GAAG;gCAChB,gBAAgB,CAAC,IAAI,GAAG;gCAExB,aAAa;gCACb,IAAI,aAAa;oCACf,MAAM,QAAQ,MAAM;oCACpB,WAAW,CAAC,MAAM,GAAG,gBAAgB,KAAK;oCAC1C,WAAW,CAAC,QAAQ,EAAE,GAAG,gBAAgB,IAAI;oCAC7C,WAAW,CAAC,QAAQ,EAAE,GAAG,eAAe;gCAC1C;4BACF;wBACF;oBACF;gBACF;gBAEA,+EAA+E;gBAC/E,IAAI,gBAAgB;oBAClB,MAAM,WAAW,KAAK,MAAM,GAAG;oBAC/B,IAAI,WAAW,GAAG;wBAChB,2BAA2B,gBAAgB,eAAe;oBAC5D;gBACF;YACF;YAEA,2CAA2C;YAC3C,MAAM,WAAW,EAAE;YACnB,cAAc,OAAO,CAAC,CAAC,EAAC,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAC;gBAClG,QAAQ,CAAC,MAAM,GAAG;oBAAC;oBAAK;oBAAY;oBAAU;oBAAW;oBAAY;oBAAW;gBAAO;YACzF;YAEA,eAAe;YACf,QAAQ,WAAW,GAAG,QAAQ;YAE9B,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,uEAAuE;gBACvE;gBACA;gBACA;gBACA,aAAa,gBAAgB,KAAK,CAAC,EAAE,CAAC,QAAQ;gBAC9C,aAAa;oBACX;oBACA,gBAAgB;oBAChB,gBAAgB;oBAChB;iBACD;gBACD;gBACA;YACF;QACF;IACF;IAGA;;;;;GAKC,GACD,SAAS,QAAQ,IAAI,EAAE,QAAQ;QAC7B,QAAQ;YAAC,GAAG,IAAI;YAAE,aAAa;QAAI,GAAG,CAAC;YACrC,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,OAAO,WAAW;YAC3C,SAAS;gBACP,OAAO,KAAK;gBACZ,QAAQ,KAAK;YACf;QACF;IACF;IAEA,SAAS,aAAa,GAAG;QACvB,IAAI,QAAQ,IAAI,KAAK,CAAC;QACtB,IAAI,MAAM,QAAQ,WAAW,KAAK,CAAC,EAAE,IAAI;QACzC,OAAO,MAAM,OAAO,IAAI,MAAM;IAChC;IAEA,SAAS,2BAA2B,cAAc,EAAE,aAAa,EAAE,QAAQ;QACzE,MAAM,YAAY,cAAc,CAAC,gBAAgB,EAAE;QACnD,MAAM,UAAU,cAAc,CAAC,gBAAgB,IAAI,EAAE;QACrD,MAAM,YAAY,cAAc,CAAC,gBAAgB,IAAI,EAAE;QACvD,MAAM,SAAS,cAAc,CAAC,gBAAgB,IAAI,EAAE;QACpD,MAAM,kBAAkB,CAAC,UAAU,SAAS,IAAI;QAChD,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;YACjC,MAAM,aAAa,CAAC,gBAAgB,CAAC,IAAI;YACzC,cAAc,CAAC,WAAW,GAAG,YAAY,kBAAkB;YAC3D,cAAc,CAAC,aAAa,EAAE,GAAG,YAAY,kBAAkB,CAAC,IAAI,CAAC;YACrE,cAAc,CAAC,aAAa,EAAE,GAAG;YACjC,cAAc,CAAC,aAAa,EAAE,GAAG;QACnC;IACF;IAEA,SAAS;QACP,OAAO,CAAC,KAAK,WAAW,IAAI,IAAI,EAAE,GAAG;IACvC;IAEA,yDAAyD;IACzD,SAAS;QACP,IAAI,CAAC,IAAI,GAAG,EAAE;IAChB;IACA,MAAM,gBAAgB;QAAC;QAAY;QAAK;QAAK;QAAS;QAAa;KAAW;IAC9E,SAAS,SAAS,GAAG;QACnB,OAAO;QACP,YAAY;QACZ,UAAU;QACV,KAAK;QACL,IAAI;QACJ,eAAe;QACf,IAAI,SAAQ;YACV,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,cAAc,MAAM;QAC1D;QACA,SAAQ,CAAC;YACP,IAAI,MAAM,SAAS,SAAS;YAC5B,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;YACpB,IAAI,KAAK,GAAG;YACZ,OAAO;QACT;QACA,SAAQ,CAAC;YACP,IAAI,UAAU,IAAI;YAClB,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,cAAc,MAAM;YACxD,OAAO;QACT;IACF;IACA,SAAS,SAAS,GAAG,cAAc,MAAM,CAAC,CAAC,KAAK,MAAM,GAAG;QACvD,OAAO,cAAc,CAAC,KAAK,MAAM;YAC/B;gBACE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,cAAc,MAAM,GAAG,EAAE;YACzD;YACA,KAAI,GAAG;gBACL,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,cAAc,MAAM,GAAG,EAAE,GAAG;YACrD;QACF;QACA,OAAO;IACT,GAAG;QAAC,MAAM;QAAM,OAAO;IAAC;IAGxB,OAAO;QACL;QACA;IACF;AACF;AAEA,MAAM,MAAM,IAAM,CAAC,KAAK,WAAW,IAAI,IAAI,EAAE,GAAG;AAEhD,MAAM,sBAAsB,WAAW,GAAG,CAAA,GAAA,kLAAA,CAAA,UAAkB,AAAD;AAE3D,IAAI;AAEJ;;;;CAIC,GACD,SAAS,YAAY,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,WAAW,IAAI;IAC3G,gBAAgB;IAChB,IAAI,CAAC,UAAU;QACb,OAAO,sBAAsB,OAAO,QAAQ,MAAM,SAAS,UAAU,UAAU,QAAQ,GAAG,GAAG;IAC/F;IAEA,2CAA2C;IAC3C,OAAO,eAAe,OAAO,QAAQ,MAAM,SAAS,UAAU,UAAU,QAAQ,GAAG,GAAG,SAAS,IAAI,CACjG,MACA,CAAA;QACE,4FAA4F;QAC5F,IAAI,CAAC,QAAQ;YACX,QAAQ,IAAI,CAAC,CAAC,+CAA+C,CAAC,EAAE;YAChE,SAAS;QACX;QACA,OAAO,sBAAsB,OAAO,QAAQ,MAAM,SAAS,UAAU,UAAU,QAAQ,GAAG,GAAG;IAC/F;AAEJ;AAEA,MAAM,QAAQ,EAAE;AAChB,MAAM,kBAAkB,GAAG,KAAK;AAChC,IAAI,QAAQ;AAEZ,SAAS;IACP,MAAM,QAAQ;IACd,MAAO,MAAM,MAAM,IAAI,QAAQ,QAAQ,gBAAiB;QACtD,MAAM,KAAK;IACb;IACA,QAAQ,MAAM,MAAM,GAAG,WAAW,WAAW,KAAK;AACpD;AAEA;;;CAGC,GACD,MAAM,iBAAiB,CAAC,GAAG;IACzB,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,IAAI,CAAC;YACT,MAAM,QAAQ;YACd,IAAI;gBACF,oBAAoB,KAAK,CAAC,kBAAkB,IAAI;gBAChD,QAAQ;oBAAE,QAAQ,QAAQ;gBAAM;YAClC,EAAE,OAAO,KAAK;gBACZ,OAAO;YACT;QACF;QACA,IAAI,CAAC,OAAO;YACV,QAAQ,WAAW,WAAW;QAChC;IACF;AACF;AAEA,MAAM,cAAc,GAAG,4BAA4B;AACnD,MAAM,cAAc,MAAM,qEAAqE;AAC/F,MAAM,UAAU,CAAC;AACjB,IAAI,UAAU;AAEd;;CAEC,GACD,SAAS,sBAAsB,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IACpG,MAAM,WAAW,+BAAgC,AAAC,YAAa;IAC/D,IAAI,SAAS,OAAO,CAAC,SAAS;IAC9B,IAAI,CAAC,QAAQ;QACX,SAAS,OAAO,CAAC,SAAS,GAAG;YAC3B,cAAc,CAAA,GAAA,wLAAA,CAAA,qBAAkB,AAAD,EAAE;gBAC/B,MAAM;gBACN;gBACA,cAAc;oBACZ,kLAAA,CAAA,UAAkB;oBAClB;iBACD;gBACD,MAAK,mBAAmB,EAAE,GAAG;oBAC3B,MAAM,WAAW,sBAAsB,UAAU,CAAC,QAAQ;oBAC1D,OAAO,SAAU,GAAG,IAAI;wBACtB,MAAM,QAAQ;wBACd,MAAM,cAAc,YAAY;wBAChC,OAAO;4BACL;4BACA,QAAQ,QAAQ;wBAClB;oBACF;gBACF;gBACA,kBAAiB,MAAM;oBACrB,OAAO;wBAAC,OAAO,WAAW,CAAC,MAAM;qBAAC;gBACpC;YACF;YACA,UAAU;YACV,WAAW;QACb;IACF;IAEA,OAAO,QAAQ;IACf,aAAa,OAAO,SAAS;IAC7B,OAAO,OAAO,YAAY,CAAC,OAAO,QAAQ,MAAM,SAAS,UAAU,UAChE,IAAI,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE;QAC5B,mCAAmC;QACnC,MAAM,QAAQ;QACd,uCAAuC;QACvC,MAAM,YAAY,IAAI,WAAW,YAAY,MAAM,GAAG;QACtD,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,SAAS,CAAC,IAAI,IAAI,QAAQ,GAAG,WAAW,CAAC,EAAE;QAC7C;QACA,oBAAoB,UAAU,CAAC,eAAe,CAAC,QAAQ,WAAW,GAAG,GAAG,OAAO,QAAQ,KAAM,IAAI;QACjG,UAAU,QAAQ;QAElB,iCAAiC;QACjC,IAAI,EAAE,OAAO,QAAQ,KAAK,GAAG;YAC3B,OAAO,SAAS,GAAG,WAAW;gBAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;YAAW,GAAG;QACtE;QACA,OAAO;YAAE;QAAO;IAClB;AACJ;AAEA,SAAS,gBAAgB,MAAM;IAC7B,IAAI,CAAC,OAAO,KAAK,EAAE;QACjB,oBAAoB,KAAK,CAAC,WAAW,CAAC;QACtC,OAAO,KAAK,GAAG;IACjB;AACF;AAEA,MAAM,mCAAmC,oBAAoB,UAAU,CAAC,gCAAgC;AAExG,MAAM,SAAS;IACb,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;IACd,WAAW,IAAI;IACf,aAAa;IACb,cAAc;IACd,WAAW;AACb;AACA,MAAM,YAAY,WAAW,GAAE,IAAI,kJAAA,CAAA,QAAK;AACxC,IAAI,eAAe;AAEnB,SAAS;IACP,OAAO,CAAC,KAAK,WAAW,IAAI,IAAI,EAAE,GAAG;AACvC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BC,GACD,SAAS,qBAAqB,MAAM;IAClC,IAAI,cAAc;QAChB,QAAQ,IAAI,CAAC;IACf,OAAO;QACL,OAAO,QAAQ;IACjB;AACF;AAEA;;;;;;;;;;;;;;CAcC,GACD,MAAM,UAAU,OAAO,MAAM,CAAC;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GAED;;;CAGC,GAED;;;;;CAKC,GACD,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACvC,eAAe;IACf,OAAO,OAAO,CAAC,GAAG;IAClB,MAAM,aAAa;IAEnB,2FAA2F;IAC3F,4EAA4E;IAC5E,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,MAAM,QAAQ,EAAE;IAChB,IAAI,gBAAgB;QAClB,MAAM,IAAI,CAAC;YAAC,OAAO;YAAW,KAAK,cAAc;QAAe;IAClE;IACA,IAAI,KAAK,IAAI,EAAE;QACb,MAAM,IAAI,CAAC;YAAC,OAAO;YAAQ,KAAK,cAAc,KAAK,IAAI;QAAC;IAC1D;IACA,KAAK,IAAI,GAAG;IAEZ,6BAA6B;IAC7B,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI;IAE1B,KAAK,YAAY,GAAG,KAAK,YAAY,IAAI,OAAO,YAAY;IAC5D,KAAK,eAAe,GAAG,KAAK,eAAe,IAAI,OAAO,eAAe;IAErE,mBAAmB;IACnB,IAAI,KAAK,WAAW,IAAI,MAAM;QAC5B,IAAI,SAAS,CAAC;QACd,IAAK,IAAI,OAAO,KAAK,WAAW,CAAE;YAChC,IAAI,KAAK,WAAW,CAAC,cAAc,CAAC,MAAM;gBACxC,IAAI,MAAM,KAAK,WAAW,CAAC,IAAI;gBAC/B,IAAI,OAAO,QAAQ,UAAU;oBAC3B,MAAM,UAAU,GAAG,CAAC,KAAK,MAAM;gBACjC;gBACA,MAAM,CAAC,IAAI,GAAG;YAChB;QACF;QACA,KAAK,WAAW,GAAG;IACrB;IAEA,OAAO,MAAM,CAAC;IAEd,2BAA2B;IAC3B,MAAM,EAAC,YAAY,EAAE,WAAW,EAAC,GAAG;IACpC,MAAM,EAAC,YAAY,EAAC,GAAG;IACvB,MAAM,eAAgB,eAAe,eAAe;IACpD,IAAI,QAAQ,OAAO,CAAC,aAAa;IACjC,IAAI,CAAC,OAAO;QACV,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG,eAAe,MAAM,cAAc,sCAAsC;QACzF,QAAQ,OAAO,CAAC,aAAa,GAAG;YAC9B,YAAY;YACZ;YACA,WAAW;YACX,YAAY,IAAI,kJAAA,CAAA,UAAO,CACrB,QACA,WACA,WACA,WACA,kJAAA,CAAA,eAAY,EACZ,kJAAA,CAAA,eAAY;YAEd,aAAa;YACb,cAAc,IAAI;QACpB;QACA,MAAM,UAAU,CAAC,eAAe,GAAG;QACnC,wBAAwB;IAC1B;IAEA,MAAM,EAAC,UAAU,EAAE,SAAS,EAAC,GAAG;IAEhC,wDAAwD;IACxD,MAAM,UAAU,OAAO,SAAS,GAAG,kBAAkB;IACrD,QAAQ,MAAM,IAAI,CAAC,CAAA;QACjB,MAAM,EAAC,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAC,GAAG;QAClF,MAAM,aAAa,EAAE;QACrB,MAAM,cAAc,IAAI,aAAa,SAAS,MAAM,GAAG;QACvD,IAAI,YAAY;QAChB,IAAI,eAAe;QACnB,MAAM,aAAa;QAEnB,MAAM,gBAAgB,SAAS,GAAG,CAAC,CAAA;YACjC,IAAI,MAAM,MAAM,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG;YACzC,IAAI,CAAC,KAAK;gBACR,MAAM,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,MAAM,IAAI;YAC7C;YACA,OAAO;QACT;QAEA,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,MAAM,YAAY,gBAAgB,CAAC,EAAE;YACrC,MAAM,EAAC,KAAK,OAAO,EAAE,UAAU,EAAC,GAAG,QAAQ,CAAC,UAAU;YACtD,IAAI,YAAY,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC;YAE7C,4DAA4D;YAC5D,IAAI,CAAC,WAAW;gBACd,MAAM,EAAC,IAAI,EAAE,UAAU,EAAC,GAAG,OAAO,SAAS,CAAC,QAAQ,CAAC,QAAQ;gBAE7D,uFAAuF;gBACvF,8FAA8F;gBAC9F,yDAAyD;gBACzD,MAAM,kBAAkB,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IACzF,eAAe,CAAC,OAAO,SAAS,GAAG,eAAe,GAAG;gBAEzD,MAAM,aAAa,MAAM,UAAU;gBACnC,MAAM,aAAa;oBACjB,UAAU,CAAC,EAAE,GAAG;oBAChB,UAAU,CAAC,EAAE,GAAG;oBAChB,UAAU,CAAC,EAAE,GAAG;oBAChB,UAAU,CAAC,EAAE,GAAG;iBACjB;gBACD,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,SAAU,YAAY;oBAAE;oBAAM;oBAAY;gBAAW;gBAElF,yCAAyC;gBACzC,WAAW,IAAI,CAAC;YAClB;YAEA,wCAAwC;YACxC,iDAAiD;YACjD,MAAM,EAAC,UAAU,EAAC,GAAG;YACrB,MAAM,OAAO,cAAc,CAAC,eAAe;YAC3C,MAAM,OAAO,cAAc,CAAC,eAAe;YAC3C,MAAM,eAAe,WAAW;YAChC,WAAW,CAAC,YAAY,GAAG,OAAO,UAAU,CAAC,EAAE,GAAG;YAClD,WAAW,CAAC,YAAY,GAAG,OAAO,UAAU,CAAC,EAAE,GAAG;YAClD,WAAW,CAAC,YAAY,GAAG,OAAO,UAAU,CAAC,EAAE,GAAG;YAClD,WAAW,CAAC,YAAY,GAAG,OAAO,UAAU,CAAC,EAAE,GAAG;YAElD,8CAA8C;YAC9C,QAAQ,CAAC,EAAE,GAAG,UAAU,UAAU;QACpC;QACA,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,UAAU;QAE5D,MAAM,WAAW;QACjB,QAAQ,GAAG,GAAG,CAAC;QAEf,kDAAkD;QAClD,MAAM,gBAAgB,UAAU,MAAM;QACtC,MAAM,aAAa,KAAK,IAAI,CAAC,MAAM,UAAU,GAAG;QAChD,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa;QAClE,IAAI,eAAe,eAAe;YAChC,8GAA8G;YAC9G,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,cAAc,EAAE,EAAE,cAAc;YAC5E,iCAAiC,WAAW,cAAc;YAC1D,kHAAkH;YAClH,WAAW,OAAO;QACpB;QAEA,QAAQ,GAAG,CAAC,WAAW,GAAG,CAAC,CAAA,YACzB,iBAAiB,WAAW,OAAO,KAAK,gBAAgB,EAAE,IAAI,CAAC,CAAC,EAAC,MAAM,EAAC;gBACtE,QAAQ,GAAG,CAAC,UAAU,UAAU,CAAC,GAAG;YACtC,KACC,IAAI,CAAC;YACN,IAAI,WAAW,MAAM,IAAI,CAAC,MAAM,WAAW,EAAE;gBAC3C,sBAAsB;gBACtB,WAAW,WAAW,GAAG;YAC3B;YACA,QAAQ,QAAQ,GAAG,UAAU;YAC7B,QAAQ,KAAK,GAAG,UAAU;YAC1B,wFAAwF;YAExF,kEAAkE;YAClE,SAAS,OAAO,MAAM,CAAC;gBACrB,YAAY;gBACZ;gBACA;gBACA;gBACA;gBACA,mBAAmB;gBACnB,aAAa,OAAO,WAAW;gBAC/B,gBAAgB,OAAO,cAAc;gBACrC,eAAe,OAAO,aAAa;gBACnC,UAAU,OAAO,QAAQ;gBACzB,WAAW,OAAO,SAAS;gBAC3B,YAAY,OAAO,UAAU;gBAC7B,WAAW,OAAO,SAAS;gBAC3B,SAAS,OAAO,OAAO;gBACvB,aAAa,OAAO,WAAW;gBAC/B,aAAa,OAAO,WAAW;gBAC/B,eAAe,OAAO,aAAa;gBACnC,SAAS,OAAO,OAAO;YACzB;QACF;IACF;IAEA,qGAAqG;IACrG,oGAAoG;IACpG,6EAA6E;IAC7E,QAAQ,OAAO,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,gBAAgB;QAClB;IACF;AACF;AAEA,SAAS,iBAAiB,EAAC,IAAI,EAAE,UAAU,EAAE,UAAU,EAAC,EAAE,EAAC,YAAY,EAAE,SAAS,EAAE,WAAW,EAAC,EAAE,MAAM;IACtG,IAAI,aAAa;QACf,kFAAkF;QAClF,+CAA+C;QAC/C,OAAO,QAAQ,OAAO,CAAC;YAAC,QAAQ,CAAC;QAAC;IACpC;IACA,MAAM,EAAC,YAAY,EAAE,WAAW,EAAC,GAAG;IACpC,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;IACrF,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,MAAM,IAAI,cAAc,CAAC,eAAe,YAAY,IAAI;IACxD,MAAM,IAAI,KAAK,KAAK,CAAC,cAAc,CAAC,eAAe,YAAY,KAAK;IACpE,MAAM,UAAU,aAAa;IAC7B,OAAO,YAAY,cAAc,cAAc,MAAM,YAAY,SAAS,aAAa,WAAW,GAAG,GAAG,SAAS;AACnH;AAEA,SAAS,wBAAwB,KAAK;IACpC,MAAM,SAAS,MAAM,SAAS;IAE9B;;;;;;;;;;;;;;;;;;;;;EAqBA,GAEA,OAAO,gBAAgB,CAAC,oBAAoB,CAAC;QAC3C,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,MAAM,cAAc;QACpB,MAAM,WAAW,GAAG;IACtB;IACA,OAAO,gBAAgB,CAAC,wBAAwB,CAAC;QAC/C,QAAQ,GAAG,CAAC,oBAAoB;QAChC,MAAM,WAAW,GAAG;QACpB,kDAAkD;QAClD,MAAM,WAAW,EAAE;QACnB,MAAM,YAAY,CAAC,OAAO,CAAC,CAAA;YACzB,SAAS,OAAO,CAAC,CAAA;gBACf,SAAS,IAAI,CAAC,iBAAiB,OAAO,OAAO;YAC/C;QACF;QACA,QAAQ,GAAG,CAAC,UAAU,IAAI,CAAC;YACzB,sBAAsB;YACtB,MAAM,UAAU,CAAC,WAAW,GAAG;QACjC;IACF;AACF;AAEA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,YAAY,EAAC,IAAI,EAAE,UAAU,EAAE,YAAY,EAAC,EAAE,QAAQ;IAC7D,IAAI,OAAO,MAAM,OAAO,CAAC,cAAc,WAAW,IAAI,CAAC,QAAQ,KAAK;IACpE,kBAAkB;QAAE;QAAM;QAAc;IAAK,GAAG;AAClD;AAGA,2DAA2D;AAC3D,SAAS,OAAO,KAAK,EAAE,OAAO;IAC5B,IAAK,IAAI,OAAO,QAAS;QACvB,IAAI,QAAQ,cAAc,CAAC,MAAM;YAC/B,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;QAC3B;IACF;IACA,OAAO;AACT;AAEA,mCAAmC;AACnC,IAAI;AACJ,SAAS,cAAc,IAAI;IACzB,IAAI,CAAC,QAAQ;QACX,SAAS,OAAO,aAAa,cAAc,CAAC,IAAI,SAAS,aAAa,CAAC;IACzE;IACA,OAAO,IAAI,GAAG;IACd,OAAO,OAAO,IAAI;AACpB;AAEA;;;;CAIC,GACD,SAAS,sBAAsB,KAAK;IAClC,wFAAwF;IACxF,+EAA+E;IAC/E,IAAI,OAAO,sBAAsB,YAAY;QAC3C,QAAQ,IAAI,CAAC;QACb,MAAM,EAAC,SAAS,EAAE,UAAU,EAAC,GAAG;QAChC,MAAM,EAAC,KAAK,EAAE,MAAM,EAAC,GAAG;QACxB,MAAM,KAAK,MAAM,SAAS,CAAC,UAAU,CAAC;QACtC,IAAI,SAAS,WAAW,KAAK,CAAC,IAAI;QAClC,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,QAAQ,SAAS,GAAG;YACnD,SAAS,IAAI,WAAW,QAAQ,SAAS;YACzC,WAAW,KAAK,GAAG;gBAAC;gBAAO;gBAAQ,MAAM;YAAM;YAC/C,WAAW,KAAK,GAAG;YACnB,WAAW,aAAa,GAAG;QAC7B;QACA,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,QAAQ,GAAG,IAAI,EAAE,GAAG,aAAa,EAAE;IAChE;AACF;AAEA,MAAM,yBAAyB,WAAW,GAAE,CAAA,GAAA,wLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC7D,MAAM;IACN,cAAc;QACZ;QACA;QACA,8IAAA,CAAA,UAAW;KACZ;IACD,MAAK,gBAAgB,EAAE,YAAY,EAAE,WAAW;QAC9C,OAAO,iBAAiB,cAAc;IACxC;AACF;AAEA,MAAM,kBAAkB,WAAW,GAAE,CAAA,GAAA,wLAAA,CAAA,qBAAkB,AAAD,EAAE;IACtD,MAAM;IACN,cAAc;QACZ;KACD;IACD,MAAK,UAAU;QACb,OAAO,SAAS,IAAI;YAClB,OAAO,IAAI,QAAQ,CAAA;gBACjB,WAAW,OAAO,CAAC,MAAM;YAC3B;QACF;IACF;IACA,kBAAiB,MAAM;QACrB,yEAAyE;QACzE,MAAM,gBAAgB,EAAE;QACxB,IAAK,IAAI,KAAK,OAAQ;YACpB,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;gBACjC,cAAc,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM;YACrC;QACF;QACA,OAAO;IACT;AACF;AAEA,MAAM,sBAAsB,gBAAgB,YAAY;AAExD,SAAS;IACP,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,CAAA;QAC3B,MAAM,SAAS,OAAO,CAAC,KAAK,CAAC,SAAS;QACtC,MAAM,EAAC,KAAK,EAAE,MAAM,EAAC,GAAG;QACxB,QAAQ,GAAG,CAAC,OAAO,CAAC;sBACF,EAAE,OAAO,SAAS,GAAG;uBACpB,EAAE,MAAM,GAAG,EAAE,OAAO;;;mBAGxB,EAAE,OAAO;oBACR,EAAE,MAAM;IACxB,CAAC;IACH;AACF;AAEA,MAAM,qBAAqB,CAAC;AAE5B,SAAS,oBAAoB,MAAM;IACjC,IAAI,OAAO,kBAAkB,CAAC,OAAO;IACrC,IAAI,CAAC,MAAM;QACT,OAAO,kBAAkB,CAAC,OAAO,GAAG,IAAI,kJAAA,CAAA,gBAAa,CAAC,GAAG,GAAG,QAAQ,QAAQ,SAAS,CAAC,KAAK,KAAK;IAClG;IACA,OAAO;AACT;AAEA,MAAM,sBAAsB;AAC5B,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAE3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,GACA,MAAM,uBAAuB,kJAAA,CAAA,0BAAuB;IAClD,aAAc;QACZ,KAAK;QAEL,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;QAEnB,+EAA+E;QAC/E,8EAA8E;QAC9E,IAAI,CAAC,MAAM,GAAG;YACZ;gBAAC,OAAO;gBAAG,OAAO;gBAAU,eAAe;YAAC;YAC5C;gBAAC,OAAO;gBAAG,OAAO;gBAAU,eAAe;YAAC;SAC7C;QAED,qCAAqC;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI,kJAAA,CAAA,SAAM;QAChC,IAAI,CAAC,WAAW,GAAG,IAAI,kJAAA,CAAA,OAAI;IAC7B;IAEA,wBAAyB;IACvB,gEAAgE;IAClE;IAEA,qBAAqB;IACnB,6DAA6D;IAC/D;IAEA,IAAI,OAAO,MAAM,EAAE;QACjB,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE;YAC3B,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,OAAO,WAAW,YAAY,SAAS,GAAG;gBAC5C,SAAS;YACX;YACA,IAAI,MAAM,oBAAoB;YAC7B;gBAAC;gBAAY;gBAAU;aAAK,CAAC,OAAO,CAAC,CAAA;gBACpC,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK;YACpD;YACA,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,GAAG,KAAK;QACpC;IACF;IACA,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,IAAI,YAAY,CAAC,EAAE;QACjB,IAAI,MAAM,IAAI,CAAC,YAAY,EAAE;YAC3B,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,aAAa;QACpB;IACF;IACA,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA;;;;;;;;;;;GAWC,GACD,aAAa,WAAW,EAAE,iBAAiB,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE;QACpF,iCAAiC;QACjC,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,aAAa;QAC3D,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,mBAAmB;QAChE,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,aAAa;QAC1D,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,kBAAkB,MAAM;QAC7C,IAAI,CAAC,aAAa;IACpB;IAEA,gBAAgB;QACd,MAAM,SAAS,IAAI,CAAC,YAAY;QAChC,IAAI,QAAQ;YACV,MAAM,EAAE,WAAW,EAAE,aAAa,IAAI,EAAE,GAAG,IAAI;YAC/C,IAAI,aAAa;gBACf,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;gBAC1C,MAAM,SAAS,KAAK;gBACpB,MAAM,QAAQ,KAAK;gBACnB,MAAM,OAAO,KAAK,GAAG,CAAC;gBACtB,MAAM,YAAY,MAAM,CAAC,EAAE,GAAG;gBAC9B,MAAM,aAAa,MAAM,CAAC,EAAE,GAAG;gBAC/B,MAAM,OAAO,MAAM,CAAC,YAAY,MAAM,IAAI,WAAW,MAAM,CAAC,aAAa,MAAM,IAAI,SAC/E,CAAC,OAAO,IAAI,IAAI,aAAa,MAAM,IAAI,cAAc;gBACzD,MAAM,OAAO,MAAM,CAAC,YAAY,MAAM,IAAI,WAAW,MAAM,CAAC,aAAa,MAAM,IAAI,SAC/E,OAAO,IAAI,IAAI,aAAa,MAAM,IAAI,cAAc;gBACxD,MAAM,OAAO,MAAM,CAAC,YAAY,EAAE,IAAI,WAAW,MAAM,CAAC,aAAa,EAAE,IAAI,SACvE,OAAO,IAAI,IAAI,OAAO,IAAI,aAAa,MAAM,OAAO,IAAI,cAAc;gBAC1E,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,EAAE,cAAc,IAAI,CAAC,OAAO;gBACxD,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,EAAE,cAAc,IAAI,IAAI;YACtD,OAAO;gBACL,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;gBACnC,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;YACrC;YACA,KAAK,iBAAiB,CAAC,IAAI,CAAC,cAAc;QAC5C;IACF;IAEA;;;;;;;;;;;;GAYC,GACD,cAAc,QAAQ,EAAE;QACtB,IAAI,QAAQ,IAAI,CAAC,YAAY,CAAC,oBAAoB,KAAK;QACvD,IAAI,SAAS,IAAI,CAAC,cAAc;QAChC,IAAI,QAAQ;YACV,IAAK,IAAI,IAAI,OAAO,MAAM,EAAE,KAAM;gBAChC,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG;gBACrB,IAAI,OAAO,MAAM,CAAC,EAAE,CAAC,IAAI;gBACzB,+BAA+B;gBAC/B,IAAI,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;oBAChG;gBACF;YACF;QACF;QACA,IAAI,CAAC,aAAa,GAAG;IACvB;IAEA;;GAEC,GACD,oBAAoB,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;QAChD,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,IAAI,UAAU;YACZ,mEAAmE;YACnE,IAAI,QAAQ,KAAK,KAAK,CAAC,MAAM,KAAK,SAAS,MAAM,EAAE;gBACjD,KAAK,KAAK,CAAC,GAAG,CAAC;gBACf,KAAK,WAAW,GAAG;YACrB,OAAO;gBACL,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,kJAAA,CAAA,2BAAwB,CAAC,UAAU;gBACnE,6FAA6F;gBAC7F,0FAA0F;gBAC1F,uFAAuF;gBACvF,mFAAmF;gBACnF,0FAA0F;gBAC1F,2DAA2D;gBAC3D,OAAO,IAAI,CAAC,iBAAiB,EAAE,6BAA6B;gBAC5D,IAAI,CAAC,OAAO,IAAI,qEAAqE;YACvF;QACF,OAAO,IAAI,MAAM;YACf,IAAI,CAAC,eAAe,CAAC;QACvB;IACF;AACF;AAEA,gBAAgB;AAChB,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;AAmBrB,CAAC;AAED,kDAAkD;AAClD,MAAM,mBAAmB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiC1B,EAAE,GAAE;;;8FAG0F,IAAG;;;;;;;;;AASjG,CAAC;AAED,gBAAgB;AAChB,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;EAmBrB,EAAE,GAAE;;;EAGJ,IAAG;;;;;;;;;;;;;;;;;;;EAmBH,EAAE,GAAE;;;;EAIJ,IAAG;;;;;;;;;;;;;;;;EAgBH,EAAE,GAAE;;;;;;;;;;;;;;;;;;;;;EAqBJ,IAAG;;;;;;;;;;;;;;;;;;;AAmBL,CAAC;AAED,kDAAkD;AAClD,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuB5B,CAAC;AAGD;;CAEC,GACD,SAAS,0BAA0B,YAAY;IAC7C,MAAM,eAAe,CAAA,GAAA,sLAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;QACvD,SAAS;QACT,YAAY;YACV,aAAa;QACf;QACA,UAAU;YACR,mBAAmB;gBAAC,OAAO;YAAI;YAC/B,uBAAuB;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAE;YAC5C,qBAAqB;gBAAC,OAAO;YAAC;YAC9B,oBAAoB;gBAAC,OAAO;YAAC;YAC7B,oBAAoB;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO,CAAC,GAAE,GAAE,GAAE;YAAE;YAChD,iBAAiB;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO,CAAC,GAAE,GAAE,GAAE;YAAE;YAC7C,mBAAmB;gBAAC,OAAO;YAAC;YAC5B,oBAAoB;gBAAC,OAAO;YAAC;YAC7B,uBAAuB;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAE;YAC5C,oBAAoB;gBAAC,OAAO;YAAC;YAC7B,mBAAmB;gBAAC,OAAO;YAAC;YAC5B,oBAAoB;gBAAC,OAAO;YAAC;YAC7B,oBAAoB;gBAAC,OAAO,IAAI,kJAAA,CAAA,QAAK;YAAE;YACvC,sBAAsB;gBAAC,OAAO;YAAC;YAC/B,eAAe;gBAAC,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAE;YACpC,uBAAuB;gBAAC,OAAO;YAAI;YACnC,iBAAiB;gBAAC,OAAO;YAAK;QAChC;QACA,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,wBAAwB;QACxB,gBAAe,EAAC,YAAY,EAAE,cAAc,EAAC;YAC3C,IAAI,aAAa;YACjB,IAAI,WAAW,IAAI,CAAC,iBAAiB;gBACnC,sDAAsD;gBACtD,iBAAiB,eACd,OAAO,CAAC,YAAY,kCACpB,OAAO,CAAC,gBAAgB;gBAC3B,mFAAmF;gBACnF,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe;oBAClC,eAAe,aAAa,OAAO,CACjC,sLAAA,CAAA,iBAAc,EACd;gBAEJ;YACF;YACA,OAAO;gBAAE;gBAAc;YAAe;QACxC;IACF;IAEA,gDAAgD;IAChD,aAAa,WAAW,GAAG;IAE3B,2CAA2C;IAC3C,aAAa,eAAe,GAAG;IAE/B,OAAO,gBAAgB,CAAC,cAAc;QACpC,sBAAsB;YAAC,OAAO;QAAI;QAElC,kFAAkF;QAClF,+EAA+E;QAC/E,YAAY;YACV;gBACE,OAAO,IAAI,CAAC,IAAI;YAClB;YACA;YACE,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AAEA,MAAM,kBAAkB,WAAW,GAAG,IAAI,kJAAA,CAAA,oBAAiB,CAAC;IAC1D,OAAO;IACP,MAAM,kJAAA,CAAA,aAAU;IAChB,aAAa;AACf;AACA,MAAM,qBAAqB;AAE3B,MAAM,WAAW,WAAW,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC1C,MAAM,YAAY,WAAW,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC3C,MAAM,YAAY,WAAW,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC3C,MAAM,YAAY,EAAE;AACpB,MAAM,SAAS,WAAW,GAAG,IAAI,kJAAA,CAAA,UAAO;AACxC,MAAM,gBAAgB;AAEtB,SAAS,MAAM,CAAC;IACd,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG;AACnC;AAEA,IAAI,qBAAqB;IACvB,MAAM,OAAO,IAAI,kJAAA,CAAA,OAAI,CACnB,IAAI,kJAAA,CAAA,gBAAa,CAAC,GAAG,IACrB;IAEF,qBAAqB,IAAM;IAC3B,OAAO;AACT;AACA,IAAI,uBAAuB;IACzB,MAAM,OAAO,IAAI,kJAAA,CAAA,OAAI,CACnB,IAAI,kJAAA,CAAA,gBAAa,CAAC,GAAG,GAAG,IAAI,IAC5B;IAEF,uBAAuB,IAAM;IAC7B,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAE,MAAM;AAAY;AAC3C,MAAM,oBAAoB;IAAE,MAAM;AAAe;AAEjD,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB,eAAe,MAAM,CAC1C,YACA,SACA,eACA,YACA,eACA,eACA;AAGF;;;;;CAKC,GACD,MAAM,aAAa,kJAAA,CAAA,OAAI;IACrB,aAAc;QACZ,MAAM,WAAW,IAAI;QACrB,KAAK,CAAC,UAAU;QAEhB,qCAAqC;QAErC;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG;QAEZ;;;;;;KAMC,GACD,IAAI,CAAC,OAAO,GAAG;QAEf;;;;;;KAMC,GACD,IAAI,CAAC,OAAO,GAAG;QAEf;;;;;;;;;KASC,GACD,IAAI,CAAC,WAAW,GAAG;QAEnB;;;;KAIC,GACD,IAAI,CAAC,SAAS,GAAG;QAEjB;;;;KAIC,GACD,IAAI,CAAC,IAAI,GAAG,MAAM,mCAAmC;QAErD,IAAI,CAAC,eAAe,GAAG,MAAM,iBAAiB;QAE9C;;;;KAIC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;;KAGC,GACD,IAAI,CAAC,UAAU,GAAG;QAElB;;;KAGC,GACD,IAAI,CAAC,SAAS,GAAG;QAEjB;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG;QAEV;;;;KAID,GACD,IAAI,CAAC,aAAa,GAAG;QAErB;;;;KAIC,GACD,IAAI,CAAC,UAAU,GAAG;QAElB;;;;KAIC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;;;;KAKC,GACD,IAAI,CAAC,YAAY,GAAG;QAEpB;;;KAGC,GACD,IAAI,CAAC,SAAS,GAAG;QAEjB;;;KAGC,GACD,IAAI,CAAC,UAAU,GAAG;QAElB;;;;;;KAMC,GACD,IAAI,CAAC,UAAU,GAAG;QAGlB,sCAAsC;QAEtC;;;;;;;;;KASC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;;;;;KAMC,GACD,IAAI,CAAC,KAAK,GAAG;QAEb;;;;;;;;KAQC,GACD,IAAI,CAAC,WAAW,GAAG;QAEnB;;;;;;;KAOC,GACD,IAAI,CAAC,YAAY,GAAG;QAEpB;;;;;KAKC,GACD,IAAI,CAAC,YAAY,GAAG;QAEpB;;;;;KAKC,GACD,IAAI,CAAC,cAAc,GAAG;QAEtB;;;;;;;KAOC,GACD,IAAI,CAAC,WAAW,GAAG;QAEnB;;;;;;KAMC,GACD,IAAI,CAAC,cAAc,GAAG;QAEtB;;;;;;KAMC,GACD,IAAI,CAAC,cAAc,GAAG;QAEtB;;;;;;KAMC,GACD,IAAI,CAAC,WAAW,GAAG;QAEnB;;;;KAIC,GACD,IAAI,CAAC,WAAW,GAAG;QAEnB;;;;KAIC,GACD,IAAI,CAAC,aAAa,GAAG;QAErB;;;;;;KAMC,GACD,IAAI,CAAC,WAAW,GAAG;QAEnB;;;;;KAKC,GACD,IAAI,CAAC,WAAW,GAAG;QAEnB;;;;;KAKC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;;;;;;;KAQC,GACD,IAAI,CAAC,WAAW,GAAG;QAEnB;;;;;KAKC,GACD,IAAI,CAAC,mBAAmB,GAAG;QAE3B;;;;;;;KAOC,GACD,IAAI,CAAC,YAAY,GAAG;QAEpB;;;;;;;KAOC,GACD,IAAI,CAAC,gBAAgB,GAAG;QAExB,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA;;;;;GAKC,GACD,KAAK,QAAQ,EAAE;QACb,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,GAAG;YAElB,mDAAmD;YACnD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC;YACvD,OAAO;gBACL,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,aAAa,CAAC;gBAEnB,kBAAkB;oBAChB,MAAM,IAAI,CAAC,IAAI;oBACf,MAAM,IAAI,CAAC,IAAI;oBACf,MAAM,IAAI,CAAC,IAAI;oBACf,UAAU,IAAI,CAAC,QAAQ,IAAI;oBAC3B,YAAY,IAAI,CAAC,UAAU,IAAI;oBAC/B,WAAW,IAAI,CAAC,SAAS,IAAI;oBAC7B,eAAe,IAAI,CAAC,aAAa,IAAI;oBACrC,YAAY,IAAI,CAAC,UAAU,IAAI;oBAC/B,UAAU,IAAI,CAAC,QAAQ;oBACvB,WAAW,IAAI,CAAC,SAAS,IAAI;oBAC7B,WAAW,IAAI,CAAC,SAAS;oBACzB,YAAY,IAAI,CAAC,UAAU;oBAC3B,YAAY,IAAI,CAAC,UAAU;oBAC3B,cAAc,IAAI,CAAC,YAAY;oBAC/B,SAAS,IAAI,CAAC,OAAO;oBACrB,SAAS,IAAI,CAAC,OAAO;oBACrB,aAAa,IAAI,CAAC,WAAW;oBAC7B,uBAAuB;oBACvB,cAAc,IAAI,CAAC,YAAY;oBAC/B,kBAAkB,IAAI,CAAC,gBAAgB;oBACvC,iBAAiB,IAAI,CAAC,eAAe;gBACvC,GAAG,CAAA;oBACD,IAAI,CAAC,UAAU,GAAG;oBAElB,8CAA8C;oBAC9C,IAAI,CAAC,eAAe,GAAG;oBAEvB,iCAAiC;oBACjC,IAAI,CAAC,QAAQ,CAAC,YAAY,CACxB,eAAe,WAAW,EAC1B,eAAe,iBAAiB,EAChC,eAAe,WAAW,EAC1B,eAAe,aAAa,EAC5B,eAAe,WAAW;oBAG5B,uDAAuD;oBACvD,MAAM,SAAS,IAAI,CAAC,YAAY;oBAChC,IAAI,QAAQ;wBACV,IAAI,CAAC,YAAY,GAAG;wBACpB,IAAI,CAAC,UAAU,GAAG;wBAClB,IAAI,CAAC,IAAI,CAAC;4BACR,OAAO,OAAO,CAAC,CAAA,KAAM,MAAM;wBAC7B;oBACF;oBAEA,IAAI,CAAC,aAAa,CAAC;oBACnB,IAAI,UAAU;wBACZ;oBACF;gBACF;YACF;QACF;IACF;IAEA;;;;;GAKC,GACD,eAAe,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE;QACjE,IAAI,CAAC,IAAI;QAET,2FAA2F;QAC3F,IAAI,SAAS,oBAAoB,EAAE;YACjC,IAAI,CAAC,iBAAiB,CAAC;QACzB;IACF;IAEA;;;;;;GAMC,GACD,UAAU;QACR,IAAI,CAAC,QAAQ,CAAC,OAAO;IACvB;IAEA;;;;;;GAMC,GACD,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,eAAe,IAAI;IACjC;IAEA;;;GAGC,GACD,sBAAsB,YAAY,EAAE;QAClC,OAAO,0BAA0B;IACnC;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI,kBAAkB,IAAI,CAAC,gBAAgB;QAC3C,MAAM,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,KAAK,EAAE;QACpH,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,aAAa,CAAC,eAAe;YACpE,kBAAkB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACrE,mEAAmE;YACnE,aAAa,gBAAgB,CAAC,WAAW,SAAS;gBAChD,aAAa,mBAAmB,CAAC,WAAW;gBAC5C,gBAAgB,OAAO;YACzB;QACF;QACA,8FAA8F;QAC9F,2FAA2F;QAC3F,wFAAwF;QACxF,wEAAwE;QACxE,IAAI,IAAI,CAAC,UAAU,IAAI;YACrB,IAAI,kBAAkB,gBAAgB,WAAW;YACjD,IAAI,CAAC,iBAAiB;gBACpB,kBAAkB,gBAAgB,WAAW,GAAG,OAAO,MAAM,CAAC,iBAAiB;oBAC7E,IAAI;wBAAC,OAAO,gBAAgB,EAAE,GAAG;oBAAG;gBACtC;gBACA,gBAAgB,qBAAqB,GAAG;gBACxC,gBAAgB,UAAU,GAAG;gBAC7B,gBAAgB,GAAG,GAAG,MAAM,KAAK;gBACjC,gBAAgB,gBAAgB,CAAC,WAAW,SAAS;oBACnD,gBAAgB,mBAAmB,CAAC,WAAW;oBAC/C,gBAAgB,OAAO;gBACzB;YACF;YACA,OAAO;gBACL;gBACA;aACD;QACH,OAAO;YACL,OAAO;QACT;IACF;IACA,IAAI,SAAS,YAAY,EAAE;QACzB,IAAI,gBAAgB,aAAa,oBAAoB,EAAE;YACrD,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,aAAa,GAAG,aAAa,YAAY;QAChD,OAAO;YACL,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;IAEA,aAAa;QACX,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc;IAC/F;IAEA,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;IAC7B;IACA,IAAI,oBAAoB,MAAM,EAAE;QAC9B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;IACzB;IAEA,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW;IAClC;IACA,IAAI,YAAY,CAAC,EAAE;QACjB,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG;IAC9B;IAEA,uDAAuD;IACvD,IAAI,sBAAsB;QACxB,OAAO,MAAM,IAAI,CAAC,QAAQ,EAAE,gBAAgB;IAC9C;IACA,IAAI,oBAAoB,CAAC,EAAE;IACzB,gDAAgD;IAClD;IACA,IAAI,yBAAyB;QAC3B,OAAO,MAAM,IAAI,CAAC,QAAQ,EAAE,mBAAmB;IACjD;IACA,IAAI,uBAAuB,CAAC,EAAE;IAC5B,gDAAgD;IAClD;IAEA,kBAAkB,QAAQ,EAAE;QAC1B,MAAM,YAAY,SAAS,qBAAqB;QAChD,MAAM,WAAW,SAAS,QAAQ;QAClC,MAAM,WAAW,IAAI,CAAC,cAAc;QACpC,IAAI,UAAU;YACZ,MAAM,EAAC,UAAU,EAAE,WAAW,EAAC,GAAG;YAClC,SAAS,iBAAiB,CAAC,KAAK,GAAG;YACnC,SAAS,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,KAAK,CAAC,KAAK,EAAE,WAAW,KAAK,CAAC,MAAM;YACxF,SAAS,mBAAmB,CAAC,KAAK,GAAG,SAAS,YAAY;YAC1D,SAAS,kBAAkB,CAAC,KAAK,GAAG,SAAS,WAAW;YACxD,SAAS,kBAAkB,CAAC,KAAK,CAAC,SAAS,CAAC;YAC5C,SAAS,qBAAqB,CAAC,KAAK,GAAG,CAAC,aAAa,CAAC,CAAC,SAAS,WAAW;YAE3E,IAAI,iBAAiB;YACrB,IAAI,aAAa;YACjB,IAAI,cAAc;YAClB,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI,UAAU;YACd,IAAI,UAAU;YAEd,IAAI,WAAW;gBACb,IAAI,EAAC,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,cAAc,EAAC,GAAG,IAAI;gBACtF,iBAAiB,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBACrD,aAAa,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB;gBAC5D,cAAc;gBACd,UAAU,IAAI,CAAC,aAAa,CAAC,mBAAmB;gBAChD,UAAU,IAAI,CAAC,aAAa,CAAC,mBAAmB;YAClD,OAAO;gBACL,cAAc,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,KAAK;gBAClE,IAAI,aAAa;oBACf,cAAc,IAAI,CAAC,WAAW;oBAC9B,SAAS,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,OAAO,qBAAqB;oBACjF,gBAAgB,IAAI,CAAC,aAAa;oBAClC,IAAI,iBAAiB,MAAM,gBAAgB;gBAC7C;gBACA,cAAc,IAAI,CAAC,WAAW;YAChC;YAEA,SAAS,iBAAiB,CAAC,KAAK,GAAG;YACnC,SAAS,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS;YAClD,SAAS,iBAAiB,CAAC,KAAK,GAAG;YACnC,SAAS,kBAAkB,CAAC,KAAK,GAAG;YACpC,SAAS,oBAAoB,CAAC,KAAK,GAAG;YACtC,SAAS,kBAAkB,CAAC,KAAK,GAAG,eAAe,OAAO,IAAI;YAC9D,SAAS,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,IAAI;YAExD,IAAI,WAAW,IAAI,CAAC,QAAQ;YAC5B,IAAI,YAAY,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,GAAG;gBAChE,SAAS,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC;YAC3C,OAAO;gBACL,sGAAsG;gBACtG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI;gBACrC,SAAS,eAAe,CAAC,KAAK,CAAC,GAAG,CAChC,WAAW,CAAC,EAAE,GAAG,KACjB,WAAW,CAAC,EAAE,GAAG,KACjB,WAAW,CAAC,EAAE,GAAG,KACjB,WAAW,CAAC,EAAE,GAAG;YAErB;YACA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,eAAe,CAAC,KAAK;QAC5D;QACA,SAAS,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ;QAChD,SAAS,aAAa,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW;QAC3C,SAAS,mBAAmB,GAAG,SAAS,kBAAkB,GAAG,IAAI,CAAC,WAAW,IAAI;QAEjF,4EAA4E;QAC5E,iFAAiF;QACjF,MAAM,QAAQ,YAAa,IAAI,CAAC,YAAY,IAAI,IAAK,IAAI,CAAC,KAAK;QAE/D,IAAI,SAAS,MAAM;YACjB,OAAO,SAAS,KAAK,EAAE,mBAAmB;QAC5C,OAAO;YACL,MAAM,WAAW,SAAS,cAAc,CAAC,WAAW,SAAS,KAAK,GAAI,SAAS,KAAK,GAAG,IAAI,kJAAA,CAAA,QAAK;YAChG,IAAI,UAAU,SAAS,MAAM,IAAI,OAAO,UAAU,UAAU;gBAC1D,SAAS,GAAG,CAAC,SAAS,MAAM,GAAG;YACjC;QACF;QAEA,mBAAmB;QACnB,IAAI,SAAS,IAAI,CAAC,WAAW,IAAI;QACjC,IAAI,WAAW,SAAS,YAAY,EAAE;YACpC,IAAI,SAAS,SAAS,aAAa,CAAC,KAAK;YACzC,SAAS,OAAO,OAAO,CAAC,aAAa;YACrC,IAAI,QAAQ,WAAW,iBAAiB,OAAO,KAAK,CAAC;YACrD,IAAI,OAAO;gBACT,IAAI,GAAG,OAAO,OAAO,OAAO,MAAM,GAAG;gBACrC,UAAU,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,MAAM,GAAG,UAAU,MAAM,IAAI,CAAC;gBACrD,UAAU,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,MAAM,GAAG,UAAU,MAAM,CAAC,IAAI;gBACrD,SAAS,MAAM,CAAC,QAAQ,UAAU,KAAK,CAAC,YAAY;gBACpD,OAAO,cAAc,CAAC;YACxB,OAAO;gBACL,OAAO,QAAQ;YACjB;YACA,SAAS,YAAY,GAAG;QAC1B;IACF;IAEA,cAAc,KAAK,EAAE;QACnB,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,QAAQ,MAAM,KAAK,CAAC;YACxB,IAAI,MAAM,QAAQ,WAAW,KAAK,CAAC,EAAE,IAAI;YACzC,QAAQ,CAAC,MAAM,OAAO,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ;QACtD;QACA,OAAO;IACT;IAEA;;GAEC,GACD,0BAA0B,QAAQ,EAAE,SAAS,IAAI,kJAAA,CAAA,UAAO,EAAE,EAAE;QAC1D,OAAO,IAAI,CAAC,WAAW,+BAA+B;QACtD,MAAM,IAAI,IAAI,CAAC,WAAW;QAC1B,IAAI,GAAG;YACL,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;QACnF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,0BAA0B,QAAQ,EAAE,SAAS,IAAI,kJAAA,CAAA,UAAO,EAAE,EAAE;QAC1D,UAAU,IAAI,CAAC;QACf,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY;IACtE;IAEA;;;GAGC,GACD,QAAQ,SAAS,EAAE,UAAU,EAAE;QAC7B,MAAM,EAAC,cAAc,EAAE,WAAW,EAAC,GAAG,IAAI;QAC1C,IAAI,gBAAgB;YAClB,MAAM,SAAS,eAAe,WAAW;YACzC,MAAM,cAAc,cAAc,yBAAyB;YAC3D,MAAM,OAAO,YAAY,QAAQ;YACjC,MAAM,EAAC,QAAQ,EAAE,EAAE,EAAC,GAAG,KAAK,UAAU;YACtC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,EAAE,IAAK;gBACjC,IAAI,IAAI,MAAM,CAAC,EAAE,GAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;gBACxD,MAAM,IAAI,MAAM,CAAC,EAAE,GAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;gBAC1D,IAAI,IAAI;gBACR,IAAI,aAAa;oBACf,IAAI,cAAc,KAAK,GAAG,CAAC,IAAI,eAAe;oBAC9C,IAAI,KAAK,GAAG,CAAC,IAAI,eAAe;gBAClC;gBACA,SAAS,MAAM,CAAC,GAAG,GAAG,GAAG;YAC3B;YACA,KAAK,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc;YAClD,KAAK,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW;YAC5C,YAAY,WAAW,GAAG,IAAI,CAAC,WAAW;YAC1C,YAAY,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI;YAC9C,UAAU,MAAM,GAAG;YACnB,YAAY,OAAO,CAAC,WAAW;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,SAAS,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI;gBAC1B,WAAW,IAAI,CAAC,SAAS,CAAC,EAAE;YAC9B;QACF;IACF;IAEA,KAAK,MAAM,EAAE;QACX,iGAAiG;QACjG,MAAM,OAAO,IAAI,CAAC,QAAQ;QAC1B,KAAK,CAAC,KAAK;QACX,IAAI,CAAC,QAAQ,GAAG;QAEhB,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;QAC3B;QACA,OAAO,IAAI;IACb;IAEA,QAAQ;QACN,OAAO,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI;IACzC;AACF;AAGA,yDAAyD;AACzD,eAAe,OAAO,CAAC,CAAA;IACrB,MAAM,aAAa,cAAc;IACjC,OAAO,cAAc,CAAC,KAAK,SAAS,EAAE,MAAM;QAC1C;YACE,OAAO,IAAI,CAAC,WAAW;QACzB;QACA,KAAI,KAAK;YACP,IAAI,UAAU,IAAI,CAAC,WAAW,EAAE;gBAC9B,IAAI,CAAC,WAAW,GAAG;gBACnB,IAAI,CAAC,UAAU,GAAG;YACpB;QACF;IACF;AACF;AAEA,MAAM,mBAAmB;IAAE,MAAM;AAAY;AAC7C,MAAM,sBAAsB;IAAE,MAAM;AAAe;AACnD,MAAM,sBAAsB;AAG5B;;;;;;;;;;;;;;;;;;;;;AAqBA,GACA,MAAM,kBAAkB;AAExB,MAAM,WAAW,IAAI,kJAAA,CAAA,OAAI;AACzB,MAAM,cAAc,IAAI,kJAAA,CAAA,QAAK;AAE7B;;;;;;;;;;CAUC,GACD,MAAM,oBAAoB;IACxB,aAAe;QACb,KAAK;QAEL;;;;KAIC,GAED;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC,aAAa,GAAG,CAAC;QAEtB,IAAI,CAAC,eAAe,GAAG,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,KAAK,GAAG;QACtC;IACF;IAEA;;;GAGC,GACD,IAAK,GAAG,OAAO,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,IAAI,OAAO,CAAC,EAAE,YAAY,MAAM;gBAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO;gBACL,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE;YACtB;QACF;QACA,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,OAAQ,GAAG,OAAO,EAAE;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,IAAI,OAAO,CAAC,EAAE,YAAY,MAAM;gBAC9B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC5B,OAAO;gBACL,KAAK,CAAC,OAAO,OAAO,CAAC,EAAE;YACzB;QACF;QACA,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,QAAS,IAAI,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO;YAC5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM;gBACtB,OAAO,CAAC;gBACR,YAAY,CAAC;gBACb,OAAO;YACT;YACA,KAAK,gBAAgB,CAAC,gBAAgB,IAAI,CAAC,eAAe;QAC5D;IACF;IAEA;;GAEC,GACD,WAAY,IAAI,EAAE;QAChB,IAAI,CAAC,YAAY,GAAG;QACpB,KAAK,mBAAmB,CAAC,gBAAgB,IAAI,CAAC,eAAe;QAC7D,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IACvB;IAEA;;GAEC,GACD,sBAAuB,YAAY,EAAE;QACnC,OAAO,0BAA0B;IACnC;IAEA,kBAAmB,KAAK,EAAE;QACxB,KAAK,CAAC,kBAAkB;QACxB,IAAI,CAAC,YAAY;IACnB;IAEA;;GAEC,GACD,eAAgB;QACd,sDAAsD;QACtD,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS;QAChD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG;YACxB,IAAI,KAAK,gBAAgB,EAAE,KAAK,YAAY,IAAI,sBAAsB;YACtE,SAAS,IAAI,CAAC,KAAK,QAAQ,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK,MAAM;YACjE,KAAK,KAAK,CAAC;QACb;QACA,KAAK,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc;IACrD;IAEA,cAAc,GACd,aAAa;QACX,0CAA0C;QAC1C,KAAK,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAI;YACvC,IAAI,OAAO,UAAU,IAAI,OAAO;QAClC;QACA,OAAO;IACT;IAEA;;;GAGC,GACD,kBAAmB,QAAQ,EAAE;QAC3B,MAAM,YAAY,SAAS,qBAAqB;QAChD,SAAS,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG;QAE3C,2CAA2C;QAC3C,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,YAAY,YAAY,OAAO;QAChE,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG;QACxE,IAAI,CAAC,WAAW,eAAe,QAAQ,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YACxD,0CAA0C;YAC1C,IAAI,SAAS,QAAQ,OAAO;YAC5B,MAAM,QAAQ,KAAK,GAAG,CAAC,aAAa,GAAG;YACvC,UAAU,IAAI,CAAC,aAAa,CAAC,YAAY,YAAY,OAAO,GAAG,IAAI,kJAAA,CAAA,cAAW,CAC5E,IAAI,aAAa,aACjB,OACA,aAAa,IAAI,OACjB,kJAAA,CAAA,aAAU,EACV,kJAAA,CAAA,YAAS;QAEb;QAEA,MAAM,UAAU,QAAQ,KAAK,CAAC,IAAI;QAClC,MAAM,aAAa,CAAC,OAAO;YACzB,IAAI,UAAU,OAAO,CAAC,MAAM,EAAE;gBAC5B,OAAO,CAAC,MAAM,GAAG;gBACjB,QAAQ,WAAW,GAAG;YACxB;QACF;QACA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACvC,IAAI,QAAQ,CAAC,GAAG;gBACd,MAAM,aAAa,QAAQ;gBAE3B,SAAS;gBACT,MAAM,SAAS,KAAK,MAAM,CAAC,QAAQ;gBACnC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;oBAC3B,WAAW,aAAa,GAAG,MAAM,CAAC,EAAE;gBACtC;gBAEA,4EAA4E;gBAC5E,6EAA6E;gBAC7E,KAAK,iBAAiB,CAAC;gBACvB,MAAM,EACJ,kBAAkB,EAClB,eAAe,EACf,qBAAqB,EACrB,iBAAiB,EACjB,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,SAAS,QAAQ;gBAErB,sBAAsB;gBACtB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,WAAW,aAAa,KAAK,GAAG,mBAAmB,KAAK,CAAC,YAAY,CAAC;gBACxE;gBAEA,YAAY;gBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,WAAW,aAAa,KAAK,GAAG,gBAAgB,KAAK,CAAC,YAAY,CAAC;gBACrE;gBAEA,QAAQ;gBACR,IAAI,QAAQ,YAAa,KAAK,YAAY,IAAI,IAAK,KAAK,KAAK;gBAC7D,IAAI,SAAS,MAAM,QAAQ,IAAI,CAAC,KAAK;gBACrC,IAAI,SAAS,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK;gBAC9C,IAAI,SAAS,MAAM,QAAQ;gBAC3B,WAAW,aAAa,IAAI,YAAY,GAAG,CAAC,OAAO,MAAM;gBAEzD,iCAAiC;gBACjC,WAAW,aAAa,IAAI,mBAAmB,KAAK;gBAEpD,eAAe;gBACf,WAAW,aAAa,IAAI,mBAAmB,KAAK;gBAEpD,IAAI,WAAW;oBACb,qBAAqB;oBACrB,WAAW,aAAa,IAAI,sBAAsB,KAAK,CAAC,CAAC;oBACzD,WAAW,aAAa,IAAI,sBAAsB,KAAK,CAAC,CAAC;oBACzD,WAAW,aAAa,IAAI,kBAAkB,KAAK;oBACnD,WAAW,aAAa,IAAI,kBAAkB,KAAK;gBACrD,OAAO;oBACL,oBAAoB;oBACpB,WAAW,aAAa,IAAI,mBAAmB,KAAK;oBACpD,WAAW,aAAa,IAAI,YAAY,GAAG,CAAC,mBAAmB,KAAK,EAAE,MAAM;oBAC5E,WAAW,aAAa,IAAI,qBAAqB,KAAK;gBACxD;YACF;QACF;QACA,SAAS,gBAAgB,CAAC;QAE1B,wCAAwC;QACxC,KAAK,CAAC,kBAAkB;IAC1B;IAEA,KAAM,QAAQ,EAAE;QACd,iFAAiF;QAEjF,2CAA2C;QAC3C,IAAI,eAAe,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG;QAC5C,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,aAAa;YAClC,IAAI,YAAY,KAAK,IAAI,KAAK,UAAU,EAAE;gBACxC,YAAY,KAAK,GAAG;gBACpB,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAA;oBACrD,IAAI,KAAK,UAAU,EAAE;wBACnB,KAAK,IAAI,CAAC;oBACZ,OAAO;wBACL;oBACF;gBACF;YACF;QACF;QAEA,4EAA4E;QAC5E,IAAI,cAAc;YAChB,IAAI,CAAC,aAAa,CAAC;YAEnB,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC;gBAC7B,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI;gBACzB,MAAM,oBAAoB,SAAS,UAAU;gBAC7C,IAAI,gBAAgB,iBAAiB,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,oBAAoB,CAAC,KAAK,IAAI,IAAI,YAAY;gBAC9H,IAAI,sBAAsB,iBAAiB,CAAC,mBAAmB,IAAI,iBAAiB,CAAC,mBAAmB,CAAC,KAAK,IAAI,IAAI,aAAa;gBACnI,IAAI,qBAAqB,iBAAiB,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,oBAAoB,CAAC,KAAK,IAAI,IAAI,aAAa;gBAEpI,4EAA4E;gBAC5E,IAAI,kBAAkB;gBACtB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,cAAc,EAAE;oBACpD,IAAI,gBAAgB;wBAClB,mBAAmB,eAAe,iBAAiB,CAAC,MAAM;wBAC1D,IAAI,CAAC,eAAe,GAAG,gBAAgB,mCAAmC;oBAC5E;gBACF;gBACA,IAAI,oBAAoB,cAAc,MAAM,EAAE;oBAC5C,gBAAgB,eAAe,eAAe;oBAC9C,sBAAsB,eAAe,qBAAqB;oBAC1D,qBAAqB,eAAe,oBAAoB,kBAAkB;gBAC5E;gBAEA,wBAAwB;gBACxB,IAAI,cAAc;gBAClB,IAAI,aAAa;gBACjB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,cAAc,EAAE;oBACpD,IAAI,gBAAgB;wBAClB,MAAM,aAAa,eAAe,iBAAiB,CAAC,MAAM;wBAC1D,cAAc,IAAI,CAAC,aAAa,YAAY,aAAa;wBAEzD,mFAAmF;wBACnF,oBAAoB,GAAG,CAAC,eAAe,iBAAiB,EAAE,YAAY,aAAa;wBACnF,mBAAmB,GAAG,CAAC,eAAe,WAAW,EAAE,aAAa,GAAG,CAAC,aAAa,UAAU,IAAI;wBAE/F,cAAc;wBACd,YAAY,KAAK,GAAG;oBACtB;gBACF;gBAEA,iCAAiC;gBACjC,SAAS,mBAAmB,CAAC,qBAAqB,eAAe;gBACjE,SAAS,YAAY,CAAC,qBAAqB,QAAQ,CAAC,kJAAA,CAAA,mBAAgB;gBACpE,SAAS,mBAAmB,CAAC,oBAAoB,qBAAqB;gBACtE,SAAS,mBAAmB,CAAC,qBAAqB,oBAAoB;gBAEtE,IAAI,CAAC,YAAY;gBAEjB,IAAI,CAAC,aAAa,CAAC;gBACnB,IAAI,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,KAAM,MAAM,EAAE;QACZ,IAAI,kBAAkB,aAAa;YACjC,KAAK,CAAC,KAAK;YACX,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,OAAS,IAAI,CAAC,UAAU,CAAC;YACnD,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,OAAS,IAAI,CAAC,OAAO,CAAC;QACpD;QACA,OAAO,IAAI;IACb;IAEA,UAAW;QACT,KAAK,CAAC;QACN,OAAO,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA,MAAO,IAAI,OAAO;IAC9D;AACF;AAEA,SAAS,eAAgB,MAAM,EAAE,SAAS;IACxC,MAAM,OAAO,IAAI,OAAO,WAAW,CAAC;IACpC,KAAK,GAAG,CAAC,OAAO,QAAQ,CAAC,GAAG;IAC5B,OAAO;AACT;AAEA,SAAS,0BAA2B,YAAY;IAC9C,MAAM,iBAAiB;IACvB,MAAM,qBAAqB;IAE3B,8EAA8E;IAC9E,2EAA2E;IAC3E,qEAAqE;IAErE,gCAAgC;IAChC,IAAI,gBAAgB,CAAA,GAAA,sLAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;QACtD,SAAS;QACT,UAAU;YACR,CAAC,mBAAmB,EAAE;gBAAE,OAAO,IAAI,kJAAA,CAAA,UAAO;YAAG;YAC7C,CAAC,eAAe,EAAE;gBAAE,OAAO;YAAK;QAClC;QACA,gBAAgB;QAChB,YAAY,CAAC;8BACa,EAAE,eAAe;mBAC5B,EAAE,mBAAmB;sBAClB,EAAE,oBAAoB;;;kBAG1B,EAAE,oBAAoB,GAAG,EAAE,gBAAgB,OAAO,CAAC,GAAG;kBACtD,EAAE,mBAAmB;oEAC6B,EAAE,mBAAmB;yBAChE,EAAE,eAAe;;IAEtC,CAAC;QACD,kDAAkD;QAClD,iBAAiB,CAAC;;;;;;;;IAQlB,CAAC;IACH;IAEA,uBAAuB;IACvB,gBAAgB,0BAA0B;IAE1C,yDAAyD;IACzD,gBAAgB,CAAA,GAAA,sLAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe;QACnD,SAAS;QACT,UAAU;YACR,kBAAkB;gBAAC,OAAO;YAAK;QACjC;QACA,gBAAe,OAAO;YACpB,gDAAgD;YAChD,MAAM,kBAAkB;gBACtB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB,OAAO,CAAC,CAAA;gBACtB,UAAU,iBAAiB,SAAS;YACtC;YACA,OAAO;QACT;QACA,gBAAgB;QAChB,YAAY,CAAC;;;;;IAKb,CAAC;QACD,kDAAkD;QAClD,iBAAiB,CAAC;;;;;;;;;;;;;;;;;;;;;;;IAuBlB,CAAC;IACH;IAEA,cAAc,gBAAgB,GAAG,CAAC;QAChC,cAAc,QAAQ,CAAC,eAAe,CAAC,KAAK,GAAG;QAC/C,cAAc,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,KAAK,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,MAAM;IAChG;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,EAAC,YAAY,EAAE,cAAc,EAAC,EAAE,WAAW,EAAE,cAAc,WAAW;IAC9F,MAAM,YAAY,IAAI,OAAO,CAAC,4CAA4C,EAAE,YAAY,GAAG,CAAC;IAE5F,IAAI;IACJ,IAAI,qBAAqB;IACzB,iBAAiB,eAAe,OAAO,CAAC,WAAW,CAAC,IAAI;QACtD,qBAAqB;QACrB,OAAO,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,EAAE,aAAa;IAC9C;IAEA,IAAI,mBAAmB;IACvB,eAAe,aAAa,OAAO,CAAC,WAAW,CAAC,GAAG;QACjD,mBAAmB;QACnB,OAAO,GAAG,qBAAqB,YAAY,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,aAAa;IAC7E;IACA,IAAI,CAAC,kBAAkB;QACrB,eAAe,GAAG,qBAAqB,YAAY,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY,GAAG,EAAE,cAAc;IAClG;IACA,OAAO;QAAC;QAAc;IAAc;AACtC;AAEA,0EAA0E;AAE1E;;;;;;;CAOC,GAED;;;;;;CAMC,GACD,SAAS,gBAAgB,cAAc,EAAE,CAAC,EAAE,CAAC;IAC3C,IAAI,eAAe;IACnB,MAAM,OAAO,iBAAiB;IAE9B,8BAA8B;IAC9B,IAAI,aAAa;IACjB,KAAK,OAAO,CAAC,CAAA;QACX,IAAI,CAAC,cAAc,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,MAAM,IAAI,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,WAAW,MAAM,IAAI,IAAI;YACpH,aAAa;QACf;IACF;IAEA,+CAA+C;IAC/C,WAAW,MAAM,CAAC,OAAO,CAAC,CAAA;QACxB,IAAI,CAAC,gBAAgB,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,aAAa,CAAC,GAAG;YACzE,eAAe;QACjB;IACF;IACA,OAAO;AACT;AAGA,MAAM,cAAc,IAAI;AAExB;;;;;;;CAOC,GACD,SAAS,kBAAkB,cAAc,EAAE,KAAK,EAAE,GAAG;IACnD,IAAI;IACJ,IAAI,gBAAgB;QAClB,2EAA2E;QAC3E,IAAI,aAAa,YAAY,GAAG,CAAC;QACjC,IAAI,cAAc,WAAW,KAAK,KAAK,SAAS,WAAW,GAAG,KAAK,KAAK;YACtE,OAAO,WAAW,KAAK;QACzB;QAEA,MAAM,EAAC,cAAc,EAAC,GAAG;QAEzB,YAAY;QACZ,IAAI,MAAM,OAAO;YACf,MAAM,IAAI;YACV,QAAQ;YACR,MAAM;QACR;QACA,QAAQ,KAAK,GAAG,CAAC,OAAO;QACxB,MAAM,KAAK,GAAG,CAAC,KAAK,eAAe,MAAM,GAAG;QAE5C,2FAA2F;QAC3F,kEAAkE;QAClE,QAAQ,EAAE;QACV,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,IAAK;YAChC,MAAM,KAAK,cAAc,CAAC,IAAI,EAAE;YAChC,MAAM,KAAK,cAAc,CAAC,IAAI,IAAI,EAAE;YACpC,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI;YAC1B,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI;YAC3B,MAAM,SAAS,cAAc,CAAC,IAAI,IAAI,EAAE;YACxC,MAAM,MAAM,cAAc,CAAC,IAAI,IAAI,EAAE;YACrC,IAAI,CAAC,eAAe,WAAW,YAAY,MAAM,IAAI,QAAQ,YAAY,GAAG,IAAI,OAAO,YAAY,KAAK,IAAI,QAAQ,YAAY,IAAI,EAAE;gBACpI,cAAc;oBACZ,MAAM;oBACN,OAAO,CAAC;oBACR;oBACA;gBACF;gBACA,MAAM,IAAI,CAAC;YACb;YACA,YAAY,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,YAAY,IAAI;YAClD,YAAY,KAAK,GAAG,KAAK,GAAG,CAAC,OAAO,YAAY,KAAK;QACvD;QAEA,uEAAuE;QACvE,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI;QAC3D,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,MAAM,GAAI;YACvC,MAAM,QAAQ,KAAK,CAAC,EAAE;YACtB,MAAM,QAAQ,KAAK,CAAC,IAAI,EAAE;YAC1B,IAAI,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,IAAI,EAAE;gBACtH,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,IAAI,EAAE,MAAM,IAAI;gBAC5C,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,EAAE,MAAM,KAAK;gBAC/C,MAAM,MAAM,CAAC,GAAG;YAClB;QACF;QAEA,YAAY,GAAG,CAAC,gBAAgB;YAAC;YAAO;YAAK;QAAK;IACpD;IACA,OAAO;AACT;AAEA,MAAM,oBAAoB,IAAI;AAE9B;;;;;CAKC,GACD,SAAS,iBAAiB,cAAc;IACtC,6DAA6D;IAC7D,IAAI,OAAO,kBAAkB,GAAG,CAAC;IACjC,IAAI,CAAC,MAAM;QACT,OAAO,EAAE;QACT,MAAM,EAAC,cAAc,EAAC,GAAG;QACzB,IAAI;QAEJ,MAAM,aAAa,CAAC,GAAG,QAAQ,KAAK;YAClC,8CAA8C;YAC9C,IAAI,CAAC,UAAW,MAAM,CAAC,OAAO,GAAG,GAAG,OAAO,MAAM,IAAI,GAAI;gBACvD,KAAK,IAAI,CAAC,SAAS;oBAAC;oBAAQ;oBAAK,QAAQ,EAAE;gBAAA;YAC7C;YACA,sCAAsC;YACtC,IAAI,MAAM,OAAO,GAAG,EAAE,OAAO,GAAG,GAAG;YACnC,IAAI,SAAS,OAAO,MAAM,EAAE,OAAO,MAAM,GAAG;YAC5C,OAAO,MAAM,CAAC,IAAI,CAAC;gBACjB;gBACA,GAAG;gBACH,QAAQ,MAAM;gBACd;YACF;QACF;QAEA,IAAI,IAAI;QACR,MAAO,IAAI,eAAe,MAAM,EAAE,KAAK,EAAG;YACxC,WAAW,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,IAAI,EAAE,EAAE,IAAI;QAClF;QACA,0CAA0C;QAC1C,WAAW,cAAc,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,IAAI,EAAE,EAAE,IAAI;IACtF;IACA,kBAAkB,GAAG,CAAC,gBAAgB;IACtC,OAAO;AACT", "ignoreList": [0], "debugId": null}}]}
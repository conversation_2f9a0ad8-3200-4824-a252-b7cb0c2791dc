'use client';

import { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Box, RoundedBox, Cylinder, Sphere } from '@react-three/drei';
import { Mesh, Group } from 'three';
import { useTheme } from '@/hooks/useTheme';

interface PlaceholderModelProps {
  position?: [number, number, number];
  scale?: number;
  rotation?: [number, number, number];
  animated?: boolean;
  glowEffect?: boolean;
  variant?: 'cpu' | 'memory' | 'gpu' | 'motherboard';
  onClick?: () => void;
  onHover?: (hovered: boolean) => void;
}

// CPU Model
export function CPUModel({
  position = [0, 0, 0],
  scale = 1,
  rotation = [0, 0, 0],
  animated = true,
  glowEffect = true,
  onClick,
  onHover,
}: PlaceholderModelProps) {
  const groupRef = useRef<Group>(null);
  const [hovered, setHovered] = useState(false);
  const { themeConfig } = useTheme();

  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;
    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.05;
  });

  const handlePointerOver = () => {
    setHovered(true);
    onHover?.(true);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    setHovered(false);
    onHover?.(false);
    document.body.style.cursor = 'auto';
  };

  return (
    <group
      ref={groupRef}
      position={position}
      scale={scale}
      rotation={rotation}
      onClick={onClick}
      onPointerOver={handlePointerOver}
      onPointerOut={handlePointerOut}
    >
      {/* CPU Base */}
      <RoundedBox
        args={[2, 0.3, 2]}
        radius={0.05}
        castShadow
        receiveShadow
      >
        <meshStandardMaterial
          color={hovered ? themeConfig.primaryColor : '#2a2a2a'}
          metalness={0.8}
          roughness={0.2}
          emissive={glowEffect ? themeConfig.primaryColor : '#000000'}
          emissiveIntensity={hovered ? 0.3 : 0.1}
        />
      </RoundedBox>

      {/* Heat spreader */}
      <RoundedBox
        args={[1.5, 0.1, 1.5]}
        radius={0.02}
        position={[0, 0.2, 0]}
        castShadow
      >
        <meshStandardMaterial
          color="#c0c0c0"
          metalness={1}
          roughness={0.1}
        />
      </RoundedBox>

      {/* Pins */}
      {Array.from({ length: 64 }, (_, i) => {
        const row = Math.floor(i / 8);
        const col = i % 8;
        const x = -0.875 + (col * 0.25);
        const z = -0.875 + (row * 0.25);
        
        return (
          <Cylinder
            key={i}
            args={[0.02, 0.02, 0.2]}
            position={[x, -0.25, z]}
            castShadow
          >
            <meshStandardMaterial
              color="#ffd700"
              metalness={1}
              roughness={0.1}
            />
          </Cylinder>
        );
      })}

      {/* Glow effect */}
      {glowEffect && hovered && (
        <RoundedBox
          args={[2.2, 0.35, 2.2]}
          radius={0.05}
          position={[0, 0, 0]}
        >
          <meshBasicMaterial
            color={themeConfig.primaryColor}
            transparent
            opacity={0.1}
          />
        </RoundedBox>
      )}
    </group>
  );
}

// Memory Module Model
export function MemoryModel({
  position = [0, 0, 0],
  scale = 1,
  animated = true,
  glowEffect = true,
}: PlaceholderModelProps) {
  const groupRef = useRef<Group>(null);
  const [hovered, setHovered] = useState(false);
  const { themeConfig } = useTheme();

  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
  });

  return (
    <group ref={groupRef} position={position} scale={scale}>
      {/* PCB */}
      <Box
        args={[0.2, 1.5, 4]}
        castShadow
        receiveShadow
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <meshStandardMaterial
          color="#1a4a1a"
          roughness={0.8}
        />
      </Box>

      {/* Memory chips */}
      {Array.from({ length: 8 }, (_, i) => (
        <RoundedBox
          key={i}
          args={[0.15, 0.3, 0.4]}
          radius={0.01}
          position={[0.025, 0, -1.4 + (i * 0.4)]}
          castShadow
        >
          <meshStandardMaterial
            color={hovered ? themeConfig.secondaryColor : '#0a0a0a'}
            emissive={glowEffect ? themeConfig.secondaryColor : '#000000'}
            emissiveIntensity={hovered ? 0.2 : 0.05}
          />
        </RoundedBox>
      ))}

      {/* Connector pins */}
      <Box
        args={[0.05, 0.2, 3.8]}
        position={[0, -0.85, 0]}
        castShadow
      >
        <meshStandardMaterial
          color="#ffd700"
          metalness={1}
          roughness={0.1}
        />
      </Box>
    </group>
  );
}

// GPU Model
export function GPUModel({
  position = [0, 0, 0],
  scale = 1,
  animated = true,
  glowEffect = true,
}: PlaceholderModelProps) {
  const groupRef = useRef<Group>(null);
  const [hovered, setHovered] = useState(false);
  const { themeConfig } = useTheme();

  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    groupRef.current.rotation.y = state.clock.elapsedTime * 0.1;
  });

  return (
    <group ref={groupRef} position={position} scale={scale}>
      {/* GPU PCB */}
      <RoundedBox
        args={[4, 0.2, 2]}
        radius={0.05}
        castShadow
        receiveShadow
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <meshStandardMaterial
          color="#1a1a4a"
          roughness={0.8}
        />
      </RoundedBox>

      {/* GPU Die */}
      <RoundedBox
        args={[1.5, 0.15, 1.5]}
        radius={0.02}
        position={[0, 0.175, 0]}
        castShadow
      >
        <meshStandardMaterial
          color={hovered ? themeConfig.accentColor : '#2a2a2a'}
          metalness={0.8}
          roughness={0.2}
          emissive={glowEffect ? themeConfig.accentColor : '#000000'}
          emissiveIntensity={hovered ? 0.3 : 0.1}
        />
      </RoundedBox>

      {/* Cooling fins */}
      {Array.from({ length: 8 }, (_, i) => (
        <Box
          key={i}
          args={[0.05, 0.5, 1.8]}
          position={[-1.5 + (i * 0.2), 0.35, 0]}
          castShadow
        >
          <meshStandardMaterial
            color="#c0c0c0"
            metalness={0.9}
            roughness={0.1}
          />
        </Box>
      ))}

      {/* Memory modules */}
      {Array.from({ length: 6 }, (_, i) => (
        <RoundedBox
          key={i}
          args={[0.3, 0.1, 0.2]}
          radius={0.01}
          position={[1.2, 0.15, -0.6 + (i * 0.24)]}
          castShadow
        >
          <meshStandardMaterial
            color="#0a0a0a"
            emissive={themeConfig.secondaryColor}
            emissiveIntensity={0.1}
          />
        </RoundedBox>
      ))}
    </group>
  );
}

// Motherboard Model
export function MotherboardModel({
  position = [0, 0, 0],
  scale = 1,
  animated = false,
}: PlaceholderModelProps) {
  const { themeConfig } = useTheme();

  return (
    <group position={position} scale={scale}>
      {/* Main PCB */}
      <RoundedBox
        args={[6, 0.1, 4]}
        radius={0.05}
        castShadow
        receiveShadow
      >
        <meshStandardMaterial
          color="#1a4a1a"
          roughness={0.8}
        />
      </RoundedBox>

      {/* CPU Socket */}
      <RoundedBox
        args={[1.2, 0.05, 1.2]}
        radius={0.02}
        position={[-1, 0.075, 0.5]}
        castShadow
      >
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.5}
        />
      </RoundedBox>

      {/* RAM Slots */}
      {Array.from({ length: 4 }, (_, i) => (
        <Box
          key={i}
          args={[0.15, 0.05, 2]}
          position={[1 + (i * 0.3), 0.075, 0]}
          castShadow
        >
          <meshStandardMaterial
            color="#0a0a0a"
            metalness={0.8}
          />
        </Box>
      ))}

      {/* Circuit traces */}
      {Array.from({ length: 20 }, (_, i) => (
        <Box
          key={i}
          args={[Math.random() * 2 + 1, 0.01, 0.02]}
          position={[
            (Math.random() - 0.5) * 5,
            0.055,
            (Math.random() - 0.5) * 3
          ]}
          castShadow
        >
          <meshStandardMaterial
            color={themeConfig.primaryColor}
            emissive={themeConfig.primaryColor}
            emissiveIntensity={0.1}
          />
        </Box>
      ))}
    </group>
  );
}

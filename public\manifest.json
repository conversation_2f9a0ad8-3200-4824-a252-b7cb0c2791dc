{"name": "3D Portfolio - Interactive Developer Showcase", "short_name": "3D Portfolio", "description": "An immersive 3D portfolio website showcasing cutting-edge web development with interactive microchip-themed design", "start_url": "/", "display": "standalone", "background_color": "#0a0a0a", "theme_color": "#00ff88", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["portfolio", "developer", "3d", "interactive"], "icons": [{"src": "/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "screenshots": [{"src": "/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "3D Portfolio Desktop View"}, {"src": "/screenshot-mobile.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "3D Portfolio Mobile View"}], "shortcuts": [{"name": "About", "short_name": "About", "description": "Learn more about the developer", "url": "/#about", "icons": [{"src": "/icon-192x192.png", "sizes": "192x192"}]}, {"name": "Projects", "short_name": "Projects", "description": "View portfolio projects", "url": "/#projects", "icons": [{"src": "/icon-192x192.png", "sizes": "192x192"}]}, {"name": "Contact", "short_name": "Contact", "description": "Get in touch", "url": "/#contact", "icons": [{"src": "/icon-192x192.png", "sizes": "192x192"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}
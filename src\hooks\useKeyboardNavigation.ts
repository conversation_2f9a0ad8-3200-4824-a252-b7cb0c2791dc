'use client';

import { useEffect, useCallback, useRef } from 'react';

interface KeyboardNavigationOptions {
  enableArrowKeys?: boolean;
  enableTabNavigation?: boolean;
  enableEscapeKey?: boolean;
  enableEnterKey?: boolean;
  enableSpaceKey?: boolean;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onEscape?: () => void;
  onEnter?: () => void;
  onSpace?: () => void;
  onTab?: (direction: 'forward' | 'backward') => void;
}

export function useKeyboardNavigation(options: KeyboardNavigationOptions = {}) {
  const {
    enableArrowKeys = true,
    enableTabNavigation = true,
    enableEscapeKey = true,
    enableEnterKey = true,
    enableSpaceKey = true,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onEscape,
    onEnter,
    onSpace,
    onTab,
  } = options;

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't interfere with form inputs
    if (
      event.target instanceof HTMLInputElement ||
      event.target instanceof HTMLTextAreaElement ||
      event.target instanceof HTMLSelectElement
    ) {
      return;
    }

    switch (event.key) {
      case 'ArrowUp':
        if (enableArrowKeys && onArrowUp) {
          event.preventDefault();
          onArrowUp();
        }
        break;
      case 'ArrowDown':
        if (enableArrowKeys && onArrowDown) {
          event.preventDefault();
          onArrowDown();
        }
        break;
      case 'ArrowLeft':
        if (enableArrowKeys && onArrowLeft) {
          event.preventDefault();
          onArrowLeft();
        }
        break;
      case 'ArrowRight':
        if (enableArrowKeys && onArrowRight) {
          event.preventDefault();
          onArrowRight();
        }
        break;
      case 'Escape':
        if (enableEscapeKey && onEscape) {
          event.preventDefault();
          onEscape();
        }
        break;
      case 'Enter':
        if (enableEnterKey && onEnter) {
          event.preventDefault();
          onEnter();
        }
        break;
      case ' ':
        if (enableSpaceKey && onSpace) {
          event.preventDefault();
          onSpace();
        }
        break;
      case 'Tab':
        if (enableTabNavigation && onTab) {
          event.preventDefault();
          onTab(event.shiftKey ? 'backward' : 'forward');
        }
        break;
    }
  }, [
    enableArrowKeys,
    enableTabNavigation,
    enableEscapeKey,
    enableEnterKey,
    enableSpaceKey,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onEscape,
    onEnter,
    onSpace,
    onTab,
  ]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);
}

// Hook for managing focus within a container
export function useFocusManagement(containerRef: React.RefObject<HTMLElement>) {
  const focusableElements = useRef<HTMLElement[]>([]);
  const currentFocusIndex = useRef(0);

  const updateFocusableElements = useCallback(() => {
    if (!containerRef.current) return;

    const elements = containerRef.current.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    focusableElements.current = Array.from(elements).filter(
      el => !el.disabled && el.offsetParent !== null
    );
  }, [containerRef]);

  const focusNext = useCallback(() => {
    updateFocusableElements();
    if (focusableElements.current.length === 0) return;

    currentFocusIndex.current = (currentFocusIndex.current + 1) % focusableElements.current.length;
    focusableElements.current[currentFocusIndex.current]?.focus();
  }, [updateFocusableElements]);

  const focusPrevious = useCallback(() => {
    updateFocusableElements();
    if (focusableElements.current.length === 0) return;

    currentFocusIndex.current = 
      currentFocusIndex.current === 0 
        ? focusableElements.current.length - 1 
        : currentFocusIndex.current - 1;
    focusableElements.current[currentFocusIndex.current]?.focus();
  }, [updateFocusableElements]);

  const focusFirst = useCallback(() => {
    updateFocusableElements();
    if (focusableElements.current.length === 0) return;

    currentFocusIndex.current = 0;
    focusableElements.current[0]?.focus();
  }, [updateFocusableElements]);

  const focusLast = useCallback(() => {
    updateFocusableElements();
    if (focusableElements.current.length === 0) return;

    currentFocusIndex.current = focusableElements.current.length - 1;
    focusableElements.current[currentFocusIndex.current]?.focus();
  }, [updateFocusableElements]);

  return {
    focusNext,
    focusPrevious,
    focusFirst,
    focusLast,
    updateFocusableElements,
  };
}

// Hook for skip links accessibility
export function useSkipLinks() {
  const skipToContent = useCallback(() => {
    const mainContent = document.getElementById('main-content') || 
                       document.querySelector('main') ||
                       document.querySelector('[role="main"]');
    
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  const skipToNavigation = useCallback(() => {
    const navigation = document.getElementById('navigation') ||
                      document.querySelector('nav') ||
                      document.querySelector('[role="navigation"]');
    
    if (navigation) {
      navigation.focus();
      navigation.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  return {
    skipToContent,
    skipToNavigation,
  };
}

// Hook for managing modal/dialog focus
export function useModalFocus(isOpen: boolean, modalRef: React.RefObject<HTMLElement>) {
  const previousFocusRef = useRef<HTMLElement | null>(null);
  const { focusFirst, updateFocusableElements } = useFocusManagement(modalRef);

  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousFocusRef.current = document.activeElement as HTMLElement;
      
      // Focus the first focusable element in the modal
      setTimeout(() => {
        updateFocusableElements();
        focusFirst();
      }, 100);
      
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else {
      // Restore focus to the previously focused element
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
      
      // Restore body scroll
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen, focusFirst, updateFocusableElements]);

  // Trap focus within modal
  useKeyboardNavigation({
    enableTabNavigation: isOpen,
    onTab: (direction) => {
      if (!modalRef.current) return;
      
      const focusableElements = modalRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as NodeListOf<HTMLElement>;
      
      const elements = Array.from(focusableElements).filter(
        el => !el.disabled && el.offsetParent !== null
      );
      
      if (elements.length === 0) return;
      
      const currentIndex = elements.indexOf(document.activeElement as HTMLElement);
      let nextIndex;
      
      if (direction === 'forward') {
        nextIndex = currentIndex === elements.length - 1 ? 0 : currentIndex + 1;
      } else {
        nextIndex = currentIndex === 0 ? elements.length - 1 : currentIndex - 1;
      }
      
      elements[nextIndex]?.focus();
    },
  });
}

// Hook for announcing changes to screen readers
export function useScreenReaderAnnouncements() {
  const announceRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create announcement element if it doesn't exist
    if (!announceRef.current) {
      const announcer = document.createElement('div');
      announcer.setAttribute('aria-live', 'polite');
      announcer.setAttribute('aria-atomic', 'true');
      announcer.style.position = 'absolute';
      announcer.style.left = '-10000px';
      announcer.style.width = '1px';
      announcer.style.height = '1px';
      announcer.style.overflow = 'hidden';
      document.body.appendChild(announcer);
      announceRef.current = announcer;
    }

    return () => {
      if (announceRef.current) {
        document.body.removeChild(announceRef.current);
        announceRef.current = null;
      }
    };
  }, []);

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (announceRef.current) {
      announceRef.current.setAttribute('aria-live', priority);
      announceRef.current.textContent = message;
      
      // Clear the message after a short delay to allow for re-announcements
      setTimeout(() => {
        if (announceRef.current) {
          announceRef.current.textContent = '';
        }
      }, 1000);
    }
  }, []);

  return { announce };
}

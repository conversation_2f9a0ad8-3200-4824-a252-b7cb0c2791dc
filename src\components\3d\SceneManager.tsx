'use client';

import { Canvas } from '@react-three/fiber';
import { Suspense, useRef, useEffect } from 'react';
import { 
  OrbitControls, 
  Environment, 
  PerspectiveCamera, 
  Stats,
  AdaptiveDpr,
  AdaptiveEvents,
  BakeShadows,
  Preload
} from '@react-three/drei';
import { BaseComponentProps } from '@/types';
import { cn } from '@/lib/utils';
import { useTheme } from '@/hooks/useTheme';

interface SceneManagerProps extends BaseComponentProps {
  cameraPosition?: [number, number, number];
  cameraTarget?: [number, number, number];
  enableControls?: boolean;
  enableEnvironment?: boolean;
  enableStats?: boolean;
  backgroundColor?: string;
  shadows?: boolean;
  antialias?: boolean;
  performance?: {
    min: number;
    max: number;
    debounce: number;
  };
  lighting?: 'default' | 'studio' | 'dramatic' | 'soft';
  postProcessing?: boolean;
}

// Lighting presets
const lightingPresets = {
  default: {
    ambient: { intensity: 0.4, color: '#ffffff' },
    directional: { 
      intensity: 1, 
      position: [10, 10, 5], 
      color: '#ffffff',
      castShadow: true 
    },
    point: { intensity: 0.5, position: [-10, -10, -10], color: '#0088ff' },
  },
  studio: {
    ambient: { intensity: 0.6, color: '#ffffff' },
    directional: { 
      intensity: 0.8, 
      position: [5, 10, 5], 
      color: '#ffffff',
      castShadow: true 
    },
    point: { intensity: 0.3, position: [-5, 5, -5], color: '#00ff88' },
  },
  dramatic: {
    ambient: { intensity: 0.2, color: '#1a1a2e' },
    directional: { 
      intensity: 1.5, 
      position: [15, 15, 10], 
      color: '#ffffff',
      castShadow: true 
    },
    point: { intensity: 0.8, position: [-15, -5, -15], color: '#ff6b00' },
  },
  soft: {
    ambient: { intensity: 0.8, color: '#f0f0f0' },
    directional: { 
      intensity: 0.6, 
      position: [8, 12, 8], 
      color: '#ffffff',
      castShadow: false 
    },
    point: { intensity: 0.2, position: [-8, 8, -8], color: '#0088ff' },
  },
};

function SceneLighting({ preset = 'default' }: { preset: keyof typeof lightingPresets }) {
  const lighting = lightingPresets[preset];
  
  return (
    <>
      <ambientLight 
        intensity={lighting.ambient.intensity} 
        color={lighting.ambient.color} 
      />
      <directionalLight
        position={lighting.directional.position as [number, number, number]}
        intensity={lighting.directional.intensity}
        color={lighting.directional.color}
        castShadow={lighting.directional.castShadow}
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-20}
        shadow-camera-right={20}
        shadow-camera-top={20}
        shadow-camera-bottom={-20}
      />
      <pointLight 
        position={lighting.point.position as [number, number, number]}
        intensity={lighting.point.intensity}
        color={lighting.point.color}
      />
    </>
  );
}

function SceneEffects({
  enableEnvironment,
  backgroundColor,
  theme = 'dark'
}: {
  enableEnvironment?: boolean;
  backgroundColor?: string;
  theme?: 'light' | 'dark';
}) {
  return (
    <>
      {enableEnvironment && (
        <Environment
          preset={theme === 'dark' ? 'night' : 'city'}
          background={false}
          intensity={0.5}
        />
      )}

      {backgroundColor && (
        <color attach="background" args={[backgroundColor]} />
      )}

      {/* Fog for depth */}
      <fog attach="fog" args={[theme === 'dark' ? '#0a0a0a' : '#f0f0f0', 10, 50]} />
    </>
  );
}

export function SceneManager({
  children,
  className,
  cameraPosition = [0, 0, 8],
  cameraTarget = [0, 0, 0],
  enableControls = true,
  enableEnvironment = true,
  enableStats = false,
  backgroundColor,
  shadows = true,
  antialias = true,
  performance = { min: 0.5, max: 1, debounce: 200 },
  lighting = 'default',
  postProcessing = false,
}: SceneManagerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { themeConfig } = useTheme();

  // Performance monitoring
  useEffect(() => {
    if (enableStats && typeof window !== 'undefined') {
      console.log('3D Scene Performance Monitoring Enabled');
    }
  }, [enableStats]);

  return (
    <div className={cn('w-full h-full relative', className)}>
      <Canvas
        ref={canvasRef}
        shadows={shadows}
        gl={{
          antialias,
          alpha: true,
          powerPreference: 'high-performance',
          stencil: false,
          depth: true,
        }}
        performance={performance}
        dpr={[1, 2]}
        camera={{
          position: cameraPosition,
          fov: 75,
          near: 0.1,
          far: 1000,
        }}
      >
        {/* Performance optimizations */}
        <AdaptiveDpr pixelated />
        <AdaptiveEvents />
        {shadows && <BakeShadows />}
        
        {/* Camera setup */}
        <PerspectiveCamera
          makeDefault
          position={cameraPosition}
          fov={75}
          near={0.1}
          far={1000}
        />
        
        {/* Lighting */}
        <SceneLighting preset={lighting} />
        
        {/* Scene effects */}
        <SceneEffects
          enableEnvironment={enableEnvironment}
          backgroundColor={backgroundColor}
          theme={themeConfig.theme}
        />
        
        {/* Controls */}
        {enableControls && (
          <OrbitControls
            target={cameraTarget}
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={20}
            maxPolarAngle={Math.PI / 2}
            enableDamping={true}
            dampingFactor={0.05}
          />
        )}
        
        {/* Performance stats */}
        {enableStats && <Stats />}
        
        {/* Scene content */}
        <Suspense fallback={null}>
          {children}
        </Suspense>
        
        {/* Preload common assets */}
        <Preload all />
      </Canvas>
      
      {/* Performance indicator */}
      {enableStats && (
        <div className="absolute top-4 left-4 bg-black/50 text-white p-2 rounded text-xs">
          <div>Theme: {themeConfig.theme}</div>
          <div>Shadows: {shadows ? 'On' : 'Off'}</div>
          <div>Lighting: {lighting}</div>
        </div>
      )}
    </div>
  );
}

// Specialized scene for homepage hero
export function HeroScene({ children, ...props }: SceneManagerProps) {
  return (
    <SceneManager
      lighting="dramatic"
      enableEnvironment={true}
      shadows={true}
      enableStats={false}
      performance={{ min: 0.8, max: 1, debounce: 100 }}
      {...props}
    >
      {children}
    </SceneManager>
  );
}

// Specialized scene for project showcase
export function ProjectScene({ children, ...props }: SceneManagerProps) {
  return (
    <SceneManager
      lighting="studio"
      enableEnvironment={false}
      shadows={true}
      enableStats={false}
      performance={{ min: 0.6, max: 1, debounce: 150 }}
      {...props}
    >
      {children}
    </SceneManager>
  );
}

// Specialized scene for about section
export function AboutScene({ children, ...props }: SceneManagerProps) {
  return (
    <SceneManager
      lighting="soft"
      enableEnvironment={true}
      shadows={false}
      enableStats={false}
      performance={{ min: 0.5, max: 1, debounce: 200 }}
      {...props}
    >
      {children}
    </SceneManager>
  );
}

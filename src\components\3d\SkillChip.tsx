'use client';

import { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { RoundedBox, Text, Html } from '@react-three/drei';
import { Mesh, Group } from 'three';
import { useTheme } from '@/hooks/useTheme';
import { Skill } from '@/types';

interface SkillChipProps {
  skill: Skill;
  position?: [number, number, number];
  scale?: number;
  rotation?: [number, number, number];
  animated?: boolean;
  glowEffect?: boolean;
  onClick?: (skill: Skill) => void;
  onHover?: (skill: Skill, hovered: boolean) => void;
}

export function SkillChip({
  skill,
  position = [0, 0, 0],
  scale = 1,
  rotation = [0, 0, 0],
  animated = true,
  glowEffect = true,
  onClick,
  onHover,
}: SkillChipProps) {
  const groupRef = useRef<Group>(null);
  const chipRef = useRef<Mesh>(null);
  const [hovered, setHovered] = useState(false);
  const [clicked, setClicked] = useState(false);
  const { themeConfig } = useTheme();

  // Get color based on skill category
  const getCategoryColor = (category: string) => {
    const colors = {
      frontend: themeConfig.primaryColor,
      backend: themeConfig.secondaryColor,
      database: themeConfig.accentColor,
      devops: '#9333ea',
      mobile: '#06b6d4',
      'ai-ml': '#f59e0b',
      tools: '#10b981',
      'soft-skills': '#ef4444',
    };
    return colors[category as keyof typeof colors] || themeConfig.primaryColor;
  };

  // Get skill level intensity
  const getLevelIntensity = (level: string) => {
    const intensities = {
      beginner: 0.3,
      intermediate: 0.5,
      advanced: 0.7,
      expert: 1.0,
    };
    return intensities[level as keyof typeof intensities] || 0.5;
  };

  const categoryColor = getCategoryColor(skill.category);
  const levelIntensity = getLevelIntensity(skill.level);

  // Animation loop
  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    
    // Gentle floating animation
    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2 + position[0]) * 0.1;
    
    // Rotation based on skill level
    if (skill.level === 'expert') {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.5;
    } else if (skill.level === 'advanced') {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.3;
    }
    
    // Hover and click effects
    if (chipRef.current) {
      const targetScale = clicked ? 1.2 : (hovered ? 1.1 : 1);
      chipRef.current.scale.lerp({ x: targetScale, y: targetScale, z: targetScale } as any, 0.1);
    }
  });

  const handlePointerOver = () => {
    setHovered(true);
    onHover?.(skill, true);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    setHovered(false);
    onHover?.(skill, false);
    document.body.style.cursor = 'auto';
  };

  const handleClick = () => {
    setClicked(!clicked);
    onClick?.(skill);
  };

  return (
    <group
      ref={groupRef}
      position={position}
      scale={scale}
      rotation={rotation}
      onClick={handleClick}
      onPointerOver={handlePointerOver}
      onPointerOut={handlePointerOut}
    >
      {/* Main chip body */}
      <RoundedBox
        ref={chipRef}
        args={[2, 0.3, 1]}
        radius={0.1}
        castShadow
        receiveShadow
      >
        <meshStandardMaterial
          color={hovered ? categoryColor : '#2a2a2a'}
          metalness={0.8}
          roughness={0.2}
          emissive={glowEffect ? categoryColor : '#000000'}
          emissiveIntensity={(hovered ? 0.3 : 0.1) * levelIntensity}
        />
      </RoundedBox>

      {/* Skill level indicators */}
      {Array.from({ length: 4 }, (_, i) => (
        <RoundedBox
          key={`level-${i}`}
          args={[0.15, 0.05, 0.8]}
          radius={0.02}
          position={[-0.6 + (i * 0.4), 0.18, 0]}
          castShadow
        >
          <meshStandardMaterial
            color={i < ['beginner', 'intermediate', 'advanced', 'expert'].indexOf(skill.level) + 1 ? categoryColor : '#555555'}
            emissive={i < ['beginner', 'intermediate', 'advanced', 'expert'].indexOf(skill.level) + 1 ? categoryColor : '#000000'}
            emissiveIntensity={0.2}
          />
        </RoundedBox>
      ))}

      {/* Years of experience indicator */}
      <RoundedBox
        args={[0.3, 0.1, 0.3]}
        radius={0.05}
        position={[0.7, 0.2, 0]}
        castShadow
      >
        <meshStandardMaterial
          color="#ffd700"
          metalness={1}
          roughness={0.1}
        />
      </RoundedBox>

      {/* Skill name text */}
      <Text
        position={[0, 0, 0.16]}
        fontSize={0.15}
        color={hovered ? '#ffffff' : categoryColor}
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-bold.woff"
        maxWidth={1.8}
      >
        {skill.name}
      </Text>

      {/* Years text */}
      <Text
        position={[0.7, 0.2, 0.16]}
        fontSize={0.08}
        color="#000000"
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-bold.woff"
      >
        {skill.yearsOfExperience}Y
      </Text>

      {/* Detailed info popup when clicked */}
      {clicked && (
        <Html
          position={[0, 0.8, 0]}
          center
          distanceFactor={8}
        >
          <div className="bg-bg-secondary/95 backdrop-blur-md border border-primary/30 rounded-lg p-4 max-w-xs">
            <h3 className="text-lg font-bold text-primary mb-2">{skill.name}</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-text-secondary">Category:</span>
                <span className="text-text-primary capitalize">{skill.category.replace('-', ' ')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Level:</span>
                <span className="text-text-primary capitalize">{skill.level}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Experience:</span>
                <span className="text-text-primary">{skill.yearsOfExperience} years</span>
              </div>
              {skill.description && (
                <div className="mt-3 pt-2 border-t border-primary/20">
                  <p className="text-text-secondary text-xs">{skill.description}</p>
                </div>
              )}
            </div>
          </div>
        </Html>
      )}

      {/* Glow effect */}
      {glowEffect && hovered && (
        <RoundedBox
          args={[2.2, 0.35, 1.2]}
          radius={0.1}
          position={[0, 0, 0]}
        >
          <meshBasicMaterial
            color={categoryColor}
            transparent
            opacity={0.1}
          />
        </RoundedBox>
      )}
    </group>
  );
}

// Skill grid component
export function SkillGrid({
  skills,
  position = [0, 0, 0],
  spacing = 2.5,
  animated = true,
}: {
  skills: Skill[];
  position?: [number, number, number];
  spacing?: number;
  animated?: boolean;
}) {
  const groupRef = useRef<Group>(null);
  const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null);

  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;
  });

  const handleSkillClick = (skill: Skill) => {
    setSelectedSkill(selectedSkill?.id === skill.id ? null : skill);
  };

  // Arrange skills in a grid
  const rows = Math.ceil(Math.sqrt(skills.length));
  const cols = Math.ceil(skills.length / rows);

  return (
    <group ref={groupRef} position={position}>
      {skills.map((skill, index) => {
        const row = Math.floor(index / cols);
        const col = index % cols;
        const x = (col - (cols - 1) / 2) * spacing;
        const z = (row - (rows - 1) / 2) * spacing;
        
        return (
          <SkillChip
            key={skill.id}
            skill={skill}
            position={[x, 0, z]}
            scale={selectedSkill?.id === skill.id ? 1.2 : 1}
            animated={animated}
            onClick={handleSkillClick}
          />
        );
      })}
    </group>
  );
}

// Floating skill constellation
export function SkillConstellation({
  skills,
  position = [0, 0, 0],
  radius = 4,
}: {
  skills: Skill[];
  position?: [number, number, number];
  radius?: number;
}) {
  const groupRef = useRef<Group>(null);

  useFrame((state) => {
    if (!groupRef.current) return;
    groupRef.current.rotation.y = state.clock.elapsedTime * 0.1;
  });

  return (
    <group ref={groupRef} position={position}>
      {skills.map((skill, index) => {
        const angle = (index / skills.length) * Math.PI * 2;
        const height = (Math.random() - 0.5) * 2;
        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;
        
        return (
          <SkillChip
            key={skill.id}
            skill={skill}
            position={[x, height, z]}
            scale={0.8}
            rotation={[0, -angle, 0]}
            animated={true}
          />
        );
      })}
    </group>
  );
}

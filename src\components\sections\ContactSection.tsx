'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ProjectScene } from '@/components/3d/SceneManager';
import { CircuitBoard } from '@/components/3d/CircuitBoard';
import { ContactForm } from '@/components/ui/ContactForm';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/hooks/useTheme';
import { useAdaptiveQuality } from '@/hooks/usePerformance';
import { cn, copyToClipboard } from '@/lib/utils';
import { ContactFormData } from '@/types';

interface ContactSectionProps {
  className?: string;
}

const socialLinks = [
  {
    id: 'email',
    label: 'Email',
    value: '<EMAIL>',
    icon: '📧',
    color: '#ff6b00',
    action: () => window.open('mailto:<EMAIL>')
  },
  {
    id: 'linkedin',
    label: 'LinkedIn',
    value: '/in/developer',
    icon: '💼',
    color: '#0077b5',
    action: () => window.open('https://linkedin.com/in/developer', '_blank')
  },
  {
    id: 'github',
    label: 'GitHub',
    value: '/developer',
    icon: '💻',
    color: '#333333',
    action: () => window.open('https://github.com/developer', '_blank')
  },
  {
    id: 'twitter',
    label: 'Twitter',
    value: '@developer',
    icon: '🐦',
    color: '#1da1f2',
    action: () => window.open('https://twitter.com/developer', '_blank')
  },
  {
    id: 'phone',
    label: 'Phone',
    value: '+****************',
    icon: '📱',
    color: '#10b981',
    action: () => copyToClipboard('+****************')
  },
  {
    id: 'location',
    label: 'Location',
    value: 'San Francisco, CA',
    icon: '📍',
    color: '#ef4444',
    action: () => window.open('https://maps.google.com/?q=San Francisco, CA', '_blank')
  }
];

export function ContactSection({ className }: ContactSectionProps) {
  const { themeConfig } = useTheme();
  const qualitySettings = useAdaptiveQuality();
  const [activeContact, setActiveContact] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);

  const handleContactClick = (type: string) => {
    const contact = socialLinks.find(link => link.id === type);
    if (contact) {
      setActiveContact(type);
      contact.action();
      
      // Show feedback
      setTimeout(() => setActiveContact(null), 1000);
    }
  };

  const handleFormSubmit = async (data: ContactFormData) => {
    // In a real app, this would send to your backend/email service
    console.log('Contact form submitted:', data);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // You could integrate with services like:
    // - EmailJS
    // - Netlify Forms
    // - Your own backend API
    // - Third-party form services
  };

  return (
    <section id="contact" className={cn(
      "min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-tertiary",
      "relative overflow-hidden py-20",
      className
    )}>
      {/* Background Pattern */}
      <div className="absolute inset-0 circuit-bg opacity-10" />
      
      <div className="relative z-10 max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-primary mb-6">
            Let's Connect
          </h2>
          <p className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto">
            Ready to bring your ideas to life? Let's collaborate and create something extraordinary together.
          </p>
        </motion.div>

        {/* Contact Method Toggle */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex justify-center mb-12"
        >
          <div className="flex bg-bg-primary/50 rounded-lg p-1 chip-border">
            <Button
              variant={!showForm ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setShowForm(false)}
            >
              🔌 Quick Connect
            </Button>
            <Button
              variant={showForm ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setShowForm(true)}
            >
              📝 Send Message
            </Button>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* 3D Circuit Board */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="relative h-96"
          >
            <ProjectScene
              enableControls={true}
              enableEnvironment={false}
              shadows={qualitySettings.shadows}
              antialias={qualitySettings.antialias}
              lighting="dramatic"
              cameraPosition={[0, 0, 6]}
            >
              <CircuitBoard
                position={[0, 0, 0]}
                scale={1}
                animated={true}
                interactive={true}
                onContactClick={handleContactClick}
              />
            </ProjectScene>
            
            {/* Interaction Hint */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2 }}
              className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center"
            >
              <p className="text-sm text-text-muted bg-bg-primary/80 backdrop-blur-sm rounded-lg px-3 py-1">
                Click on the contact points to connect!
              </p>
            </motion.div>
          </motion.div>

          {/* Contact Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {!showForm ? (
              /* Quick Contact Links */
              <div className="space-y-6">
                <div>
                  <h3 className="text-2xl font-bold text-secondary mb-4">
                    Get In Touch Instantly
                  </h3>
                  <p className="text-text-secondary mb-6">
                    Choose your preferred way to connect. I'm always excited to discuss new projects and opportunities.
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {socialLinks.map((link, index) => (
                    <motion.div
                      key={link.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        variant={activeContact === link.id ? 'primary' : 'ghost'}
                        size="lg"
                        fullWidth
                        onClick={link.action}
                        className={cn(
                          "justify-start p-4 h-auto chip-border",
                          "bg-bg-secondary/50 hover:bg-bg-secondary/80",
                          "transition-all duration-300"
                        )}
                      >
                        <div className="flex items-center space-x-3">
                          <span 
                            className="text-2xl"
                            style={{ color: link.color }}
                          >
                            {link.icon}
                          </span>
                          <div className="text-left">
                            <p className="font-bold text-text-primary">{link.label}</p>
                            <p className="text-sm text-text-secondary">{link.value}</p>
                          </div>
                        </div>
                      </Button>
                    </motion.div>
                  ))}
                </div>

                {/* Call to Action */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 chip-border"
                >
                  <h4 className="text-lg font-bold text-primary mb-2">
                    Ready to Start a Project?
                  </h4>
                  <p className="text-text-secondary mb-4">
                    I'm currently available for freelance work and exciting opportunities. 
                    Let's discuss how we can bring your vision to life!
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="primary"
                      onClick={() => setShowForm(true)}
                      className="group"
                    >
                      <span className="group-hover:scale-110 transition-transform">
                        📝 Send Detailed Message
                      </span>
                    </Button>
                    <Button
                      variant="secondary"
                      onClick={() => window.open('/resume.pdf', '_blank')}
                      className="group"
                    >
                      <span className="group-hover:scale-110 transition-transform">
                        📄 Download Resume
                      </span>
                    </Button>
                  </div>
                </motion.div>
              </div>
            ) : (
              /* Contact Form */
              <ContactForm onSubmit={handleFormSubmit} />
            )}
          </motion.div>
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="chip-border p-6 bg-bg-secondary/30">
              <div className="text-3xl mb-3">⚡</div>
              <h4 className="text-lg font-bold text-primary mb-2">Fast Response</h4>
              <p className="text-text-secondary text-sm">
                I typically respond to messages within 24 hours
              </p>
            </div>
            <div className="chip-border p-6 bg-bg-secondary/30">
              <div className="text-3xl mb-3">🌍</div>
              <h4 className="text-lg font-bold text-secondary mb-2">Remote Friendly</h4>
              <p className="text-text-secondary text-sm">
                Available for remote work and global collaborations
              </p>
            </div>
            <div className="chip-border p-6 bg-bg-secondary/30">
              <div className="text-3xl mb-3">🤝</div>
              <h4 className="text-lg font-bold text-accent mb-2">Collaborative</h4>
              <p className="text-text-secondary text-sm">
                I believe in transparent communication and teamwork
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

'use client';

import { Suspense, useState } from 'react';
import { motion } from 'framer-motion';
import { AboutScene } from '@/components/3d/SceneManager';
import { PhotoFrame } from '@/components/3d/PhotoFrame';
import { SkillGrid, SkillConstellation } from '@/components/3d/SkillChip';
import { Timeline } from '@/components/ui/Timeline';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/hooks/useTheme';
import { useAdaptiveQuality } from '@/hooks/usePerformance';
import { cn } from '@/lib/utils';
import { Skill, Experience, Education } from '@/types';

interface AboutSectionProps {
  className?: string;
}

// Sample data - in a real app, this would come from a CMS or API
const sampleSkills: Skill[] = [
  {
    id: '1',
    name: 'React',
    category: 'frontend',
    level: 'expert',
    yearsOfExperience: 5,
    description: 'Building complex user interfaces with React and its ecosystem'
  },
  {
    id: '2',
    name: 'Three.js',
    category: 'frontend',
    level: 'advanced',
    yearsOfExperience: 3,
    description: 'Creating immersive 3D web experiences'
  },
  {
    id: '3',
    name: 'TypeScript',
    category: 'frontend',
    level: 'expert',
    yearsOfExperience: 4,
    description: 'Type-safe JavaScript development'
  },
  {
    id: '4',
    name: 'Node.js',
    category: 'backend',
    level: 'advanced',
    yearsOfExperience: 4,
    description: 'Server-side JavaScript development'
  },
  {
    id: '5',
    name: 'Python',
    category: 'backend',
    level: 'advanced',
    yearsOfExperience: 3,
    description: 'Backend development and data science'
  },
  {
    id: '6',
    name: 'PostgreSQL',
    category: 'database',
    level: 'intermediate',
    yearsOfExperience: 3,
    description: 'Relational database design and optimization'
  },
  {
    id: '7',
    name: 'Docker',
    category: 'devops',
    level: 'intermediate',
    yearsOfExperience: 2,
    description: 'Containerization and deployment'
  },
  {
    id: '8',
    name: 'AWS',
    category: 'devops',
    level: 'intermediate',
    yearsOfExperience: 2,
    description: 'Cloud infrastructure and services'
  }
];

const sampleExperiences: Experience[] = [
  {
    id: '1',
    company: 'Tech Innovations Inc.',
    position: 'Senior Frontend Developer',
    startDate: new Date('2022-01-01'),
    endDate: undefined,
    description: 'Leading the development of interactive 3D web applications using React and Three.js',
    achievements: [
      'Increased user engagement by 40% with immersive 3D interfaces',
      'Led a team of 5 developers on multiple projects',
      'Implemented performance optimizations reducing load times by 60%'
    ],
    technologies: ['React', 'Three.js', 'TypeScript', 'WebGL', 'Next.js'],
    location: 'San Francisco, CA',
    type: 'full-time'
  },
  {
    id: '2',
    company: 'Digital Solutions Ltd.',
    position: 'Full Stack Developer',
    startDate: new Date('2020-06-01'),
    endDate: new Date('2021-12-31'),
    description: 'Developed and maintained web applications using modern JavaScript frameworks',
    achievements: [
      'Built 15+ responsive web applications',
      'Improved application performance by 35%',
      'Mentored junior developers'
    ],
    technologies: ['React', 'Node.js', 'MongoDB', 'Express.js', 'AWS'],
    location: 'New York, NY',
    type: 'full-time'
  }
];

const sampleEducation: Education[] = [
  {
    id: '1',
    institution: 'University of Technology',
    degree: 'Bachelor of Science',
    field: 'Computer Science',
    startDate: new Date('2016-09-01'),
    endDate: new Date('2020-05-31'),
    gpa: 3.8,
    achievements: [
      'Magna Cum Laude',
      'Dean\'s List for 6 semesters',
      'Computer Science Society President'
    ],
    location: 'Boston, MA'
  }
];

export function AboutSection({ className }: AboutSectionProps) {
  const { themeConfig } = useTheme();
  const qualitySettings = useAdaptiveQuality();
  const [activeView, setActiveView] = useState<'overview' | 'skills' | 'timeline'>('overview');

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    });
  };

  return (
    <section id="about" className={cn(
      "min-h-screen bg-gradient-to-br from-bg-secondary via-bg-tertiary to-bg-secondary",
      "relative overflow-hidden",
      className
    )}>
      {/* Background Pattern */}
      <div className="absolute inset-0 circuit-bg opacity-10" />
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 py-20">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-primary mb-6">
            About Me
          </h2>
          <p className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto">
            A passionate developer creating immersive 3D web experiences with cutting-edge technologies
          </p>
        </motion.div>

        {/* View Toggle */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex justify-center mb-12"
        >
          <div className="flex bg-bg-primary/50 rounded-lg p-1 chip-border">
            {[
              { id: 'overview', label: 'Overview', icon: '👨‍💻' },
              { id: 'skills', label: 'Skills', icon: '🛠️' },
              { id: 'timeline', label: 'Timeline', icon: '📅' }
            ].map((tab) => (
              <Button
                key={tab.id}
                variant={activeView === tab.id ? 'primary' : 'ghost'}
                size="md"
                onClick={() => setActiveView(tab.id as any)}
                className="mx-1"
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </Button>
            ))}
          </div>
        </motion.div>

        {/* Content Views */}
        <div className="relative">
          {/* Overview */}
          {activeView === 'overview' && (
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 50 }}
              transition={{ duration: 0.6 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
            >
              {/* 3D Photo Frame */}
              <div className="relative h-96">
                <AboutScene
                  enableControls={false}
                  enableEnvironment={true}
                  shadows={qualitySettings.shadows}
                  antialias={qualitySettings.antialias}
                  lighting="soft"
                >
                  <PhotoFrame
                    position={[0, 0, 0]}
                    scale={1}
                    frameStyle="chip"
                    animated={true}
                    glowEffect={true}
                    primaryColor={themeConfig.primaryColor}
                    secondaryColor={themeConfig.secondaryColor}
                  />
                </AboutScene>
              </div>

              {/* About Content */}
              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <h3 className="text-2xl font-bold text-secondary mb-4">
                    Hello, I'm a Creative Developer
                  </h3>
                  <p className="text-text-secondary text-lg leading-relaxed mb-6">
                    With over 5 years of experience in web development, I specialize in creating 
                    immersive 3D web experiences that push the boundaries of what's possible in the browser. 
                    I'm passionate about combining cutting-edge technology with beautiful design to create 
                    memorable digital experiences.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="grid grid-cols-2 gap-4"
                >
                  <div className="chip-border p-4 bg-bg-primary/30">
                    <h4 className="text-lg font-bold text-primary mb-2">Frontend</h4>
                    <p className="text-text-secondary text-sm">React, Three.js, TypeScript</p>
                  </div>
                  <div className="chip-border p-4 bg-bg-primary/30">
                    <h4 className="text-lg font-bold text-secondary mb-2">3D Graphics</h4>
                    <p className="text-text-secondary text-sm">WebGL, Blender, GLTF</p>
                  </div>
                  <div className="chip-border p-4 bg-bg-primary/30">
                    <h4 className="text-lg font-bold text-accent mb-2">Backend</h4>
                    <p className="text-text-secondary text-sm">Node.js, Python, Databases</p>
                  </div>
                  <div className="chip-border p-4 bg-bg-primary/30">
                    <h4 className="text-lg font-bold text-primary mb-2">DevOps</h4>
                    <p className="text-text-secondary text-sm">Docker, AWS, CI/CD</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => scrollToSection('projects')}
                    className="group"
                  >
                    <span className="group-hover:scale-110 transition-transform">
                      💼 View My Work
                    </span>
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => scrollToSection('contact')}
                    className="group"
                  >
                    <span className="group-hover:scale-110 transition-transform">
                      📧 Get In Touch
                    </span>
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          )}

          {/* Skills View */}
          {activeView === 'skills' && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.6 }}
              className="space-y-12"
            >
              <div className="text-center">
                <h3 className="text-3xl font-bold text-accent mb-4">Technical Skills</h3>
                <p className="text-text-secondary max-w-2xl mx-auto">
                  Click on any skill chip to learn more about my experience and proficiency level
                </p>
              </div>

              {/* 3D Skills Display */}
              <div className="relative h-96">
                <AboutScene
                  enableControls={true}
                  enableEnvironment={false}
                  shadows={qualitySettings.shadows}
                  antialias={qualitySettings.antialias}
                  lighting="studio"
                >
                  {qualitySettings.particleCount > 100 ? (
                    <SkillConstellation
                      skills={sampleSkills}
                      position={[0, 0, 0]}
                      radius={3}
                      primaryColor={themeConfig.primaryColor}
                      secondaryColor={themeConfig.secondaryColor}
                      accentColor={themeConfig.accentColor}
                    />
                  ) : (
                    <SkillGrid
                      skills={sampleSkills}
                      position={[0, 0, 0]}
                      spacing={2}
                      animated={true}
                      primaryColor={themeConfig.primaryColor}
                      secondaryColor={themeConfig.secondaryColor}
                      accentColor={themeConfig.accentColor}
                    />
                  )}
                </AboutScene>
              </div>
            </motion.div>
          )}

          {/* Timeline View */}
          {activeView === 'timeline' && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.05 }}
              transition={{ duration: 0.6 }}
            >
              <Timeline
                experiences={sampleExperiences}
                education={sampleEducation}
              />
            </motion.div>
          )}
        </div>
      </div>
    </section>
  );
}

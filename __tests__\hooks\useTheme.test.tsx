import { renderHook, act } from '@testing-library/react';
import { useTheme, ThemeProvider } from '@/components/ui/ThemeProvider';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Wrapper for hooks that need ThemeProvider
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
);

describe('useTheme Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    
    // Mock document.documentElement
    Object.defineProperty(document, 'documentElement', {
      value: {
        setAttribute: jest.fn(),
        style: {
          setProperty: jest.fn(),
        },
      },
      writable: true,
    });

    // Mock meta theme-color element
    const mockMetaElement = {
      setAttribute: jest.fn(),
    };
    jest.spyOn(document, 'querySelector').mockReturnValue(mockMetaElement as any);
  });

  it('initializes with default theme configuration', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });

    expect(result.current.theme).toBe('dark');
    expect(result.current.themeConfig).toEqual({
      theme: 'dark',
      primaryColor: '#00ff88',
      secondaryColor: '#0088ff',
      accentColor: '#ff6b00',
      enableGlowEffects: true,
      enableAnimations: true,
    });
  });

  it('loads saved theme from localStorage', () => {
    const savedConfig = {
      theme: 'light',
      primaryColor: '#ff0000',
      secondaryColor: '#00ff00',
      accentColor: '#0000ff',
      enableGlowEffects: false,
      enableAnimations: false,
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(savedConfig));

    const { result } = renderHook(() => useTheme(), { wrapper });

    expect(result.current.theme).toBe('light');
    expect(result.current.themeConfig.primaryColor).toBe('#ff0000');
    expect(result.current.themeConfig.enableGlowEffects).toBe(false);
  });

  it('toggles theme correctly', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });

    expect(result.current.theme).toBe('dark');

    act(() => {
      result.current.toggleTheme();
    });

    expect(result.current.theme).toBe('light');

    act(() => {
      result.current.toggleTheme();
    });

    expect(result.current.theme).toBe('dark');
  });

  it('sets specific theme', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });

    act(() => {
      result.current.setTheme('light');
    });

    expect(result.current.theme).toBe('light');

    act(() => {
      result.current.setTheme('dark');
    });

    expect(result.current.theme).toBe('dark');
  });

  it('updates theme configuration', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });

    const newConfig = {
      primaryColor: '#ff00ff',
      enableGlowEffects: false,
    };

    act(() => {
      result.current.updateThemeConfig(newConfig);
    });

    expect(result.current.themeConfig.primaryColor).toBe('#ff00ff');
    expect(result.current.themeConfig.enableGlowEffects).toBe(false);
    // Other properties should remain unchanged
    expect(result.current.themeConfig.theme).toBe('dark');
    expect(result.current.themeConfig.secondaryColor).toBe('#0088ff');
  });

  it('saves theme changes to localStorage', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });

    act(() => {
      result.current.toggleTheme();
    });

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'themeConfig',
      expect.stringContaining('"theme":"light"')
    );
  });

  it('applies theme to document element', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });

    act(() => {
      result.current.setTheme('light');
    });

    expect(document.documentElement.setAttribute).toHaveBeenCalledWith(
      'data-theme',
      'light'
    );
  });

  it('applies CSS custom properties', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });

    act(() => {
      result.current.updateThemeConfig({
        primaryColor: '#custom-color',
      });
    });

    expect(document.documentElement.style.setProperty).toHaveBeenCalledWith(
      '--color-primary',
      '#custom-color'
    );
  });

  it('handles system preference when no saved theme', () => {
    // Mock matchMedia to return dark preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    mockLocalStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useTheme(), { wrapper });

    expect(result.current.theme).toBe('dark');
  });

  it('tracks transition state', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });

    expect(result.current.isTransitioning).toBe(false);

    act(() => {
      result.current.toggleTheme();
    });

    expect(result.current.isTransitioning).toBe(true);

    // Simulate transition completion
    act(() => {
      jest.advanceTimersByTime(300);
    });

    expect(result.current.isTransitioning).toBe(false);
  });

  it('listens for system theme changes', () => {
    const mockAddEventListener = jest.fn();
    const mockRemoveEventListener = jest.fn();

    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(() => ({
        matches: false,
        media: '',
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: mockAddEventListener,
        removeEventListener: mockRemoveEventListener,
        dispatchEvent: jest.fn(),
      })),
    });

    const { unmount } = renderHook(() => useTheme(), { wrapper });

    expect(mockAddEventListener).toHaveBeenCalledWith('change', expect.any(Function));

    unmount();

    expect(mockRemoveEventListener).toHaveBeenCalledWith('change', expect.any(Function));
  });

  it('updates meta theme color', () => {
    const mockSetAttribute = jest.fn();
    const mockMetaElement = { setAttribute: mockSetAttribute };
    
    jest.spyOn(document, 'querySelector').mockReturnValue(mockMetaElement as any);

    const { result } = renderHook(() => useTheme(), { wrapper });

    act(() => {
      result.current.updateThemeConfig({
        primaryColor: '#new-color',
      });
    });

    expect(mockSetAttribute).toHaveBeenCalledWith('content', '#new-color');
  });
});

'use client';

import { HeroSection } from '@/components/sections/HeroSection';

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section id="home">
        <HeroSection />
      </section>

      {/* Placeholder sections for future development */}
      <section id="about" className="min-h-screen flex items-center justify-center bg-bg-secondary">
        <div className="text-center max-w-4xl mx-auto px-4">
          <h2 className="text-4xl md:text-6xl font-bold text-primary mb-6">About Me</h2>
          <p className="text-xl text-text-secondary mb-8">
            A passionate developer creating immersive 3D web experiences with cutting-edge technologies.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div className="chip-border p-6 bg-bg-primary/50">
              <h3 className="text-xl font-bold text-secondary mb-2">Frontend</h3>
              <p className="text-text-secondary">React, Next.js, Three.js, TypeScript</p>
            </div>
            <div className="chip-border p-6 bg-bg-primary/50">
              <h3 className="text-xl font-bold text-accent mb-2">3D Graphics</h3>
              <p className="text-text-secondary">WebGL, Three.js, Blender, GLTF</p>
            </div>
            <div className="chip-border p-6 bg-bg-primary/50">
              <h3 className="text-xl font-bold text-primary mb-2">Backend</h3>
              <p className="text-text-secondary">Node.js, Python, Databases, APIs</p>
            </div>
          </div>
          <p className="text-text-muted mt-8">Coming soon in Phase 4...</p>
        </div>
      </section>

      <section id="projects" className="min-h-screen flex items-center justify-center bg-bg-tertiary">
        <div className="text-center max-w-6xl mx-auto px-4">
          <h2 className="text-4xl md:text-6xl font-bold text-secondary mb-6">Projects</h2>
          <p className="text-xl text-text-secondary mb-12">
            Explore my portfolio of interactive 3D web applications and innovative digital experiences.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="chip-border p-6 bg-bg-primary/30 hover:bg-bg-primary/50 transition-all duration-300">
                <div className="w-full h-48 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg mb-4 flex items-center justify-center">
                  <span className="text-4xl">🚀</span>
                </div>
                <h3 className="text-xl font-bold text-primary mb-2">Project {i}</h3>
                <p className="text-text-secondary">Interactive 3D web application with modern technologies.</p>
              </div>
            ))}
          </div>
          <p className="text-text-muted mt-8">Coming soon in Phase 5...</p>
        </div>
      </section>

      <section id="skills" className="min-h-screen flex items-center justify-center bg-bg-secondary">
        <div className="text-center max-w-4xl mx-auto px-4">
          <h2 className="text-4xl md:text-6xl font-bold text-accent mb-6">Skills</h2>
          <p className="text-xl text-text-secondary mb-12">
            Technologies and tools I use to create amazing digital experiences.
          </p>
          <p className="text-text-muted">Coming soon in Phase 7...</p>
        </div>
      </section>

      <section id="contact" className="min-h-screen flex items-center justify-center bg-bg-primary">
        <div className="text-center max-w-4xl mx-auto px-4">
          <h2 className="text-4xl md:text-6xl font-bold text-primary mb-6">Get In Touch</h2>
          <p className="text-xl text-text-secondary mb-12">
            Ready to collaborate on your next 3D web project? Let's create something amazing together.
          </p>
          <p className="text-text-muted">Coming soon in Phase 6...</p>
        </div>
      </section>
    </div>
  );
}

'use client';

import { Suspense } from 'react';
import { Scene, SceneLoader } from '@/components/3d/Scene';
import { Microchip } from '@/components/3d/Microchip';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/hooks/useTheme';

export default function Home() {
  const { toggleTheme, theme } = useTheme();

  return (
    <div className="min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Background Circuit Pattern */}
        <div className="absolute inset-0 circuit-bg opacity-20" />

        {/* 3D Scene */}
        <div className="absolute inset-0">
          <Suspense fallback={<SceneLoader />}>
            <Scene
              cameraPosition={[0, 0, 8]}
              enableControls={true}
              enableEnvironment={true}
            >
              <Microchip
                position={[0, 0, 0]}
                scale={1.5}
                animated={true}
                glowEffect={true}
                text="PORTFOLIO"
              />
            </Scene>
          </Suspense>
        </div>

        {/* Content Overlay */}
        <div className="relative z-10 text-center px-4 max-w-4xl mx-auto">
          <h1 className="text-6xl md:text-8xl font-bold mb-6 text-glow">
            <span className="bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent">
              3D Portfolio
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-text-secondary mb-8 max-w-2xl mx-auto">
            Welcome to an immersive 3D experience showcasing cutting-edge web development
            with interactive microchip-themed design.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              variant="primary"
              size="lg"
              className="animate-pulse-glow"
              onClick={() => {
                document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              Explore Portfolio
            </Button>

            <Button
              variant="outline"
              size="lg"
              onClick={() => {
                document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              View Projects
            </Button>
          </div>
        </div>

        {/* Theme Toggle */}
        <div className="absolute top-6 right-6 z-20">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className="chip-border"
          >
            {theme === 'dark' ? '☀️' : '🌙'}
          </Button>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center">
            <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </section>

      {/* Placeholder sections for future development */}
      <section id="about" className="min-h-screen flex items-center justify-center bg-bg-secondary">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-primary mb-4">About Section</h2>
          <p className="text-text-secondary">Coming soon in Phase 4...</p>
        </div>
      </section>

      <section id="projects" className="min-h-screen flex items-center justify-center bg-bg-tertiary">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-secondary mb-4">Projects Section</h2>
          <p className="text-text-secondary">Coming soon in Phase 5...</p>
        </div>
      </section>
    </div>
  );
}

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Navigation } from '@/components/ui/Navigation';
import { ThemeProvider } from '@/components/ui/ThemeProvider';

// Mock next/navigation
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  usePathname: () => '/',
}));

// Mock scrollIntoView
Element.prototype.scrollIntoView = jest.fn();

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
);

describe('Navigation Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock sections in DOM
    const mockSections = [
      { id: 'home', getBoundingClientRect: () => ({ top: 0, bottom: 100 }) },
      { id: 'about', getBoundingClientRect: () => ({ top: 200, bottom: 300 }) },
      { id: 'projects', getBoundingClientRect: () => ({ top: 400, bottom: 500 }) },
      { id: 'skills', getBoundingClientRect: () => ({ top: 600, bottom: 700 }) },
      { id: 'contact', getBoundingClientRect: () => ({ top: 800, bottom: 900 }) },
    ];

    jest.spyOn(document, 'getElementById').mockImplementation((id) => {
      const section = mockSections.find(s => s.id === id);
      return section as any;
    });
  });

  it('renders all navigation items', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Projects')).toBeInTheDocument();
    expect(screen.getByText('Skills')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('scrolls to section when navigation item is clicked', async () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    const aboutButton = screen.getByText('About');
    fireEvent.click(aboutButton);

    await waitFor(() => {
      expect(document.getElementById).toHaveBeenCalledWith('about');
    });
  });

  it('shows active section indicator', async () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    // Simulate scroll to trigger active section detection
    fireEvent.scroll(window, { target: { scrollY: 250 } });

    await waitFor(() => {
      const aboutButton = screen.getByText('About');
      expect(aboutButton.closest('button')).toHaveClass('btn-primary');
    });
  });

  it('toggles mobile menu', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    // Mobile menu should be hidden initially
    expect(screen.queryByRole('navigation')).not.toBeVisible();

    // Click mobile menu button
    const menuButton = screen.getByRole('button', { name: /menu/i });
    fireEvent.click(menuButton);

    // Mobile menu should be visible
    expect(screen.getByRole('navigation')).toBeVisible();
  });

  it('closes mobile menu when navigation item is clicked', () => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    // Open mobile menu
    const menuButton = screen.getByRole('button', { name: /menu/i });
    fireEvent.click(menuButton);

    // Click navigation item
    const aboutButton = screen.getByText('About');
    fireEvent.click(aboutButton);

    // Mobile menu should be closed
    expect(screen.queryByRole('navigation')).not.toBeVisible();
  });

  it('handles theme toggle', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    const themeButton = screen.getByRole('button', { name: /theme/i });
    fireEvent.click(themeButton);

    // Theme should change (this would be tested more thoroughly in theme tests)
    expect(themeButton).toBeInTheDocument();
  });

  it('updates scroll indicator on scroll', async () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    // Mock scroll position
    Object.defineProperty(window, 'scrollY', {
      writable: true,
      configurable: true,
      value: 100,
    });

    fireEvent.scroll(window);

    await waitFor(() => {
      // Navigation should have scrolled class
      const nav = screen.getByRole('navigation');
      expect(nav).toHaveClass('bg-bg-primary/80');
    });
  });

  it('supports keyboard navigation', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    const homeButton = screen.getByText('Home');
    homeButton.focus();

    expect(homeButton).toHaveFocus();

    // Tab to next item
    fireEvent.keyDown(homeButton, { key: 'Tab' });
    
    const aboutButton = screen.getByText('About');
    expect(aboutButton).toHaveFocus();
  });

  it('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    const nav = screen.getByRole('navigation');
    expect(nav).toHaveAttribute('aria-label', 'Main navigation');

    const homeButton = screen.getByText('Home');
    expect(homeButton.closest('button')).toHaveAttribute('aria-current', 'page');
  });

  it('handles logo click', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    const logo = screen.getByText('3D');
    fireEvent.click(logo);

    expect(document.getElementById).toHaveBeenCalledWith('home');
  });

  it('shows navigation items with icons', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    // Check that icons are present (emojis in this case)
    expect(screen.getByText('🏠')).toBeInTheDocument(); // Home icon
    expect(screen.getByText('👨‍💻')).toBeInTheDocument(); // About icon
    expect(screen.getByText('💼')).toBeInTheDocument(); // Projects icon
  });

  it('handles window resize for mobile menu', () => {
    render(
      <TestWrapper>
        <Navigation />
      </TestWrapper>
    );

    // Start with mobile size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    fireEvent.resize(window);

    // Mobile menu button should be visible
    expect(screen.getByRole('button', { name: /menu/i })).toBeInTheDocument();

    // Resize to desktop
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });

    fireEvent.resize(window);

    // Mobile menu button should be hidden
    expect(screen.queryByRole('button', { name: /menu/i })).not.toBeInTheDocument();
  });
});

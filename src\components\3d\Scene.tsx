'use client';

import { Canvas } from '@react-three/fiber';
import { Suspense } from 'react';
import { OrbitControls, Environment, PerspectiveCamera } from '@react-three/drei';
import { BaseComponentProps } from '@/types';
import { cn } from '@/lib/utils';

interface SceneProps extends BaseComponentProps {
  cameraPosition?: [number, number, number];
  enableControls?: boolean;
  enableEnvironment?: boolean;
  backgroundColor?: string;
  shadows?: boolean;
  antialias?: boolean;
  performance?: {
    min: number;
    max: number;
    debounce: number;
  };
}

export function Scene({
  children,
  className,
  cameraPosition = [0, 0, 5],
  enableControls = true,
  enableEnvironment = true,
  backgroundColor = 'transparent',
  shadows = true,
  antialias = true,
  performance = { min: 0.5, max: 1, debounce: 200 },
}: SceneProps) {
  return (
    <div className={cn('w-full h-full', className)}>
      <Canvas
        shadows={shadows}
        gl={{
          antialias,
          alpha: true,
          powerPreference: 'high-performance',
        }}
        performance={performance}
        style={{ background: backgroundColor }}
        dpr={[1, 2]}
      >
        <PerspectiveCamera
          makeDefault
          position={cameraPosition}
          fov={75}
          near={0.1}
          far={1000}
        />
        
        {/* Lighting Setup */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1}
          castShadow={shadows}
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />
        <pointLight position={[-10, -10, -10]} intensity={0.5} />
        
        {/* Environment */}
        {enableEnvironment && (
          <Environment preset="city" background={false} />
        )}
        
        {/* Controls */}
        {enableControls && (
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={20}
            maxPolarAngle={Math.PI / 2}
          />
        )}
        
        {/* Scene Content */}
        <Suspense fallback={null}>
          {children}
        </Suspense>
      </Canvas>
    </div>
  );
}

// Loading fallback component for 3D scenes
export function SceneLoader({ className }: { className?: string }) {
  return (
    <div className={cn(
      'w-full h-full flex items-center justify-center',
      'bg-gradient-to-br from-gray-900 to-black',
      className
    )}>
      <div className="flex flex-col items-center space-y-4">
        <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin" />
        <p className="text-primary text-sm font-medium">Loading 3D Scene...</p>
      </div>
    </div>
  );
}

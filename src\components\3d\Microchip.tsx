'use client';

import { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Box, RoundedBox, Text } from '@react-three/drei';
import { Mesh, Group } from 'three';

interface MicrochipProps {
  position?: [number, number, number];
  scale?: number;
  rotation?: [number, number, number];
  animated?: boolean;
  glowEffect?: boolean;
  text?: string;
  onClick?: () => void;
  onHover?: (hovered: boolean) => void;
  primaryColor?: string;
  secondaryColor?: string;
}

export function Microchip({
  position = [0, 0, 0],
  scale = 1,
  rotation = [0, 0, 0],
  animated = true,
  glowEffect = true,
  text,
  onClick,
  onHover,
  primaryColor = '#00ff88',
  secondaryColor = '#0088ff',
}: MicrochipProps) {
  const groupRef = useRef<Group>(null);
  const chipRef = useRef<Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // Animation loop
  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    
    // Gentle rotation animation
    groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;
    
    // Floating animation
    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1;
    
    // Hover effect
    if (hovered && chipRef.current) {
      chipRef.current.scale.setScalar(1.1);
    } else if (chipRef.current) {
      chipRef.current.scale.setScalar(1);
    }
  });

  const handlePointerOver = () => {
    setHovered(true);
    onHover?.(true);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    setHovered(false);
    onHover?.(false);
    document.body.style.cursor = 'auto';
  };

  return (
    <group
      ref={groupRef}
      position={position}
      scale={scale}
      rotation={rotation}
      onClick={onClick}
      onPointerOver={handlePointerOver}
      onPointerOut={handlePointerOut}
    >
      {/* Main chip body */}
      <RoundedBox
        ref={chipRef}
        args={[2, 0.2, 2]}
        radius={0.05}
        smoothness={4}
        castShadow
        receiveShadow
      >
        <meshStandardMaterial
          color={hovered ? primaryColor : '#2a2a2a'}
          metalness={0.8}
          roughness={0.2}
          emissive={glowEffect ? primaryColor : '#000000'}
          emissiveIntensity={hovered ? 0.3 : 0.1}
        />
      </RoundedBox>

      {/* Circuit traces */}
      {Array.from({ length: 8 }, (_, i) => (
        <Box
          key={`trace-${i}`}
          args={[0.05, 0.01, 1.8]}
          position={[-0.8 + (i * 0.2), 0.11, 0]}
          castShadow
        >
          <meshStandardMaterial
            color={secondaryColor}
            emissive={secondaryColor}
            emissiveIntensity={0.2}
          />
        </Box>
      ))}

      {/* Pins */}
      {Array.from({ length: 16 }, (_, i) => {
        const side = Math.floor(i / 4);
        const pinIndex = i % 4;
        let pinPosition: [number, number, number];
        
        switch (side) {
          case 0: // Top
            pinPosition = [-0.6 + (pinIndex * 0.4), -0.15, 1.1];
            break;
          case 1: // Right
            pinPosition = [1.1, -0.15, 0.6 - (pinIndex * 0.4)];
            break;
          case 2: // Bottom
            pinPosition = [0.6 - (pinIndex * 0.4), -0.15, -1.1];
            break;
          case 3: // Left
            pinPosition = [-1.1, -0.15, -0.6 + (pinIndex * 0.4)];
            break;
          default:
            pinPosition = [0, 0, 0];
        }

        return (
          <Box
            key={`pin-${i}`}
            args={[0.1, 0.3, 0.05]}
            position={pinPosition}
            castShadow
          >
            <meshStandardMaterial
              color="#ffd700"
              metalness={1}
              roughness={0.1}
            />
          </Box>
        );
      })}

      {/* Central processing unit indicator */}
      <RoundedBox
        args={[1, 0.05, 1]}
        radius={0.02}
        position={[0, 0.13, 0]}
        castShadow
      >
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.5}
          roughness={0.3}
        />
      </RoundedBox>

      {/* Text label */}
      {text && (
        <Text
          position={[0, 0.2, 0]}
          fontSize={0.2}
          color={primaryColor}
          anchorX="center"
          anchorY="middle"
          font="/fonts/inter-bold.woff"
        >
          {text}
        </Text>
      )}

      {/* Glow effect */}
      {glowEffect && hovered && (
        <RoundedBox
          args={[2.2, 0.25, 2.2]}
          radius={0.05}
          position={[0, 0, 0]}
        >
          <meshBasicMaterial
            color={primaryColor}
            transparent
            opacity={0.1}
          />
        </RoundedBox>
      )}
    </group>
  );
}

// Simplified microchip for performance-critical scenarios
export function SimpleMicrochip({
  position = [0, 0, 0],
  scale = 1,
  color = '#2a2a2a',
}: {
  position?: [number, number, number];
  scale?: number;
  color?: string;
}) {
  return (
    <RoundedBox
      args={[2, 0.2, 2]}
      radius={0.05}
      position={position}
      scale={scale}
      castShadow
      receiveShadow
    >
      <meshStandardMaterial
        color={color}
        metalness={0.8}
        roughness={0.2}
      />
    </RoundedBox>
  );
}

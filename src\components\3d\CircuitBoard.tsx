'use client';

import { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { RoundedBox, Text, Html, Cylinder, Sphere } from '@react-three/drei';
import { Mesh, Group } from 'three';
import { useTheme } from '@/hooks/useTheme';

interface CircuitBoardProps {
  position?: [number, number, number];
  scale?: number;
  rotation?: [number, number, number];
  animated?: boolean;
  interactive?: boolean;
  onContactClick?: (type: string) => void;
}

interface ContactPoint {
  id: string;
  type: 'email' | 'linkedin' | 'github' | 'twitter' | 'phone' | 'location';
  position: [number, number, number];
  label: string;
  value: string;
  icon: string;
  color: string;
}

const contactPoints: ContactPoint[] = [
  {
    id: 'email',
    type: 'email',
    position: [-2, 1, 0.1],
    label: 'Email',
    value: '<EMAIL>',
    icon: '📧',
    color: '#ff6b00'
  },
  {
    id: 'linkedin',
    type: 'linkedin',
    position: [2, 1, 0.1],
    label: 'LinkedIn',
    value: '/in/developer',
    icon: '💼',
    color: '#0077b5'
  },
  {
    id: 'github',
    type: 'github',
    position: [-2, -1, 0.1],
    label: 'GitHub',
    value: '/developer',
    icon: '💻',
    color: '#333333'
  },
  {
    id: 'twitter',
    type: 'twitter',
    position: [2, -1, 0.1],
    label: 'Twitter',
    value: '@developer',
    icon: '🐦',
    color: '#1da1f2'
  },
  {
    id: 'phone',
    type: 'phone',
    position: [0, 1.5, 0.1],
    label: 'Phone',
    value: '+****************',
    icon: '📱',
    color: '#10b981'
  },
  {
    id: 'location',
    type: 'location',
    position: [0, -1.5, 0.1],
    label: 'Location',
    value: 'San Francisco, CA',
    icon: '📍',
    color: '#ef4444'
  }
];

export function CircuitBoard({
  position = [0, 0, 0],
  scale = 1,
  rotation = [0, 0, 0],
  animated = true,
  interactive = true,
  onContactClick,
}: CircuitBoardProps) {
  const groupRef = useRef<Group>(null);
  const [hoveredPoint, setHoveredPoint] = useState<string | null>(null);
  const [activeConnections, setActiveConnections] = useState<string[]>([]);
  const { themeConfig } = useTheme();

  // Animation loop
  useFrame((state) => {
    if (!animated || !groupRef.current) return;
    
    // Gentle floating animation
    groupRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.5) * 0.02;
    
    // Subtle rotation
    groupRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.3) * 0.02;
  });

  const handleContactPointClick = (contactPoint: ContactPoint) => {
    if (!interactive) return;
    
    // Add visual feedback
    setActiveConnections(prev => 
      prev.includes(contactPoint.id) 
        ? prev.filter(id => id !== contactPoint.id)
        : [...prev, contactPoint.id]
    );
    
    onContactClick?.(contactPoint.type);
  };

  const handleContactPointHover = (contactPointId: string | null) => {
    setHoveredPoint(contactPointId);
    document.body.style.cursor = contactPointId ? 'pointer' : 'auto';
  };

  return (
    <group
      ref={groupRef}
      position={position}
      scale={scale}
      rotation={rotation}
    >
      {/* Main PCB */}
      <RoundedBox
        args={[6, 4, 0.1]}
        radius={0.1}
        castShadow
        receiveShadow
      >
        <meshStandardMaterial
          color="#1a4a1a"
          roughness={0.8}
          metalness={0.2}
        />
      </RoundedBox>

      {/* Circuit traces - main pathways */}
      {/* Horizontal traces */}
      <RoundedBox
        args={[5.5, 0.05, 0.01]}
        radius={0.01}
        position={[0, 0, 0.06]}
        castShadow
      >
        <meshStandardMaterial
          color={themeConfig.primaryColor}
          emissive={themeConfig.primaryColor}
          emissiveIntensity={0.3}
        />
      </RoundedBox>

      {/* Vertical traces */}
      <RoundedBox
        args={[0.05, 3.5, 0.01]}
        radius={0.01}
        position={[0, 0, 0.06]}
        castShadow
      >
        <meshStandardMaterial
          color={themeConfig.primaryColor}
          emissive={themeConfig.primaryColor}
          emissiveIntensity={0.3}
        />
      </RoundedBox>

      {/* Connection traces to contact points */}
      {contactPoints.map((point) => {
        const isActive = activeConnections.includes(point.id);
        const isHovered = hoveredPoint === point.id;
        
        return (
          <group key={`trace-${point.id}`}>
            {/* Trace line from center to contact point */}
            <RoundedBox
              args={[
                Math.abs(point.position[0]) * 0.8,
                0.03,
                0.01
              ]}
              radius={0.005}
              position={[
                point.position[0] * 0.4,
                point.position[1] * 0.8,
                0.06
              ]}
              castShadow
            >
              <meshStandardMaterial
                color={isActive || isHovered ? point.color : themeConfig.secondaryColor}
                emissive={isActive || isHovered ? point.color : themeConfig.secondaryColor}
                emissiveIntensity={isActive || isHovered ? 0.5 : 0.2}
              />
            </RoundedBox>
            
            {/* Vertical connection */}
            <RoundedBox
              args={[
                0.03,
                Math.abs(point.position[1]) * 0.2,
                0.01
              ]}
              radius={0.005}
              position={[
                point.position[0] * 0.8,
                point.position[1] * 0.9,
                0.06
              ]}
              castShadow
            >
              <meshStandardMaterial
                color={isActive || isHovered ? point.color : themeConfig.secondaryColor}
                emissive={isActive || isHovered ? point.color : themeConfig.secondaryColor}
                emissiveIntensity={isActive || isHovered ? 0.5 : 0.2}
              />
            </RoundedBox>
          </group>
        );
      })}

      {/* Contact points */}
      {contactPoints.map((point) => {
        const isActive = activeConnections.includes(point.id);
        const isHovered = hoveredPoint === point.id;
        
        return (
          <group
            key={point.id}
            position={point.position}
            onClick={() => handleContactPointClick(point)}
            onPointerOver={() => handleContactPointHover(point.id)}
            onPointerOut={() => handleContactPointHover(null)}
          >
            {/* Contact pad */}
            <Cylinder
              args={[0.2, 0.2, 0.05]}
              castShadow
            >
              <meshStandardMaterial
                color={isActive || isHovered ? point.color : '#ffd700'}
                metalness={1}
                roughness={0.1}
                emissive={isActive || isHovered ? point.color : '#000000'}
                emissiveIntensity={isActive || isHovered ? 0.3 : 0}
              />
            </Cylinder>

            {/* Connection pin */}
            <Cylinder
              args={[0.05, 0.05, 0.15]}
              position={[0, 0, 0.1]}
              castShadow
            >
              <meshStandardMaterial
                color="#c0c0c0"
                metalness={1}
                roughness={0.1}
              />
            </Cylinder>

            {/* Glow effect when active */}
            {(isActive || isHovered) && (
              <Sphere
                args={[0.3]}
                position={[0, 0, 0]}
              >
                <meshBasicMaterial
                  color={point.color}
                  transparent
                  opacity={0.2}
                />
              </Sphere>
            )}

            {/* Label */}
            <Text
              position={[0, -0.4, 0]}
              fontSize={0.1}
              color={isActive || isHovered ? point.color : themeConfig.primaryColor}
              anchorX="center"
              anchorY="middle"
              font="/fonts/inter-bold.woff"
            >
              {point.label}
            </Text>

            {/* Info popup when hovered */}
            {isHovered && (
              <Html
                position={[0, 0.6, 0]}
                center
                distanceFactor={8}
              >
                <div className="bg-bg-primary/95 backdrop-blur-md border border-primary/30 rounded-lg p-3 min-w-max">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{point.icon}</span>
                    <div>
                      <p className="text-sm font-bold text-primary">{point.label}</p>
                      <p className="text-xs text-text-secondary">{point.value}</p>
                    </div>
                  </div>
                </div>
              </Html>
            )}
          </group>
        );
      })}

      {/* Central processor/logo area */}
      <RoundedBox
        args={[1, 1, 0.08]}
        radius={0.05}
        position={[0, 0, 0.09]}
        castShadow
      >
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.9}
          roughness={0.1}
          emissive={themeConfig.primaryColor}
          emissiveIntensity={0.1}
        />
      </RoundedBox>

      {/* Logo/Brand text */}
      <Text
        position={[0, 0, 0.14]}
        fontSize={0.15}
        color={themeConfig.primaryColor}
        anchorX="center"
        anchorY="middle"
        font="/fonts/inter-bold.woff"
      >
        CONTACT
      </Text>

      {/* Corner mounting holes */}
      {[[-2.5, 1.5], [2.5, 1.5], [-2.5, -1.5], [2.5, -1.5]].map(([x, y], i) => (
        <Cylinder
          key={`hole-${i}`}
          args={[0.1, 0.1, 0.12]}
          position={[x, y, 0]}
          castShadow
        >
          <meshStandardMaterial
            color="#000000"
          />
        </Cylinder>
      ))}

      {/* Resistors and capacitors for detail */}
      {Array.from({ length: 12 }, (_, i) => (
        <RoundedBox
          key={`component-${i}`}
          args={[0.1, 0.05, 0.03]}
          radius={0.01}
          position={[
            (Math.random() - 0.5) * 4,
            (Math.random() - 0.5) * 2.5,
            0.07
          ]}
          castShadow
        >
          <meshStandardMaterial
            color={Math.random() > 0.5 ? '#8b4513' : '#4169e1'}
            roughness={0.6}
          />
        </RoundedBox>
      ))}

      {/* LED indicators */}
      {Array.from({ length: 4 }, (_, i) => (
        <Cylinder
          key={`led-${i}`}
          args={[0.03, 0.03, 0.04]}
          position={[
            -2.5 + (i * 1.7),
            1.8,
            0.08
          ]}
          castShadow
        >
          <meshStandardMaterial
            color={i % 2 === 0 ? '#ff0000' : '#00ff00'}
            emissive={i % 2 === 0 ? '#ff0000' : '#00ff00'}
            emissiveIntensity={0.5}
          />
        </Cylinder>
      ))}
    </group>
  );
}

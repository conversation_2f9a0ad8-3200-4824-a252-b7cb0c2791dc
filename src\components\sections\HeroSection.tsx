'use client';

import { Suspense, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HeroScene } from '@/components/3d/SceneManager';
import { Microchip } from '@/components/3d/Microchip';
import { MicrochipGrid } from '@/components/3d/MicrochipModel';
import { CPUModel } from '@/components/3d/PlaceholderModels';
import { InteractiveCamera } from '@/components/3d/InteractiveCamera';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/hooks/useTheme';
import { useAdaptiveQuality } from '@/hooks/usePerformance';
import { cn } from '@/lib/utils';

interface HeroSectionProps {
  className?: string;
}

const heroTexts = [
  "3D Portfolio",
  "Interactive Design",
  "Web Innovation",
  "Digital Experience"
];

export function HeroSection({ className }: HeroSectionProps) {
  const { toggleTheme, theme } = useTheme();
  const qualitySettings = useAdaptiveQuality();
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);

  // Cycle through hero texts
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTextIndex((prev) => (prev + 1) % heroTexts.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  // Loading animation
  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    });
  };

  return (
    <section className={cn(
      "relative h-screen flex items-center justify-center overflow-hidden",
      "bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary",
      className
    )}>
      {/* Animated Background Pattern */}
      <div className="absolute inset-0">
        <div className="circuit-bg opacity-20 animate-pulse" />
        <div className="absolute inset-0 bg-gradient-to-t from-bg-primary/50 to-transparent" />
      </div>
      
      {/* 3D Scene */}
      <div className="absolute inset-0 z-10">
        <HeroScene
          enableControls={false}
          enableEnvironment={true}
          shadows={qualitySettings.shadows}
          antialias={qualitySettings.antialias}
          lighting="dramatic"
        >
          {/* Interactive Camera */}
          <InteractiveCamera
            enableMouseTracking={true}
            enableScrollTracking={true}
            mouseSensitivity={0.3}
            scrollSensitivity={0.2}
            basePosition={[0, 0, 8]}
            lookAt={[0, 0, 0]}
            smoothing={0.08}
          />
          {/* Main hero microchip */}
          <CPUModel
            position={[0, 0, 0]}
            scale={1.2}
            animated={true}
            glowEffect={true}
          />
          
          {/* Floating microchips around the main one */}
          <Microchip
            position={[-3, 1, -2]}
            scale={0.6}
            animated={true}
            glowEffect={true}
            primaryColor={themeConfig.primaryColor}
            secondaryColor={themeConfig.secondaryColor}
          />
          <Microchip
            position={[3, -1, -2]}
            scale={0.8}
            animated={true}
            glowEffect={true}
            primaryColor={themeConfig.primaryColor}
            secondaryColor={themeConfig.secondaryColor}
          />
          <Microchip
            position={[0, 2, -3]}
            scale={0.5}
            animated={true}
            glowEffect={true}
            primaryColor={themeConfig.primaryColor}
            secondaryColor={themeConfig.secondaryColor}
          />
          
          {/* Background grid for depth */}
          {qualitySettings.particleCount > 50 && (
            <MicrochipGrid
              count={Math.min(qualitySettings.particleCount / 4, 15)}
              spread={20}
              animated={true}
              primaryColor={themeConfig.primaryColor}
              secondaryColor={themeConfig.secondaryColor}
            />
          )}
        </HeroScene>
      </div>
      
      {/* Content Overlay */}
      <div className="relative z-20 text-center px-4 max-w-6xl mx-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key="hero-content"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 50 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="space-y-8"
          >
            {/* Animated Title */}
            <div className="relative h-32 flex items-center justify-center">
              <AnimatePresence mode="wait">
                <motion.h1
                  key={currentTextIndex}
                  initial={{ opacity: 0, rotateX: 90 }}
                  animate={{ opacity: 1, rotateX: 0 }}
                  exit={{ opacity: 0, rotateX: -90 }}
                  transition={{ duration: 0.8 }}
                  className="text-6xl md:text-8xl lg:text-9xl font-bold text-glow"
                >
                  <span className="bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent">
                    {heroTexts[currentTextIndex]}
                  </span>
                </motion.h1>
              </AnimatePresence>
            </div>
            
            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1 }}
              className="text-xl md:text-2xl lg:text-3xl text-text-secondary max-w-4xl mx-auto leading-relaxed"
            >
              Welcome to an immersive 3D experience showcasing cutting-edge web development 
              with interactive microchip-themed design and modern technologies.
            </motion.p>
            
            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.3 }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            >
              <Button
                variant="primary"
                size="xl"
                className="animate-pulse-glow group"
                onClick={() => scrollToSection('about')}
              >
                <span className="group-hover:scale-110 transition-transform">
                  🚀 Explore Portfolio
                </span>
              </Button>
              
              <Button
                variant="outline"
                size="xl"
                onClick={() => scrollToSection('projects')}
                className="group"
              >
                <span className="group-hover:scale-110 transition-transform">
                  💼 View Projects
                </span>
              </Button>
              
              <Button
                variant="secondary"
                size="xl"
                onClick={() => scrollToSection('contact')}
                className="group"
              >
                <span className="group-hover:scale-110 transition-transform">
                  📧 Get In Touch
                </span>
              </Button>
            </motion.div>
            
            {/* Tech Stack Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.6 }}
              className="flex flex-wrap justify-center gap-4 mt-12"
            >
              {['React', 'Three.js', 'TypeScript', 'Next.js', 'Tailwind'].map((tech, index) => (
                <motion.div
                  key={tech}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 1.8 + (index * 0.1) }}
                  className="chip-border px-4 py-2 text-sm font-medium text-primary bg-bg-secondary/50 backdrop-blur-sm"
                >
                  {tech}
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </AnimatePresence>
      </div>
      
      {/* Theme Toggle */}
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 2 }}
        className="absolute top-6 right-6 z-30"
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleTheme}
          className="chip-border backdrop-blur-sm bg-bg-secondary/30 hover:bg-bg-secondary/50"
        >
          <motion.span
            key={theme}
            initial={{ rotate: 180, opacity: 0 }}
            animate={{ rotate: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-2xl"
          >
            {theme === 'dark' ? '☀️' : '🌙'}
          </motion.span>
        </Button>
      </motion.div>
      
      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 2.2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="flex flex-col items-center space-y-2 cursor-pointer"
          onClick={() => scrollToSection('about')}
        >
          <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center relative overflow-hidden">
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-primary rounded-full mt-2"
            />
          </div>
          <span className="text-xs text-text-secondary font-medium">Scroll Down</span>
        </motion.div>
      </motion.div>
      
      {/* Loading Overlay */}
      <AnimatePresence>
        {!isLoaded && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8 }}
            className="absolute inset-0 z-50 bg-bg-primary flex items-center justify-center"
          >
            <div className="text-center space-y-4">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto"
              />
              <motion.p
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.5, repeat: Infinity }}
                className="text-primary font-medium"
              >
                Initializing 3D Experience...
              </motion.p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}

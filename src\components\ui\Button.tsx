'use client';

import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { InteractiveElementProps } from '@/types';

interface ButtonProps extends InteractiveElementProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    fullWidth = false,
    leftIcon,
    rightIcon,
    children,
    disabled = false,
    loading = false,
    onClick,
    type = 'button',
    ...props
  }, ref) => {
    const baseClasses = [
      'inline-flex items-center justify-center',
      'font-medium transition-all duration-200',
      'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
      'disabled:pointer-events-none disabled:opacity-50',
      'relative overflow-hidden',
    ];

    const variantClasses = {
      primary: [
        'bg-gradient-to-r from-primary to-primary-dark',
        'text-white shadow-lg',
        'hover:shadow-xl hover:shadow-primary/25',
        'focus-visible:ring-primary',
        'glow-primary',
      ],
      secondary: [
        'bg-gradient-to-r from-secondary to-blue-600',
        'text-white shadow-lg',
        'hover:shadow-xl hover:shadow-secondary/25',
        'focus-visible:ring-secondary',
        'glow-secondary',
      ],
      accent: [
        'bg-gradient-to-r from-accent to-orange-600',
        'text-white shadow-lg',
        'hover:shadow-xl hover:shadow-accent/25',
        'focus-visible:ring-accent',
        'glow-accent',
      ],
      ghost: [
        'bg-transparent text-text-primary',
        'hover:bg-bg-secondary',
        'focus-visible:ring-primary',
      ],
      outline: [
        'border-2 border-primary bg-transparent',
        'text-primary',
        'hover:bg-primary hover:text-black',
        'focus-visible:ring-primary',
      ],
    };

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm rounded-md',
      md: 'px-4 py-2 text-base rounded-lg',
      lg: 'px-6 py-3 text-lg rounded-lg',
      xl: 'px-8 py-4 text-xl rounded-xl',
    };

    const widthClasses = fullWidth ? 'w-full' : '';

    return (
      <button
        ref={ref}
        type={type}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          widthClasses,
          className
        )}
        disabled={disabled || loading}
        onClick={onClick}
        {...props}
      >
        {/* Loading spinner */}
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          </div>
        )}
        
        {/* Button content */}
        <span className={cn('flex items-center gap-2', loading && 'opacity-0')}>
          {leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
          {children}
          {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
        </span>
        
        {/* Hover effect overlay */}
        <div className="absolute inset-0 bg-white/10 opacity-0 hover:opacity-100 transition-opacity duration-200" />
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button };

// Icon button variant
export const IconButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'leftIcon' | 'rightIcon'> & { icon: React.ReactNode }>(
  ({ icon, className, size = 'md', ...props }, ref) => {
    const iconSizeClasses = {
      sm: 'w-8 h-8',
      md: 'w-10 h-10',
      lg: 'w-12 h-12',
      xl: 'w-14 h-14',
    };

    return (
      <Button
        ref={ref}
        className={cn(
          'p-0 rounded-full',
          iconSizeClasses[size],
          className
        )}
        size={size}
        {...props}
      >
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

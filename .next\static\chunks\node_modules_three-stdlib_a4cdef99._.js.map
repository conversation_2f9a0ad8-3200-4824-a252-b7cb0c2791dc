{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "EventDispatcher.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/src/controls/EventDispatcher.ts"], "sourcesContent": ["/*\nDue to @types/three r168 breaking change\nwe have to manually copy the EventDispatcher class from three.js.\nSo this files merges the declarations from https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/three/src/core/EventDispatcher.d.ts\nwith the implementation from https://github.com/mrdoob/three.js/blob/dev/src/core/EventDispatcher.js\nMore info in https://github.com/pmndrs/three-stdlib/issues/387\n*/\n\n/**\n * The minimal basic Event that can be dispatched by a {@link EventDispatcher<>}.\n */\nexport interface BaseEvent<TEventType extends string = string> {\n    readonly type: TEventType;\n    // not defined in @types/three\n    target: any;\n}\n\n/**\n * The minimal expected contract of a fired Event that was dispatched by a {@link EventDispatcher<>}.\n */\nexport interface Event<TEventType extends string = string, TTarget = unknown> {\n    readonly type: TEventType;\n    readonly target: TTarget;\n}\n\nexport type EventListener<TEventData, TEventType extends string, TTarget> = (\n    event: TEventData & Event<TEventType, TTarget>,\n) => void;\n\nexport class EventDispatcher<TEventMap extends {} = {}> {\n    // not defined in @types/three\n    private _listeners: any;\n\n    /**\n     * Adds a listener to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n\taddEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) this._listeners = {};\n\n\t\tconst listeners = this._listeners;\n\n\t\tif ( listeners[ type ] === undefined ) {\n\n\t\t\tlisteners[ type ] = [];\n\n\t\t}\n\n\t\tif ( listeners[ type ].indexOf( listener ) === - 1 ) {\n\n\t\t\tlisteners[ type ].push( listener );\n\n\t\t}\n\n\t}\n\n\t/**\n     * Checks if listener is added to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n    hasEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): boolean {\n\n\t\tif ( this._listeners === undefined ) return false;\n\n\t\tconst listeners = this._listeners;\n\n\t\treturn listeners[ type ] !== undefined && listeners[ type ].indexOf( listener ) !== - 1;\n\n\t}\n\n\t/**\n     * Removes a listener from an event type.\n     * @param type The type of the listener that gets removed.\n     * @param listener The listener function that gets removed.\n     */\n    removeEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tconst index = listenerArray.indexOf( listener );\n\n\t\t\tif ( index !== - 1 ) {\n\n\t\t\t\tlistenerArray.splice( index, 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n     * Fire an event type.\n     * @param event The event that gets fired.\n     */\n    dispatchEvent<T extends Extract<keyof TEventMap, string>>(event: BaseEvent<T> & TEventMap[T]): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ event.type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tevent.target = this;\n\n\t\t\t// Make a copy, in case listeners are removed while iterating.\n\t\t\tconst array = listenerArray.slice( 0 );\n\n\t\t\tfor ( let i = 0, l = array.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ].call( this, event );\n\n\t\t\t}\n\n\t\t\tevent.target = null;\n\n\t\t}\n\n\t}\n\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;AA6BO,MAAM,gBAA2C;IAAjD,aAAA;QAEK,8BAAA;QAAA,cAAA,IAAA,EAAA;IAAA;IAAA;;;;GAAA,GAOX,iBACO,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY,IAAA,CAAK,UAAA,GAAa,CAAA;QAEvD,MAAM,YAAY,IAAA,CAAK,UAAA;QAElB,IAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,GAAY;YAE3B,SAAA,CAAA,IAAK,CAAA,GAAI,EAAA;QAErB;QAEA,IAAK,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA,GAAM;YAEzC,SAAA,CAAA,IAAK,CAAA,CAAE,IAAA,CAAM,QAAS;QAElC;IAED;IAAA;;;;MAAA,GAOG,iBACI,IAAA,EACA,QAAA,EACO;QAEb,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAmB,OAAA;QAE5C,MAAM,YAAY,IAAA,CAAK,UAAA;QAEhB,OAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,KAAa,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA;IAErF;IAAA;;;;MAAA,GAOG,oBACI,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,IAAK,CAAA;QAEtC,IAAK,kBAAkB,KAAA,GAAY;YAE5B,MAAA,QAAQ,cAAc,OAAA,CAAS,QAAS;YAE9C,IAAK,UAAU,CAAA,GAAM;gBAEN,cAAA,MAAA,CAAQ,OAAO,CAAE;YAEhC;QAED;IAED;IAAA;;;MAAA,GAMG,cAA0D,KAAA,EAA0C;QAEtG,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,MAAM,IAAK,CAAA;QAE5C,IAAK,kBAAkB,KAAA,GAAY;YAElC,MAAM,MAAA,GAAS,IAAA;YAGT,MAAA,QAAQ,cAAc,KAAA,CAAO,CAAE;YAErC,IAAA,IAAU,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAI,GAAG,IAAO;gBAEhD,KAAA,CAAO,CAAE,CAAA,CAAE,IAAA,CAAM,IAAA,EAAM,KAAM;YAE9B;YAEA,MAAM,MAAA,GAAS;QAEhB;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "file": "OrbitControls.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/src/controls/OrbitControls.ts"], "sourcesContent": ["import {\n  Matrix4,\n  MOUSE,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n  Ray,\n  Plane,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst _ray = /* @__PURE__ */ new Ray()\nconst _plane = /* @__PURE__ */ new Plane()\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180))\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n//\n//    Orbit - left mouse / touch: one-finger move\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n\nconst moduloWrapAround = (offset: number, capacity: number) => ((offset % capacity) + capacity) % capacity\n\nclass OrbitControls extends EventDispatcher<StandardControlsEventMap> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement | undefined\n  // Set to false to disable this control\n  enabled = true\n  // \"target\" sets the location of focus, where the object orbits around\n  target = new Vector3()\n  // How far you can dolly in and out ( PerspectiveCamera only )\n  minDistance = 0\n  maxDistance = Infinity\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0 // radians\n  maxPolarAngle = Math.PI // radians\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n  // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n  // Set to false to disable zooming\n  enableZoom = true\n  zoomSpeed = 1.0\n  // Set to false to disable rotating\n  enableRotate = true\n  rotateSpeed = 1.0\n  // Set to false to disable panning\n  enablePan = true\n  panSpeed = 1.0\n  screenSpacePanning = true // if false, pan orthogonal to world-space direction camera.up\n  keyPanSpeed = 7.0 // pixels moved per arrow key push\n  zoomToCursor = false\n  // Set to true to automatically rotate around the target\n  // If auto-rotate is enabled, you must call controls.update() in your animation loop\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per orbit when fps is 60\n  reverseOrbit = false // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n  reverseHorizontalOrbit = false // true if you want to reverse the horizontal orbit direction\n  reverseVerticalOrbit = false // true if you want to reverse the vertical orbit direction\n  // The four arrow keys\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n  // Mouse buttons\n  mouseButtons: Partial<{\n    LEFT: MOUSE\n    MIDDLE: MOUSE\n    RIGHT: MOUSE\n  }> = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n  // Touch fingers\n  touches: Partial<{\n    ONE: TOUCH\n    TWO: TOUCH\n  }> = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n  target0: Vector3\n  position0: Vector3\n  zoom0: number\n  // the target DOM element for key events\n  _domElementKeyEvents: any = null\n\n  getPolarAngle: () => number\n  getAzimuthalAngle: () => number\n  setPolarAngle: (x: number) => void\n  setAzimuthalAngle: (x: number) => void\n  getDistance: () => number\n  // Not used in most scenarios, however they can be useful for specific use cases\n  getZoomScale: () => number\n\n  listenToKeyEvents: (domElement: HTMLElement) => void\n  stopListenToKeyEvents: () => void\n  saveState: () => void\n  reset: () => void\n  update: () => void\n  connect: (domElement: HTMLElement) => void\n  dispose: () => void\n\n  // Dolly in programmatically\n  dollyIn: (dollyScale?: number) => void\n  // Dolly out programmatically\n  dollyOut: (dollyScale?: number) => void\n  // Get the current scale\n  getScale: () => number\n  // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n  setScale: (newScale: number) => void\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // public methods\n    //\n\n    this.getPolarAngle = (): number => spherical.phi\n\n    this.getAzimuthalAngle = (): number => spherical.theta\n\n    this.setPolarAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let phi = moduloWrapAround(value, 2 * Math.PI)\n      let currentPhi = spherical.phi\n\n      // convert to the equivalent shortest angle\n      if (currentPhi < 0) currentPhi += 2 * Math.PI\n      if (phi < 0) phi += 2 * Math.PI\n      let phiDist = Math.abs(phi - currentPhi)\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI\n        } else {\n          currentPhi += 2 * Math.PI\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi\n      scope.update()\n    }\n\n    this.setAzimuthalAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let theta = moduloWrapAround(value, 2 * Math.PI)\n      let currentTheta = spherical.theta\n\n      // convert to the equivalent shortest angle\n      if (currentTheta < 0) currentTheta += 2 * Math.PI\n      if (theta < 0) theta += 2 * Math.PI\n      let thetaDist = Math.abs(theta - currentTheta)\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI\n        } else {\n          currentTheta += 2 * Math.PI\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta\n      scope.update()\n    }\n\n    this.getDistance = (): number => scope.object.position.distanceTo(scope.target)\n\n    this.listenToKeyEvents = (domElement: HTMLElement): void => {\n      domElement.addEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = domElement\n    }\n\n    this.stopListenToKeyEvents = (): void => {\n      this._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = null\n    }\n\n    this.saveState = (): void => {\n      scope.target0.copy(scope.target)\n      scope.position0.copy(scope.object.position)\n      scope.zoom0 = scope.object.zoom\n    }\n\n    this.reset = (): void => {\n      scope.target.copy(scope.target0)\n      scope.object.position.copy(scope.position0)\n      scope.object.zoom = scope.zoom0\n      scope.object.updateProjectionMatrix()\n\n      // @ts-ignore\n      scope.dispatchEvent(changeEvent)\n\n      scope.update()\n\n      state = STATE.NONE\n    }\n\n    // this method is exposed, but perhaps it would be better if we can make it private...\n    this.update = ((): (() => void) => {\n      const offset = new Vector3()\n      const up = new Vector3(0, 1, 0)\n\n      // so camera.up is the orbit axis\n      const quat = new Quaternion().setFromUnitVectors(object.up, up)\n      const quatInverse = quat.clone().invert()\n\n      const lastPosition = new Vector3()\n      const lastQuaternion = new Quaternion()\n\n      const twoPI = 2 * Math.PI\n\n      return function update(): boolean {\n        const position = scope.object.position\n\n        // update new up direction\n        quat.setFromUnitVectors(object.up, up)\n        quatInverse.copy(quat).invert()\n\n        offset.copy(position).sub(scope.target)\n\n        // rotate offset to \"y-axis-is-up\" space\n        offset.applyQuaternion(quat)\n\n        // angle from z-axis around y-axis\n        spherical.setFromVector3(offset)\n\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle())\n        }\n\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor\n        } else {\n          spherical.theta += sphericalDelta.theta\n          spherical.phi += sphericalDelta.phi\n        }\n\n        // restrict theta to be between desired limits\n\n        let min = scope.minAzimuthAngle\n        let max = scope.maxAzimuthAngle\n\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI) min += twoPI\n          else if (min > Math.PI) min -= twoPI\n\n          if (max < -Math.PI) max += twoPI\n          else if (max > Math.PI) max -= twoPI\n\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta))\n          } else {\n            spherical.theta =\n              spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta)\n          }\n        }\n\n        // restrict phi to be between desired limits\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi))\n        spherical.makeSafe()\n\n        // move target to panned location\n\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor)\n        } else {\n          scope.target.add(panOffset)\n        }\n\n        // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n        // we adjust zoom later in these cases\n        if ((scope.zoomToCursor && performCursorZoom) || (scope.object as OrthographicCamera).isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius)\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale)\n        }\n\n        offset.setFromSpherical(spherical)\n\n        // rotate offset back to \"camera-up-vector-is-up\" space\n        offset.applyQuaternion(quatInverse)\n\n        position.copy(scope.target).add(offset)\n\n        if (!scope.object.matrixAutoUpdate) scope.object.updateMatrix()\n        scope.object.lookAt(scope.target)\n\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor\n          sphericalDelta.phi *= 1 - scope.dampingFactor\n\n          panOffset.multiplyScalar(1 - scope.dampingFactor)\n        } else {\n          sphericalDelta.set(0, 0, 0)\n\n          panOffset.set(0, 0, 0)\n        }\n\n        // adjust camera position\n        let zoomChanged = false\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null\n          if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            // move the camera down the pointer ray\n            // this method avoids floating point error\n            const prevRadius = offset.length()\n            newRadius = clampDistance(prevRadius * scale)\n\n            const radiusDelta = prevRadius - newRadius\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta)\n            scope.object.updateMatrixWorld()\n          } else if ((scope.object as OrthographicCamera).isOrthographicCamera) {\n            // adjust the ortho camera position based on zoom changes\n            const mouseBefore = new Vector3(mouse.x, mouse.y, 0)\n            mouseBefore.unproject(scope.object)\n\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n            zoomChanged = true\n\n            const mouseAfter = new Vector3(mouse.x, mouse.y, 0)\n            mouseAfter.unproject(scope.object)\n\n            scope.object.position.sub(mouseAfter).add(mouseBefore)\n            scope.object.updateMatrixWorld()\n\n            newRadius = offset.length()\n          } else {\n            console.warn('WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.')\n            scope.zoomToCursor = false\n          }\n\n          // handle the placement of the target\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              // position the orbit target in front of the new camera position\n              scope.target\n                .set(0, 0, -1)\n                .transformDirection(scope.object.matrix)\n                .multiplyScalar(newRadius)\n                .add(scope.object.position)\n            } else {\n              // get the ray and translation plane to compute target\n              _ray.origin.copy(scope.object.position)\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix)\n\n              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n              // extremely large values\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target)\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target)\n                _ray.intersectPlane(_plane, scope.target)\n              }\n            }\n          }\n        } else if (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1\n\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n          }\n        }\n\n        scale = 1\n        performCursorZoom = false\n\n        // update condition is:\n        // min(camera displacement, camera rotation in radians)^2 > EPS\n        // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n        if (\n          zoomChanged ||\n          lastPosition.distanceToSquared(scope.object.position) > EPS ||\n          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS\n        ) {\n          // @ts-ignore\n          scope.dispatchEvent(changeEvent)\n\n          lastPosition.copy(scope.object.position)\n          lastQuaternion.copy(scope.object.quaternion)\n          zoomChanged = false\n\n          return true\n        }\n\n        return false\n      }\n    })()\n\n    // https://github.com/mrdoob/three.js/issues/20575\n    this.connect = (domElement: HTMLElement): void => {\n      scope.domElement = domElement\n      // disables touch scroll\n      // touch-action needs to be defined for pointer events to work on mobile\n      // https://stackoverflow.com/a/48254578\n      scope.domElement.style.touchAction = 'none'\n      scope.domElement.addEventListener('contextmenu', onContextMenu)\n      scope.domElement.addEventListener('pointerdown', onPointerDown)\n      scope.domElement.addEventListener('pointercancel', onPointerUp)\n      scope.domElement.addEventListener('wheel', onMouseWheel)\n    }\n\n    this.dispose = (): void => {\n      // Enabling touch scroll\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = 'auto'\n      }\n      scope.domElement?.removeEventListener('contextmenu', onContextMenu)\n      scope.domElement?.removeEventListener('pointerdown', onPointerDown)\n      scope.domElement?.removeEventListener('pointercancel', onPointerUp)\n      scope.domElement?.removeEventListener('wheel', onMouseWheel)\n      scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n      scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      }\n      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n    }\n\n    //\n    // internals\n    //\n\n    const scope = this\n\n    const changeEvent = { type: 'change' }\n    const startEvent = { type: 'start' }\n    const endEvent = { type: 'end' }\n\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6,\n    }\n\n    let state = STATE.NONE\n\n    const EPS = 0.000001\n\n    // current position in spherical coordinates\n    const spherical = new Spherical()\n    const sphericalDelta = new Spherical()\n\n    let scale = 1\n    const panOffset = new Vector3()\n\n    const rotateStart = new Vector2()\n    const rotateEnd = new Vector2()\n    const rotateDelta = new Vector2()\n\n    const panStart = new Vector2()\n    const panEnd = new Vector2()\n    const panDelta = new Vector2()\n\n    const dollyStart = new Vector2()\n    const dollyEnd = new Vector2()\n    const dollyDelta = new Vector2()\n\n    const dollyDirection = new Vector3()\n    const mouse = new Vector2()\n    let performCursorZoom = false\n\n    const pointers: PointerEvent[] = []\n    const pointerPositions: { [key: string]: Vector2 } = {}\n\n    function getAutoRotationAngle(): number {\n      return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed\n    }\n\n    function getZoomScale(): number {\n      return Math.pow(0.95, scope.zoomSpeed)\n    }\n\n    function rotateLeft(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle\n      } else {\n        sphericalDelta.theta -= angle\n      }\n    }\n\n    function rotateUp(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle\n      } else {\n        sphericalDelta.phi -= angle\n      }\n    }\n\n    const panLeft = (() => {\n      const v = new Vector3()\n\n      return function panLeft(distance: number, objectMatrix: Matrix4) {\n        v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n        v.multiplyScalar(-distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    const panUp = (() => {\n      const v = new Vector3()\n\n      return function panUp(distance: number, objectMatrix: Matrix4) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1)\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0)\n          v.crossVectors(scope.object.up, v)\n        }\n\n        v.multiplyScalar(distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    // deltaX and deltaY are in pixels; right and down are positive\n    const pan = (() => {\n      const offset = new Vector3()\n\n      return function pan(deltaX: number, deltaY: number) {\n        const element = scope.domElement\n\n        if (element && scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          // perspective\n          const position = scope.object.position\n          offset.copy(position).sub(scope.target)\n          let targetDistance = offset.length()\n\n          // half of the fov is center to top of screen\n          targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0)\n\n          // we use only clientHeight here so aspect ratio does not distort speed\n          panLeft((2 * deltaX * targetDistance) / element.clientHeight, scope.object.matrix)\n          panUp((2 * deltaY * targetDistance) / element.clientHeight, scope.object.matrix)\n        } else if (element && scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          // orthographic\n          panLeft(\n            (deltaX * (scope.object.right - scope.object.left)) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix,\n          )\n          panUp(\n            (deltaY * (scope.object.top - scope.object.bottom)) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix,\n          )\n        } else {\n          // camera neither orthographic nor perspective\n          console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.')\n          scope.enablePan = false\n        }\n      }\n    })()\n\n    function setScale(newScale: number) {\n      if (\n        (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) ||\n        (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera)\n      ) {\n        scale = newScale\n      } else {\n        console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.')\n        scope.enableZoom = false\n      }\n    }\n\n    function dollyOut(dollyScale: number) {\n      setScale(scale / dollyScale)\n    }\n\n    function dollyIn(dollyScale: number) {\n      setScale(scale * dollyScale)\n    }\n\n    function updateMouseParameters(event: MouseEvent): void {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return\n      }\n\n      performCursorZoom = true\n\n      const rect = scope.domElement.getBoundingClientRect()\n      const x = event.clientX - rect.left\n      const y = event.clientY - rect.top\n      const w = rect.width\n      const h = rect.height\n\n      mouse.x = (x / w) * 2 - 1\n      mouse.y = -(y / h) * 2 + 1\n\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize()\n    }\n\n    function clampDistance(dist: number): number {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist))\n    }\n\n    //\n    // event callbacks - update the object state\n    //\n\n    function handleMouseDownRotate(event: MouseEvent) {\n      rotateStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownDolly(event: MouseEvent) {\n      updateMouseParameters(event)\n      dollyStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownPan(event: MouseEvent) {\n      panStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseMoveRotate(event: MouseEvent) {\n      rotateEnd.set(event.clientX, event.clientY)\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n      scope.update()\n    }\n\n    function handleMouseMoveDolly(event: MouseEvent) {\n      dollyEnd.set(event.clientX, event.clientY)\n      dollyDelta.subVectors(dollyEnd, dollyStart)\n\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale())\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale())\n      }\n\n      dollyStart.copy(dollyEnd)\n      scope.update()\n    }\n\n    function handleMouseMovePan(event: MouseEvent) {\n      panEnd.set(event.clientX, event.clientY)\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n      scope.update()\n    }\n\n    function handleMouseWheel(event: WheelEvent) {\n      updateMouseParameters(event)\n\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale())\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale())\n      }\n\n      scope.update()\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      let needsUpdate = false\n\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n      }\n\n      if (needsUpdate) {\n        // prevent the browser from scrolling on cursor keys\n        event.preventDefault()\n        scope.update()\n      }\n    }\n\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        rotateStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        panStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX\n      const dy = pointers[0].pageY - pointers[1].pageY\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyStart.set(0, distance)\n    }\n\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enablePan) handleTouchStartPan()\n    }\n\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enableRotate) handleTouchStartRotate()\n    }\n\n    function handleTouchMoveRotate(event: PointerEvent) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        rotateEnd.set(x, y)\n      }\n\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n    }\n\n    function handleTouchMovePan(event: PointerEvent) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        panEnd.set(x, y)\n      }\n\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n    }\n\n    function handleTouchMoveDolly(event: PointerEvent) {\n      const position = getSecondPointerPosition(event)\n      const dx = event.pageX - position.x\n      const dy = event.pageY - position.y\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyEnd.set(0, distance)\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed))\n      dollyOut(dollyDelta.y)\n      dollyStart.copy(dollyEnd)\n    }\n\n    function handleTouchMoveDollyPan(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enablePan) handleTouchMovePan(event)\n    }\n\n    function handleTouchMoveDollyRotate(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enableRotate) handleTouchMoveRotate(event)\n    }\n\n    //\n    // event handlers - FSM: listen for events and reset state\n    //\n\n    function onPointerDown(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (pointers.length === 0) {\n        scope.domElement?.ownerDocument.addEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.addEventListener('pointerup', onPointerUp)\n      }\n\n      addPointer(event)\n\n      if (event.pointerType === 'touch') {\n        onTouchStart(event)\n      } else {\n        onMouseDown(event)\n      }\n    }\n\n    function onPointerMove(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (event.pointerType === 'touch') {\n        onTouchMove(event)\n      } else {\n        onMouseMove(event)\n      }\n    }\n\n    function onPointerUp(event: PointerEvent) {\n      removePointer(event)\n\n      if (pointers.length === 0) {\n        scope.domElement?.releasePointerCapture(event.pointerId)\n\n        scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      }\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n\n      state = STATE.NONE\n    }\n\n    function onMouseDown(event: MouseEvent) {\n      let mouseAction\n\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT\n          break\n\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE\n          break\n\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT\n          break\n\n        default:\n          mouseAction = -1\n      }\n\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseDownDolly(event)\n          state = STATE.DOLLY\n          break\n\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          } else {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          }\n          break\n\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          } else {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          }\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onMouseMove(event: MouseEvent) {\n      if (scope.enabled === false) return\n\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false) return\n          handleMouseMoveRotate(event)\n          break\n\n        case STATE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseMoveDolly(event)\n          break\n\n        case STATE.PAN:\n          if (scope.enablePan === false) return\n          handleMouseMovePan(event)\n          break\n      }\n    }\n\n    function onMouseWheel(event: WheelEvent) {\n      if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) {\n        return\n      }\n\n      event.preventDefault()\n\n      // @ts-ignore\n      scope.dispatchEvent(startEvent)\n\n      handleMouseWheel(event)\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n    }\n\n    function onKeyDown(event: KeyboardEvent) {\n      if (scope.enabled === false || scope.enablePan === false) return\n      handleKeyDown(event)\n    }\n\n    function onTouchStart(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (scope.enableRotate === false) return\n              handleTouchStartRotate()\n              state = STATE.TOUCH_ROTATE\n              break\n\n            case TOUCH.PAN:\n              if (scope.enablePan === false) return\n              handleTouchStartPan()\n              state = STATE.TOUCH_PAN\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        case 2:\n          switch (scope.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false) return\n              handleTouchStartDollyPan()\n              state = STATE.TOUCH_DOLLY_PAN\n              break\n\n            case TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false) return\n              handleTouchStartDollyRotate()\n              state = STATE.TOUCH_DOLLY_ROTATE\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onTouchMove(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false) return\n          handleTouchMoveRotate(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false) return\n          handleTouchMovePan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false) return\n          handleTouchMoveDollyPan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false) return\n          handleTouchMoveDollyRotate(event)\n          scope.update()\n          break\n\n        default:\n          state = STATE.NONE\n      }\n    }\n\n    function onContextMenu(event: Event) {\n      if (scope.enabled === false) return\n      event.preventDefault()\n    }\n\n    function addPointer(event: PointerEvent) {\n      pointers.push(event)\n    }\n\n    function removePointer(event: PointerEvent) {\n      delete pointerPositions[event.pointerId]\n\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1)\n          return\n        }\n      }\n    }\n\n    function trackPointer(event: PointerEvent) {\n      let position = pointerPositions[event.pointerId]\n\n      if (position === undefined) {\n        position = new Vector2()\n        pointerPositions[event.pointerId] = position\n      }\n\n      position.set(event.pageX, event.pageY)\n    }\n\n    function getSecondPointerPosition(event: PointerEvent) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0]\n      return pointerPositions[pointer.pointerId]\n    }\n\n    // Add dolly in/out methods for public API\n\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale)\n      scope.update()\n    }\n\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale)\n      scope.update()\n    }\n\n    this.getScale = () => {\n      return scale\n    }\n\n    this.setScale = (newScale) => {\n      setScale(newScale)\n      scope.update()\n    }\n\n    this.getZoomScale = () => {\n      return getZoomScale()\n    }\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n    // force an update at start\n    this.update()\n  }\n}\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n// This is very similar to OrbitControls, another set of touch behavior\n//\n//    Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - left mouse, or arrow keys / touch: one-finger move\n\nclass MapControls extends OrbitControls {\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super(object, domElement)\n\n    this.screenSpacePanning = false // pan orthogonal to world-space direction camera.up\n\n    this.mouseButtons.LEFT = MOUSE.PAN\n    this.mouseButtons.RIGHT = MOUSE.ROTATE\n\n    this.touches.ONE = TOUCH.PAN\n    this.touches.TWO = TOUCH.DOLLY_ROTATE\n  }\n}\n\nexport { OrbitControls, MapControls }\n"], "names": ["dom<PERSON>lement", "panLeft", "panUp", "pan"], "mappings": ";;;;;;;;;;;;;;;;;;;AAgBA,MAAM,OAAA,aAAA,GAAA,uJAA2B,MAAA;AACjC,MAAM,SAAA,aAAA,GAAA,uJAA6B,QAAA;AACnC,MAAM,aAAa,KAAK,GAAA,CAAI,KAAA,CAAM,KAAK,EAAA,GAAK,GAAA,CAAI;AAShD,MAAM,mBAAmB,CAAC,QAAgB,WAAA,CAAuB,SAAS,WAAY,QAAA,IAAY;AAElG,MAAM,wLAAsB,kBAAA,CAA0C;IA6FpE,YAAY,MAAA,EAAgD,UAAA,CAA0B;QAC9E,KAAA;QA7FR,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,uCAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QAEV,sEAAA;QAAA,cAAA,IAAA,EAAA,UAAS,uJAAI,UAAA;QAEb,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,eAAc;QAEd,8DAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QACV,cAAA,IAAA,EAAA,WAAU;QAGV,4DAAA;QAAA,iCAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,UAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB,KAAK,EAAA;QAGrB,UAAA;QAAA,8DAAA;QAAA,0GAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB,CAAA;QAClB,UAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB;QAGlB,UAAA;QAAA,0CAAA;QAAA,gFAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,cAAA,IAAA,EAAA,iBAAgB;QAGhB,gGAAA;QAAA,kCAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,aAAY;QAEZ,mCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,cAAA,IAAA,EAAA,eAAc;QAEd,kCAAA;QAAA,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,sBAAqB;QACrB,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,kCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QAGf,wDAAA;QAAA,oFAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,mBAAkB;QAClB,sCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,uFAAA;QAAA,cAAA,IAAA,EAAA,0BAAyB;QACzB,6DAAA;QAAA,cAAA,IAAA,EAAA,wBAAuB;QAEvB,2DAAA;QAAA,sBAAA;QAAA,cAAA,IAAA,EAAA,QAAO;YAAE,MAAM;YAAa,IAAI;YAAW,OAAO;YAAc,QAAQ;QAAA;QAExE,gBAAA;QAAA,cAAA,IAAA,EAAA,gBAIK;YACH,MAAM,2JAAA,CAAM,MAAA;YACZ,2JAAQ,QAAA,CAAM,KAAA;YACd,0JAAO,QAAA,CAAM,GAAA;QAAA;QAGf,gBAAA;QAAA,cAAA,IAAA,EAAA,WAGK;YAAE,wJAAK,QAAA,CAAM,MAAA;YAAQ,wJAAK,QAAA,CAAM,SAAA;QAAA;QACrC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,wCAAA;QAAA,cAAA,IAAA,EAAA,wBAA4B;QAE5B,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,gFAAA;QAAA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAGA,4BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,6BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,wBAAA;QAAA,cAAA,IAAA,EAAA;QAEA,kHAAA;QAAA,cAAA,IAAA,EAAA;QAKE,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,UAAA,GAAa;QAGb,IAAA,CAAA,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM;QACjC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,KAAA,CAAM;QACvC,IAAA,CAAA,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;QAMpB,IAAA,CAAA,aAAA,GAAgB,IAAc,UAAU,GAAA;QAExC,IAAA,CAAA,iBAAA,GAAoB,IAAc,UAAU,KAAA;QAE5C,IAAA,CAAA,aAAA,GAAgB,CAAC,UAAwB;YAE5C,IAAI,MAAM,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC7C,IAAI,aAAa,UAAU,GAAA;YAG3B,IAAI,aAAa,GAAG,cAAc,IAAI,KAAK,EAAA;YAC3C,IAAI,MAAM,GAAG,OAAO,IAAI,KAAK,EAAA;YAC7B,IAAI,UAAU,KAAK,GAAA,CAAI,MAAM,UAAU;YACvC,IAAI,IAAI,KAAK,EAAA,GAAK,UAAU,SAAS;gBACnC,IAAI,MAAM,YAAY;oBACpB,OAAO,IAAI,KAAK,EAAA;gBAAA,OACX;oBACL,cAAc,IAAI,KAAK,EAAA;gBACzB;YACF;YACA,eAAe,GAAA,GAAM,MAAM;YAC3B,MAAM,MAAA,CAAO;QAAA;QAGV,IAAA,CAAA,iBAAA,GAAoB,CAAC,UAAwB;YAEhD,IAAI,QAAQ,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC/C,IAAI,eAAe,UAAU,KAAA;YAG7B,IAAI,eAAe,GAAG,gBAAgB,IAAI,KAAK,EAAA;YAC/C,IAAI,QAAQ,GAAG,SAAS,IAAI,KAAK,EAAA;YACjC,IAAI,YAAY,KAAK,GAAA,CAAI,QAAQ,YAAY;YAC7C,IAAI,IAAI,KAAK,EAAA,GAAK,YAAY,WAAW;gBACvC,IAAI,QAAQ,cAAc;oBACxB,SAAS,IAAI,KAAK,EAAA;gBAAA,OACb;oBACL,gBAAgB,IAAI,KAAK,EAAA;gBAC3B;YACF;YACA,eAAe,KAAA,GAAQ,QAAQ;YAC/B,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,WAAA,GAAc,IAAc,MAAM,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,MAAM,MAAM;QAEzE,IAAA,CAAA,iBAAA,GAAoB,CAACA,gBAAkC;YAC1DA,YAAW,gBAAA,CAAiB,WAAW,SAAS;YAChD,IAAA,CAAK,oBAAA,GAAuBA;QAAA;QAG9B,IAAA,CAAK,qBAAA,GAAwB,MAAY;YAClC,IAAA,CAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YAClE,IAAA,CAAK,oBAAA,GAAuB;QAAA;QAG9B,IAAA,CAAK,SAAA,GAAY,MAAY;YACrB,MAAA,OAAA,CAAQ,IAAA,CAAK,MAAM,MAAM;YAC/B,MAAM,SAAA,CAAU,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;YACpC,MAAA,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA;QAAA;QAG7B,IAAA,CAAK,KAAA,GAAQ,MAAY;YACjB,MAAA,MAAA,CAAO,IAAA,CAAK,MAAM,OAAO;YAC/B,MAAM,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,MAAM,SAAS;YACpC,MAAA,MAAA,CAAO,IAAA,GAAO,MAAM,KAAA;YAC1B,MAAM,MAAA,CAAO,sBAAA;YAGb,MAAM,aAAA,CAAc,WAAW;YAE/B,MAAM,MAAA,CAAO;YAEb,QAAQ,MAAM,IAAA;QAAA;QAIhB,IAAA,CAAK,MAAA,GAAA,CAAU,MAAoB;YAC3B,MAAA,SAAS,uJAAI,UAAA;YACnB,MAAM,KAAK,uJAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;YAG9B,MAAM,OAAO,uJAAI,aAAA,GAAa,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;YAC9D,MAAM,cAAc,KAAK,KAAA,CAAM,EAAE,MAAA,CAAO;YAElC,MAAA,eAAe,uJAAI,UAAA;YACnB,MAAA,iBAAiB,uJAAI,aAAA;YAErB,MAAA,QAAQ,IAAI,KAAK,EAAA;YAEvB,OAAO,SAAS,SAAkB;gBAC1B,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;gBAGzB,KAAA,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;gBACzB,YAAA,IAAA,CAAK,IAAI,EAAE,MAAA,CAAO;gBAE9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;gBAGtC,OAAO,eAAA,CAAgB,IAAI;gBAG3B,UAAU,cAAA,CAAe,MAAM;gBAE/B,IAAI,MAAM,UAAA,IAAc,UAAU,MAAM,IAAA,EAAM;oBAC5C,WAAW,sBAAsB;gBACnC;gBAEA,IAAI,MAAM,aAAA,EAAe;oBACb,UAAA,KAAA,IAAS,eAAe,KAAA,GAAQ,MAAM,aAAA;oBACtC,UAAA,GAAA,IAAO,eAAe,GAAA,GAAM,MAAM,aAAA;gBAAA,OACvC;oBACL,UAAU,KAAA,IAAS,eAAe,KAAA;oBAClC,UAAU,GAAA,IAAO,eAAe,GAAA;gBAClC;gBAIA,IAAI,MAAM,MAAM,eAAA;gBAChB,IAAI,MAAM,MAAM,eAAA;gBAEhB,IAAI,SAAS,GAAG,KAAK,SAAS,GAAG,GAAG;oBAC9B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE3B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE/B,IAAI,OAAO,KAAK;wBACJ,UAAA,KAAA,GAAQ,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,CAAC;oBAAA,OACzD;wBACL,UAAU,KAAA,GACR,UAAU,KAAA,GAAA,CAAS,MAAM,GAAA,IAAO,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK;oBACtG;gBACF;gBAGU,UAAA,GAAA,GAAM,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,UAAU,GAAG,CAAC;gBAC1F,UAAU,QAAA,CAAS;gBAIf,IAAA,MAAM,aAAA,KAAkB,MAAM;oBAChC,MAAM,MAAA,CAAO,eAAA,CAAgB,WAAW,MAAM,aAAa;gBAAA,OACtD;oBACC,MAAA,MAAA,CAAO,GAAA,CAAI,SAAS;gBAC5B;gBAIA,IAAK,MAAM,YAAA,IAAgB,qBAAuB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;oBAChG,UAAA,MAAA,GAAS,cAAc,UAAU,MAAM;gBAAA,OAC5C;oBACL,UAAU,MAAA,GAAS,cAAc,UAAU,MAAA,GAAS,KAAK;gBAC3D;gBAEA,OAAO,gBAAA,CAAiB,SAAS;gBAGjC,OAAO,eAAA,CAAgB,WAAW;gBAElC,SAAS,IAAA,CAAK,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM;gBAElC,IAAA,CAAC,MAAM,MAAA,CAAO,gBAAA,EAAkB,MAAM,MAAA,CAAO,YAAA;gBAC3C,MAAA,MAAA,CAAO,MAAA,CAAO,MAAM,MAAM;gBAE5B,IAAA,MAAM,aAAA,KAAkB,MAAM;oBACjB,eAAA,KAAA,IAAS,IAAI,MAAM,aAAA;oBACnB,eAAA,GAAA,IAAO,IAAI,MAAM,aAAA;oBAEtB,UAAA,cAAA,CAAe,IAAI,MAAM,aAAa;gBAAA,OAC3C;oBACU,eAAA,GAAA,CAAI,GAAG,GAAG,CAAC;oBAEhB,UAAA,GAAA,CAAI,GAAG,GAAG,CAAC;gBACvB;gBAGA,IAAI,cAAc;gBACd,IAAA,MAAM,YAAA,IAAgB,mBAAmB;oBAC3C,IAAI,YAAY;oBAChB,IAAI,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;wBAG3E,MAAA,aAAa,OAAO,MAAA;wBACd,YAAA,cAAc,aAAa,KAAK;wBAE5C,MAAM,cAAc,aAAa;wBACjC,MAAM,MAAA,CAAO,QAAA,CAAS,eAAA,CAAgB,gBAAgB,WAAW;wBACjE,MAAM,MAAA,CAAO,iBAAA;oBAAkB,OAAA,IACrB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;wBAEpE,MAAM,cAAc,uJAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,YAAA,SAAA,CAAU,MAAM,MAAM;wBAElC,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;wBACC,cAAA;wBAEd,MAAM,aAAa,uJAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,WAAA,SAAA,CAAU,MAAM,MAAM;wBAEjC,MAAM,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,UAAU,EAAE,GAAA,CAAI,WAAW;wBACrD,MAAM,MAAA,CAAO,iBAAA;wBAEb,YAAY,OAAO,MAAA;oBAAO,OACrB;wBACL,QAAQ,IAAA,CAAK,yFAAyF;wBACtG,MAAM,YAAA,GAAe;oBACvB;oBAGA,IAAI,cAAc,MAAM;wBACtB,IAAI,MAAM,kBAAA,EAAoB;4BAE5B,MAAM,MAAA,CACH,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EACZ,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM,EACtC,cAAA,CAAe,SAAS,EACxB,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ;wBAAA,OACvB;4BAEL,KAAK,MAAA,CAAO,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;4BACjC,KAAA,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EAAE,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM;4BAI/D,IAAA,KAAK,GAAA,CAAI,MAAM,MAAA,CAAO,EAAA,CAAG,GAAA,CAAI,KAAK,SAAS,CAAC,IAAI,YAAY;gCACvD,OAAA,MAAA,CAAO,MAAM,MAAM;4BAAA,OACrB;gCACL,OAAO,6BAAA,CAA8B,MAAM,MAAA,CAAO,EAAA,EAAI,MAAM,MAAM;gCAC7D,KAAA,cAAA,CAAe,QAAQ,MAAM,MAAM;4BAC1C;wBACF;oBACF;gBAAA,OAAA,IACS,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAC1F,cAAc,UAAU;oBAExB,IAAI,aAAa;wBACf,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;oBACf;gBACF;gBAEQ,QAAA;gBACY,oBAAA;gBAMpB,IACE,eACA,aAAa,iBAAA,CAAkB,MAAM,MAAA,CAAO,QAAQ,IAAI,OACxD,IAAA,CAAK,IAAI,eAAe,GAAA,CAAI,MAAM,MAAA,CAAO,UAAU,CAAA,IAAK,KACxD;oBAEA,MAAM,aAAA,CAAc,WAAW;oBAElB,aAAA,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;oBACxB,eAAA,IAAA,CAAK,MAAM,MAAA,CAAO,UAAU;oBAC7B,cAAA;oBAEP,OAAA;gBACT;gBAEO,OAAA;YAAA;QACT,CAAA;QAIG,IAAA,CAAA,OAAA,GAAU,CAACA,gBAAkC;YAChD,MAAM,UAAA,GAAaA;YAIb,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YAC/B,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,iBAAiB,WAAW;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,SAAS,YAAY;QAAA;QAGzD,IAAA,CAAK,OAAA,GAAU,MAAY;;YAEzB,IAAI,MAAM,UAAA,EAAY;gBACd,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YACvC;YACM,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,iBAAiB;YACjD,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,SAAS;YAC/C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;YACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YAC7D,IAAA,MAAM,oBAAA,KAAyB,MAAM;gBACjC,MAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YACrE;QAAA;QAQF,MAAM,QAAQ,IAAA;QAER,MAAA,cAAc;YAAE,MAAM;QAAA;QACtB,MAAA,aAAa;YAAE,MAAM;QAAA;QACrB,MAAA,WAAW;YAAE,MAAM;QAAA;QAEzB,MAAM,QAAQ;YACZ,MAAM,CAAA;YACN,QAAQ;YACR,OAAO;YACP,KAAK;YACL,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,oBAAoB;QAAA;QAGtB,IAAI,QAAQ,MAAM,IAAA;QAElB,MAAM,MAAM;QAGN,MAAA,YAAY,uJAAI,YAAA;QAChB,MAAA,iBAAiB,uJAAI,YAAA;QAE3B,IAAI,QAAQ;QACN,MAAA,YAAY,uJAAI,UAAA;QAEhB,MAAA,cAAc,uJAAI,UAAA;QAClB,MAAA,YAAY,uJAAI,UAAA;QAChB,MAAA,cAAc,uJAAI,UAAA;QAElB,MAAA,WAAW,uJAAI,UAAA;QACf,MAAA,SAAS,IAAI,6JAAA;QACb,MAAA,WAAW,uJAAI,UAAA;QAEf,MAAA,aAAa,uJAAI,UAAA;QACjB,MAAA,WAAW,uJAAI,UAAA;QACf,MAAA,aAAa,uJAAI,UAAA;QAEjB,MAAA,iBAAiB,uJAAI,UAAA;QACrB,MAAA,QAAQ,uJAAI,UAAA;QAClB,IAAI,oBAAoB;QAExB,MAAM,WAA2B,CAAA,CAAA;QACjC,MAAM,mBAA+C,CAAA;QAErD,SAAS,uBAA+B;YACtC,OAAS,IAAI,KAAK,EAAA,GAAM,KAAK,KAAM,MAAM,eAAA;QAC3C;QAEA,SAAS,eAAuB;YAC9B,OAAO,KAAK,GAAA,CAAI,MAAM,MAAM,SAAS;QACvC;QAEA,SAAS,WAAW,KAAA,EAAqB;YACnC,IAAA,MAAM,YAAA,IAAgB,MAAM,sBAAA,EAAwB;gBACtD,eAAe,KAAA,IAAS;YAAA,OACnB;gBACL,eAAe,KAAA,IAAS;YAC1B;QACF;QAEA,SAAS,SAAS,KAAA,EAAqB;YACjC,IAAA,MAAM,YAAA,IAAgB,MAAM,oBAAA,EAAsB;gBACpD,eAAe,GAAA,IAAO;YAAA,OACjB;gBACL,eAAe,GAAA,IAAO;YACxB;QACF;QAEA,MAAM,UAAA,CAAW,MAAM;YACf,MAAA,IAAI,uJAAI,UAAA;YAEP,OAAA,SAASC,SAAQ,QAAA,EAAkB,YAAA,EAAuB;gBAC7D,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBACnC,EAAA,cAAA,CAAe,CAAC,QAAQ;gBAE1B,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAGF,MAAM,QAAA,CAAS,MAAM;YACb,MAAA,IAAI,uJAAI,UAAA;YAEP,OAAA,SAASC,OAAM,QAAA,EAAkB,YAAA,EAAuB;gBACzD,IAAA,MAAM,kBAAA,KAAuB,MAAM;oBACnC,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBAAA,OAChC;oBACH,EAAA,mBAAA,CAAoB,cAAc,CAAC;oBACrC,EAAE,YAAA,CAAa,MAAM,MAAA,CAAO,EAAA,EAAI,CAAC;gBACnC;gBAEA,EAAE,cAAA,CAAe,QAAQ;gBAEzB,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAIF,MAAM,MAAA,CAAO,MAAM;YACX,MAAA,SAAS,uJAAI,UAAA;YAEZ,OAAA,SAASC,KAAI,MAAA,EAAgB,MAAA,EAAgB;gBAClD,MAAM,UAAU,MAAM,UAAA;gBAEtB,IAAI,WAAW,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;oBAEtF,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;oBAC9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;oBAClC,IAAA,iBAAiB,OAAO,MAAA;oBAGV,kBAAA,KAAK,GAAA,CAAM,MAAM,MAAA,CAAO,GAAA,GAAM,IAAK,KAAK,EAAA,GAAM,GAAK;oBAGrE,QAAS,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;oBACjF,MAAO,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;gBAAA,OAAA,IACtE,WAAW,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAErG,QACG,SAAA,CAAU,MAAM,MAAA,CAAO,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA,IAAS,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,WAAA,EAClF,MAAM,MAAA,CAAO,MAAA;oBAEf,MACG,SAAA,CAAU,MAAM,MAAA,CAAO,GAAA,GAAM,MAAM,MAAA,CAAO,MAAA,IAAW,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,YAAA,EAClF,MAAM,MAAA,CAAO,MAAA;gBACf,OACK;oBAEL,QAAQ,IAAA,CAAK,8EAA8E;oBAC3F,MAAM,SAAA,GAAY;gBACpB;YAAA;QACF,CAAA;QAGF,SAAS,SAAS,QAAA,EAAkB;YAE/B,IAAA,MAAM,MAAA,+JAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,IAC1D,MAAM,MAAA,+JAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAC5D;gBACQ,QAAA;YAAA,OACH;gBACL,QAAQ,IAAA,CAAK,qFAAqF;gBAClG,MAAM,UAAA,GAAa;YACrB;QACF;QAEA,SAAS,SAAS,UAAA,EAAoB;YACpC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,QAAQ,UAAA,EAAoB;YACnC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,sBAAsB,KAAA,EAAyB;YACtD,IAAI,CAAC,MAAM,YAAA,IAAgB,CAAC,MAAM,UAAA,EAAY;gBAC5C;YACF;YAEoB,oBAAA;YAEd,MAAA,OAAO,MAAM,UAAA,CAAW,qBAAA,CAAsB;YAC9C,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,IAAA;YACzB,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,GAAA;YAC/B,MAAM,IAAI,KAAK,KAAA;YACf,MAAM,IAAI,KAAK,MAAA;YAET,MAAA,CAAA,GAAK,IAAI,IAAK,IAAI;YACxB,MAAM,CAAA,GAAI,CAAA,CAAE,IAAI,CAAA,IAAK,IAAI;YAEzB,eAAe,GAAA,CAAI,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC,EAAE,SAAA,CAAU,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ,EAAE,SAAA;QAC7F;QAEA,SAAS,cAAc,IAAA,EAAsB;YACpC,OAAA,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,IAAI,CAAC;QACtE;QAMA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,YAAY,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC9C;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,sBAAsB,KAAK;YAC3B,WAAW,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC7C;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC3C;QAEA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,UAAU,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC1C,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;YAC1B,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC9B,WAAA,UAAA,CAAW,UAAU,UAAU;YAEtC,IAAA,WAAW,CAAA,GAAI,GAAG;gBACpB,SAAS,cAAc;YAAA,OAAA,IACd,WAAW,CAAA,GAAI,GAAG;gBAC3B,QAAQ,cAAc;YACxB;YAEA,WAAW,IAAA,CAAK,QAAQ;YACxB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,OAAO,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YACvC,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;YACpB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,iBAAiB,KAAA,EAAmB;YAC3C,sBAAsB,KAAK;YAEvB,IAAA,MAAM,MAAA,GAAS,GAAG;gBACpB,QAAQ,cAAc;YAAA,OAAA,IACb,MAAM,MAAA,GAAS,GAAG;gBAC3B,SAAS,cAAc;YACzB;YAEA,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,cAAc,KAAA,EAAsB;YAC3C,IAAI,cAAc;YAElB,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK,MAAM,IAAA,CAAK,EAAA;oBACV,IAAA,GAAG,MAAM,WAAW;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,MAAA;oBACV,IAAA,GAAG,CAAC,MAAM,WAAW;oBACX,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,IAAA;oBACV,IAAA,MAAM,WAAA,EAAa,CAAC;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,KAAA;oBACV,IAAA,CAAC,MAAM,WAAA,EAAa,CAAC;oBACX,cAAA;oBACd;YACJ;YAEA,IAAI,aAAa;gBAEf,MAAM,cAAA,CAAe;gBACrB,MAAM,MAAA,CAAO;YACf;QACF;QAEA,SAAS,yBAAyB;YAC5B,IAAA,SAAS,MAAA,IAAU,GAAG;gBACZ,YAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC/C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAErC,YAAA,GAAA,CAAI,GAAG,CAAC;YACtB;QACF;QAEA,SAAS,sBAAsB;YACzB,IAAA,SAAS,MAAA,IAAU,GAAG;gBACf,SAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC5C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAExC,SAAA,GAAA,CAAI,GAAG,CAAC;YACnB;QACF;QAEA,SAAS,wBAAwB;YAC/B,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEjC,WAAA,GAAA,CAAI,GAAG,QAAQ;QAC5B;QAEA,SAAS,2BAA2B;YAClC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,SAAA,EAA+B;QAC3C;QAEA,SAAS,8BAA8B;YACrC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,YAAA,EAAqC;QACjD;QAEA,SAAS,sBAAsB,KAAA,EAAqB;YAC9C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,UAAU,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OACjC;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBAC9B,UAAA,GAAA,CAAI,GAAG,CAAC;YACpB;YAEA,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;QAC5B;QAEA,SAAS,mBAAmB,KAAA,EAAqB;YAC3C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,OAAO,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OAC9B;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACjC,OAAA,GAAA,CAAI,GAAG,CAAC;YACjB;YAEA,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;QACtB;QAEA,SAAS,qBAAqB,KAAA,EAAqB;YAC3C,MAAA,WAAW,yBAAyB,KAAK;YACzC,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAC5B,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAClC,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEnC,SAAA,GAAA,CAAI,GAAG,QAAQ;YACb,WAAA,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,SAAS,CAAA,GAAI,WAAW,CAAA,EAAG,MAAM,SAAS,CAAC;YACtE,SAAS,WAAW,CAAC;YACrB,WAAW,IAAA,CAAK,QAAQ;QAC1B;QAEA,SAAS,wBAAwB,KAAA,EAAqB;YACpD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,SAAA,EAAW,mBAAmB,KAAK;QAC/C;QAEA,SAAS,2BAA2B,KAAA,EAAqB;YACvD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,YAAA,EAAc,sBAAsB,KAAK;QACrD;QAMA,SAAS,cAAc,KAAA,EAAqB;;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,SAAS,MAAA,KAAW,GAAG;gBACzB,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,eAAe;gBAChE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,aAAa;YAChE;YAEA,WAAW,KAAK;YAEZ,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,aAAa,KAAK;YAAA,OACb;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,cAAc,KAAA,EAAqB;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,YAAY,KAAK;YAAA,OACZ;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;;YACxC,cAAc,KAAK;YAEf,IAAA,SAAS,MAAA,KAAW,GAAG;gBACnB,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,qBAAA,CAAsB,MAAM,SAAA;gBAE9C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;gBACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YACnE;YAGA,MAAM,aAAA,CAAc,QAAQ;YAE5B,QAAQ,MAAM,IAAA;QAChB;QAEA,SAAS,YAAY,KAAA,EAAmB;YAClC,IAAA;YAEJ,OAAQ,MAAM,MAAA,EAAQ;gBACpB,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,IAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,MAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,KAAA;oBACjC;gBAEF;oBACgB,cAAA,CAAA;YAClB;YAEA,OAAQ,aAAa;gBACnB,KAAK,2JAAA,CAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B,QAAQ,MAAM,KAAA;oBACd;gBAEF,wJAAK,QAAA,CAAM,MAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAAA,OACT;wBACL,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAChB;oBACA;gBAEF,wJAAK,QAAA,CAAM,GAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAAA,OACT;wBACL,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAChB;oBACA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAmB;YACtC,IAAI,MAAM,OAAA,KAAY,OAAO;YAE7B,OAAQ,OAAO;gBACb,KAAK,MAAM,MAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B;gBAEF,KAAK,MAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B;gBAEF,KAAK,MAAM,GAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB;YACJ;QACF;QAEA,SAAS,aAAa,KAAA,EAAmB;YACnC,IAAA,MAAM,OAAA,KAAY,SAAS,MAAM,UAAA,KAAe,SAAU,UAAU,MAAM,IAAA,IAAQ,UAAU,MAAM,MAAA,EAAS;gBAC7G;YACF;YAEA,MAAM,cAAA,CAAe;YAGrB,MAAM,aAAA,CAAc,UAAU;YAE9B,iBAAiB,KAAK;YAGtB,MAAM,aAAA,CAAc,QAAQ;QAC9B;QAEA,SAAS,UAAU,KAAA,EAAsB;YACvC,IAAI,MAAM,OAAA,KAAY,SAAS,MAAM,SAAA,KAAc,OAAO;YAC1D,cAAc,KAAK;QACrB;QAEA,SAAS,aAAa,KAAA,EAAqB;YACzC,aAAa,KAAK;YAElB,OAAQ,SAAS,MAAA,EAAQ;gBACvB,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,wJAAK,QAAA,CAAM,MAAA;4BACT,IAAI,MAAM,YAAA,KAAiB,OAAO;4BACX;4BACvB,QAAQ,MAAM,YAAA;4BACd;wBAEF,wJAAK,QAAA,CAAM,GAAA;4BACT,IAAI,MAAM,SAAA,KAAc,OAAO;4BACX;4BACpB,QAAQ,MAAM,SAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,wJAAK,QAAA,CAAM,SAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;4BACpC;4BACzB,QAAQ,MAAM,eAAA;4BACd;wBAEF,wJAAK,QAAA,CAAM,YAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;4BACpC;4BAC5B,QAAQ,MAAM,kBAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;YACxC,aAAa,KAAK;YAElB,OAAQ,OAAO;gBACb,KAAK,MAAM,YAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,SAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,eAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;oBAC7D,wBAAwB,KAAK;oBAC7B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,kBAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;oBAChE,2BAA2B,KAAK;oBAChC,MAAM,MAAA,CAAO;oBACb;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;QACF;QAEA,SAAS,cAAc,KAAA,EAAc;YACnC,IAAI,MAAM,OAAA,KAAY,OAAO;YAC7B,MAAM,cAAA,CAAe;QACvB;QAEA,SAAS,WAAW,KAAA,EAAqB;YACvC,SAAS,IAAA,CAAK,KAAK;QACrB;QAEA,SAAS,cAAc,KAAA,EAAqB;YACnC,OAAA,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAEvC,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;gBACxC,IAAI,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,IAAa,MAAM,SAAA,EAAW;oBACnC,SAAA,MAAA,CAAO,GAAG,CAAC;oBACpB;gBACF;YACF;QACF;QAEA,SAAS,aAAa,KAAA,EAAqB;YACrC,IAAA,WAAW,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAE/C,IAAI,aAAa,KAAA,GAAW;gBAC1B,WAAW,uJAAI,UAAA;gBACE,gBAAA,CAAA,MAAM,SAAS,CAAA,GAAI;YACtC;YAEA,SAAS,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;QACvC;QAEA,SAAS,yBAAyB,KAAA,EAAqB;YAC/C,MAAA,UAAU,MAAM,SAAA,KAAc,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,GAAY,QAAA,CAAS,CAAC,CAAA,GAAI,QAAA,CAAS,CAAC,CAAA;YAC7E,OAAA,gBAAA,CAAiB,QAAQ,SAAS,CAAA;QAC3C;QAIA,IAAA,CAAK,OAAA,GAAU,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC9C,QAAQ,UAAU;YAClB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC/C,SAAS,UAAU;YACnB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,MAAM;YACb,OAAA;QAAA;QAGJ,IAAA,CAAA,QAAA,GAAW,CAAC,aAAa;YAC5B,SAAS,QAAQ;YACjB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,YAAA,GAAe,MAAM;YACxB,OAAO,aAAa;QAAA;QAItB,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,UAAU;QAErD,IAAA,CAAK,MAAA,CAAO;IACd;AACF;AAUA,MAAM,oBAAoB,cAAc;IACtC,YAAY,MAAA,EAAgD,UAAA,CAA0B;QACpF,KAAA,CAAM,QAAQ,UAAU;QAExB,IAAA,CAAK,kBAAA,GAAqB;QAErB,IAAA,CAAA,YAAA,CAAa,IAAA,sJAAO,QAAA,CAAM,GAAA;QAC1B,IAAA,CAAA,YAAA,CAAa,KAAA,sJAAQ,QAAA,CAAM,MAAA;QAE3B,IAAA,CAAA,OAAA,CAAQ,GAAA,sJAAM,QAAA,CAAM,GAAA;QACpB,IAAA,CAAA,OAAA,CAAQ,GAAA,sJAAM,QAAA,CAAM,YAAA;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/src/_polyfill/constants.ts"], "sourcesContent": ["import { REVISION } from 'three'\n\nexport const version = /* @__PURE__ */ (() => parseInt(REVISION.replace(/\\D+/g, '')))()\n"], "names": [], "mappings": ";;;;;AAEa,MAAA,UAAA,aAAA,GAAA,CAAA,IAAiC,4JAAS,WAAA,CAAS,OAAA,CAAQ,QAAQ,EAAE,CAAC,CAAA,EAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "file": "GroundProjectedEnv.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/src/objects/GroundProjectedEnv.ts"], "sourcesContent": ["import { Mesh, IcosahedronGeometry, ShaderMaterial, DoubleSide, Texture, CubeTexture, BufferGeometry } from 'three'\nimport { version } from '../_polyfill/constants'\n\nexport interface GroundProjectedEnvParameters {\n  height?: number\n  radius?: number\n}\n\nconst isCubeTexture = (def: CubeTexture | Texture): def is CubeTexture => def && (def as CubeTexture).isCubeTexture\n\nexport class GroundProjectedEnv extends Mesh<BufferGeometry, ShaderMaterial> {\n  constructor(texture: CubeTexture | Texture, options?: GroundProjectedEnvParameters) {\n    const isCubeMap = isCubeTexture(texture)\n    const w = (isCubeMap ? texture.image[0]?.width : texture.image.width) ?? 1024\n    const cubeSize = w / 4\n    const _lodMax = Math.floor(Math.log2(cubeSize))\n    const _cubeSize = Math.pow(2, _lodMax)\n    const width = 3 * Math.max(_cubeSize, 16 * 7)\n    const height = 4 * _cubeSize\n\n    const defines = [\n      isCubeMap ? '#define ENVMAP_TYPE_CUBE' : '',\n      `#define CUBEUV_TEXEL_WIDTH ${1.0 / width}`,\n      `#define CUBEUV_TEXEL_HEIGHT ${1.0 / height}`,\n      `#define CUBEUV_MAX_MIP ${_lodMax}.0`,\n    ]\n\n    const vertexShader = /* glsl */ `\n        varying vec3 vWorldPosition;\n        void main() \n        {\n            vec4 worldPosition = ( modelMatrix * vec4( position, 1.0 ) );\n            vWorldPosition = worldPosition.xyz;\n            \n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }\n        `\n    const fragmentShader =\n      defines.join('\\n') +\n      /* glsl */ `\n        #define ENVMAP_TYPE_CUBE_UV\n        varying vec3 vWorldPosition;\n        uniform float radius;\n        uniform float height;\n        uniform float angle;\n        #ifdef ENVMAP_TYPE_CUBE\n            uniform samplerCube map;\n        #else\n            uniform sampler2D map;\n        #endif\n        // From: https://www.shadertoy.com/view/4tsBD7\n        float diskIntersectWithBackFaceCulling( vec3 ro, vec3 rd, vec3 c, vec3 n, float r ) \n        {\n            float d = dot ( rd, n );\n            \n            if( d > 0.0 ) { return 1e6; }\n            \n            vec3  o = ro - c;\n            float t = - dot( n, o ) / d;\n            vec3  q = o + rd * t;\n            \n            return ( dot( q, q ) < r * r ) ? t : 1e6;\n        }\n        // From: https://www.iquilezles.org/www/articles/intersectors/intersectors.htm\n        float sphereIntersect( vec3 ro, vec3 rd, vec3 ce, float ra ) \n        {\n            vec3 oc = ro - ce;\n            float b = dot( oc, rd );\n            float c = dot( oc, oc ) - ra * ra;\n            float h = b * b - c;\n            \n            if( h < 0.0 ) { return -1.0; }\n            \n            h = sqrt( h );\n            \n            return - b + h;\n        }\n        vec3 project() \n        {\n            vec3 p = normalize( vWorldPosition );\n            vec3 camPos = cameraPosition;\n            camPos.y -= height;\n            float intersection = sphereIntersect( camPos, p, vec3( 0.0 ), radius );\n            if( intersection > 0.0 ) {\n                \n                vec3 h = vec3( 0.0, - height, 0.0 );\n                float intersection2 = diskIntersectWithBackFaceCulling( camPos, p, h, vec3( 0.0, 1.0, 0.0 ), radius );\n                p = ( camPos + min( intersection, intersection2 ) * p ) / radius;\n            } else {\n                p = vec3( 0.0, 1.0, 0.0 );\n            }\n            return p;\n        }\n        #include <common>\n        #include <cube_uv_reflection_fragment>\n        void main() \n        {\n            vec3 projectedWorldPosition = project();\n            \n            #ifdef ENVMAP_TYPE_CUBE\n                vec3 outcolor = textureCube( map, projectedWorldPosition ).rgb;\n            #else\n                vec3 direction = normalize( projectedWorldPosition );\n                vec2 uv = equirectUv( direction );\n                vec3 outcolor = texture2D( map, uv ).rgb;\n            #endif\n            gl_FragColor = vec4( outcolor, 1.0 );\n            #include <tonemapping_fragment>\n            #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n        `\n\n    const uniforms = {\n      map: { value: texture },\n      height: { value: options?.height || 15 },\n      radius: { value: options?.radius || 100 },\n    }\n\n    const geometry = new IcosahedronGeometry(1, 16)\n    const material = new ShaderMaterial({\n      uniforms,\n      fragmentShader,\n      vertexShader,\n      side: DoubleSide,\n    })\n\n    super(geometry, material)\n  }\n\n  set radius(radius: number) {\n    this.material.uniforms.radius.value = radius\n  }\n\n  get radius(): number {\n    return this.material.uniforms.radius.value\n  }\n\n  set height(height: number) {\n    this.material.uniforms.height.value = height\n  }\n\n  get height(): number {\n    return this.material.uniforms.height.value\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAQA,MAAM,gBAAgB,CAAC,MAAmD,OAAQ,IAAoB,aAAA;AAE/F,MAAM,8KAA2B,OAAA,CAAqC;IAC3E,YAAY,OAAA,EAAgC,OAAA,CAAwC;;QAC5E,MAAA,YAAY,cAAc,OAAO;QACjC,MAAA,IAAA,CAAK,KAAA,YAAA,CAAY,KAAA,QAAQ,KAAA,CAAM,CAAC,CAAA,KAAf,OAAA,KAAA,IAAA,GAAkB,KAAA,GAAQ,QAAQ,KAAA,CAAM,KAAA,KAApD,OAAA,KAA8D;QACzE,MAAM,WAAW,IAAI;QACrB,MAAM,UAAU,KAAK,KAAA,CAAM,KAAK,IAAA,CAAK,QAAQ,CAAC;QAC9C,MAAM,YAAY,KAAK,GAAA,CAAI,GAAG,OAAO;QACrC,MAAM,QAAQ,IAAI,KAAK,GAAA,CAAI,WAAW,KAAK,CAAC;QAC5C,MAAM,SAAS,IAAI;QAEnB,MAAM,UAAU;YACd,YAAY,6BAA6B;YACzC,CAAA,2BAAA,EAA8B,IAAM,OAAA;YACpC,CAAA,4BAAA,EAA+B,IAAM,QAAA;YACrC,CAAA,uBAAA,EAA0B,QAAA,EAAA,CAAA;SAAA;QAGtB,MAAA,eAAA,QAAA,GAA0B,CAAA;;;;;;;;;QAAA,CAAA;QAU1B,MAAA,iBACJ,QAAQ,IAAA,CAAK,IAAI,IAAA,QAAA,GACN,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBAAA,+JAqEO,UAAA,IAAW,MAAM,wBAAwB,qBAAA;;QAAA,CAAA;QAI7D,MAAM,WAAW;YACf,KAAK;gBAAE,OAAO;YAAQ;YACtB,QAAQ;gBAAE,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,KAAU;YAAG;YACvC,QAAQ;gBAAE,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,KAAU;YAAI;QAAA;QAG1C,MAAM,WAAW,uJAAI,sBAAA,CAAoB,GAAG,EAAE;QACxC,MAAA,WAAW,uJAAI,iBAAA,CAAe;YAClC;YACA;YACA;YACA,yJAAM,aAAA;QAAA,CACP;QAED,KAAA,CAAM,UAAU,QAAQ;IAC1B;IAEA,IAAI,OAAO,MAAA,EAAgB;QACpB,IAAA,CAAA,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,KAAA,GAAQ;IACxC;IAEA,IAAI,SAAiB;QACZ,OAAA,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,KAAA;IACvC;IAEA,IAAI,OAAO,MAAA,EAAgB;QACpB,IAAA,CAAA,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,KAAA,GAAQ;IACxC;IAEA,IAAI,SAAiB;QACZ,OAAA,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,KAAA;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "file": "RGBELoader.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/src/loaders/RGBELoader.js"], "sourcesContent": ["import { DataTextureLoader, DataUtils, FloatType, HalfFloatType, LinearFilter } from 'three'\n\n// https://github.com/mrdoob/three.js/issues/5552\n// http://en.wikipedia.org/wiki/RGBE_image_format\n\nclass RGBELoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n\n    this.type = HalfFloatType\n  }\n\n  // adapted from http://www.graphics.cornell.edu/~bjw/rgbe.html\n\n  parse(buffer) {\n    const /* default error routine.  change this to change error handling */\n      rgbe_read_error = 1,\n      rgbe_write_error = 2,\n      rgbe_format_error = 3,\n      rgbe_memory_error = 4,\n      rgbe_error = function (rgbe_error_code, msg) {\n        switch (rgbe_error_code) {\n          case rgbe_read_error:\n            throw new Error('THREE.RGBELoader: Read Error: ' + (msg || ''))\n          case rgbe_write_error:\n            throw new Error('THREE.RGBELoader: Write Error: ' + (msg || ''))\n          case rgbe_format_error:\n            throw new Error('THREE.RGBELoader: Bad File Format: ' + (msg || ''))\n          default:\n          case rgbe_memory_error:\n            throw new Error('THREE.RGBELoader: Memory Error: ' + (msg || ''))\n        }\n      },\n      /* offsets to red, green, and blue components in a data (float) pixel */\n      //RGBE_DATA_RED = 0,\n      //RGBE_DATA_GREEN = 1,\n      //RGBE_DATA_BLUE = 2,\n\n      /* number of floats per pixel, use 4 since stored in rgba image format */\n      //RGBE_DATA_SIZE = 4,\n\n      /* flags indicating which fields in an rgbe_header_info are valid */\n      RGBE_VALID_PROGRAMTYPE = 1,\n      RGBE_VALID_FORMAT = 2,\n      RGBE_VALID_DIMENSIONS = 4,\n      NEWLINE = '\\n',\n      fgets = function (buffer, lineLimit, consume) {\n        const chunkSize = 128\n\n        lineLimit = !lineLimit ? 1024 : lineLimit\n        let p = buffer.pos,\n          i = -1,\n          len = 0,\n          s = '',\n          chunk = String.fromCharCode.apply(null, new Uint16Array(buffer.subarray(p, p + chunkSize)))\n\n        while (0 > (i = chunk.indexOf(NEWLINE)) && len < lineLimit && p < buffer.byteLength) {\n          s += chunk\n          len += chunk.length\n          p += chunkSize\n          chunk += String.fromCharCode.apply(null, new Uint16Array(buffer.subarray(p, p + chunkSize)))\n        }\n\n        if (-1 < i) {\n          /*for (i=l-1; i>=0; i--) {\n\t\t\t\t\t\tbyteCode = m.charCodeAt(i);\n\t\t\t\t\t\tif (byteCode > 0x7f && byteCode <= 0x7ff) byteLen++;\n\t\t\t\t\t\telse if (byteCode > 0x7ff && byteCode <= 0xffff) byteLen += 2;\n\t\t\t\t\t\tif (byteCode >= 0xDC00 && byteCode <= 0xDFFF) i--; //trail surrogate\n\t\t\t\t\t}*/\n          if (false !== consume) buffer.pos += len + i + 1\n          return s + chunk.slice(0, i)\n        }\n\n        return false\n      },\n      /* minimal header reading.  modify if you want to parse more information */\n      RGBE_ReadHeader = function (buffer) {\n        // regexes to parse header info fields\n        const magic_token_re = /^#\\?(\\S+)/,\n          gamma_re = /^\\s*GAMMA\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n          exposure_re = /^\\s*EXPOSURE\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n          format_re = /^\\s*FORMAT=(\\S+)\\s*$/,\n          dimensions_re = /^\\s*\\-Y\\s+(\\d+)\\s+\\+X\\s+(\\d+)\\s*$/,\n          // RGBE format header struct\n          header = {\n            valid: 0 /* indicate which fields are valid */,\n\n            string: '' /* the actual header string */,\n\n            comments: '' /* comments found in header */,\n\n            programtype: 'RGBE' /* listed at beginning of file to identify it after \"#?\". defaults to \"RGBE\" */,\n\n            format: '' /* RGBE format, default 32-bit_rle_rgbe */,\n\n            gamma: 1.0 /* image has already been gamma corrected with given gamma. defaults to 1.0 (no correction) */,\n\n            exposure: 1.0 /* a value of 1.0 in an image corresponds to <exposure> watts/steradian/m^2. defaults to 1.0 */,\n\n            width: 0,\n            height: 0 /* image dimensions, width/height */,\n          }\n\n        let line, match\n\n        if (buffer.pos >= buffer.byteLength || !(line = fgets(buffer))) {\n          rgbe_error(rgbe_read_error, 'no header found')\n        }\n\n        /* if you want to require the magic token then uncomment the next line */\n        if (!(match = line.match(magic_token_re))) {\n          rgbe_error(rgbe_format_error, 'bad initial token')\n        }\n\n        header.valid |= RGBE_VALID_PROGRAMTYPE\n        header.programtype = match[1]\n        header.string += line + '\\n'\n\n        while (true) {\n          line = fgets(buffer)\n          if (false === line) break\n          header.string += line + '\\n'\n\n          if ('#' === line.charAt(0)) {\n            header.comments += line + '\\n'\n            continue // comment line\n          }\n\n          if ((match = line.match(gamma_re))) {\n            header.gamma = parseFloat(match[1])\n          }\n\n          if ((match = line.match(exposure_re))) {\n            header.exposure = parseFloat(match[1])\n          }\n\n          if ((match = line.match(format_re))) {\n            header.valid |= RGBE_VALID_FORMAT\n            header.format = match[1] //'32-bit_rle_rgbe';\n          }\n\n          if ((match = line.match(dimensions_re))) {\n            header.valid |= RGBE_VALID_DIMENSIONS\n            header.height = parseInt(match[1], 10)\n            header.width = parseInt(match[2], 10)\n          }\n\n          if (header.valid & RGBE_VALID_FORMAT && header.valid & RGBE_VALID_DIMENSIONS) break\n        }\n\n        if (!(header.valid & RGBE_VALID_FORMAT)) {\n          rgbe_error(rgbe_format_error, 'missing format specifier')\n        }\n\n        if (!(header.valid & RGBE_VALID_DIMENSIONS)) {\n          rgbe_error(rgbe_format_error, 'missing image size specifier')\n        }\n\n        return header\n      },\n      RGBE_ReadPixels_RLE = function (buffer, w, h) {\n        const scanline_width = w\n\n        if (\n          // run length encoding is not allowed so read flat\n          scanline_width < 8 ||\n          scanline_width > 0x7fff ||\n          // this file is not run length encoded\n          2 !== buffer[0] ||\n          2 !== buffer[1] ||\n          buffer[2] & 0x80\n        ) {\n          // return the flat buffer\n          return new Uint8Array(buffer)\n        }\n\n        if (scanline_width !== ((buffer[2] << 8) | buffer[3])) {\n          rgbe_error(rgbe_format_error, 'wrong scanline width')\n        }\n\n        const data_rgba = new Uint8Array(4 * w * h)\n\n        if (!data_rgba.length) {\n          rgbe_error(rgbe_memory_error, 'unable to allocate buffer space')\n        }\n\n        let offset = 0,\n          pos = 0\n\n        const ptr_end = 4 * scanline_width\n        const rgbeStart = new Uint8Array(4)\n        const scanline_buffer = new Uint8Array(ptr_end)\n        let num_scanlines = h\n\n        // read in each successive scanline\n        while (num_scanlines > 0 && pos < buffer.byteLength) {\n          if (pos + 4 > buffer.byteLength) {\n            rgbe_error(rgbe_read_error)\n          }\n\n          rgbeStart[0] = buffer[pos++]\n          rgbeStart[1] = buffer[pos++]\n          rgbeStart[2] = buffer[pos++]\n          rgbeStart[3] = buffer[pos++]\n\n          if (2 != rgbeStart[0] || 2 != rgbeStart[1] || ((rgbeStart[2] << 8) | rgbeStart[3]) != scanline_width) {\n            rgbe_error(rgbe_format_error, 'bad rgbe scanline format')\n          }\n\n          // read each of the four channels for the scanline into the buffer\n          // first red, then green, then blue, then exponent\n          let ptr = 0,\n            count\n\n          while (ptr < ptr_end && pos < buffer.byteLength) {\n            count = buffer[pos++]\n            const isEncodedRun = count > 128\n            if (isEncodedRun) count -= 128\n\n            if (0 === count || ptr + count > ptr_end) {\n              rgbe_error(rgbe_format_error, 'bad scanline data')\n            }\n\n            if (isEncodedRun) {\n              // a (encoded) run of the same value\n              const byteValue = buffer[pos++]\n              for (let i = 0; i < count; i++) {\n                scanline_buffer[ptr++] = byteValue\n              }\n              //ptr += count;\n            } else {\n              // a literal-run\n              scanline_buffer.set(buffer.subarray(pos, pos + count), ptr)\n              ptr += count\n              pos += count\n            }\n          }\n\n          // now convert data from buffer into rgba\n          // first red, then green, then blue, then exponent (alpha)\n          const l = scanline_width //scanline_buffer.byteLength;\n          for (let i = 0; i < l; i++) {\n            let off = 0\n            data_rgba[offset] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 1] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 2] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 3] = scanline_buffer[i + off]\n            offset += 4\n          }\n\n          num_scanlines--\n        }\n\n        return data_rgba\n      }\n\n    const RGBEByteToRGBFloat = function (sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3]\n      const scale = Math.pow(2.0, e - 128.0) / 255.0\n\n      destArray[destOffset + 0] = sourceArray[sourceOffset + 0] * scale\n      destArray[destOffset + 1] = sourceArray[sourceOffset + 1] * scale\n      destArray[destOffset + 2] = sourceArray[sourceOffset + 2] * scale\n      destArray[destOffset + 3] = 1\n    }\n\n    const RGBEByteToRGBHalf = function (sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3]\n      const scale = Math.pow(2.0, e - 128.0) / 255.0\n\n      // clamping to 65504, the maximum representable value in float16\n      destArray[destOffset + 0] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 0] * scale, 65504))\n      destArray[destOffset + 1] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 1] * scale, 65504))\n      destArray[destOffset + 2] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 2] * scale, 65504))\n      destArray[destOffset + 3] = DataUtils.toHalfFloat(1)\n    }\n\n    const byteArray = new Uint8Array(buffer)\n    byteArray.pos = 0\n    const rgbe_header_info = RGBE_ReadHeader(byteArray)\n\n    const w = rgbe_header_info.width,\n      h = rgbe_header_info.height,\n      image_rgba_data = RGBE_ReadPixels_RLE(byteArray.subarray(byteArray.pos), w, h)\n\n    let data, type\n    let numElements\n\n    switch (this.type) {\n      case FloatType:\n        numElements = image_rgba_data.length / 4\n        const floatArray = new Float32Array(numElements * 4)\n\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBFloat(image_rgba_data, j * 4, floatArray, j * 4)\n        }\n\n        data = floatArray\n        type = FloatType\n        break\n\n      case HalfFloatType:\n        numElements = image_rgba_data.length / 4\n        const halfArray = new Uint16Array(numElements * 4)\n\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBHalf(image_rgba_data, j * 4, halfArray, j * 4)\n        }\n\n        data = halfArray\n        type = HalfFloatType\n        break\n\n      default:\n        throw new Error('THREE.RGBELoader: Unsupported type: ' + this.type)\n        break\n    }\n\n    return {\n      width: w,\n      height: h,\n      data: data,\n      header: rgbe_header_info.string,\n      gamma: rgbe_header_info.gamma,\n      exposure: rgbe_header_info.exposure,\n      type: type,\n    }\n  }\n\n  setDataType(value) {\n    this.type = value\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      switch (texture.type) {\n        case FloatType:\n        case HalfFloatType:\n          if ('colorSpace' in texture) texture.colorSpace = 'srgb-linear'\n          else texture.encoding = 3000 // LinearEncoding\n          texture.minFilter = LinearFilter\n          texture.magFilter = LinearFilter\n          texture.generateMipmaps = false\n          texture.flipY = true\n\n          break\n      }\n\n      if (onLoad) onLoad(texture, texData)\n    }\n\n    return super.load(url, onLoadCallback, onProgress, onError)\n  }\n}\n\nexport { RGBELoader }\n"], "names": ["buffer", "w", "h"], "mappings": ";;;;;AAKA,MAAM,sKAAmB,oBAAA,CAAkB;IACzC,YAAY,OAAA,CAAS;QACnB,KAAA,CAAM,OAAO;QAEb,IAAA,CAAK,IAAA,sJAAO,gBAAA;IACb;IAAA,8DAAA;IAID,MAAM,MAAA,EAAQ;QACZ,MACE,kBAAkB,GAClB,mBAAmB,GACnB,oBAAoB,GACpB,oBAAoB,GACpB,aAAa,SAAU,eAAA,EAAiB,GAAA,EAAK;YAC3C,OAAQ,iBAAe;gBACrB,KAAK;oBACH,MAAM,IAAI,MAAM,mCAAA,CAAoC,OAAO,EAAA,CAAG;gBAChE,KAAK;oBACH,MAAM,IAAI,MAAM,oCAAA,CAAqC,OAAO,EAAA,CAAG;gBACjE,KAAK;oBACH,MAAM,IAAI,MAAM,wCAAA,CAAyC,OAAO,EAAA,CAAG;gBACrE;gBACA,KAAK;oBACH,MAAM,IAAI,MAAM,qCAAA,CAAsC,OAAO,EAAA,CAAG;YACnE;QACF,GAUD,yBAAyB,GACzB,oBAAoB,GACpB,wBAAwB,GACxB,UAAU,MACV,QAAQ,SAAUA,OAAAA,EAAQ,SAAA,EAAW,OAAA,EAAS;YAC5C,MAAM,YAAY;YAElB,YAAY,CAAC,YAAY,OAAO;YAChC,IAAI,IAAIA,QAAO,GAAA,EACb,IAAI,CAAA,GACJ,MAAM,GACN,IAAI,IACJ,QAAQ,OAAO,YAAA,CAAa,KAAA,CAAM,MAAM,IAAI,YAAYA,QAAO,QAAA,CAAS,GAAG,IAAI,SAAS,CAAC,CAAC;YAE5F,MAAO,IAAA,CAAK,IAAI,MAAM,OAAA,CAAQ,OAAO,CAAA,KAAM,MAAM,aAAa,IAAIA,QAAO,UAAA,CAAY;gBACnF,KAAK;gBACL,OAAO,MAAM,MAAA;gBACb,KAAK;gBACL,SAAS,OAAO,YAAA,CAAa,KAAA,CAAM,MAAM,IAAI,YAAYA,QAAO,QAAA,CAAS,GAAG,IAAI,SAAS,CAAC,CAAC;YAC5F;YAED,IAAI,CAAA,IAAK,GAAG;gBAOV,IAAI,UAAU,SAASA,QAAO,GAAA,IAAO,MAAM,IAAI;gBAC/C,OAAO,IAAI,MAAM,KAAA,CAAM,GAAG,CAAC;YAC5B;YAED,OAAO;QACR,GAED,kBAAkB,SAAUA,OAAAA,EAAQ;YAElC,MAAM,iBAAiB,aACrB,WAAW,qCACX,cAAc,wCACd,YAAY,wBACZ,gBAAgB,qCAEhB,SAAS;gBACP,OAAO;gBAEP,QAAQ;gBAER,UAAU;gBAEV,aAAa;gBAEb,QAAQ;gBAER,OAAO;gBAEP,UAAU;gBAEV,OAAO;gBACP,QAAQ;YACT;YAEH,IAAI,MAAM;YAEV,IAAIA,QAAO,GAAA,IAAOA,QAAO,UAAA,IAAc,CAAA,CAAE,OAAO,MAAMA,OAAM,CAAA,GAAI;gBAC9D,WAAW,iBAAiB,iBAAiB;YAC9C;YAGD,IAAI,CAAA,CAAE,QAAQ,KAAK,KAAA,CAAM,cAAc,CAAA,GAAI;gBACzC,WAAW,mBAAmB,mBAAmB;YAClD;YAED,OAAO,KAAA,IAAS;YAChB,OAAO,WAAA,GAAc,KAAA,CAAM,CAAC,CAAA;YAC5B,OAAO,MAAA,IAAU,OAAO;YAExB,MAAO,KAAM;gBACX,OAAO,MAAMA,OAAM;gBACnB,IAAI,UAAU,MAAM;gBACpB,OAAO,MAAA,IAAU,OAAO;gBAExB,IAAI,QAAQ,KAAK,MAAA,CAAO,CAAC,GAAG;oBAC1B,OAAO,QAAA,IAAY,OAAO;oBAC1B;gBACD;gBAED,IAAK,QAAQ,KAAK,KAAA,CAAM,QAAQ,GAAI;oBAClC,OAAO,KAAA,GAAQ,WAAW,KAAA,CAAM,CAAC,CAAC;gBACnC;gBAED,IAAK,QAAQ,KAAK,KAAA,CAAM,WAAW,GAAI;oBACrC,OAAO,QAAA,GAAW,WAAW,KAAA,CAAM,CAAC,CAAC;gBACtC;gBAED,IAAK,QAAQ,KAAK,KAAA,CAAM,SAAS,GAAI;oBACnC,OAAO,KAAA,IAAS;oBAChB,OAAO,MAAA,GAAS,KAAA,CAAM,CAAC,CAAA;gBACxB;gBAED,IAAK,QAAQ,KAAK,KAAA,CAAM,aAAa,GAAI;oBACvC,OAAO,KAAA,IAAS;oBAChB,OAAO,MAAA,GAAS,SAAS,KAAA,CAAM,CAAC,CAAA,EAAG,EAAE;oBACrC,OAAO,KAAA,GAAQ,SAAS,KAAA,CAAM,CAAC,CAAA,EAAG,EAAE;gBACrC;gBAED,IAAI,OAAO,KAAA,GAAQ,qBAAqB,OAAO,KAAA,GAAQ,uBAAuB;YAC/E;YAED,IAAI,CAAA,CAAE,OAAO,KAAA,GAAQ,iBAAA,GAAoB;gBACvC,WAAW,mBAAmB,0BAA0B;YACzD;YAED,IAAI,CAAA,CAAE,OAAO,KAAA,GAAQ,qBAAA,GAAwB;gBAC3C,WAAW,mBAAmB,8BAA8B;YAC7D;YAED,OAAO;QACR,GACD,sBAAsB,SAAUA,OAAAA,EAAQC,EAAAA,EAAGC,EAAAA,EAAG;YAC5C,MAAM,iBAAiBD;YAEvB,IAAA,kDAAA;YAEE,iBAAiB,KACjB,iBAAiB,SAAA,sCAAA;YAEjB,MAAMD,OAAAA,CAAO,CAAC,CAAA,IACd,MAAMA,OAAAA,CAAO,CAAC,CAAA,IACdA,OAAAA,CAAO,CAAC,CAAA,GAAI,KACZ;gBAEA,OAAO,IAAI,WAAWA,OAAM;YAC7B;YAED,IAAI,mBAAA,CAAqBA,OAAAA,CAAO,CAAC,CAAA,IAAK,IAAKA,OAAAA,CAAO,CAAC,CAAA,GAAI;gBACrD,WAAW,mBAAmB,sBAAsB;YACrD;YAED,MAAM,YAAY,IAAI,WAAW,IAAIC,KAAIC,EAAC;YAE1C,IAAI,CAAC,UAAU,MAAA,EAAQ;gBACrB,WAAW,mBAAmB,iCAAiC;YAChE;YAED,IAAI,SAAS,GACX,MAAM;YAER,MAAM,UAAU,IAAI;YACpB,MAAM,YAAY,IAAI,WAAW,CAAC;YAClC,MAAM,kBAAkB,IAAI,WAAW,OAAO;YAC9C,IAAI,gBAAgBA;YAGpB,MAAO,gBAAgB,KAAK,MAAMF,QAAO,UAAA,CAAY;gBACnD,IAAI,MAAM,IAAIA,QAAO,UAAA,EAAY;oBAC/B,WAAW,eAAe;gBAC3B;gBAED,SAAA,CAAU,CAAC,CAAA,GAAIA,OAAAA,CAAO,KAAK,CAAA;gBAC3B,SAAA,CAAU,CAAC,CAAA,GAAIA,OAAAA,CAAO,KAAK,CAAA;gBAC3B,SAAA,CAAU,CAAC,CAAA,GAAIA,OAAAA,CAAO,KAAK,CAAA;gBAC3B,SAAA,CAAU,CAAC,CAAA,GAAIA,OAAAA,CAAO,KAAK,CAAA;gBAE3B,IAAI,KAAK,SAAA,CAAU,CAAC,CAAA,IAAK,KAAK,SAAA,CAAU,CAAC,CAAA,IAAA,CAAO,SAAA,CAAU,CAAC,CAAA,IAAK,IAAK,SAAA,CAAU,CAAC,CAAA,KAAM,gBAAgB;oBACpG,WAAW,mBAAmB,0BAA0B;gBACzD;gBAID,IAAI,MAAM,GACR;gBAEF,MAAO,MAAM,WAAW,MAAMA,QAAO,UAAA,CAAY;oBAC/C,QAAQA,OAAAA,CAAO,KAAK,CAAA;oBACpB,MAAM,eAAe,QAAQ;oBAC7B,IAAI,cAAc,SAAS;oBAE3B,IAAI,MAAM,SAAS,MAAM,QAAQ,SAAS;wBACxC,WAAW,mBAAmB,mBAAmB;oBAClD;oBAED,IAAI,cAAc;wBAEhB,MAAM,YAAYA,OAAAA,CAAO,KAAK,CAAA;wBAC9B,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;4BAC9B,eAAA,CAAgB,KAAK,CAAA,GAAI;wBAC1B;oBAEf,OAAmB;wBAEL,gBAAgB,GAAA,CAAIA,QAAO,QAAA,CAAS,KAAK,MAAM,KAAK,GAAG,GAAG;wBAC1D,OAAO;wBACP,OAAO;oBACR;gBACF;gBAID,MAAM,IAAI;gBACV,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,IAAI,MAAM;oBACV,SAAA,CAAU,MAAM,CAAA,GAAI,eAAA,CAAgB,IAAI,GAAG,CAAA;oBAC3C,OAAO;oBACP,SAAA,CAAU,SAAS,CAAC,CAAA,GAAI,eAAA,CAAgB,IAAI,GAAG,CAAA;oBAC/C,OAAO;oBACP,SAAA,CAAU,SAAS,CAAC,CAAA,GAAI,eAAA,CAAgB,IAAI,GAAG,CAAA;oBAC/C,OAAO;oBACP,SAAA,CAAU,SAAS,CAAC,CAAA,GAAI,eAAA,CAAgB,IAAI,GAAG,CAAA;oBAC/C,UAAU;gBACX;gBAED;YACD;YAED,OAAO;QACR;QAEH,MAAM,qBAAqB,SAAU,WAAA,EAAa,YAAA,EAAc,SAAA,EAAW,UAAA,EAAY;YACrF,MAAM,IAAI,WAAA,CAAY,eAAe,CAAC,CAAA;YACtC,MAAM,QAAQ,KAAK,GAAA,CAAI,GAAK,IAAI,GAAK,IAAI;YAEzC,SAAA,CAAU,aAAa,CAAC,CAAA,GAAI,WAAA,CAAY,eAAe,CAAC,CAAA,GAAI;YAC5D,SAAA,CAAU,aAAa,CAAC,CAAA,GAAI,WAAA,CAAY,eAAe,CAAC,CAAA,GAAI;YAC5D,SAAA,CAAU,aAAa,CAAC,CAAA,GAAI,WAAA,CAAY,eAAe,CAAC,CAAA,GAAI;YAC5D,SAAA,CAAU,aAAa,CAAC,CAAA,GAAI;QAC7B;QAED,MAAM,oBAAoB,SAAU,WAAA,EAAa,YAAA,EAAc,SAAA,EAAW,UAAA,EAAY;YACpF,MAAM,IAAI,WAAA,CAAY,eAAe,CAAC,CAAA;YACtC,MAAM,QAAQ,KAAK,GAAA,CAAI,GAAK,IAAI,GAAK,IAAI;YAGzC,SAAA,CAAU,aAAa,CAAC,CAAA,sJAAI,YAAA,CAAU,WAAA,CAAY,KAAK,GAAA,CAAI,WAAA,CAAY,eAAe,CAAC,CAAA,GAAI,OAAO,KAAK,CAAC;YACxG,SAAA,CAAU,aAAa,CAAC,CAAA,sJAAI,YAAA,CAAU,WAAA,CAAY,KAAK,GAAA,CAAI,WAAA,CAAY,eAAe,CAAC,CAAA,GAAI,OAAO,KAAK,CAAC;YACxG,SAAA,CAAU,aAAa,CAAC,CAAA,sJAAI,YAAA,CAAU,WAAA,CAAY,KAAK,GAAA,CAAI,WAAA,CAAY,eAAe,CAAC,CAAA,GAAI,OAAO,KAAK,CAAC;YACxG,SAAA,CAAU,aAAa,CAAC,CAAA,sJAAI,YAAA,CAAU,WAAA,CAAY,CAAC;QACpD;QAED,MAAM,YAAY,IAAI,WAAW,MAAM;QACvC,UAAU,GAAA,GAAM;QAChB,MAAM,mBAAmB,gBAAgB,SAAS;QAElD,MAAM,IAAI,iBAAiB,KAAA,EACzB,IAAI,iBAAiB,MAAA,EACrB,kBAAkB,oBAAoB,UAAU,QAAA,CAAS,UAAU,GAAG,GAAG,GAAG,CAAC;QAE/E,IAAI,MAAM;QACV,IAAI;QAEJ,OAAQ,IAAA,CAAK,IAAA,EAAI;YACf,wJAAK,YAAA;gBACH,cAAc,gBAAgB,MAAA,GAAS;gBACvC,MAAM,aAAa,IAAI,aAAa,cAAc,CAAC;gBAEnD,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,IAAK;oBACpC,mBAAmB,iBAAiB,IAAI,GAAG,YAAY,IAAI,CAAC;gBAC7D;gBAED,OAAO;gBACP,0JAAO,YAAA;gBACP;YAEF,wJAAK,gBAAA;gBACH,cAAc,gBAAgB,MAAA,GAAS;gBACvC,MAAM,YAAY,IAAI,YAAY,cAAc,CAAC;gBAEjD,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,IAAK;oBACpC,kBAAkB,iBAAiB,IAAI,GAAG,WAAW,IAAI,CAAC;gBAC3D;gBAED,OAAO;gBACP,0JAAO,gBAAA;gBACP;YAEF;gBACE,MAAM,IAAI,MAAM,yCAAyC,IAAA,CAAK,IAAI;QAErE;QAED,OAAO;YACL,OAAO;YACP,QAAQ;YACR;YACA,QAAQ,iBAAiB,MAAA;YACzB,OAAO,iBAAiB,KAAA;YACxB,UAAU,iBAAiB,QAAA;YAC3B;QACD;IACF;IAED,YAAY,KAAA,EAAO;QACjB,IAAA,CAAK,IAAA,GAAO;QACZ,OAAO,IAAA;IACR;IAED,KAAK,GAAA,EAAK,MAAA,EAAQ,UAAA,EAAY,OAAA,EAAS;QACrC,SAAS,eAAe,OAAA,EAAS,OAAA,EAAS;YACxC,OAAQ,QAAQ,IAAA,EAAI;gBAClB,wJAAK,YAAA;gBACL,wJAAK,gBAAA;oBACH,IAAI,gBAAgB,SAAS,QAAQ,UAAA,GAAa;yBAC7C,QAAQ,QAAA,GAAW;oBACxB,QAAQ,SAAA,sJAAY,eAAA;oBACpB,QAAQ,SAAA,sJAAY,eAAA;oBACpB,QAAQ,eAAA,GAAkB;oBAC1B,QAAQ,KAAA,GAAQ;oBAEhB;YACH;YAED,IAAI,QAAQ,OAAO,SAAS,OAAO;QACpC;QAED,OAAO,KAAA,CAAM,KAAK,KAAK,gBAAgB,YAAY,OAAO;IAC3D;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "file": "EXRLoader.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/src/loaders/EXRLoader.js"], "sourcesContent": ["import {\n  Texture,\n  DataTextureLoader,\n  DataUtils,\n  FloatType,\n  HalfFloatType,\n  LinearFilter,\n  RedFormat,\n  RGBAFormat,\n} from 'three'\nimport { unzlibSync } from 'fflate'\nimport { version } from '../_polyfill/constants'\n\n/**\n * OpenEXR loader currently supports uncompressed, ZIP(S), RLE, PIZ and DWA/B compression.\n * Supports reading as UnsignedByte, HalfFloat and Float type data texture.\n *\n * Referred to the original Industrial Light & Magic OpenEXR implementation and the TinyEXR / Syoyo Fujita\n * implementation, so I have preserved their copyright notices.\n */\n\n// /*\n// Copyright (c) 2014 - 2017, Syoyo Fujita\n// All rights reserved.\n\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//     * Neither the name of the Syoyo Fujita nor the\n//       names of its contributors may be used to endorse or promote products\n//       derived from this software without specific prior written permission.\n\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// */\n\n// // TinyEXR contains some OpenEXR code, which is licensed under ------------\n\n// ///////////////////////////////////////////////////////////////////////////\n// //\n// // Copyright (c) 2002, Industrial Light & Magic, a division of Lucas\n// // Digital Ltd. LLC\n// //\n// // All rights reserved.\n// //\n// // Redistribution and use in source and binary forms, with or without\n// // modification, are permitted provided that the following conditions are\n// // met:\n// // *       Redistributions of source code must retain the above copyright\n// // notice, this list of conditions and the following disclaimer.\n// // *       Redistributions in binary form must reproduce the above\n// // copyright notice, this list of conditions and the following disclaimer\n// // in the documentation and/or other materials provided with the\n// // distribution.\n// // *       Neither the name of Industrial Light & Magic nor the names of\n// // its contributors may be used to endorse or promote products derived\n// // from this software without specific prior written permission.\n// //\n// // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// // \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// //\n// ///////////////////////////////////////////////////////////////////////////\n\n// // End of OpenEXR license -------------------------------------------------\n\n// https://github.com/mrdoob/three.js/pull/25771\nconst hasColorSpace = version >= 152\n\nclass EXRLoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n\n    this.type = HalfFloatType\n  }\n\n  parse(buffer) {\n    const USHORT_RANGE = 1 << 16\n    const BITMAP_SIZE = USHORT_RANGE >> 3\n\n    const HUF_ENCBITS = 16 // literal (value) bit length\n    const HUF_DECBITS = 14 // decoding bit size (>= 8)\n\n    const HUF_ENCSIZE = (1 << HUF_ENCBITS) + 1 // encoding table size\n    const HUF_DECSIZE = 1 << HUF_DECBITS // decoding table size\n    const HUF_DECMASK = HUF_DECSIZE - 1\n\n    const NBITS = 16\n    const A_OFFSET = 1 << (NBITS - 1)\n    const MOD_MASK = (1 << NBITS) - 1\n\n    const SHORT_ZEROCODE_RUN = 59\n    const LONG_ZEROCODE_RUN = 63\n    const SHORTEST_LONG_RUN = 2 + LONG_ZEROCODE_RUN - SHORT_ZEROCODE_RUN\n\n    const ULONG_SIZE = 8\n    const FLOAT32_SIZE = 4\n    const INT32_SIZE = 4\n    const INT16_SIZE = 2\n    const INT8_SIZE = 1\n\n    const STATIC_HUFFMAN = 0\n    const DEFLATE = 1\n\n    const UNKNOWN = 0\n    const LOSSY_DCT = 1\n    const RLE = 2\n\n    const logBase = Math.pow(2.7182818, 2.2)\n\n    function reverseLutFromBitmap(bitmap, lut) {\n      var k = 0\n\n      for (var i = 0; i < USHORT_RANGE; ++i) {\n        if (i == 0 || bitmap[i >> 3] & (1 << (i & 7))) {\n          lut[k++] = i\n        }\n      }\n\n      var n = k - 1\n\n      while (k < USHORT_RANGE) lut[k++] = 0\n\n      return n\n    }\n\n    function hufClearDecTable(hdec) {\n      for (var i = 0; i < HUF_DECSIZE; i++) {\n        hdec[i] = {}\n        hdec[i].len = 0\n        hdec[i].lit = 0\n        hdec[i].p = null\n      }\n    }\n\n    const getBitsReturn = { l: 0, c: 0, lc: 0 }\n\n    function getBits(nBits, c, lc, uInt8Array, inOffset) {\n      while (lc < nBits) {\n        c = (c << 8) | parseUint8Array(uInt8Array, inOffset)\n        lc += 8\n      }\n\n      lc -= nBits\n\n      getBitsReturn.l = (c >> lc) & ((1 << nBits) - 1)\n      getBitsReturn.c = c\n      getBitsReturn.lc = lc\n    }\n\n    const hufTableBuffer = new Array(59)\n\n    function hufCanonicalCodeTable(hcode) {\n      for (var i = 0; i <= 58; ++i) hufTableBuffer[i] = 0\n      for (var i = 0; i < HUF_ENCSIZE; ++i) hufTableBuffer[hcode[i]] += 1\n\n      var c = 0\n\n      for (var i = 58; i > 0; --i) {\n        var nc = (c + hufTableBuffer[i]) >> 1\n        hufTableBuffer[i] = c\n        c = nc\n      }\n\n      for (var i = 0; i < HUF_ENCSIZE; ++i) {\n        var l = hcode[i]\n        if (l > 0) hcode[i] = l | (hufTableBuffer[l]++ << 6)\n      }\n    }\n\n    function hufUnpackEncTable(uInt8Array, inDataView, inOffset, ni, im, iM, hcode) {\n      var p = inOffset\n      var c = 0\n      var lc = 0\n\n      for (; im <= iM; im++) {\n        if (p.value - inOffset.value > ni) return false\n\n        getBits(6, c, lc, uInt8Array, p)\n\n        var l = getBitsReturn.l\n        c = getBitsReturn.c\n        lc = getBitsReturn.lc\n\n        hcode[im] = l\n\n        if (l == LONG_ZEROCODE_RUN) {\n          if (p.value - inOffset.value > ni) {\n            throw 'Something wrong with hufUnpackEncTable'\n          }\n\n          getBits(8, c, lc, uInt8Array, p)\n\n          var zerun = getBitsReturn.l + SHORTEST_LONG_RUN\n          c = getBitsReturn.c\n          lc = getBitsReturn.lc\n\n          if (im + zerun > iM + 1) {\n            throw 'Something wrong with hufUnpackEncTable'\n          }\n\n          while (zerun--) hcode[im++] = 0\n\n          im--\n        } else if (l >= SHORT_ZEROCODE_RUN) {\n          var zerun = l - SHORT_ZEROCODE_RUN + 2\n\n          if (im + zerun > iM + 1) {\n            throw 'Something wrong with hufUnpackEncTable'\n          }\n\n          while (zerun--) hcode[im++] = 0\n\n          im--\n        }\n      }\n\n      hufCanonicalCodeTable(hcode)\n    }\n\n    function hufLength(code) {\n      return code & 63\n    }\n\n    function hufCode(code) {\n      return code >> 6\n    }\n\n    function hufBuildDecTable(hcode, im, iM, hdecod) {\n      for (; im <= iM; im++) {\n        var c = hufCode(hcode[im])\n        var l = hufLength(hcode[im])\n\n        if (c >> l) {\n          throw 'Invalid table entry'\n        }\n\n        if (l > HUF_DECBITS) {\n          var pl = hdecod[c >> (l - HUF_DECBITS)]\n\n          if (pl.len) {\n            throw 'Invalid table entry'\n          }\n\n          pl.lit++\n\n          if (pl.p) {\n            var p = pl.p\n            pl.p = new Array(pl.lit)\n\n            for (var i = 0; i < pl.lit - 1; ++i) {\n              pl.p[i] = p[i]\n            }\n          } else {\n            pl.p = new Array(1)\n          }\n\n          pl.p[pl.lit - 1] = im\n        } else if (l) {\n          var plOffset = 0\n\n          for (var i = 1 << (HUF_DECBITS - l); i > 0; i--) {\n            var pl = hdecod[(c << (HUF_DECBITS - l)) + plOffset]\n\n            if (pl.len || pl.p) {\n              throw 'Invalid table entry'\n            }\n\n            pl.len = l\n            pl.lit = im\n\n            plOffset++\n          }\n        }\n      }\n\n      return true\n    }\n\n    const getCharReturn = { c: 0, lc: 0 }\n\n    function getChar(c, lc, uInt8Array, inOffset) {\n      c = (c << 8) | parseUint8Array(uInt8Array, inOffset)\n      lc += 8\n\n      getCharReturn.c = c\n      getCharReturn.lc = lc\n    }\n\n    const getCodeReturn = { c: 0, lc: 0 }\n\n    function getCode(po, rlc, c, lc, uInt8Array, inDataView, inOffset, outBuffer, outBufferOffset, outBufferEndOffset) {\n      if (po == rlc) {\n        if (lc < 8) {\n          getChar(c, lc, uInt8Array, inOffset)\n          c = getCharReturn.c\n          lc = getCharReturn.lc\n        }\n\n        lc -= 8\n\n        var cs = c >> lc\n        var cs = new Uint8Array([cs])[0]\n\n        if (outBufferOffset.value + cs > outBufferEndOffset) {\n          return false\n        }\n\n        var s = outBuffer[outBufferOffset.value - 1]\n\n        while (cs-- > 0) {\n          outBuffer[outBufferOffset.value++] = s\n        }\n      } else if (outBufferOffset.value < outBufferEndOffset) {\n        outBuffer[outBufferOffset.value++] = po\n      } else {\n        return false\n      }\n\n      getCodeReturn.c = c\n      getCodeReturn.lc = lc\n    }\n\n    function UInt16(value) {\n      return value & 0xffff\n    }\n\n    function Int16(value) {\n      var ref = UInt16(value)\n      return ref > 0x7fff ? ref - 0x10000 : ref\n    }\n\n    const wdec14Return = { a: 0, b: 0 }\n\n    function wdec14(l, h) {\n      var ls = Int16(l)\n      var hs = Int16(h)\n\n      var hi = hs\n      var ai = ls + (hi & 1) + (hi >> 1)\n\n      var as = ai\n      var bs = ai - hi\n\n      wdec14Return.a = as\n      wdec14Return.b = bs\n    }\n\n    function wdec16(l, h) {\n      var m = UInt16(l)\n      var d = UInt16(h)\n\n      var bb = (m - (d >> 1)) & MOD_MASK\n      var aa = (d + bb - A_OFFSET) & MOD_MASK\n\n      wdec14Return.a = aa\n      wdec14Return.b = bb\n    }\n\n    function wav2Decode(buffer, j, nx, ox, ny, oy, mx) {\n      var w14 = mx < 1 << 14\n      var n = nx > ny ? ny : nx\n      var p = 1\n      var p2\n\n      while (p <= n) p <<= 1\n\n      p >>= 1\n      p2 = p\n      p >>= 1\n\n      while (p >= 1) {\n        var py = 0\n        var ey = py + oy * (ny - p2)\n        var oy1 = oy * p\n        var oy2 = oy * p2\n        var ox1 = ox * p\n        var ox2 = ox * p2\n        var i00, i01, i10, i11\n\n        for (; py <= ey; py += oy2) {\n          var px = py\n          var ex = py + ox * (nx - p2)\n\n          for (; px <= ex; px += ox2) {\n            var p01 = px + ox1\n            var p10 = px + oy1\n            var p11 = p10 + ox1\n\n            if (w14) {\n              wdec14(buffer[px + j], buffer[p10 + j])\n\n              i00 = wdec14Return.a\n              i10 = wdec14Return.b\n\n              wdec14(buffer[p01 + j], buffer[p11 + j])\n\n              i01 = wdec14Return.a\n              i11 = wdec14Return.b\n\n              wdec14(i00, i01)\n\n              buffer[px + j] = wdec14Return.a\n              buffer[p01 + j] = wdec14Return.b\n\n              wdec14(i10, i11)\n\n              buffer[p10 + j] = wdec14Return.a\n              buffer[p11 + j] = wdec14Return.b\n            } else {\n              wdec16(buffer[px + j], buffer[p10 + j])\n\n              i00 = wdec14Return.a\n              i10 = wdec14Return.b\n\n              wdec16(buffer[p01 + j], buffer[p11 + j])\n\n              i01 = wdec14Return.a\n              i11 = wdec14Return.b\n\n              wdec16(i00, i01)\n\n              buffer[px + j] = wdec14Return.a\n              buffer[p01 + j] = wdec14Return.b\n\n              wdec16(i10, i11)\n\n              buffer[p10 + j] = wdec14Return.a\n              buffer[p11 + j] = wdec14Return.b\n            }\n          }\n\n          if (nx & p) {\n            var p10 = px + oy1\n\n            if (w14) wdec14(buffer[px + j], buffer[p10 + j])\n            else wdec16(buffer[px + j], buffer[p10 + j])\n\n            i00 = wdec14Return.a\n            buffer[p10 + j] = wdec14Return.b\n\n            buffer[px + j] = i00\n          }\n        }\n\n        if (ny & p) {\n          var px = py\n          var ex = py + ox * (nx - p2)\n\n          for (; px <= ex; px += ox2) {\n            var p01 = px + ox1\n\n            if (w14) wdec14(buffer[px + j], buffer[p01 + j])\n            else wdec16(buffer[px + j], buffer[p01 + j])\n\n            i00 = wdec14Return.a\n            buffer[p01 + j] = wdec14Return.b\n\n            buffer[px + j] = i00\n          }\n        }\n\n        p2 = p\n        p >>= 1\n      }\n\n      return py\n    }\n\n    function hufDecode(\n      encodingTable,\n      decodingTable,\n      uInt8Array,\n      inDataView,\n      inOffset,\n      ni,\n      rlc,\n      no,\n      outBuffer,\n      outOffset,\n    ) {\n      var c = 0\n      var lc = 0\n      var outBufferEndOffset = no\n      var inOffsetEnd = Math.trunc(inOffset.value + (ni + 7) / 8)\n\n      while (inOffset.value < inOffsetEnd) {\n        getChar(c, lc, uInt8Array, inOffset)\n\n        c = getCharReturn.c\n        lc = getCharReturn.lc\n\n        while (lc >= HUF_DECBITS) {\n          var index = (c >> (lc - HUF_DECBITS)) & HUF_DECMASK\n          var pl = decodingTable[index]\n\n          if (pl.len) {\n            lc -= pl.len\n\n            getCode(pl.lit, rlc, c, lc, uInt8Array, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset)\n\n            c = getCodeReturn.c\n            lc = getCodeReturn.lc\n          } else {\n            if (!pl.p) {\n              throw 'hufDecode issues'\n            }\n\n            var j\n\n            for (j = 0; j < pl.lit; j++) {\n              var l = hufLength(encodingTable[pl.p[j]])\n\n              while (lc < l && inOffset.value < inOffsetEnd) {\n                getChar(c, lc, uInt8Array, inOffset)\n\n                c = getCharReturn.c\n                lc = getCharReturn.lc\n              }\n\n              if (lc >= l) {\n                if (hufCode(encodingTable[pl.p[j]]) == ((c >> (lc - l)) & ((1 << l) - 1))) {\n                  lc -= l\n\n                  getCode(\n                    pl.p[j],\n                    rlc,\n                    c,\n                    lc,\n                    uInt8Array,\n                    inDataView,\n                    inOffset,\n                    outBuffer,\n                    outOffset,\n                    outBufferEndOffset,\n                  )\n\n                  c = getCodeReturn.c\n                  lc = getCodeReturn.lc\n\n                  break\n                }\n              }\n            }\n\n            if (j == pl.lit) {\n              throw 'hufDecode issues'\n            }\n          }\n        }\n      }\n\n      var i = (8 - ni) & 7\n\n      c >>= i\n      lc -= i\n\n      while (lc > 0) {\n        var pl = decodingTable[(c << (HUF_DECBITS - lc)) & HUF_DECMASK]\n\n        if (pl.len) {\n          lc -= pl.len\n\n          getCode(pl.lit, rlc, c, lc, uInt8Array, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset)\n\n          c = getCodeReturn.c\n          lc = getCodeReturn.lc\n        } else {\n          throw 'hufDecode issues'\n        }\n      }\n\n      return true\n    }\n\n    function hufUncompress(uInt8Array, inDataView, inOffset, nCompressed, outBuffer, nRaw) {\n      var outOffset = { value: 0 }\n      var initialInOffset = inOffset.value\n\n      var im = parseUint32(inDataView, inOffset)\n      var iM = parseUint32(inDataView, inOffset)\n\n      inOffset.value += 4\n\n      var nBits = parseUint32(inDataView, inOffset)\n\n      inOffset.value += 4\n\n      if (im < 0 || im >= HUF_ENCSIZE || iM < 0 || iM >= HUF_ENCSIZE) {\n        throw 'Something wrong with HUF_ENCSIZE'\n      }\n\n      var freq = new Array(HUF_ENCSIZE)\n      var hdec = new Array(HUF_DECSIZE)\n\n      hufClearDecTable(hdec)\n\n      var ni = nCompressed - (inOffset.value - initialInOffset)\n\n      hufUnpackEncTable(uInt8Array, inDataView, inOffset, ni, im, iM, freq)\n\n      if (nBits > 8 * (nCompressed - (inOffset.value - initialInOffset))) {\n        throw 'Something wrong with hufUncompress'\n      }\n\n      hufBuildDecTable(freq, im, iM, hdec)\n\n      hufDecode(freq, hdec, uInt8Array, inDataView, inOffset, nBits, iM, nRaw, outBuffer, outOffset)\n    }\n\n    function applyLut(lut, data, nData) {\n      for (var i = 0; i < nData; ++i) {\n        data[i] = lut[data[i]]\n      }\n    }\n\n    function predictor(source) {\n      for (var t = 1; t < source.length; t++) {\n        var d = source[t - 1] + source[t] - 128\n        source[t] = d\n      }\n    }\n\n    function interleaveScalar(source, out) {\n      var t1 = 0\n      var t2 = Math.floor((source.length + 1) / 2)\n      var s = 0\n      var stop = source.length - 1\n\n      while (true) {\n        if (s > stop) break\n        out[s++] = source[t1++]\n\n        if (s > stop) break\n        out[s++] = source[t2++]\n      }\n    }\n\n    function decodeRunLength(source) {\n      var size = source.byteLength\n      var out = new Array()\n      var p = 0\n\n      var reader = new DataView(source)\n\n      while (size > 0) {\n        var l = reader.getInt8(p++)\n\n        if (l < 0) {\n          var count = -l\n          size -= count + 1\n\n          for (var i = 0; i < count; i++) {\n            out.push(reader.getUint8(p++))\n          }\n        } else {\n          var count = l\n          size -= 2\n\n          var value = reader.getUint8(p++)\n\n          for (var i = 0; i < count + 1; i++) {\n            out.push(value)\n          }\n        }\n      }\n\n      return out\n    }\n\n    function lossyDctDecode(cscSet, rowPtrs, channelData, acBuffer, dcBuffer, outBuffer) {\n      var dataView = new DataView(outBuffer.buffer)\n\n      var width = channelData[cscSet.idx[0]].width\n      var height = channelData[cscSet.idx[0]].height\n\n      var numComp = 3\n\n      var numFullBlocksX = Math.floor(width / 8.0)\n      var numBlocksX = Math.ceil(width / 8.0)\n      var numBlocksY = Math.ceil(height / 8.0)\n      var leftoverX = width - (numBlocksX - 1) * 8\n      var leftoverY = height - (numBlocksY - 1) * 8\n\n      var currAcComp = { value: 0 }\n      var currDcComp = new Array(numComp)\n      var dctData = new Array(numComp)\n      var halfZigBlock = new Array(numComp)\n      var rowBlock = new Array(numComp)\n      var rowOffsets = new Array(numComp)\n\n      for (let comp = 0; comp < numComp; ++comp) {\n        rowOffsets[comp] = rowPtrs[cscSet.idx[comp]]\n        currDcComp[comp] = comp < 1 ? 0 : currDcComp[comp - 1] + numBlocksX * numBlocksY\n        dctData[comp] = new Float32Array(64)\n        halfZigBlock[comp] = new Uint16Array(64)\n        rowBlock[comp] = new Uint16Array(numBlocksX * 64)\n      }\n\n      for (let blocky = 0; blocky < numBlocksY; ++blocky) {\n        var maxY = 8\n\n        if (blocky == numBlocksY - 1) maxY = leftoverY\n\n        var maxX = 8\n\n        for (let blockx = 0; blockx < numBlocksX; ++blockx) {\n          if (blockx == numBlocksX - 1) maxX = leftoverX\n\n          for (let comp = 0; comp < numComp; ++comp) {\n            halfZigBlock[comp].fill(0)\n\n            // set block DC component\n            halfZigBlock[comp][0] = dcBuffer[currDcComp[comp]++]\n            // set block AC components\n            unRleAC(currAcComp, acBuffer, halfZigBlock[comp])\n\n            // UnZigZag block to float\n            unZigZag(halfZigBlock[comp], dctData[comp])\n            // decode float dct\n            dctInverse(dctData[comp])\n          }\n\n          if (numComp == 3) {\n            csc709Inverse(dctData)\n          }\n\n          for (let comp = 0; comp < numComp; ++comp) {\n            convertToHalf(dctData[comp], rowBlock[comp], blockx * 64)\n          }\n        } // blockx\n\n        let offset = 0\n\n        for (let comp = 0; comp < numComp; ++comp) {\n          const type = channelData[cscSet.idx[comp]].type\n\n          for (let y = 8 * blocky; y < 8 * blocky + maxY; ++y) {\n            offset = rowOffsets[comp][y]\n\n            for (let blockx = 0; blockx < numFullBlocksX; ++blockx) {\n              const src = blockx * 64 + (y & 0x7) * 8\n\n              dataView.setUint16(offset + 0 * INT16_SIZE * type, rowBlock[comp][src + 0], true)\n              dataView.setUint16(offset + 1 * INT16_SIZE * type, rowBlock[comp][src + 1], true)\n              dataView.setUint16(offset + 2 * INT16_SIZE * type, rowBlock[comp][src + 2], true)\n              dataView.setUint16(offset + 3 * INT16_SIZE * type, rowBlock[comp][src + 3], true)\n\n              dataView.setUint16(offset + 4 * INT16_SIZE * type, rowBlock[comp][src + 4], true)\n              dataView.setUint16(offset + 5 * INT16_SIZE * type, rowBlock[comp][src + 5], true)\n              dataView.setUint16(offset + 6 * INT16_SIZE * type, rowBlock[comp][src + 6], true)\n              dataView.setUint16(offset + 7 * INT16_SIZE * type, rowBlock[comp][src + 7], true)\n\n              offset += 8 * INT16_SIZE * type\n            }\n          }\n\n          // handle partial X blocks\n          if (numFullBlocksX != numBlocksX) {\n            for (let y = 8 * blocky; y < 8 * blocky + maxY; ++y) {\n              const offset = rowOffsets[comp][y] + 8 * numFullBlocksX * INT16_SIZE * type\n              const src = numFullBlocksX * 64 + (y & 0x7) * 8\n\n              for (let x = 0; x < maxX; ++x) {\n                dataView.setUint16(offset + x * INT16_SIZE * type, rowBlock[comp][src + x], true)\n              }\n            }\n          }\n        } // comp\n      } // blocky\n\n      var halfRow = new Uint16Array(width)\n      var dataView = new DataView(outBuffer.buffer)\n\n      // convert channels back to float, if needed\n      for (var comp = 0; comp < numComp; ++comp) {\n        channelData[cscSet.idx[comp]].decoded = true\n        var type = channelData[cscSet.idx[comp]].type\n\n        if (channelData[comp].type != 2) continue\n\n        for (var y = 0; y < height; ++y) {\n          const offset = rowOffsets[comp][y]\n\n          for (var x = 0; x < width; ++x) {\n            halfRow[x] = dataView.getUint16(offset + x * INT16_SIZE * type, true)\n          }\n\n          for (var x = 0; x < width; ++x) {\n            dataView.setFloat32(offset + x * INT16_SIZE * type, decodeFloat16(halfRow[x]), true)\n          }\n        }\n      }\n    }\n\n    function unRleAC(currAcComp, acBuffer, halfZigBlock) {\n      var acValue\n      var dctComp = 1\n\n      while (dctComp < 64) {\n        acValue = acBuffer[currAcComp.value]\n\n        if (acValue == 0xff00) {\n          dctComp = 64\n        } else if (acValue >> 8 == 0xff) {\n          dctComp += acValue & 0xff\n        } else {\n          halfZigBlock[dctComp] = acValue\n          dctComp++\n        }\n\n        currAcComp.value++\n      }\n    }\n\n    function unZigZag(src, dst) {\n      dst[0] = decodeFloat16(src[0])\n      dst[1] = decodeFloat16(src[1])\n      dst[2] = decodeFloat16(src[5])\n      dst[3] = decodeFloat16(src[6])\n      dst[4] = decodeFloat16(src[14])\n      dst[5] = decodeFloat16(src[15])\n      dst[6] = decodeFloat16(src[27])\n      dst[7] = decodeFloat16(src[28])\n      dst[8] = decodeFloat16(src[2])\n      dst[9] = decodeFloat16(src[4])\n\n      dst[10] = decodeFloat16(src[7])\n      dst[11] = decodeFloat16(src[13])\n      dst[12] = decodeFloat16(src[16])\n      dst[13] = decodeFloat16(src[26])\n      dst[14] = decodeFloat16(src[29])\n      dst[15] = decodeFloat16(src[42])\n      dst[16] = decodeFloat16(src[3])\n      dst[17] = decodeFloat16(src[8])\n      dst[18] = decodeFloat16(src[12])\n      dst[19] = decodeFloat16(src[17])\n\n      dst[20] = decodeFloat16(src[25])\n      dst[21] = decodeFloat16(src[30])\n      dst[22] = decodeFloat16(src[41])\n      dst[23] = decodeFloat16(src[43])\n      dst[24] = decodeFloat16(src[9])\n      dst[25] = decodeFloat16(src[11])\n      dst[26] = decodeFloat16(src[18])\n      dst[27] = decodeFloat16(src[24])\n      dst[28] = decodeFloat16(src[31])\n      dst[29] = decodeFloat16(src[40])\n\n      dst[30] = decodeFloat16(src[44])\n      dst[31] = decodeFloat16(src[53])\n      dst[32] = decodeFloat16(src[10])\n      dst[33] = decodeFloat16(src[19])\n      dst[34] = decodeFloat16(src[23])\n      dst[35] = decodeFloat16(src[32])\n      dst[36] = decodeFloat16(src[39])\n      dst[37] = decodeFloat16(src[45])\n      dst[38] = decodeFloat16(src[52])\n      dst[39] = decodeFloat16(src[54])\n\n      dst[40] = decodeFloat16(src[20])\n      dst[41] = decodeFloat16(src[22])\n      dst[42] = decodeFloat16(src[33])\n      dst[43] = decodeFloat16(src[38])\n      dst[44] = decodeFloat16(src[46])\n      dst[45] = decodeFloat16(src[51])\n      dst[46] = decodeFloat16(src[55])\n      dst[47] = decodeFloat16(src[60])\n      dst[48] = decodeFloat16(src[21])\n      dst[49] = decodeFloat16(src[34])\n\n      dst[50] = decodeFloat16(src[37])\n      dst[51] = decodeFloat16(src[47])\n      dst[52] = decodeFloat16(src[50])\n      dst[53] = decodeFloat16(src[56])\n      dst[54] = decodeFloat16(src[59])\n      dst[55] = decodeFloat16(src[61])\n      dst[56] = decodeFloat16(src[35])\n      dst[57] = decodeFloat16(src[36])\n      dst[58] = decodeFloat16(src[48])\n      dst[59] = decodeFloat16(src[49])\n\n      dst[60] = decodeFloat16(src[57])\n      dst[61] = decodeFloat16(src[58])\n      dst[62] = decodeFloat16(src[62])\n      dst[63] = decodeFloat16(src[63])\n    }\n\n    function dctInverse(data) {\n      const a = 0.5 * Math.cos(3.14159 / 4.0)\n      const b = 0.5 * Math.cos(3.14159 / 16.0)\n      const c = 0.5 * Math.cos(3.14159 / 8.0)\n      const d = 0.5 * Math.cos((3.0 * 3.14159) / 16.0)\n      const e = 0.5 * Math.cos((5.0 * 3.14159) / 16.0)\n      const f = 0.5 * Math.cos((3.0 * 3.14159) / 8.0)\n      const g = 0.5 * Math.cos((7.0 * 3.14159) / 16.0)\n\n      var alpha = new Array(4)\n      var beta = new Array(4)\n      var theta = new Array(4)\n      var gamma = new Array(4)\n\n      for (var row = 0; row < 8; ++row) {\n        var rowPtr = row * 8\n\n        alpha[0] = c * data[rowPtr + 2]\n        alpha[1] = f * data[rowPtr + 2]\n        alpha[2] = c * data[rowPtr + 6]\n        alpha[3] = f * data[rowPtr + 6]\n\n        beta[0] = b * data[rowPtr + 1] + d * data[rowPtr + 3] + e * data[rowPtr + 5] + g * data[rowPtr + 7]\n        beta[1] = d * data[rowPtr + 1] - g * data[rowPtr + 3] - b * data[rowPtr + 5] - e * data[rowPtr + 7]\n        beta[2] = e * data[rowPtr + 1] - b * data[rowPtr + 3] + g * data[rowPtr + 5] + d * data[rowPtr + 7]\n        beta[3] = g * data[rowPtr + 1] - e * data[rowPtr + 3] + d * data[rowPtr + 5] - b * data[rowPtr + 7]\n\n        theta[0] = a * (data[rowPtr + 0] + data[rowPtr + 4])\n        theta[3] = a * (data[rowPtr + 0] - data[rowPtr + 4])\n        theta[1] = alpha[0] + alpha[3]\n        theta[2] = alpha[1] - alpha[2]\n\n        gamma[0] = theta[0] + theta[1]\n        gamma[1] = theta[3] + theta[2]\n        gamma[2] = theta[3] - theta[2]\n        gamma[3] = theta[0] - theta[1]\n\n        data[rowPtr + 0] = gamma[0] + beta[0]\n        data[rowPtr + 1] = gamma[1] + beta[1]\n        data[rowPtr + 2] = gamma[2] + beta[2]\n        data[rowPtr + 3] = gamma[3] + beta[3]\n\n        data[rowPtr + 4] = gamma[3] - beta[3]\n        data[rowPtr + 5] = gamma[2] - beta[2]\n        data[rowPtr + 6] = gamma[1] - beta[1]\n        data[rowPtr + 7] = gamma[0] - beta[0]\n      }\n\n      for (var column = 0; column < 8; ++column) {\n        alpha[0] = c * data[16 + column]\n        alpha[1] = f * data[16 + column]\n        alpha[2] = c * data[48 + column]\n        alpha[3] = f * data[48 + column]\n\n        beta[0] = b * data[8 + column] + d * data[24 + column] + e * data[40 + column] + g * data[56 + column]\n        beta[1] = d * data[8 + column] - g * data[24 + column] - b * data[40 + column] - e * data[56 + column]\n        beta[2] = e * data[8 + column] - b * data[24 + column] + g * data[40 + column] + d * data[56 + column]\n        beta[3] = g * data[8 + column] - e * data[24 + column] + d * data[40 + column] - b * data[56 + column]\n\n        theta[0] = a * (data[column] + data[32 + column])\n        theta[3] = a * (data[column] - data[32 + column])\n\n        theta[1] = alpha[0] + alpha[3]\n        theta[2] = alpha[1] - alpha[2]\n\n        gamma[0] = theta[0] + theta[1]\n        gamma[1] = theta[3] + theta[2]\n        gamma[2] = theta[3] - theta[2]\n        gamma[3] = theta[0] - theta[1]\n\n        data[0 + column] = gamma[0] + beta[0]\n        data[8 + column] = gamma[1] + beta[1]\n        data[16 + column] = gamma[2] + beta[2]\n        data[24 + column] = gamma[3] + beta[3]\n\n        data[32 + column] = gamma[3] - beta[3]\n        data[40 + column] = gamma[2] - beta[2]\n        data[48 + column] = gamma[1] - beta[1]\n        data[56 + column] = gamma[0] - beta[0]\n      }\n    }\n\n    function csc709Inverse(data) {\n      for (var i = 0; i < 64; ++i) {\n        var y = data[0][i]\n        var cb = data[1][i]\n        var cr = data[2][i]\n\n        data[0][i] = y + 1.5747 * cr\n        data[1][i] = y - 0.1873 * cb - 0.4682 * cr\n        data[2][i] = y + 1.8556 * cb\n      }\n    }\n\n    function convertToHalf(src, dst, idx) {\n      for (var i = 0; i < 64; ++i) {\n        dst[idx + i] = DataUtils.toHalfFloat(toLinear(src[i]))\n      }\n    }\n\n    function toLinear(float) {\n      if (float <= 1) {\n        return Math.sign(float) * Math.pow(Math.abs(float), 2.2)\n      } else {\n        return Math.sign(float) * Math.pow(logBase, Math.abs(float) - 1.0)\n      }\n    }\n\n    function uncompressRAW(info) {\n      return new DataView(info.array.buffer, info.offset.value, info.size)\n    }\n\n    function uncompressRLE(info) {\n      var compressed = info.viewer.buffer.slice(info.offset.value, info.offset.value + info.size)\n\n      var rawBuffer = new Uint8Array(decodeRunLength(compressed))\n      var tmpBuffer = new Uint8Array(rawBuffer.length)\n\n      predictor(rawBuffer) // revert predictor\n\n      interleaveScalar(rawBuffer, tmpBuffer) // interleave pixels\n\n      return new DataView(tmpBuffer.buffer)\n    }\n\n    function uncompressZIP(info) {\n      var compressed = info.array.slice(info.offset.value, info.offset.value + info.size)\n      var rawBuffer = unzlibSync(compressed)\n      var tmpBuffer = new Uint8Array(rawBuffer.length)\n\n      predictor(rawBuffer) // revert predictor\n\n      interleaveScalar(rawBuffer, tmpBuffer) // interleave pixels\n\n      return new DataView(tmpBuffer.buffer)\n    }\n\n    function uncompressPIZ(info) {\n      var inDataView = info.viewer\n      var inOffset = { value: info.offset.value }\n\n      var outBuffer = new Uint16Array(info.width * info.scanlineBlockSize * (info.channels * info.type))\n      var bitmap = new Uint8Array(BITMAP_SIZE)\n\n      // Setup channel info\n      var outBufferEnd = 0\n      var pizChannelData = new Array(info.channels)\n      for (var i = 0; i < info.channels; i++) {\n        pizChannelData[i] = {}\n        pizChannelData[i]['start'] = outBufferEnd\n        pizChannelData[i]['end'] = pizChannelData[i]['start']\n        pizChannelData[i]['nx'] = info.width\n        pizChannelData[i]['ny'] = info.lines\n        pizChannelData[i]['size'] = info.type\n\n        outBufferEnd += pizChannelData[i].nx * pizChannelData[i].ny * pizChannelData[i].size\n      }\n\n      // Read range compression data\n\n      var minNonZero = parseUint16(inDataView, inOffset)\n      var maxNonZero = parseUint16(inDataView, inOffset)\n\n      if (maxNonZero >= BITMAP_SIZE) {\n        throw 'Something is wrong with PIZ_COMPRESSION BITMAP_SIZE'\n      }\n\n      if (minNonZero <= maxNonZero) {\n        for (var i = 0; i < maxNonZero - minNonZero + 1; i++) {\n          bitmap[i + minNonZero] = parseUint8(inDataView, inOffset)\n        }\n      }\n\n      // Reverse LUT\n      var lut = new Uint16Array(USHORT_RANGE)\n      var maxValue = reverseLutFromBitmap(bitmap, lut)\n\n      var length = parseUint32(inDataView, inOffset)\n\n      // Huffman decoding\n      hufUncompress(info.array, inDataView, inOffset, length, outBuffer, outBufferEnd)\n\n      // Wavelet decoding\n      for (var i = 0; i < info.channels; ++i) {\n        var cd = pizChannelData[i]\n\n        for (var j = 0; j < pizChannelData[i].size; ++j) {\n          wav2Decode(outBuffer, cd.start + j, cd.nx, cd.size, cd.ny, cd.nx * cd.size, maxValue)\n        }\n      }\n\n      // Expand the pixel data to their original range\n      applyLut(lut, outBuffer, outBufferEnd)\n\n      // Rearrange the pixel data into the format expected by the caller.\n      var tmpOffset = 0\n      var tmpBuffer = new Uint8Array(outBuffer.buffer.byteLength)\n      for (var y = 0; y < info.lines; y++) {\n        for (var c = 0; c < info.channels; c++) {\n          var cd = pizChannelData[c]\n\n          var n = cd.nx * cd.size\n          var cp = new Uint8Array(outBuffer.buffer, cd.end * INT16_SIZE, n * INT16_SIZE)\n\n          tmpBuffer.set(cp, tmpOffset)\n          tmpOffset += n * INT16_SIZE\n          cd.end += n\n        }\n      }\n\n      return new DataView(tmpBuffer.buffer)\n    }\n\n    function uncompressPXR(info) {\n      var compressed = info.array.slice(info.offset.value, info.offset.value + info.size)\n      var rawBuffer = unzlibSync(compressed)\n\n      const sz = info.lines * info.channels * info.width\n      const tmpBuffer = info.type == 1 ? new Uint16Array(sz) : new Uint32Array(sz)\n\n      let tmpBufferEnd = 0\n      let writePtr = 0\n      const ptr = new Array(4)\n\n      for (let y = 0; y < info.lines; y++) {\n        for (let c = 0; c < info.channels; c++) {\n          let pixel = 0\n\n          switch (info.type) {\n            case 1:\n              ptr[0] = tmpBufferEnd\n              ptr[1] = ptr[0] + info.width\n              tmpBufferEnd = ptr[1] + info.width\n\n              for (let j = 0; j < info.width; ++j) {\n                const diff = (rawBuffer[ptr[0]++] << 8) | rawBuffer[ptr[1]++]\n\n                pixel += diff\n\n                tmpBuffer[writePtr] = pixel\n                writePtr++\n              }\n\n              break\n\n            case 2:\n              ptr[0] = tmpBufferEnd\n              ptr[1] = ptr[0] + info.width\n              ptr[2] = ptr[1] + info.width\n              tmpBufferEnd = ptr[2] + info.width\n\n              for (let j = 0; j < info.width; ++j) {\n                const diff = (rawBuffer[ptr[0]++] << 24) | (rawBuffer[ptr[1]++] << 16) | (rawBuffer[ptr[2]++] << 8)\n\n                pixel += diff\n\n                tmpBuffer[writePtr] = pixel\n                writePtr++\n              }\n\n              break\n          }\n        }\n      }\n\n      return new DataView(tmpBuffer.buffer)\n    }\n\n    function uncompressDWA(info) {\n      var inDataView = info.viewer\n      var inOffset = { value: info.offset.value }\n      var outBuffer = new Uint8Array(info.width * info.lines * (info.channels * info.type * INT16_SIZE))\n\n      // Read compression header information\n      var dwaHeader = {\n        version: parseInt64(inDataView, inOffset),\n        unknownUncompressedSize: parseInt64(inDataView, inOffset),\n        unknownCompressedSize: parseInt64(inDataView, inOffset),\n        acCompressedSize: parseInt64(inDataView, inOffset),\n        dcCompressedSize: parseInt64(inDataView, inOffset),\n        rleCompressedSize: parseInt64(inDataView, inOffset),\n        rleUncompressedSize: parseInt64(inDataView, inOffset),\n        rleRawSize: parseInt64(inDataView, inOffset),\n        totalAcUncompressedCount: parseInt64(inDataView, inOffset),\n        totalDcUncompressedCount: parseInt64(inDataView, inOffset),\n        acCompression: parseInt64(inDataView, inOffset),\n      }\n\n      if (dwaHeader.version < 2) {\n        throw 'EXRLoader.parse: ' + EXRHeader.compression + ' version ' + dwaHeader.version + ' is unsupported'\n      }\n\n      // Read channel ruleset information\n      var channelRules = new Array()\n      var ruleSize = parseUint16(inDataView, inOffset) - INT16_SIZE\n\n      while (ruleSize > 0) {\n        var name = parseNullTerminatedString(inDataView.buffer, inOffset)\n        var value = parseUint8(inDataView, inOffset)\n        var compression = (value >> 2) & 3\n        var csc = (value >> 4) - 1\n        var index = new Int8Array([csc])[0]\n        var type = parseUint8(inDataView, inOffset)\n\n        channelRules.push({\n          name: name,\n          index: index,\n          type: type,\n          compression: compression,\n        })\n\n        ruleSize -= name.length + 3\n      }\n\n      // Classify channels\n      var channels = EXRHeader.channels\n      var channelData = new Array(info.channels)\n\n      for (var i = 0; i < info.channels; ++i) {\n        var cd = (channelData[i] = {})\n        var channel = channels[i]\n\n        cd.name = channel.name\n        cd.compression = UNKNOWN\n        cd.decoded = false\n        cd.type = channel.pixelType\n        cd.pLinear = channel.pLinear\n        cd.width = info.width\n        cd.height = info.lines\n      }\n\n      var cscSet = {\n        idx: new Array(3),\n      }\n\n      for (var offset = 0; offset < info.channels; ++offset) {\n        var cd = channelData[offset]\n\n        for (var i = 0; i < channelRules.length; ++i) {\n          var rule = channelRules[i]\n\n          if (cd.name == rule.name) {\n            cd.compression = rule.compression\n\n            if (rule.index >= 0) {\n              cscSet.idx[rule.index] = offset\n            }\n\n            cd.offset = offset\n          }\n        }\n      }\n\n      // Read DCT - AC component data\n      if (dwaHeader.acCompressedSize > 0) {\n        switch (dwaHeader.acCompression) {\n          case STATIC_HUFFMAN:\n            var acBuffer = new Uint16Array(dwaHeader.totalAcUncompressedCount)\n            hufUncompress(\n              info.array,\n              inDataView,\n              inOffset,\n              dwaHeader.acCompressedSize,\n              acBuffer,\n              dwaHeader.totalAcUncompressedCount,\n            )\n            break\n\n          case DEFLATE:\n            var compressed = info.array.slice(inOffset.value, inOffset.value + dwaHeader.totalAcUncompressedCount)\n            var data = unzlibSync(compressed)\n            var acBuffer = new Uint16Array(data.buffer)\n            inOffset.value += dwaHeader.totalAcUncompressedCount\n            break\n        }\n      }\n\n      // Read DCT - DC component data\n      if (dwaHeader.dcCompressedSize > 0) {\n        var zlibInfo = {\n          array: info.array,\n          offset: inOffset,\n          size: dwaHeader.dcCompressedSize,\n        }\n        var dcBuffer = new Uint16Array(uncompressZIP(zlibInfo).buffer)\n        inOffset.value += dwaHeader.dcCompressedSize\n      }\n\n      // Read RLE compressed data\n      if (dwaHeader.rleRawSize > 0) {\n        var compressed = info.array.slice(inOffset.value, inOffset.value + dwaHeader.rleCompressedSize)\n        var data = unzlibSync(compressed)\n        var rleBuffer = decodeRunLength(data.buffer)\n\n        inOffset.value += dwaHeader.rleCompressedSize\n      }\n\n      // Prepare outbuffer data offset\n      var outBufferEnd = 0\n      var rowOffsets = new Array(channelData.length)\n      for (var i = 0; i < rowOffsets.length; ++i) {\n        rowOffsets[i] = new Array()\n      }\n\n      for (var y = 0; y < info.lines; ++y) {\n        for (var chan = 0; chan < channelData.length; ++chan) {\n          rowOffsets[chan].push(outBufferEnd)\n          outBufferEnd += channelData[chan].width * info.type * INT16_SIZE\n        }\n      }\n\n      // Lossy DCT decode RGB channels\n      lossyDctDecode(cscSet, rowOffsets, channelData, acBuffer, dcBuffer, outBuffer)\n\n      // Decode other channels\n      for (var i = 0; i < channelData.length; ++i) {\n        var cd = channelData[i]\n\n        if (cd.decoded) continue\n\n        switch (cd.compression) {\n          case RLE:\n            var row = 0\n            var rleOffset = 0\n\n            for (var y = 0; y < info.lines; ++y) {\n              var rowOffsetBytes = rowOffsets[i][row]\n\n              for (var x = 0; x < cd.width; ++x) {\n                for (var byte = 0; byte < INT16_SIZE * cd.type; ++byte) {\n                  outBuffer[rowOffsetBytes++] = rleBuffer[rleOffset + byte * cd.width * cd.height]\n                }\n\n                rleOffset++\n              }\n\n              row++\n            }\n\n            break\n\n          case LOSSY_DCT: // skip\n\n          default:\n            throw 'EXRLoader.parse: unsupported channel compression'\n        }\n      }\n\n      return new DataView(outBuffer.buffer)\n    }\n\n    function parseNullTerminatedString(buffer, offset) {\n      var uintBuffer = new Uint8Array(buffer)\n      var endOffset = 0\n\n      while (uintBuffer[offset.value + endOffset] != 0) {\n        endOffset += 1\n      }\n\n      var stringValue = new TextDecoder().decode(uintBuffer.slice(offset.value, offset.value + endOffset))\n\n      offset.value = offset.value + endOffset + 1\n\n      return stringValue\n    }\n\n    function parseFixedLengthString(buffer, offset, size) {\n      var stringValue = new TextDecoder().decode(new Uint8Array(buffer).slice(offset.value, offset.value + size))\n\n      offset.value = offset.value + size\n\n      return stringValue\n    }\n\n    function parseRational(dataView, offset) {\n      var x = parseInt32(dataView, offset)\n      var y = parseUint32(dataView, offset)\n\n      return [x, y]\n    }\n\n    function parseTimecode(dataView, offset) {\n      var x = parseUint32(dataView, offset)\n      var y = parseUint32(dataView, offset)\n\n      return [x, y]\n    }\n\n    function parseInt32(dataView, offset) {\n      var Int32 = dataView.getInt32(offset.value, true)\n\n      offset.value = offset.value + INT32_SIZE\n\n      return Int32\n    }\n\n    function parseUint32(dataView, offset) {\n      var Uint32 = dataView.getUint32(offset.value, true)\n\n      offset.value = offset.value + INT32_SIZE\n\n      return Uint32\n    }\n\n    function parseUint8Array(uInt8Array, offset) {\n      var Uint8 = uInt8Array[offset.value]\n\n      offset.value = offset.value + INT8_SIZE\n\n      return Uint8\n    }\n\n    function parseUint8(dataView, offset) {\n      var Uint8 = dataView.getUint8(offset.value)\n\n      offset.value = offset.value + INT8_SIZE\n\n      return Uint8\n    }\n\n    const parseInt64 = function (dataView, offset) {\n      let int\n\n      if ('getBigInt64' in DataView.prototype) {\n        int = Number(dataView.getBigInt64(offset.value, true))\n      } else {\n        int = dataView.getUint32(offset.value + 4, true) + Number(dataView.getUint32(offset.value, true) << 32)\n      }\n\n      offset.value += ULONG_SIZE\n\n      return int\n    }\n\n    function parseFloat32(dataView, offset) {\n      var float = dataView.getFloat32(offset.value, true)\n\n      offset.value += FLOAT32_SIZE\n\n      return float\n    }\n\n    function decodeFloat32(dataView, offset) {\n      return DataUtils.toHalfFloat(parseFloat32(dataView, offset))\n    }\n\n    // https://stackoverflow.com/questions/5678432/decompressing-half-precision-floats-in-javascript\n    function decodeFloat16(binary) {\n      var exponent = (binary & 0x7c00) >> 10,\n        fraction = binary & 0x03ff\n\n      return (\n        (binary >> 15 ? -1 : 1) *\n        (exponent\n          ? exponent === 0x1f\n            ? fraction\n              ? NaN\n              : Infinity\n            : Math.pow(2, exponent - 15) * (1 + fraction / 0x400)\n          : 6.103515625e-5 * (fraction / 0x400))\n      )\n    }\n\n    function parseUint16(dataView, offset) {\n      var Uint16 = dataView.getUint16(offset.value, true)\n\n      offset.value += INT16_SIZE\n\n      return Uint16\n    }\n\n    function parseFloat16(buffer, offset) {\n      return decodeFloat16(parseUint16(buffer, offset))\n    }\n\n    function parseChlist(dataView, buffer, offset, size) {\n      var startOffset = offset.value\n      var channels = []\n\n      while (offset.value < startOffset + size - 1) {\n        var name = parseNullTerminatedString(buffer, offset)\n        var pixelType = parseInt32(dataView, offset)\n        var pLinear = parseUint8(dataView, offset)\n        offset.value += 3 // reserved, three chars\n        var xSampling = parseInt32(dataView, offset)\n        var ySampling = parseInt32(dataView, offset)\n\n        channels.push({\n          name: name,\n          pixelType: pixelType,\n          pLinear: pLinear,\n          xSampling: xSampling,\n          ySampling: ySampling,\n        })\n      }\n\n      offset.value += 1\n\n      return channels\n    }\n\n    function parseChromaticities(dataView, offset) {\n      var redX = parseFloat32(dataView, offset)\n      var redY = parseFloat32(dataView, offset)\n      var greenX = parseFloat32(dataView, offset)\n      var greenY = parseFloat32(dataView, offset)\n      var blueX = parseFloat32(dataView, offset)\n      var blueY = parseFloat32(dataView, offset)\n      var whiteX = parseFloat32(dataView, offset)\n      var whiteY = parseFloat32(dataView, offset)\n\n      return {\n        redX: redX,\n        redY: redY,\n        greenX: greenX,\n        greenY: greenY,\n        blueX: blueX,\n        blueY: blueY,\n        whiteX: whiteX,\n        whiteY: whiteY,\n      }\n    }\n\n    function parseCompression(dataView, offset) {\n      var compressionCodes = [\n        'NO_COMPRESSION',\n        'RLE_COMPRESSION',\n        'ZIPS_COMPRESSION',\n        'ZIP_COMPRESSION',\n        'PIZ_COMPRESSION',\n        'PXR24_COMPRESSION',\n        'B44_COMPRESSION',\n        'B44A_COMPRESSION',\n        'DWAA_COMPRESSION',\n        'DWAB_COMPRESSION',\n      ]\n\n      var compression = parseUint8(dataView, offset)\n\n      return compressionCodes[compression]\n    }\n\n    function parseBox2i(dataView, offset) {\n      var xMin = parseUint32(dataView, offset)\n      var yMin = parseUint32(dataView, offset)\n      var xMax = parseUint32(dataView, offset)\n      var yMax = parseUint32(dataView, offset)\n\n      return { xMin: xMin, yMin: yMin, xMax: xMax, yMax: yMax }\n    }\n\n    function parseLineOrder(dataView, offset) {\n      var lineOrders = ['INCREASING_Y']\n\n      var lineOrder = parseUint8(dataView, offset)\n\n      return lineOrders[lineOrder]\n    }\n\n    function parseV2f(dataView, offset) {\n      var x = parseFloat32(dataView, offset)\n      var y = parseFloat32(dataView, offset)\n\n      return [x, y]\n    }\n\n    function parseV3f(dataView, offset) {\n      var x = parseFloat32(dataView, offset)\n      var y = parseFloat32(dataView, offset)\n      var z = parseFloat32(dataView, offset)\n\n      return [x, y, z]\n    }\n\n    function parseValue(dataView, buffer, offset, type, size) {\n      if (type === 'string' || type === 'stringvector' || type === 'iccProfile') {\n        return parseFixedLengthString(buffer, offset, size)\n      } else if (type === 'chlist') {\n        return parseChlist(dataView, buffer, offset, size)\n      } else if (type === 'chromaticities') {\n        return parseChromaticities(dataView, offset)\n      } else if (type === 'compression') {\n        return parseCompression(dataView, offset)\n      } else if (type === 'box2i') {\n        return parseBox2i(dataView, offset)\n      } else if (type === 'lineOrder') {\n        return parseLineOrder(dataView, offset)\n      } else if (type === 'float') {\n        return parseFloat32(dataView, offset)\n      } else if (type === 'v2f') {\n        return parseV2f(dataView, offset)\n      } else if (type === 'v3f') {\n        return parseV3f(dataView, offset)\n      } else if (type === 'int') {\n        return parseInt32(dataView, offset)\n      } else if (type === 'rational') {\n        return parseRational(dataView, offset)\n      } else if (type === 'timecode') {\n        return parseTimecode(dataView, offset)\n      } else if (type === 'preview') {\n        offset.value += size\n        return 'skipped'\n      } else {\n        offset.value += size\n        return undefined\n      }\n    }\n\n    function parseHeader(dataView, buffer, offset) {\n      const EXRHeader = {}\n\n      if (dataView.getUint32(0, true) != 20000630) {\n        // magic\n        throw \"THREE.EXRLoader: provided file doesn't appear to be in OpenEXR format.\"\n      }\n\n      EXRHeader.version = dataView.getUint8(4)\n\n      const spec = dataView.getUint8(5) // fullMask\n\n      EXRHeader.spec = {\n        singleTile: !!(spec & 2),\n        longName: !!(spec & 4),\n        deepFormat: !!(spec & 8),\n        multiPart: !!(spec & 16),\n      }\n\n      // start of header\n\n      offset.value = 8 // start at 8 - after pre-amble\n\n      var keepReading = true\n\n      while (keepReading) {\n        var attributeName = parseNullTerminatedString(buffer, offset)\n\n        if (attributeName == 0) {\n          keepReading = false\n        } else {\n          var attributeType = parseNullTerminatedString(buffer, offset)\n          var attributeSize = parseUint32(dataView, offset)\n          var attributeValue = parseValue(dataView, buffer, offset, attributeType, attributeSize)\n\n          if (attributeValue === undefined) {\n            console.warn(`EXRLoader.parse: skipped unknown header attribute type \\'${attributeType}\\'.`)\n          } else {\n            EXRHeader[attributeName] = attributeValue\n          }\n        }\n      }\n\n      if ((spec & ~0x04) != 0) {\n        // unsupported tiled, deep-image, multi-part\n        console.error('EXRHeader:', EXRHeader)\n        throw 'THREE.EXRLoader: provided file is currently unsupported.'\n      }\n\n      return EXRHeader\n    }\n\n    function setupDecoder(EXRHeader, dataView, uInt8Array, offset, outputType) {\n      const EXRDecoder = {\n        size: 0,\n        viewer: dataView,\n        array: uInt8Array,\n        offset: offset,\n        width: EXRHeader.dataWindow.xMax - EXRHeader.dataWindow.xMin + 1,\n        height: EXRHeader.dataWindow.yMax - EXRHeader.dataWindow.yMin + 1,\n        channels: EXRHeader.channels.length,\n        bytesPerLine: null,\n        lines: null,\n        inputSize: null,\n        type: EXRHeader.channels[0].pixelType,\n        uncompress: null,\n        getter: null,\n        format: null,\n        [hasColorSpace ? 'colorSpace' : 'encoding']: null,\n      }\n\n      switch (EXRHeader.compression) {\n        case 'NO_COMPRESSION':\n          EXRDecoder.lines = 1\n          EXRDecoder.uncompress = uncompressRAW\n          break\n\n        case 'RLE_COMPRESSION':\n          EXRDecoder.lines = 1\n          EXRDecoder.uncompress = uncompressRLE\n          break\n\n        case 'ZIPS_COMPRESSION':\n          EXRDecoder.lines = 1\n          EXRDecoder.uncompress = uncompressZIP\n          break\n\n        case 'ZIP_COMPRESSION':\n          EXRDecoder.lines = 16\n          EXRDecoder.uncompress = uncompressZIP\n          break\n\n        case 'PIZ_COMPRESSION':\n          EXRDecoder.lines = 32\n          EXRDecoder.uncompress = uncompressPIZ\n          break\n\n        case 'PXR24_COMPRESSION':\n          EXRDecoder.lines = 16\n          EXRDecoder.uncompress = uncompressPXR\n          break\n\n        case 'DWAA_COMPRESSION':\n          EXRDecoder.lines = 32\n          EXRDecoder.uncompress = uncompressDWA\n          break\n\n        case 'DWAB_COMPRESSION':\n          EXRDecoder.lines = 256\n          EXRDecoder.uncompress = uncompressDWA\n          break\n\n        default:\n          throw 'EXRLoader.parse: ' + EXRHeader.compression + ' is unsupported'\n      }\n\n      EXRDecoder.scanlineBlockSize = EXRDecoder.lines\n\n      if (EXRDecoder.type == 1) {\n        // half\n        switch (outputType) {\n          case FloatType:\n            EXRDecoder.getter = parseFloat16\n            EXRDecoder.inputSize = INT16_SIZE\n            break\n\n          case HalfFloatType:\n            EXRDecoder.getter = parseUint16\n            EXRDecoder.inputSize = INT16_SIZE\n            break\n        }\n      } else if (EXRDecoder.type == 2) {\n        // float\n        switch (outputType) {\n          case FloatType:\n            EXRDecoder.getter = parseFloat32\n            EXRDecoder.inputSize = FLOAT32_SIZE\n            break\n\n          case HalfFloatType:\n            EXRDecoder.getter = decodeFloat32\n            EXRDecoder.inputSize = FLOAT32_SIZE\n        }\n      } else {\n        throw 'EXRLoader.parse: unsupported pixelType ' + EXRDecoder.type + ' for ' + EXRHeader.compression + '.'\n      }\n\n      EXRDecoder.blockCount = (EXRHeader.dataWindow.yMax + 1) / EXRDecoder.scanlineBlockSize\n\n      for (var i = 0; i < EXRDecoder.blockCount; i++) parseInt64(dataView, offset) // scanlineOffset\n\n      // we should be passed the scanline offset table, ready to start reading pixel data.\n\n      // RGB images will be converted to RGBA format, preventing software emulation in select devices.\n      EXRDecoder.outputChannels = EXRDecoder.channels == 3 ? 4 : EXRDecoder.channels\n      const size = EXRDecoder.width * EXRDecoder.height * EXRDecoder.outputChannels\n\n      switch (outputType) {\n        case FloatType:\n          EXRDecoder.byteArray = new Float32Array(size)\n\n          // Fill initially with 1s for the alpha value if the texture is not RGBA, RGB values will be overwritten\n          if (EXRDecoder.channels < EXRDecoder.outputChannels) EXRDecoder.byteArray.fill(1, 0, size)\n\n          break\n\n        case HalfFloatType:\n          EXRDecoder.byteArray = new Uint16Array(size)\n\n          if (EXRDecoder.channels < EXRDecoder.outputChannels) EXRDecoder.byteArray.fill(0x3c00, 0, size) // Uint16Array holds half float data, 0x3C00 is 1\n\n          break\n\n        default:\n          console.error('THREE.EXRLoader: unsupported type: ', outputType)\n          break\n      }\n\n      EXRDecoder.bytesPerLine = EXRDecoder.width * EXRDecoder.inputSize * EXRDecoder.channels\n\n      if (EXRDecoder.outputChannels == 4) EXRDecoder.format = RGBAFormat\n      else EXRDecoder.format = RedFormat\n\n      if (hasColorSpace) EXRDecoder.colorSpace = 'srgb-linear'\n      else EXRDecoder.encoding = 3000 // LinearEncoding\n\n      return EXRDecoder\n    }\n\n    // start parsing file [START]\n\n    const bufferDataView = new DataView(buffer)\n    const uInt8Array = new Uint8Array(buffer)\n    const offset = { value: 0 }\n\n    // get header information and validate format.\n    const EXRHeader = parseHeader(bufferDataView, buffer, offset)\n\n    // get input compression information and prepare decoding.\n    const EXRDecoder = setupDecoder(EXRHeader, bufferDataView, uInt8Array, offset, this.type)\n\n    const tmpOffset = { value: 0 }\n    const channelOffsets = { R: 0, G: 1, B: 2, A: 3, Y: 0 }\n\n    for (\n      let scanlineBlockIdx = 0;\n      scanlineBlockIdx < EXRDecoder.height / EXRDecoder.scanlineBlockSize;\n      scanlineBlockIdx++\n    ) {\n      const line = parseUint32(bufferDataView, offset) // line_no\n      EXRDecoder.size = parseUint32(bufferDataView, offset) // data_len\n      EXRDecoder.lines =\n        line + EXRDecoder.scanlineBlockSize > EXRDecoder.height\n          ? EXRDecoder.height - line\n          : EXRDecoder.scanlineBlockSize\n\n      const isCompressed = EXRDecoder.size < EXRDecoder.lines * EXRDecoder.bytesPerLine\n      const viewer = isCompressed ? EXRDecoder.uncompress(EXRDecoder) : uncompressRAW(EXRDecoder)\n\n      offset.value += EXRDecoder.size\n\n      for (let line_y = 0; line_y < EXRDecoder.scanlineBlockSize; line_y++) {\n        const true_y = line_y + scanlineBlockIdx * EXRDecoder.scanlineBlockSize\n        if (true_y >= EXRDecoder.height) break\n\n        for (let channelID = 0; channelID < EXRDecoder.channels; channelID++) {\n          const cOff = channelOffsets[EXRHeader.channels[channelID].name]\n\n          for (let x = 0; x < EXRDecoder.width; x++) {\n            tmpOffset.value =\n              (line_y * (EXRDecoder.channels * EXRDecoder.width) + channelID * EXRDecoder.width + x) *\n              EXRDecoder.inputSize\n            const outIndex =\n              (EXRDecoder.height - 1 - true_y) * (EXRDecoder.width * EXRDecoder.outputChannels) +\n              x * EXRDecoder.outputChannels +\n              cOff\n            EXRDecoder.byteArray[outIndex] = EXRDecoder.getter(viewer, tmpOffset)\n          }\n        }\n      }\n    }\n\n    return {\n      header: EXRHeader,\n      width: EXRDecoder.width,\n      height: EXRDecoder.height,\n      data: EXRDecoder.byteArray,\n      format: EXRDecoder.format,\n      [hasColorSpace ? 'colorSpace' : 'encoding']: EXRDecoder[hasColorSpace ? 'colorSpace' : 'encoding'],\n      type: this.type,\n    }\n  }\n\n  setDataType(value) {\n    this.type = value\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      if (hasColorSpace) texture.colorSpace = texData.colorSpace\n      else texture.encoding = texData.encoding\n      texture.minFilter = LinearFilter\n      texture.magFilter = LinearFilter\n      texture.generateMipmaps = false\n      texture.flipY = false\n\n      if (onLoad) onLoad(texture, texData)\n    }\n\n    return super.load(url, onLoadCallback, onProgress, onError)\n  }\n}\n\nexport { EXRLoader }\n"], "names": ["uInt8Array", "buffer", "comp", "offset", "type", "y", "x", "tmpOffset", "EXRHeader", "EXRDecoder"], "mappings": ";;;;;;;;;AAuFA,MAAM,gBAAgB,uKAAA,IAAW;AAEjC,MAAM,qKAAkB,oBAAA,CAAkB;IACxC,YAAY,OAAA,CAAS;QACnB,KAAA,CAAM,OAAO;QAEb,IAAA,CAAK,IAAA,sJAAO,gBAAA;IACb;IAED,MAAM,MAAA,EAAQ;QACZ,MAAM,eAAe,KAAK;QAC1B,MAAM,cAAc,gBAAgB;QAEpC,MAAM,cAAc;QACpB,MAAM,cAAc;QAEpB,MAAM,cAAA,CAAe,KAAK,WAAA,IAAe;QACzC,MAAM,cAAc,KAAK;QACzB,MAAM,cAAc,cAAc;QAElC,MAAM,QAAQ;QACd,MAAM,WAAW,KAAM,QAAQ;QAC/B,MAAM,WAAA,CAAY,KAAK,KAAA,IAAS;QAEhC,MAAM,qBAAqB;QAC3B,MAAM,oBAAoB;QAC1B,MAAM,oBAAoB,IAAI,oBAAoB;QAElD,MAAM,aAAa;QACnB,MAAM,eAAe;QACrB,MAAM,aAAa;QACnB,MAAM,aAAa;QACnB,MAAM,YAAY;QAElB,MAAM,iBAAiB;QACvB,MAAM,UAAU;QAEhB,MAAM,UAAU;QAChB,MAAM,YAAY;QAClB,MAAM,MAAM;QAEZ,MAAM,UAAU,KAAK,GAAA,CAAI,WAAW,GAAG;QAEvC,SAAS,qBAAqB,MAAA,EAAQ,GAAA,EAAK;YACzC,IAAI,IAAI;YAER,IAAA,IAAS,IAAI,GAAG,IAAI,cAAc,EAAE,EAAG;gBACrC,IAAI,KAAK,KAAK,MAAA,CAAO,KAAK,CAAC,CAAA,GAAK,KAAA,CAAM,IAAI,CAAA,GAAK;oBAC7C,GAAA,CAAI,GAAG,CAAA,GAAI;gBACZ;YACF;YAED,IAAI,IAAI,IAAI;YAEZ,MAAO,IAAI,aAAc,GAAA,CAAI,GAAG,CAAA,GAAI;YAEpC,OAAO;QACR;QAED,SAAS,iBAAiB,IAAA,EAAM;YAC9B,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,IAAK;gBACpC,IAAA,CAAK,CAAC,CAAA,GAAI,CAAE;gBACZ,IAAA,CAAK,CAAC,CAAA,CAAE,GAAA,GAAM;gBACd,IAAA,CAAK,CAAC,CAAA,CAAE,GAAA,GAAM;gBACd,IAAA,CAAK,CAAC,CAAA,CAAE,CAAA,GAAI;YACb;QACF;QAED,MAAM,gBAAgB;YAAE,GAAG;YAAG,GAAG;YAAG,IAAI;QAAG;QAE3C,SAAS,QAAQ,KAAA,EAAO,CAAA,EAAG,EAAA,EAAIA,WAAAA,EAAY,QAAA,EAAU;YACnD,MAAO,KAAK,MAAO;gBACjB,IAAK,KAAK,IAAK,gBAAgBA,aAAY,QAAQ;gBACnD,MAAM;YACP;YAED,MAAM;YAEN,cAAc,CAAA,GAAK,KAAK,KAAA,CAAQ,KAAK,KAAA,IAAS;YAC9C,cAAc,CAAA,GAAI;YAClB,cAAc,EAAA,GAAK;QACpB;QAED,MAAM,iBAAiB,IAAI,MAAM,EAAE;QAEnC,SAAS,sBAAsB,KAAA,EAAO;YACpC,IAAA,IAAS,IAAI,GAAG,KAAK,IAAI,EAAE,EAAG,cAAA,CAAe,CAAC,CAAA,GAAI;YAClD,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,EAAE,EAAG,cAAA,CAAe,KAAA,CAAM,CAAC,CAAC,CAAA,IAAK;YAElE,IAAI,IAAI;YAER,IAAA,IAAS,IAAI,IAAI,IAAI,GAAG,EAAE,EAAG;gBAC3B,IAAI,KAAM,IAAI,cAAA,CAAe,CAAC,CAAA,IAAM;gBACpC,cAAA,CAAe,CAAC,CAAA,GAAI;gBACpB,IAAI;YACL;YAED,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,EAAE,EAAG;gBACpC,IAAI,IAAI,KAAA,CAAM,CAAC,CAAA;gBACf,IAAI,IAAI,GAAG,KAAA,CAAM,CAAC,CAAA,GAAI,IAAK,cAAA,CAAe,CAAC,CAAA,MAAO;YACnD;QACF;QAED,SAAS,kBAAkBA,WAAAA,EAAY,UAAA,EAAY,QAAA,EAAU,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,KAAA,EAAO;YAC9E,IAAI,IAAI;YACR,IAAI,IAAI;YACR,IAAI,KAAK;YAET,MAAO,MAAM,IAAI,KAAM;gBACrB,IAAI,EAAE,KAAA,GAAQ,SAAS,KAAA,GAAQ,IAAI,OAAO;gBAE1C,QAAQ,GAAG,GAAG,IAAIA,aAAY,CAAC;gBAE/B,IAAI,IAAI,cAAc,CAAA;gBACtB,IAAI,cAAc,CAAA;gBAClB,KAAK,cAAc,EAAA;gBAEnB,KAAA,CAAM,EAAE,CAAA,GAAI;gBAEZ,IAAI,KAAK,mBAAmB;oBAC1B,IAAI,EAAE,KAAA,GAAQ,SAAS,KAAA,GAAQ,IAAI;wBACjC,MAAM;oBACP;oBAED,QAAQ,GAAG,GAAG,IAAIA,aAAY,CAAC;oBAE/B,IAAI,QAAQ,cAAc,CAAA,GAAI;oBAC9B,IAAI,cAAc,CAAA;oBAClB,KAAK,cAAc,EAAA;oBAEnB,IAAI,KAAK,QAAQ,KAAK,GAAG;wBACvB,MAAM;oBACP;oBAED,MAAO,QAAS,KAAA,CAAM,IAAI,CAAA,GAAI;oBAE9B;gBACV,OAAA,IAAmB,KAAK,oBAAoB;oBAClC,IAAI,QAAQ,IAAI,qBAAqB;oBAErC,IAAI,KAAK,QAAQ,KAAK,GAAG;wBACvB,MAAM;oBACP;oBAED,MAAO,QAAS,KAAA,CAAM,IAAI,CAAA,GAAI;oBAE9B;gBACD;YACF;YAED,sBAAsB,KAAK;QAC5B;QAED,SAAS,UAAU,IAAA,EAAM;YACvB,OAAO,OAAO;QACf;QAED,SAAS,QAAQ,IAAA,EAAM;YACrB,OAAO,QAAQ;QAChB;QAED,SAAS,iBAAiB,KAAA,EAAO,EAAA,EAAI,EAAA,EAAI,MAAA,EAAQ;YAC/C,MAAO,MAAM,IAAI,KAAM;gBACrB,IAAI,IAAI,QAAQ,KAAA,CAAM,EAAE,CAAC;gBACzB,IAAI,IAAI,UAAU,KAAA,CAAM,EAAE,CAAC;gBAE3B,IAAI,KAAK,GAAG;oBACV,MAAM;gBACP;gBAED,IAAI,IAAI,aAAa;oBACnB,IAAI,KAAK,MAAA,CAAO,KAAM,IAAI,WAAY,CAAA;oBAEtC,IAAI,GAAG,GAAA,EAAK;wBACV,MAAM;oBACP;oBAED,GAAG,GAAA;oBAEH,IAAI,GAAG,CAAA,EAAG;wBACR,IAAI,IAAI,GAAG,CAAA;wBACX,GAAG,CAAA,GAAI,IAAI,MAAM,GAAG,GAAG;wBAEvB,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,GAAA,GAAM,GAAG,EAAE,EAAG;4BACnC,GAAG,CAAA,CAAE,CAAC,CAAA,GAAI,CAAA,CAAE,CAAC,CAAA;wBACd;oBACb,OAAiB;wBACL,GAAG,CAAA,GAAI,IAAI,MAAM,CAAC;oBACnB;oBAED,GAAG,CAAA,CAAE,GAAG,GAAA,GAAM,CAAC,CAAA,GAAI;gBACpB,OAAA,IAAU,GAAG;oBACZ,IAAI,WAAW;oBAEf,IAAA,IAAS,IAAI,KAAM,cAAc,GAAI,IAAI,GAAG,IAAK;wBAC/C,IAAI,KAAK,MAAA,CAAA,CAAQ,KAAM,cAAc,CAAA,IAAM,QAAQ,CAAA;wBAEnD,IAAI,GAAG,GAAA,IAAO,GAAG,CAAA,EAAG;4BAClB,MAAM;wBACP;wBAED,GAAG,GAAA,GAAM;wBACT,GAAG,GAAA,GAAM;wBAET;oBACD;gBACF;YACF;YAED,OAAO;QACR;QAED,MAAM,gBAAgB;YAAE,GAAG;YAAG,IAAI;QAAG;QAErC,SAAS,QAAQ,CAAA,EAAG,EAAA,EAAIA,WAAAA,EAAY,QAAA,EAAU;YAC5C,IAAK,KAAK,IAAK,gBAAgBA,aAAY,QAAQ;YACnD,MAAM;YAEN,cAAc,CAAA,GAAI;YAClB,cAAc,EAAA,GAAK;QACpB;QAED,MAAM,gBAAgB;YAAE,GAAG;YAAG,IAAI;QAAG;QAErC,SAAS,QAAQ,EAAA,EAAI,GAAA,EAAK,CAAA,EAAG,EAAA,EAAIA,WAAAA,EAAY,UAAA,EAAY,QAAA,EAAU,SAAA,EAAW,eAAA,EAAiB,kBAAA,EAAoB;YACjH,IAAI,MAAM,KAAK;gBACb,IAAI,KAAK,GAAG;oBACV,QAAQ,GAAG,IAAIA,aAAY,QAAQ;oBACnC,IAAI,cAAc,CAAA;oBAClB,KAAK,cAAc,EAAA;gBACpB;gBAED,MAAM;gBAEN,IAAI,KAAK,KAAK;gBACd,IAAI,KAAK,IAAI,WAAW;oBAAC,EAAE;iBAAC,CAAA,CAAE,CAAC,CAAA;gBAE/B,IAAI,gBAAgB,KAAA,GAAQ,KAAK,oBAAoB;oBACnD,OAAO;gBACR;gBAED,IAAI,IAAI,SAAA,CAAU,gBAAgB,KAAA,GAAQ,CAAC,CAAA;gBAE3C,MAAO,OAAO,EAAG;oBACf,SAAA,CAAU,gBAAgB,KAAA,EAAO,CAAA,GAAI;gBACtC;YACT,OAAA,IAAiB,gBAAgB,KAAA,GAAQ,oBAAoB;gBACrD,SAAA,CAAU,gBAAgB,KAAA,EAAO,CAAA,GAAI;YAC7C,OAAa;gBACL,OAAO;YACR;YAED,cAAc,CAAA,GAAI;YAClB,cAAc,EAAA,GAAK;QACpB;QAED,SAAS,OAAO,KAAA,EAAO;YACrB,OAAO,QAAQ;QAChB;QAED,SAAS,MAAM,KAAA,EAAO;YACpB,IAAI,MAAM,OAAO,KAAK;YACtB,OAAO,MAAM,QAAS,MAAM,QAAU;QACvC;QAED,MAAM,eAAe;YAAE,GAAG;YAAG,GAAG;QAAG;QAEnC,SAAS,OAAO,CAAA,EAAG,CAAA,EAAG;YACpB,IAAI,KAAK,MAAM,CAAC;YAChB,IAAI,KAAK,MAAM,CAAC;YAEhB,IAAI,KAAK;YACT,IAAI,KAAK,KAAA,CAAM,KAAK,CAAA,IAAA,CAAM,MAAM,CAAA;YAEhC,IAAI,KAAK;YACT,IAAI,KAAK,KAAK;YAEd,aAAa,CAAA,GAAI;YACjB,aAAa,CAAA,GAAI;QAClB;QAED,SAAS,OAAO,CAAA,EAAG,CAAA,EAAG;YACpB,IAAI,IAAI,OAAO,CAAC;YAChB,IAAI,IAAI,OAAO,CAAC;YAEhB,IAAI,KAAM,IAAA,CAAK,KAAK,CAAA,IAAM;YAC1B,IAAI,KAAM,IAAI,KAAK,WAAY;YAE/B,aAAa,CAAA,GAAI;YACjB,aAAa,CAAA,GAAI;QAClB;QAED,SAAS,WAAWC,OAAAA,EAAQ,CAAA,EAAG,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI;YACjD,IAAI,MAAM,KAAK,KAAK;YACpB,IAAI,IAAI,KAAK,KAAK,KAAK;YACvB,IAAI,IAAI;YACR,IAAI;YAEJ,MAAO,KAAK,EAAG,MAAM;YAErB,MAAM;YACN,KAAK;YACL,MAAM;YAEN,MAAO,KAAK,EAAG;gBACb,IAAI,KAAK;gBACT,IAAI,KAAK,KAAK,KAAA,CAAM,KAAK,EAAA;gBACzB,IAAI,MAAM,KAAK;gBACf,IAAI,MAAM,KAAK;gBACf,IAAI,MAAM,KAAK;gBACf,IAAI,MAAM,KAAK;gBACf,IAAI,KAAK,KAAK,KAAK;gBAEnB,MAAO,MAAM,IAAI,MAAM,IAAK;oBAC1B,IAAI,KAAK;oBACT,IAAI,KAAK,KAAK,KAAA,CAAM,KAAK,EAAA;oBAEzB,MAAO,MAAM,IAAI,MAAM,IAAK;wBAC1B,IAAI,MAAM,KAAK;wBACf,IAAI,MAAM,KAAK;wBACf,IAAI,MAAM,MAAM;wBAEhB,IAAI,KAAK;4BACP,OAAOA,OAAAA,CAAO,KAAK,CAAC,CAAA,EAAGA,OAAAA,CAAO,MAAM,CAAC,CAAC;4BAEtC,MAAM,aAAa,CAAA;4BACnB,MAAM,aAAa,CAAA;4BAEnB,OAAOA,OAAAA,CAAO,MAAM,CAAC,CAAA,EAAGA,OAAAA,CAAO,MAAM,CAAC,CAAC;4BAEvC,MAAM,aAAa,CAAA;4BACnB,MAAM,aAAa,CAAA;4BAEnB,OAAO,KAAK,GAAG;4BAEfA,OAAAA,CAAO,KAAK,CAAC,CAAA,GAAI,aAAa,CAAA;4BAC9BA,OAAAA,CAAO,MAAM,CAAC,CAAA,GAAI,aAAa,CAAA;4BAE/B,OAAO,KAAK,GAAG;4BAEfA,OAAAA,CAAO,MAAM,CAAC,CAAA,GAAI,aAAa,CAAA;4BAC/BA,OAAAA,CAAO,MAAM,CAAC,CAAA,GAAI,aAAa,CAAA;wBAC7C,OAAmB;4BACL,OAAOA,OAAAA,CAAO,KAAK,CAAC,CAAA,EAAGA,OAAAA,CAAO,MAAM,CAAC,CAAC;4BAEtC,MAAM,aAAa,CAAA;4BACnB,MAAM,aAAa,CAAA;4BAEnB,OAAOA,OAAAA,CAAO,MAAM,CAAC,CAAA,EAAGA,OAAAA,CAAO,MAAM,CAAC,CAAC;4BAEvC,MAAM,aAAa,CAAA;4BACnB,MAAM,aAAa,CAAA;4BAEnB,OAAO,KAAK,GAAG;4BAEfA,OAAAA,CAAO,KAAK,CAAC,CAAA,GAAI,aAAa,CAAA;4BAC9BA,OAAAA,CAAO,MAAM,CAAC,CAAA,GAAI,aAAa,CAAA;4BAE/B,OAAO,KAAK,GAAG;4BAEfA,OAAAA,CAAO,MAAM,CAAC,CAAA,GAAI,aAAa,CAAA;4BAC/BA,OAAAA,CAAO,MAAM,CAAC,CAAA,GAAI,aAAa,CAAA;wBAChC;oBACF;oBAED,IAAI,KAAK,GAAG;wBACV,IAAI,MAAM,KAAK;wBAEf,IAAI,KAAK,OAAOA,OAAAA,CAAO,KAAK,CAAC,CAAA,EAAGA,OAAAA,CAAO,MAAM,CAAC,CAAC;6BAC1C,OAAOA,OAAAA,CAAO,KAAK,CAAC,CAAA,EAAGA,OAAAA,CAAO,MAAM,CAAC,CAAC;wBAE3C,MAAM,aAAa,CAAA;wBACnBA,OAAAA,CAAO,MAAM,CAAC,CAAA,GAAI,aAAa,CAAA;wBAE/BA,OAAAA,CAAO,KAAK,CAAC,CAAA,GAAI;oBAClB;gBACF;gBAED,IAAI,KAAK,GAAG;oBACV,IAAI,KAAK;oBACT,IAAI,KAAK,KAAK,KAAA,CAAM,KAAK,EAAA;oBAEzB,MAAO,MAAM,IAAI,MAAM,IAAK;wBAC1B,IAAI,MAAM,KAAK;wBAEf,IAAI,KAAK,OAAOA,OAAAA,CAAO,KAAK,CAAC,CAAA,EAAGA,OAAAA,CAAO,MAAM,CAAC,CAAC;6BAC1C,OAAOA,OAAAA,CAAO,KAAK,CAAC,CAAA,EAAGA,OAAAA,CAAO,MAAM,CAAC,CAAC;wBAE3C,MAAM,aAAa,CAAA;wBACnBA,OAAAA,CAAO,MAAM,CAAC,CAAA,GAAI,aAAa,CAAA;wBAE/BA,OAAAA,CAAO,KAAK,CAAC,CAAA,GAAI;oBAClB;gBACF;gBAED,KAAK;gBACL,MAAM;YACP;YAED,OAAO;QACR;QAED,SAAS,UACP,aAAA,EACA,aAAA,EACAD,WAAAA,EACA,UAAA,EACA,QAAA,EACA,EAAA,EACA,GAAA,EACA,EAAA,EACA,SAAA,EACA,SAAA,EACA;YACA,IAAI,IAAI;YACR,IAAI,KAAK;YACT,IAAI,qBAAqB;YACzB,IAAI,cAAc,KAAK,KAAA,CAAM,SAAS,KAAA,GAAA,CAAS,KAAK,CAAA,IAAK,CAAC;YAE1D,MAAO,SAAS,KAAA,GAAQ,YAAa;gBACnC,QAAQ,GAAG,IAAIA,aAAY,QAAQ;gBAEnC,IAAI,cAAc,CAAA;gBAClB,KAAK,cAAc,EAAA;gBAEnB,MAAO,MAAM,YAAa;oBACxB,IAAI,QAAS,KAAM,KAAK,cAAgB;oBACxC,IAAI,KAAK,aAAA,CAAc,KAAK,CAAA;oBAE5B,IAAI,GAAG,GAAA,EAAK;wBACV,MAAM,GAAG,GAAA;wBAET,QAAQ,GAAG,GAAA,EAAK,KAAK,GAAG,IAAIA,aAAY,YAAY,UAAU,WAAW,WAAW,kBAAkB;wBAEtG,IAAI,cAAc,CAAA;wBAClB,KAAK,cAAc,EAAA;oBAC/B,OAAiB;wBACL,IAAI,CAAC,GAAG,CAAA,EAAG;4BACT,MAAM;wBACP;wBAED,IAAI;wBAEJ,IAAK,IAAI,GAAG,IAAI,GAAG,GAAA,EAAK,IAAK;4BAC3B,IAAI,IAAI,UAAU,aAAA,CAAc,GAAG,CAAA,CAAE,CAAC,CAAC,CAAC;4BAExC,MAAO,KAAK,KAAK,SAAS,KAAA,GAAQ,YAAa;gCAC7C,QAAQ,GAAG,IAAIA,aAAY,QAAQ;gCAEnC,IAAI,cAAc,CAAA;gCAClB,KAAK,cAAc,EAAA;4BACpB;4BAED,IAAI,MAAM,GAAG;gCACX,IAAI,QAAQ,aAAA,CAAc,GAAG,CAAA,CAAE,CAAC,CAAC,CAAC,KAAA,CAAO,KAAM,KAAK,IAAA,CAAQ,KAAK,CAAA,IAAK,CAAA,GAAK;oCACzE,MAAM;oCAEN,QACE,GAAG,CAAA,CAAE,CAAC,CAAA,EACN,KACA,GACA,IACAA,aACA,YACA,UACA,WACA,WACA;oCAGF,IAAI,cAAc,CAAA;oCAClB,KAAK,cAAc,EAAA;oCAEnB;gCACD;4BACF;wBACF;wBAED,IAAI,KAAK,GAAG,GAAA,EAAK;4BACf,MAAM;wBACP;oBACF;gBACF;YACF;YAED,IAAI,IAAK,IAAI,KAAM;YAEnB,MAAM;YACN,MAAM;YAEN,MAAO,KAAK,EAAG;gBACb,IAAI,KAAK,aAAA,CAAe,KAAM,cAAc,KAAO,WAAW,CAAA;gBAE9D,IAAI,GAAG,GAAA,EAAK;oBACV,MAAM,GAAG,GAAA;oBAET,QAAQ,GAAG,GAAA,EAAK,KAAK,GAAG,IAAIA,aAAY,YAAY,UAAU,WAAW,WAAW,kBAAkB;oBAEtG,IAAI,cAAc,CAAA;oBAClB,KAAK,cAAc,EAAA;gBAC7B,OAAe;oBACL,MAAM;gBACP;YACF;YAED,OAAO;QACR;QAED,SAAS,cAAcA,WAAAA,EAAY,UAAA,EAAY,QAAA,EAAU,WAAA,EAAa,SAAA,EAAW,IAAA,EAAM;YACrF,IAAI,YAAY;gBAAE,OAAO;YAAG;YAC5B,IAAI,kBAAkB,SAAS,KAAA;YAE/B,IAAI,KAAK,YAAY,YAAY,QAAQ;YACzC,IAAI,KAAK,YAAY,YAAY,QAAQ;YAEzC,SAAS,KAAA,IAAS;YAElB,IAAI,QAAQ,YAAY,YAAY,QAAQ;YAE5C,SAAS,KAAA,IAAS;YAElB,IAAI,KAAK,KAAK,MAAM,eAAe,KAAK,KAAK,MAAM,aAAa;gBAC9D,MAAM;YACP;YAED,IAAI,OAAO,IAAI,MAAM,WAAW;YAChC,IAAI,OAAO,IAAI,MAAM,WAAW;YAEhC,iBAAiB,IAAI;YAErB,IAAI,KAAK,cAAA,CAAe,SAAS,KAAA,GAAQ,eAAA;YAEzC,kBAAkBA,aAAY,YAAY,UAAU,IAAI,IAAI,IAAI,IAAI;YAEpE,IAAI,QAAQ,IAAA,CAAK,cAAA,CAAe,SAAS,KAAA,GAAQ,eAAA,CAAA,GAAmB;gBAClE,MAAM;YACP;YAED,iBAAiB,MAAM,IAAI,IAAI,IAAI;YAEnC,UAAU,MAAM,MAAMA,aAAY,YAAY,UAAU,OAAO,IAAI,MAAM,WAAW,SAAS;QAC9F;QAED,SAAS,SAAS,GAAA,EAAK,IAAA,EAAM,KAAA,EAAO;YAClC,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;gBAC9B,IAAA,CAAK,CAAC,CAAA,GAAI,GAAA,CAAI,IAAA,CAAK,CAAC,CAAC,CAAA;YACtB;QACF;QAED,SAAS,UAAU,MAAA,EAAQ;YACzB,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,EAAQ,IAAK;gBACtC,IAAI,IAAI,MAAA,CAAO,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,CAAC,CAAA,GAAI;gBACpC,MAAA,CAAO,CAAC,CAAA,GAAI;YACb;QACF;QAED,SAAS,iBAAiB,MAAA,EAAQ,GAAA,EAAK;YACrC,IAAI,KAAK;YACT,IAAI,KAAK,KAAK,KAAA,CAAA,CAAO,OAAO,MAAA,GAAS,CAAA,IAAK,CAAC;YAC3C,IAAI,IAAI;YACR,IAAI,OAAO,OAAO,MAAA,GAAS;YAE3B,MAAO,KAAM;gBACX,IAAI,IAAI,MAAM;gBACd,GAAA,CAAI,GAAG,CAAA,GAAI,MAAA,CAAO,IAAI,CAAA;gBAEtB,IAAI,IAAI,MAAM;gBACd,GAAA,CAAI,GAAG,CAAA,GAAI,MAAA,CAAO,IAAI,CAAA;YACvB;QACF;QAED,SAAS,gBAAgB,MAAA,EAAQ;YAC/B,IAAI,OAAO,OAAO,UAAA;YAClB,IAAI,MAAM,IAAI,MAAO;YACrB,IAAI,IAAI;YAER,IAAI,SAAS,IAAI,SAAS,MAAM;YAEhC,MAAO,OAAO,EAAG;gBACf,IAAI,IAAI,OAAO,OAAA,CAAQ,GAAG;gBAE1B,IAAI,IAAI,GAAG;oBACT,IAAI,QAAQ,CAAC;oBACb,QAAQ,QAAQ;oBAEhB,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;wBAC9B,IAAI,IAAA,CAAK,OAAO,QAAA,CAAS,GAAG,CAAC;oBAC9B;gBACX,OAAe;oBACL,IAAI,QAAQ;oBACZ,QAAQ;oBAER,IAAI,QAAQ,OAAO,QAAA,CAAS,GAAG;oBAE/B,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;wBAClC,IAAI,IAAA,CAAK,KAAK;oBACf;gBACF;YACF;YAED,OAAO;QACR;QAED,SAAS,eAAe,MAAA,EAAQ,OAAA,EAAS,WAAA,EAAa,QAAA,EAAU,QAAA,EAAU,SAAA,EAAW;YACnF,IAAI,WAAW,IAAI,SAAS,UAAU,MAAM;YAE5C,IAAI,QAAQ,WAAA,CAAY,OAAO,GAAA,CAAI,CAAC,CAAC,CAAA,CAAE,KAAA;YACvC,IAAI,SAAS,WAAA,CAAY,OAAO,GAAA,CAAI,CAAC,CAAC,CAAA,CAAE,MAAA;YAExC,IAAI,UAAU;YAEd,IAAI,iBAAiB,KAAK,KAAA,CAAM,QAAQ,CAAG;YAC3C,IAAI,aAAa,KAAK,IAAA,CAAK,QAAQ,CAAG;YACtC,IAAI,aAAa,KAAK,IAAA,CAAK,SAAS,CAAG;YACvC,IAAI,YAAY,QAAA,CAAS,aAAa,CAAA,IAAK;YAC3C,IAAI,YAAY,SAAA,CAAU,aAAa,CAAA,IAAK;YAE5C,IAAI,aAAa;gBAAE,OAAO;YAAG;YAC7B,IAAI,aAAa,IAAI,MAAM,OAAO;YAClC,IAAI,UAAU,IAAI,MAAM,OAAO;YAC/B,IAAI,eAAe,IAAI,MAAM,OAAO;YACpC,IAAI,WAAW,IAAI,MAAM,OAAO;YAChC,IAAI,aAAa,IAAI,MAAM,OAAO;YAElC,IAAA,IAASE,QAAO,GAAGA,QAAO,SAAS,EAAEA,MAAM;gBACzC,UAAA,CAAWA,KAAI,CAAA,GAAI,OAAA,CAAQ,OAAO,GAAA,CAAIA,KAAI,CAAC,CAAA;gBAC3C,UAAA,CAAWA,KAAI,CAAA,GAAIA,QAAO,IAAI,IAAI,UAAA,CAAWA,QAAO,CAAC,CAAA,GAAI,aAAa;gBACtE,OAAA,CAAQA,KAAI,CAAA,GAAI,IAAI,aAAa,EAAE;gBACnC,YAAA,CAAaA,KAAI,CAAA,GAAI,IAAI,YAAY,EAAE;gBACvC,QAAA,CAASA,KAAI,CAAA,GAAI,IAAI,YAAY,aAAa,EAAE;YACjD;YAED,IAAA,IAAS,SAAS,GAAG,SAAS,YAAY,EAAE,OAAQ;gBAClD,IAAI,OAAO;gBAEX,IAAI,UAAU,aAAa,GAAG,OAAO;gBAErC,IAAI,OAAO;gBAEX,IAAA,IAAS,SAAS,GAAG,SAAS,YAAY,EAAE,OAAQ;oBAClD,IAAI,UAAU,aAAa,GAAG,OAAO;oBAErC,IAAA,IAASA,QAAO,GAAGA,QAAO,SAAS,EAAEA,MAAM;wBACzC,YAAA,CAAaA,KAAI,CAAA,CAAE,IAAA,CAAK,CAAC;wBAGzB,YAAA,CAAaA,KAAI,CAAA,CAAE,CAAC,CAAA,GAAI,QAAA,CAAS,UAAA,CAAWA,KAAI,CAAA,EAAG,CAAA;wBAEnD,QAAQ,YAAY,UAAU,YAAA,CAAaA,KAAI,CAAC;wBAGhD,SAAS,YAAA,CAAaA,KAAI,CAAA,EAAG,OAAA,CAAQA,KAAI,CAAC;wBAE1C,WAAW,OAAA,CAAQA,KAAI,CAAC;oBACzB;oBAEiB;wBAChB,cAAc,OAAO;oBACtB;oBAED,IAAA,IAASA,QAAO,GAAGA,QAAO,SAAS,EAAEA,MAAM;wBACzC,cAAc,OAAA,CAAQA,KAAI,CAAA,EAAG,QAAA,CAASA,KAAI,CAAA,EAAG,SAAS,EAAE;oBACzD;gBACF;gBAED,IAAIC,UAAS;gBAEb,IAAA,IAASD,QAAO,GAAGA,QAAO,SAAS,EAAEA,MAAM;oBACzC,MAAME,QAAO,WAAA,CAAY,OAAO,GAAA,CAAIF,KAAI,CAAC,CAAA,CAAE,IAAA;oBAE3C,IAAA,IAASG,KAAI,IAAI,QAAQA,KAAI,IAAI,SAAS,MAAM,EAAEA,GAAG;wBACnDF,UAAS,UAAA,CAAWD,KAAI,CAAA,CAAEG,EAAC,CAAA;wBAE3B,IAAA,IAAS,SAAS,GAAG,SAAS,gBAAgB,EAAE,OAAQ;4BACtD,MAAM,MAAM,SAAS,KAAA,CAAMA,KAAI,CAAA,IAAO;4BAEtC,SAAS,SAAA,CAAUF,UAAS,IAAI,aAAaC,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAM,CAAC,CAAA,EAAG,IAAI;4BAChF,SAAS,SAAA,CAAUC,UAAS,IAAI,aAAaC,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAM,CAAC,CAAA,EAAG,IAAI;4BAChF,SAAS,SAAA,CAAUC,UAAS,IAAI,aAAaC,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAM,CAAC,CAAA,EAAG,IAAI;4BAChF,SAAS,SAAA,CAAUC,UAAS,IAAI,aAAaC,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAM,CAAC,CAAA,EAAG,IAAI;4BAEhF,SAAS,SAAA,CAAUC,UAAS,IAAI,aAAaC,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAM,CAAC,CAAA,EAAG,IAAI;4BAChF,SAAS,SAAA,CAAUC,UAAS,IAAI,aAAaC,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAM,CAAC,CAAA,EAAG,IAAI;4BAChF,SAAS,SAAA,CAAUC,UAAS,IAAI,aAAaC,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAM,CAAC,CAAA,EAAG,IAAI;4BAChF,SAAS,SAAA,CAAUC,UAAS,IAAI,aAAaC,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAM,CAAC,CAAA,EAAG,IAAI;4BAEhFC,WAAU,IAAI,aAAaC;wBAC5B;oBACF;oBAGD,IAAI,kBAAkB,YAAY;wBAChC,IAAA,IAASC,KAAI,IAAI,QAAQA,KAAI,IAAI,SAAS,MAAM,EAAEA,GAAG;4BACnD,MAAMF,UAAS,UAAA,CAAWD,KAAI,CAAA,CAAEG,EAAC,CAAA,GAAI,IAAI,iBAAiB,aAAaD;4BACvE,MAAM,MAAM,iBAAiB,KAAA,CAAMC,KAAI,CAAA,IAAO;4BAE9C,IAAA,IAASC,KAAI,GAAGA,KAAI,MAAM,EAAEA,GAAG;gCAC7B,SAAS,SAAA,CAAUH,UAASG,KAAI,aAAaF,OAAM,QAAA,CAASF,KAAI,CAAA,CAAE,MAAMI,EAAC,CAAA,EAAG,IAAI;4BACjF;wBACF;oBACF;gBACF;YACF;YAED,IAAI,UAAU,IAAI,YAAY,KAAK;YACnC,IAAI,WAAW,IAAI,SAAS,UAAU,MAAM;YAG5C,IAAA,IAAS,OAAO,GAAG,OAAO,SAAS,EAAE,KAAM;gBACzC,WAAA,CAAY,OAAO,GAAA,CAAI,IAAI,CAAC,CAAA,CAAE,OAAA,GAAU;gBACxC,IAAI,OAAO,WAAA,CAAY,OAAO,GAAA,CAAI,IAAI,CAAC,CAAA,CAAE,IAAA;gBAEzC,IAAI,WAAA,CAAY,IAAI,CAAA,CAAE,IAAA,IAAQ,GAAG;gBAEjC,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;oBAC/B,MAAMH,UAAS,UAAA,CAAW,IAAI,CAAA,CAAE,CAAC,CAAA;oBAEjC,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;wBAC9B,OAAA,CAAQ,CAAC,CAAA,GAAI,SAAS,SAAA,CAAUA,UAAS,IAAI,aAAa,MAAM,IAAI;oBACrE;oBAED,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;wBAC9B,SAAS,UAAA,CAAWA,UAAS,IAAI,aAAa,MAAM,cAAc,OAAA,CAAQ,CAAC,CAAC,GAAG,IAAI;oBACpF;gBACF;YACF;QACF;QAED,SAAS,QAAQ,UAAA,EAAY,QAAA,EAAU,YAAA,EAAc;YACnD,IAAI;YACJ,IAAI,UAAU;YAEd,MAAO,UAAU,GAAI;gBACnB,UAAU,QAAA,CAAS,WAAW,KAAK,CAAA;gBAEnC,IAAI,WAAW,OAAQ;oBACrB,UAAU;gBACpB,OAAA,IAAmB,WAAW,KAAK,KAAM;oBAC/B,WAAW,UAAU;gBAC/B,OAAe;oBACL,YAAA,CAAa,OAAO,CAAA,GAAI;oBACxB;gBACD;gBAED,WAAW,KAAA;YACZ;QACF;QAED,SAAS,SAAS,GAAA,EAAK,GAAA,EAAK;YAC1B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC7B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC7B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC7B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC7B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC9B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC9B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC9B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC9B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC7B,GAAA,CAAI,CAAC,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAE7B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC9B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC9B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC9B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAE/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,CAAC,CAAC;YAC9B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAE/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAE/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAE/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAE/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;YAC/B,GAAA,CAAI,EAAE,CAAA,GAAI,cAAc,GAAA,CAAI,EAAE,CAAC;QAChC;QAED,SAAS,WAAW,IAAA,EAAM;YACxB,MAAM,IAAI,MAAM,KAAK,GAAA,CAAI,UAAU,CAAG;YACtC,MAAM,IAAI,MAAM,KAAK,GAAA,CAAI,UAAU,EAAI;YACvC,MAAM,IAAI,MAAM,KAAK,GAAA,CAAI,UAAU,CAAG;YACtC,MAAM,IAAI,MAAM,KAAK,GAAA,CAAK,IAAM,UAAW,EAAI;YAC/C,MAAM,IAAI,MAAM,KAAK,GAAA,CAAK,IAAM,UAAW,EAAI;YAC/C,MAAM,IAAI,MAAM,KAAK,GAAA,CAAK,IAAM,UAAW,CAAG;YAC9C,MAAM,IAAI,MAAM,KAAK,GAAA,CAAK,IAAM,UAAW,EAAI;YAE/C,IAAI,QAAQ,IAAI,MAAM,CAAC;YACvB,IAAI,OAAO,IAAI,MAAM,CAAC;YACtB,IAAI,QAAQ,IAAI,MAAM,CAAC;YACvB,IAAI,QAAQ,IAAI,MAAM,CAAC;YAEvB,IAAA,IAAS,MAAM,GAAG,MAAM,GAAG,EAAE,IAAK;gBAChC,IAAI,SAAS,MAAM;gBAEnB,KAAA,CAAM,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAC9B,KAAA,CAAM,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAC9B,KAAA,CAAM,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAC9B,KAAA,CAAM,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAE9B,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAClG,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAClG,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAClG,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAElG,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAClD,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,IAAA,CAAK,SAAS,CAAC,CAAA;gBAClD,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAE7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAE7B,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACpC,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACpC,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACpC,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBAEpC,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACpC,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACpC,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACpC,IAAA,CAAK,SAAS,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;YACrC;YAED,IAAA,IAAS,SAAS,GAAG,SAAS,GAAG,EAAE,OAAQ;gBACzC,KAAA,CAAM,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBAC/B,KAAA,CAAM,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBAC/B,KAAA,CAAM,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBAC/B,KAAA,CAAM,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBAE/B,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,IAAI,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBACrG,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,IAAI,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBACrG,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,IAAI,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBACrG,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,IAAA,CAAK,IAAI,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,IAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBAErG,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,IAAA,CAAK,MAAM,CAAA,GAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBAC/C,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,IAAA,CAAK,MAAM,CAAA,GAAI,IAAA,CAAK,KAAK,MAAM,CAAA;gBAE/C,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAE7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBAE7B,IAAA,CAAK,IAAI,MAAM,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACpC,IAAA,CAAK,IAAI,MAAM,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACpC,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACrC,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBAErC,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACrC,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACrC,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;gBACrC,IAAA,CAAK,KAAK,MAAM,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA;YACtC;QACF;QAED,SAAS,cAAc,IAAA,EAAM;YAC3B,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;gBAC3B,IAAI,IAAI,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA;gBACjB,IAAI,KAAK,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA;gBAClB,IAAI,KAAK,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA;gBAElB,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,SAAS;gBAC1B,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,SAAS,KAAK,SAAS;gBACxC,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,SAAS;YAC3B;QACF;QAED,SAAS,cAAc,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK;YACpC,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;gBAC3B,GAAA,CAAI,MAAM,CAAC,CAAA,sJAAI,YAAA,CAAU,WAAA,CAAY,SAAS,GAAA,CAAI,CAAC,CAAC,CAAC;YACtD;QACF;QAED,SAAS,SAAS,KAAA,EAAO;YACvB,IAAI,SAAS,GAAG;gBACd,OAAO,KAAK,IAAA,CAAK,KAAK,IAAI,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,KAAK,GAAG,GAAG;YAC/D,OAAa;gBACL,OAAO,KAAK,IAAA,CAAK,KAAK,IAAI,KAAK,GAAA,CAAI,SAAS,KAAK,GAAA,CAAI,KAAK,IAAI,CAAG;YAClE;QACF;QAED,SAAS,cAAc,IAAA,EAAM;YAC3B,OAAO,IAAI,SAAS,KAAK,KAAA,CAAM,MAAA,EAAQ,KAAK,MAAA,CAAO,KAAA,EAAO,KAAK,IAAI;QACpE;QAED,SAAS,cAAc,IAAA,EAAM;YAC3B,IAAI,aAAa,KAAK,MAAA,CAAO,MAAA,CAAO,KAAA,CAAM,KAAK,MAAA,CAAO,KAAA,EAAO,KAAK,MAAA,CAAO,KAAA,GAAQ,KAAK,IAAI;YAE1F,IAAI,YAAY,IAAI,WAAW,gBAAgB,UAAU,CAAC;YAC1D,IAAI,YAAY,IAAI,WAAW,UAAU,MAAM;YAE/C,UAAU,SAAS;YAEnB,iBAAiB,WAAW,SAAS;YAErC,OAAO,IAAI,SAAS,UAAU,MAAM;QACrC;QAED,SAAS,cAAc,IAAA,EAAM;YAC3B,IAAI,aAAa,KAAK,KAAA,CAAM,KAAA,CAAM,KAAK,MAAA,CAAO,KAAA,EAAO,KAAK,MAAA,CAAO,KAAA,GAAQ,KAAK,IAAI;YAClF,IAAI,+LAAY,aAAA,EAAW,UAAU;YACrC,IAAI,YAAY,IAAI,WAAW,UAAU,MAAM;YAE/C,UAAU,SAAS;YAEnB,iBAAiB,WAAW,SAAS;YAErC,OAAO,IAAI,SAAS,UAAU,MAAM;QACrC;QAED,SAAS,cAAc,IAAA,EAAM;YAC3B,IAAI,aAAa,KAAK,MAAA;YACtB,IAAI,WAAW;gBAAE,OAAO,KAAK,MAAA,CAAO,KAAA;YAAO;YAE3C,IAAI,YAAY,IAAI,YAAY,KAAK,KAAA,GAAQ,KAAK,iBAAA,GAAA,CAAqB,KAAK,QAAA,GAAW,KAAK,IAAA,CAAK;YACjG,IAAI,SAAS,IAAI,WAAW,WAAW;YAGvC,IAAI,eAAe;YACnB,IAAI,iBAAiB,IAAI,MAAM,KAAK,QAAQ;YAC5C,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,QAAA,EAAU,IAAK;gBACtC,cAAA,CAAe,CAAC,CAAA,GAAI,CAAE;gBACtB,cAAA,CAAe,CAAC,CAAA,CAAE,OAAO,CAAA,GAAI;gBAC7B,cAAA,CAAe,CAAC,CAAA,CAAE,KAAK,CAAA,GAAI,cAAA,CAAe,CAAC,CAAA,CAAE,OAAO,CAAA;gBACpD,cAAA,CAAe,CAAC,CAAA,CAAE,IAAI,CAAA,GAAI,KAAK,KAAA;gBAC/B,cAAA,CAAe,CAAC,CAAA,CAAE,IAAI,CAAA,GAAI,KAAK,KAAA;gBAC/B,cAAA,CAAe,CAAC,CAAA,CAAE,MAAM,CAAA,GAAI,KAAK,IAAA;gBAEjC,gBAAgB,cAAA,CAAe,CAAC,CAAA,CAAE,EAAA,GAAK,cAAA,CAAe,CAAC,CAAA,CAAE,EAAA,GAAK,cAAA,CAAe,CAAC,CAAA,CAAE,IAAA;YACjF;YAID,IAAI,aAAa,YAAY,YAAY,QAAQ;YACjD,IAAI,aAAa,YAAY,YAAY,QAAQ;YAEjD,IAAI,cAAc,aAAa;gBAC7B,MAAM;YACP;YAED,IAAI,cAAc,YAAY;gBAC5B,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,aAAa,GAAG,IAAK;oBACpD,MAAA,CAAO,IAAI,UAAU,CAAA,GAAI,WAAW,YAAY,QAAQ;gBACzD;YACF;YAGD,IAAI,MAAM,IAAI,YAAY,YAAY;YACtC,IAAI,WAAW,qBAAqB,QAAQ,GAAG;YAE/C,IAAI,SAAS,YAAY,YAAY,QAAQ;YAG7C,cAAc,KAAK,KAAA,EAAO,YAAY,UAAU,QAAQ,WAAW,YAAY;YAG/E,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,QAAA,EAAU,EAAE,EAAG;gBACtC,IAAI,KAAK,cAAA,CAAe,CAAC,CAAA;gBAEzB,IAAA,IAAS,IAAI,GAAG,IAAI,cAAA,CAAe,CAAC,CAAA,CAAE,IAAA,EAAM,EAAE,EAAG;oBAC/C,WAAW,WAAW,GAAG,KAAA,GAAQ,GAAG,GAAG,EAAA,EAAI,GAAG,IAAA,EAAM,GAAG,EAAA,EAAI,GAAG,EAAA,GAAK,GAAG,IAAA,EAAM,QAAQ;gBACrF;YACF;YAGD,SAAS,KAAK,WAAW,YAAY;YAGrC,IAAII,aAAY;YAChB,IAAI,YAAY,IAAI,WAAW,UAAU,MAAA,CAAO,UAAU;YAC1D,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,KAAA,EAAO,IAAK;gBACnC,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,QAAA,EAAU,IAAK;oBACtC,IAAI,KAAK,cAAA,CAAe,CAAC,CAAA;oBAEzB,IAAI,IAAI,GAAG,EAAA,GAAK,GAAG,IAAA;oBACnB,IAAI,KAAK,IAAI,WAAW,UAAU,MAAA,EAAQ,GAAG,GAAA,GAAM,YAAY,IAAI,UAAU;oBAE7E,UAAU,GAAA,CAAI,IAAIA,UAAS;oBAC3BA,cAAa,IAAI;oBACjB,GAAG,GAAA,IAAO;gBACX;YACF;YAED,OAAO,IAAI,SAAS,UAAU,MAAM;QACrC;QAED,SAAS,cAAc,IAAA,EAAM;YAC3B,IAAI,aAAa,KAAK,KAAA,CAAM,KAAA,CAAM,KAAK,MAAA,CAAO,KAAA,EAAO,KAAK,MAAA,CAAO,KAAA,GAAQ,KAAK,IAAI;YAClF,IAAI,YAAY,gMAAA,EAAW,UAAU;YAErC,MAAM,KAAK,KAAK,KAAA,GAAQ,KAAK,QAAA,GAAW,KAAK,KAAA;YAC7C,MAAM,YAAY,KAAK,IAAA,IAAQ,IAAI,IAAI,YAAY,EAAE,IAAI,IAAI,YAAY,EAAE;YAE3E,IAAI,eAAe;YACnB,IAAI,WAAW;YACf,MAAM,MAAM,IAAI,MAAM,CAAC;YAEvB,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,KAAA,EAAO,IAAK;gBACnC,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,QAAA,EAAU,IAAK;oBACtC,IAAI,QAAQ;oBAEZ,OAAQ,KAAK,IAAA,EAAI;wBACf,KAAK;4BACH,GAAA,CAAI,CAAC,CAAA,GAAI;4BACT,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,GAAI,KAAK,KAAA;4BACvB,eAAe,GAAA,CAAI,CAAC,CAAA,GAAI,KAAK,KAAA;4BAE7B,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,KAAA,EAAO,EAAE,EAAG;gCACnC,MAAM,OAAQ,SAAA,CAAU,GAAA,CAAI,CAAC,CAAA,EAAG,CAAA,IAAK,IAAK,SAAA,CAAU,GAAA,CAAI,CAAC,CAAA,EAAG,CAAA;gCAE5D,SAAS;gCAET,SAAA,CAAU,QAAQ,CAAA,GAAI;gCACtB;4BACD;4BAED;wBAEF,KAAK;4BACH,GAAA,CAAI,CAAC,CAAA,GAAI;4BACT,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,GAAI,KAAK,KAAA;4BACvB,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,GAAI,KAAK,KAAA;4BACvB,eAAe,GAAA,CAAI,CAAC,CAAA,GAAI,KAAK,KAAA;4BAE7B,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,KAAA,EAAO,EAAE,EAAG;gCACnC,MAAM,OAAQ,SAAA,CAAU,GAAA,CAAI,CAAC,CAAA,EAAG,CAAA,IAAK,KAAO,SAAA,CAAU,GAAA,CAAI,CAAC,CAAA,EAAG,CAAA,IAAK,KAAO,SAAA,CAAU,GAAA,CAAI,CAAC,CAAA,EAAG,CAAA,IAAK;gCAEjG,SAAS;gCAET,SAAA,CAAU,QAAQ,CAAA,GAAI;gCACtB;4BACD;4BAED;oBACH;gBACF;YACF;YAED,OAAO,IAAI,SAAS,UAAU,MAAM;QACrC;QAED,SAAS,cAAc,IAAA,EAAM;YAC3B,IAAI,aAAa,KAAK,MAAA;YACtB,IAAI,WAAW;gBAAE,OAAO,KAAK,MAAA,CAAO,KAAA;YAAO;YAC3C,IAAI,YAAY,IAAI,WAAW,KAAK,KAAA,GAAQ,KAAK,KAAA,GAAA,CAAS,KAAK,QAAA,GAAW,KAAK,IAAA,GAAO,UAAA,CAAW;YAGjG,IAAI,YAAY;gBACd,SAAS,WAAW,YAAY,QAAQ;gBACxC,yBAAyB,WAAW,YAAY,QAAQ;gBACxD,uBAAuB,WAAW,YAAY,QAAQ;gBACtD,kBAAkB,WAAW,YAAY,QAAQ;gBACjD,kBAAkB,WAAW,YAAY,QAAQ;gBACjD,mBAAmB,WAAW,YAAY,QAAQ;gBAClD,qBAAqB,WAAW,YAAY,QAAQ;gBACpD,YAAY,WAAW,YAAY,QAAQ;gBAC3C,0BAA0B,WAAW,YAAY,QAAQ;gBACzD,0BAA0B,WAAW,YAAY,QAAQ;gBACzD,eAAe,WAAW,YAAY,QAAQ;YAC/C;YAED,IAAI,UAAU,OAAA,GAAU,GAAG;gBACzB,MAAM,sBAAsB,UAAU,WAAA,GAAc,cAAc,UAAU,OAAA,GAAU;YACvF;YAGD,IAAI,eAAe,IAAI,MAAO;YAC9B,IAAI,WAAW,YAAY,YAAY,QAAQ,IAAI;YAEnD,MAAO,WAAW,EAAG;gBACnB,IAAI,OAAO,0BAA0B,WAAW,MAAA,EAAQ,QAAQ;gBAChE,IAAI,QAAQ,WAAW,YAAY,QAAQ;gBAC3C,IAAI,cAAe,SAAS,IAAK;gBACjC,IAAI,MAAA,CAAO,SAAS,CAAA,IAAK;gBACzB,IAAI,QAAQ,IAAI,UAAU;oBAAC,GAAG;iBAAC,CAAA,CAAE,CAAC,CAAA;gBAClC,IAAI,OAAO,WAAW,YAAY,QAAQ;gBAE1C,aAAa,IAAA,CAAK;oBAChB;oBACA;oBACA;oBACA;gBACV,CAAS;gBAED,YAAY,KAAK,MAAA,GAAS;YAC3B;YAGD,IAAI,WAAW,UAAU,QAAA;YACzB,IAAI,cAAc,IAAI,MAAM,KAAK,QAAQ;YAEzC,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,QAAA,EAAU,EAAE,EAAG;gBACtC,IAAI,KAAM,WAAA,CAAY,CAAC,CAAA,GAAI,CAAA;gBAC3B,IAAI,UAAU,QAAA,CAAS,CAAC,CAAA;gBAExB,GAAG,IAAA,GAAO,QAAQ,IAAA;gBAClB,GAAG,WAAA,GAAc;gBACjB,GAAG,OAAA,GAAU;gBACb,GAAG,IAAA,GAAO,QAAQ,SAAA;gBAClB,GAAG,OAAA,GAAU,QAAQ,OAAA;gBACrB,GAAG,KAAA,GAAQ,KAAK,KAAA;gBAChB,GAAG,MAAA,GAAS,KAAK,KAAA;YAClB;YAED,IAAI,SAAS;gBACX,KAAK,IAAI,MAAM,CAAC;YACjB;YAED,IAAA,IAASJ,UAAS,GAAGA,UAAS,KAAK,QAAA,EAAU,EAAEA,QAAQ;gBACrD,IAAI,KAAK,WAAA,CAAYA,OAAM,CAAA;gBAE3B,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,MAAA,EAAQ,EAAE,EAAG;oBAC5C,IAAI,OAAO,YAAA,CAAa,CAAC,CAAA;oBAEzB,IAAI,GAAG,IAAA,IAAQ,KAAK,IAAA,EAAM;wBACxB,GAAG,WAAA,GAAc,KAAK,WAAA;wBAEtB,IAAI,KAAK,KAAA,IAAS,GAAG;4BACnB,OAAO,GAAA,CAAI,KAAK,KAAK,CAAA,GAAIA;wBAC1B;wBAED,GAAG,MAAA,GAASA;oBACb;gBACF;YACF;YAGD,IAAI,UAAU,gBAAA,GAAmB,GAAG;gBAClC,OAAQ,UAAU,aAAA,EAAa;oBAC7B,KAAK;wBACH,IAAI,WAAW,IAAI,YAAY,UAAU,wBAAwB;wBACjE,cACE,KAAK,KAAA,EACL,YACA,UACA,UAAU,gBAAA,EACV,UACA,UAAU,wBAAA;wBAEZ;oBAEF,KAAK;wBACH,IAAI,aAAa,KAAK,KAAA,CAAM,KAAA,CAAM,SAAS,KAAA,EAAO,SAAS,KAAA,GAAQ,UAAU,wBAAwB;wBACrG,IAAI,yLAAO,cAAA,EAAW,UAAU;wBAChC,IAAI,WAAW,IAAI,YAAY,KAAK,MAAM;wBAC1C,SAAS,KAAA,IAAS,UAAU,wBAAA;wBAC5B;gBACH;YACF;YAGD,IAAI,UAAU,gBAAA,GAAmB,GAAG;gBAClC,IAAI,WAAW;oBACb,OAAO,KAAK,KAAA;oBACZ,QAAQ;oBACR,MAAM,UAAU,gBAAA;gBACjB;gBACD,IAAI,WAAW,IAAI,YAAY,cAAc,QAAQ,EAAE,MAAM;gBAC7D,SAAS,KAAA,IAAS,UAAU,gBAAA;YAC7B;YAGD,IAAI,UAAU,UAAA,GAAa,GAAG;gBAC5B,IAAI,aAAa,KAAK,KAAA,CAAM,KAAA,CAAM,SAAS,KAAA,EAAO,SAAS,KAAA,GAAQ,UAAU,iBAAiB;gBAC9F,IAAI,yLAAO,cAAA,EAAW,UAAU;gBAChC,IAAI,YAAY,gBAAgB,KAAK,MAAM;gBAE3C,SAAS,KAAA,IAAS,UAAU,iBAAA;YAC7B;YAGD,IAAI,eAAe;YACnB,IAAI,aAAa,IAAI,MAAM,YAAY,MAAM;YAC7C,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,EAAE,EAAG;gBAC1C,UAAA,CAAW,CAAC,CAAA,GAAI,IAAI,MAAO;YAC5B;YAED,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,KAAA,EAAO,EAAE,EAAG;gBACnC,IAAA,IAAS,OAAO,GAAG,OAAO,YAAY,MAAA,EAAQ,EAAE,KAAM;oBACpD,UAAA,CAAW,IAAI,CAAA,CAAE,IAAA,CAAK,YAAY;oBAClC,gBAAgB,WAAA,CAAY,IAAI,CAAA,CAAE,KAAA,GAAQ,KAAK,IAAA,GAAO;gBACvD;YACF;YAGD,eAAe,QAAQ,YAAY,aAAa,UAAU,UAAU,SAAS;YAG7E,IAAA,IAAS,IAAI,GAAG,IAAI,YAAY,MAAA,EAAQ,EAAE,EAAG;gBAC3C,IAAI,KAAK,WAAA,CAAY,CAAC,CAAA;gBAEtB,IAAI,GAAG,OAAA,EAAS;gBAEhB,OAAQ,GAAG,WAAA,EAAW;oBACpB,KAAK;wBACH,IAAI,MAAM;wBACV,IAAI,YAAY;wBAEhB,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,KAAA,EAAO,EAAE,EAAG;4BACnC,IAAI,iBAAiB,UAAA,CAAW,CAAC,CAAA,CAAE,GAAG,CAAA;4BAEtC,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,KAAA,EAAO,EAAE,EAAG;gCACjC,IAAA,IAAS,OAAO,GAAG,OAAO,aAAa,GAAG,IAAA,EAAM,EAAE,KAAM;oCACtD,SAAA,CAAU,gBAAgB,CAAA,GAAI,SAAA,CAAU,YAAY,OAAO,GAAG,KAAA,GAAQ,GAAG,MAAM,CAAA;gCAChF;gCAED;4BACD;4BAED;wBACD;wBAED;oBAEF,KAAK;oBAEL;wBACE,MAAM;gBACT;YACF;YAED,OAAO,IAAI,SAAS,UAAU,MAAM;QACrC;QAED,SAAS,0BAA0BF,OAAAA,EAAQE,OAAAA,EAAQ;YACjD,IAAI,aAAa,IAAI,WAAWF,OAAM;YACtC,IAAI,YAAY;YAEhB,MAAO,UAAA,CAAWE,QAAO,KAAA,GAAQ,SAAS,CAAA,IAAK,EAAG;gBAChD,aAAa;YACd;YAED,IAAI,cAAc,IAAI,YAAa,EAAC,MAAA,CAAO,WAAW,KAAA,CAAMA,QAAO,KAAA,EAAOA,QAAO,KAAA,GAAQ,SAAS,CAAC;YAEnGA,QAAO,KAAA,GAAQA,QAAO,KAAA,GAAQ,YAAY;YAE1C,OAAO;QACR;QAED,SAAS,uBAAuBF,OAAAA,EAAQE,OAAAA,EAAQ,IAAA,EAAM;YACpD,IAAI,cAAc,IAAI,YAAa,EAAC,MAAA,CAAO,IAAI,WAAWF,OAAM,EAAE,KAAA,CAAME,QAAO,KAAA,EAAOA,QAAO,KAAA,GAAQ,IAAI,CAAC;YAE1GA,QAAO,KAAA,GAAQA,QAAO,KAAA,GAAQ;YAE9B,OAAO;QACR;QAED,SAAS,cAAc,QAAA,EAAUA,OAAAA,EAAQ;YACvC,IAAI,IAAI,WAAW,UAAUA,OAAM;YACnC,IAAI,IAAI,YAAY,UAAUA,OAAM;YAEpC,OAAO;gBAAC;gBAAG,CAAC;aAAA;QACb;QAED,SAAS,cAAc,QAAA,EAAUA,OAAAA,EAAQ;YACvC,IAAI,IAAI,YAAY,UAAUA,OAAM;YACpC,IAAI,IAAI,YAAY,UAAUA,OAAM;YAEpC,OAAO;gBAAC;gBAAG,CAAC;aAAA;QACb;QAED,SAAS,WAAW,QAAA,EAAUA,OAAAA,EAAQ;YACpC,IAAI,QAAQ,SAAS,QAAA,CAASA,QAAO,KAAA,EAAO,IAAI;YAEhDA,QAAO,KAAA,GAAQA,QAAO,KAAA,GAAQ;YAE9B,OAAO;QACR;QAED,SAAS,YAAY,QAAA,EAAUA,OAAAA,EAAQ;YACrC,IAAI,SAAS,SAAS,SAAA,CAAUA,QAAO,KAAA,EAAO,IAAI;YAElDA,QAAO,KAAA,GAAQA,QAAO,KAAA,GAAQ;YAE9B,OAAO;QACR;QAED,SAAS,gBAAgBH,WAAAA,EAAYG,OAAAA,EAAQ;YAC3C,IAAI,QAAQH,WAAAA,CAAWG,QAAO,KAAK,CAAA;YAEnCA,QAAO,KAAA,GAAQA,QAAO,KAAA,GAAQ;YAE9B,OAAO;QACR;QAED,SAAS,WAAW,QAAA,EAAUA,OAAAA,EAAQ;YACpC,IAAI,QAAQ,SAAS,QAAA,CAASA,QAAO,KAAK;YAE1CA,QAAO,KAAA,GAAQA,QAAO,KAAA,GAAQ;YAE9B,OAAO;QACR;QAED,MAAM,aAAa,SAAU,QAAA,EAAUA,OAAAA,EAAQ;YAC7C,IAAI;YAEJ,IAAI,iBAAiB,SAAS,SAAA,EAAW;gBACvC,MAAM,OAAO,SAAS,WAAA,CAAYA,QAAO,KAAA,EAAO,IAAI,CAAC;YAC7D,OAAa;gBACL,MAAM,SAAS,SAAA,CAAUA,QAAO,KAAA,GAAQ,GAAG,IAAI,IAAI,OAAO,SAAS,SAAA,CAAUA,QAAO,KAAA,EAAO,IAAI,KAAK,EAAE;YACvG;YAEDA,QAAO,KAAA,IAAS;YAEhB,OAAO;QACR;QAED,SAAS,aAAa,QAAA,EAAUA,OAAAA,EAAQ;YACtC,IAAI,QAAQ,SAAS,UAAA,CAAWA,QAAO,KAAA,EAAO,IAAI;YAElDA,QAAO,KAAA,IAAS;YAEhB,OAAO;QACR;QAED,SAAS,cAAc,QAAA,EAAUA,OAAAA,EAAQ;YACvC,OAAO,+JAAA,CAAU,WAAA,CAAY,aAAa,UAAUA,OAAM,CAAC;QAC5D;QAGD,SAAS,cAAc,MAAA,EAAQ;YAC7B,IAAI,WAAA,CAAY,SAAS,KAAA,KAAW,IAClC,WAAW,SAAS;YAEtB,OAAA,CACG,UAAU,KAAK,CAAA,IAAK,CAAA,IAAA,CACpB,WACG,aAAa,KACX,WACE,MACA,WACF,KAAK,GAAA,CAAI,GAAG,WAAW,EAAE,IAAA,CAAK,IAAI,WAAW,IAAA,IAC/C,iBAAA,CAAkB,WAAW,IAAA,CAAA;QAEpC;QAED,SAAS,YAAY,QAAA,EAAUA,OAAAA,EAAQ;YACrC,IAAI,SAAS,SAAS,SAAA,CAAUA,QAAO,KAAA,EAAO,IAAI;YAElDA,QAAO,KAAA,IAAS;YAEhB,OAAO;QACR;QAED,SAAS,aAAaF,OAAAA,EAAQE,OAAAA,EAAQ;YACpC,OAAO,cAAc,YAAYF,SAAQE,OAAM,CAAC;QACjD;QAED,SAAS,YAAY,QAAA,EAAUF,OAAAA,EAAQE,OAAAA,EAAQ,IAAA,EAAM;YACnD,IAAI,cAAcA,QAAO,KAAA;YACzB,IAAI,WAAW,CAAE,CAAA;YAEjB,MAAOA,QAAO,KAAA,GAAQ,cAAc,OAAO,EAAG;gBAC5C,IAAI,OAAO,0BAA0BF,SAAQE,OAAM;gBACnD,IAAI,YAAY,WAAW,UAAUA,OAAM;gBAC3C,IAAI,UAAU,WAAW,UAAUA,OAAM;gBACzCA,QAAO,KAAA,IAAS;gBAChB,IAAI,YAAY,WAAW,UAAUA,OAAM;gBAC3C,IAAI,YAAY,WAAW,UAAUA,OAAM;gBAE3C,SAAS,IAAA,CAAK;oBACZ;oBACA;oBACA;oBACA;oBACA;gBACV,CAAS;YACF;YAEDA,QAAO,KAAA,IAAS;YAEhB,OAAO;QACR;QAED,SAAS,oBAAoB,QAAA,EAAUA,OAAAA,EAAQ;YAC7C,IAAI,OAAO,aAAa,UAAUA,OAAM;YACxC,IAAI,OAAO,aAAa,UAAUA,OAAM;YACxC,IAAI,SAAS,aAAa,UAAUA,OAAM;YAC1C,IAAI,SAAS,aAAa,UAAUA,OAAM;YAC1C,IAAI,QAAQ,aAAa,UAAUA,OAAM;YACzC,IAAI,QAAQ,aAAa,UAAUA,OAAM;YACzC,IAAI,SAAS,aAAa,UAAUA,OAAM;YAC1C,IAAI,SAAS,aAAa,UAAUA,OAAM;YAE1C,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACD;QACF;QAED,SAAS,iBAAiB,QAAA,EAAUA,OAAAA,EAAQ;YAC1C,IAAI,mBAAmB;gBACrB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,IAAI,cAAc,WAAW,UAAUA,OAAM;YAE7C,OAAO,gBAAA,CAAiB,WAAW,CAAA;QACpC;QAED,SAAS,WAAW,QAAA,EAAUA,OAAAA,EAAQ;YACpC,IAAI,OAAO,YAAY,UAAUA,OAAM;YACvC,IAAI,OAAO,YAAY,UAAUA,OAAM;YACvC,IAAI,OAAO,YAAY,UAAUA,OAAM;YACvC,IAAI,OAAO,YAAY,UAAUA,OAAM;YAEvC,OAAO;gBAAE;gBAAY;gBAAY;gBAAY;YAAY;QAC1D;QAED,SAAS,eAAe,QAAA,EAAUA,OAAAA,EAAQ;YACxC,IAAI,aAAa;gBAAC,cAAc;aAAA;YAEhC,IAAI,YAAY,WAAW,UAAUA,OAAM;YAE3C,OAAO,UAAA,CAAW,SAAS,CAAA;QAC5B;QAED,SAAS,SAAS,QAAA,EAAUA,OAAAA,EAAQ;YAClC,IAAI,IAAI,aAAa,UAAUA,OAAM;YACrC,IAAI,IAAI,aAAa,UAAUA,OAAM;YAErC,OAAO;gBAAC;gBAAG,CAAC;aAAA;QACb;QAED,SAAS,SAAS,QAAA,EAAUA,OAAAA,EAAQ;YAClC,IAAI,IAAI,aAAa,UAAUA,OAAM;YACrC,IAAI,IAAI,aAAa,UAAUA,OAAM;YACrC,IAAI,IAAI,aAAa,UAAUA,OAAM;YAErC,OAAO;gBAAC;gBAAG;gBAAG,CAAC;aAAA;QAChB;QAED,SAAS,WAAW,QAAA,EAAUF,OAAAA,EAAQE,OAAAA,EAAQ,IAAA,EAAM,IAAA,EAAM;YACxD,IAAI,SAAS,YAAY,SAAS,kBAAkB,SAAS,cAAc;gBACzE,OAAO,uBAAuBF,SAAQE,SAAQ,IAAI;YAC1D,OAAA,IAAiB,SAAS,UAAU;gBAC5B,OAAO,YAAY,UAAUF,SAAQE,SAAQ,IAAI;YACzD,OAAA,IAAiB,SAAS,kBAAkB;gBACpC,OAAO,oBAAoB,UAAUA,OAAM;YACnD,OAAA,IAAiB,SAAS,eAAe;gBACjC,OAAO,iBAAiB,UAAUA,OAAM;YAChD,OAAA,IAAiB,SAAS,SAAS;gBAC3B,OAAO,WAAW,UAAUA,OAAM;YAC1C,OAAA,IAAiB,SAAS,aAAa;gBAC/B,OAAO,eAAe,UAAUA,OAAM;YAC9C,OAAA,IAAiB,SAAS,SAAS;gBAC3B,OAAO,aAAa,UAAUA,OAAM;YAC5C,OAAA,IAAiB,SAAS,OAAO;gBACzB,OAAO,SAAS,UAAUA,OAAM;YACxC,OAAA,IAAiB,SAAS,OAAO;gBACzB,OAAO,SAAS,UAAUA,OAAM;YACxC,OAAA,IAAiB,SAAS,OAAO;gBACzB,OAAO,WAAW,UAAUA,OAAM;YAC1C,OAAA,IAAiB,SAAS,YAAY;gBAC9B,OAAO,cAAc,UAAUA,OAAM;YAC7C,OAAA,IAAiB,SAAS,YAAY;gBAC9B,OAAO,cAAc,UAAUA,OAAM;YAC7C,OAAA,IAAiB,SAAS,WAAW;gBAC7BA,QAAO,KAAA,IAAS;gBAChB,OAAO;YACf,OAAa;gBACLA,QAAO,KAAA,IAAS;gBAChB,OAAO,KAAA;YACR;QACF;QAED,SAAS,YAAY,QAAA,EAAUF,OAAAA,EAAQE,OAAAA,EAAQ;YAC7C,MAAMK,aAAY,CAAE;YAEpB,IAAI,SAAS,SAAA,CAAU,GAAG,IAAI,KAAK,UAAU;gBAE3C,MAAM;YACP;YAEDA,WAAU,OAAA,GAAU,SAAS,QAAA,CAAS,CAAC;YAEvC,MAAM,OAAO,SAAS,QAAA,CAAS,CAAC;YAEhCA,WAAU,IAAA,GAAO;gBACf,YAAY,CAAC,CAAA,CAAE,OAAO,CAAA;gBACtB,UAAU,CAAC,CAAA,CAAE,OAAO,CAAA;gBACpB,YAAY,CAAC,CAAA,CAAE,OAAO,CAAA;gBACtB,WAAW,CAAC,CAAA,CAAE,OAAO,EAAA;YACtB;YAIDL,QAAO,KAAA,GAAQ;YAEf,IAAI,cAAc;YAElB,MAAO,YAAa;gBAClB,IAAI,gBAAgB,0BAA0BF,SAAQE,OAAM;gBAE5D,IAAI,iBAAiB,GAAG;oBACtB,cAAc;gBACxB,OAAe;oBACL,IAAI,gBAAgB,0BAA0BF,SAAQE,OAAM;oBAC5D,IAAI,gBAAgB,YAAY,UAAUA,OAAM;oBAChD,IAAI,iBAAiB,WAAW,UAAUF,SAAQE,SAAQ,eAAe,aAAa;oBAEtF,IAAI,mBAAmB,KAAA,GAAW;wBAChC,QAAQ,IAAA,CAAK,CAAA,wDAAA,EAA4D,cAAA,EAAA,CAAkB;oBACvG,OAAiB;wBACLK,UAAAA,CAAU,aAAa,CAAA,GAAI;oBAC5B;gBACF;YACF;YAED,IAAA,CAAK,OAAO,CAAC,CAAA,KAAS,GAAG;gBAEvB,QAAQ,KAAA,CAAM,cAAcA,UAAS;gBACrC,MAAM;YACP;YAED,OAAOA;QACR;QAED,SAAS,aAAaA,UAAAA,EAAW,QAAA,EAAUR,WAAAA,EAAYG,OAAAA,EAAQ,UAAA,EAAY;YACzE,MAAMM,cAAa;gBACjB,MAAM;gBACN,QAAQ;gBACR,OAAOT;gBACP,QAAQG;gBACR,OAAOK,WAAU,UAAA,CAAW,IAAA,GAAOA,WAAU,UAAA,CAAW,IAAA,GAAO;gBAC/D,QAAQA,WAAU,UAAA,CAAW,IAAA,GAAOA,WAAU,UAAA,CAAW,IAAA,GAAO;gBAChE,UAAUA,WAAU,QAAA,CAAS,MAAA;gBAC7B,cAAc;gBACd,OAAO;gBACP,WAAW;gBACX,MAAMA,WAAU,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA;gBAC5B,YAAY;gBACZ,QAAQ;gBACR,QAAQ;gBACR,CAAC,gBAAgB,eAAe,UAAU,CAAA,EAAG;YAC9C;YAED,OAAQA,WAAU,WAAA,EAAW;gBAC3B,KAAK;oBACHC,YAAW,KAAA,GAAQ;oBACnBA,YAAW,UAAA,GAAa;oBACxB;gBAEF,KAAK;oBACHA,YAAW,KAAA,GAAQ;oBACnBA,YAAW,UAAA,GAAa;oBACxB;gBAEF,KAAK;oBACHA,YAAW,KAAA,GAAQ;oBACnBA,YAAW,UAAA,GAAa;oBACxB;gBAEF,KAAK;oBACHA,YAAW,KAAA,GAAQ;oBACnBA,YAAW,UAAA,GAAa;oBACxB;gBAEF,KAAK;oBACHA,YAAW,KAAA,GAAQ;oBACnBA,YAAW,UAAA,GAAa;oBACxB;gBAEF,KAAK;oBACHA,YAAW,KAAA,GAAQ;oBACnBA,YAAW,UAAA,GAAa;oBACxB;gBAEF,KAAK;oBACHA,YAAW,KAAA,GAAQ;oBACnBA,YAAW,UAAA,GAAa;oBACxB;gBAEF,KAAK;oBACHA,YAAW,KAAA,GAAQ;oBACnBA,YAAW,UAAA,GAAa;oBACxB;gBAEF;oBACE,MAAM,sBAAsBD,WAAU,WAAA,GAAc;YACvD;YAEDC,YAAW,iBAAA,GAAoBA,YAAW,KAAA;YAE1C,IAAIA,YAAW,IAAA,IAAQ,GAAG;gBAExB,OAAQ,YAAU;oBAChB,wJAAK,YAAA;wBACHA,YAAW,MAAA,GAAS;wBACpBA,YAAW,SAAA,GAAY;wBACvB;oBAEF,KAAK,mKAAA;wBACHA,YAAW,MAAA,GAAS;wBACpBA,YAAW,SAAA,GAAY;wBACvB;gBACH;YACT,OAAA,IAAiBA,YAAW,IAAA,IAAQ,GAAG;gBAE/B,OAAQ,YAAU;oBAChB,wJAAK,YAAA;wBACHA,YAAW,MAAA,GAAS;wBACpBA,YAAW,SAAA,GAAY;wBACvB;oBAEF,wJAAK,gBAAA;wBACHA,YAAW,MAAA,GAAS;wBACpBA,YAAW,SAAA,GAAY;gBAC1B;YACT,OAAa;gBACL,MAAM,4CAA4CA,YAAW,IAAA,GAAO,UAAUD,WAAU,WAAA,GAAc;YACvG;YAEDC,YAAW,UAAA,GAAA,CAAcD,WAAU,UAAA,CAAW,IAAA,GAAO,CAAA,IAAKC,YAAW,iBAAA;YAErE,IAAA,IAAS,IAAI,GAAG,IAAIA,YAAW,UAAA,EAAY,IAAK,WAAW,UAAUN,OAAM;YAK3EM,YAAW,cAAA,GAAiBA,YAAW,QAAA,IAAY,IAAI,IAAIA,YAAW,QAAA;YACtE,MAAM,OAAOA,YAAW,KAAA,GAAQA,YAAW,MAAA,GAASA,YAAW,cAAA;YAE/D,OAAQ,YAAU;gBAChB,wJAAK,YAAA;oBACHA,YAAW,SAAA,GAAY,IAAI,aAAa,IAAI;oBAG5C,IAAIA,YAAW,QAAA,GAAWA,YAAW,cAAA,EAAgBA,YAAW,SAAA,CAAU,IAAA,CAAK,GAAG,GAAG,IAAI;oBAEzF;gBAEF,wJAAK,gBAAA;oBACHA,YAAW,SAAA,GAAY,IAAI,YAAY,IAAI;oBAE3C,IAAIA,YAAW,QAAA,GAAWA,YAAW,cAAA,EAAgBA,YAAW,SAAA,CAAU,IAAA,CAAK,OAAQ,GAAG,IAAI;oBAE9F;gBAEF;oBACE,QAAQ,KAAA,CAAM,uCAAuC,UAAU;oBAC/D;YACH;YAEDA,YAAW,YAAA,GAAeA,YAAW,KAAA,GAAQA,YAAW,SAAA,GAAYA,YAAW,QAAA;YAE/E,IAAIA,YAAW,cAAA,IAAkB,GAAGA,YAAW,MAAA,sJAAS,aAAA;iBACnDA,YAAW,MAAA,sJAAS,YAAA;YAEzB,IAAI,eAAeA,YAAW,UAAA,GAAa;iBACtCA,YAAW,QAAA,GAAW;YAE3B,OAAOA;QACR;QAID,MAAM,iBAAiB,IAAI,SAAS,MAAM;QAC1C,MAAM,aAAa,IAAI,WAAW,MAAM;QACxC,MAAM,SAAS;YAAE,OAAO;QAAG;QAG3B,MAAM,YAAY,YAAY,gBAAgB,QAAQ,MAAM;QAG5D,MAAM,aAAa,aAAa,WAAW,gBAAgB,YAAY,QAAQ,IAAA,CAAK,IAAI;QAExF,MAAM,YAAY;YAAE,OAAO;QAAG;QAC9B,MAAM,iBAAiB;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,GAAG;QAAG;QAEvD,IAAA,IACM,mBAAmB,GACvB,mBAAmB,WAAW,MAAA,GAAS,WAAW,iBAAA,EAClD,mBACA;YACA,MAAM,OAAO,YAAY,gBAAgB,MAAM;YAC/C,WAAW,IAAA,GAAO,YAAY,gBAAgB,MAAM;YACpD,WAAW,KAAA,GACT,OAAO,WAAW,iBAAA,GAAoB,WAAW,MAAA,GAC7C,WAAW,MAAA,GAAS,OACpB,WAAW,iBAAA;YAEjB,MAAM,eAAe,WAAW,IAAA,GAAO,WAAW,KAAA,GAAQ,WAAW,YAAA;YACrE,MAAM,SAAS,eAAe,WAAW,UAAA,CAAW,UAAU,IAAI,cAAc,UAAU;YAE1F,OAAO,KAAA,IAAS,WAAW,IAAA;YAE3B,IAAA,IAAS,SAAS,GAAG,SAAS,WAAW,iBAAA,EAAmB,SAAU;gBACpE,MAAM,SAAS,SAAS,mBAAmB,WAAW,iBAAA;gBACtD,IAAI,UAAU,WAAW,MAAA,EAAQ;gBAEjC,IAAA,IAAS,YAAY,GAAG,YAAY,WAAW,QAAA,EAAU,YAAa;oBACpE,MAAM,OAAO,cAAA,CAAe,UAAU,QAAA,CAAS,SAAS,CAAA,CAAE,IAAI,CAAA;oBAE9D,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,KAAA,EAAO,IAAK;wBACzC,UAAU,KAAA,GAAA,CACP,SAAA,CAAU,WAAW,QAAA,GAAW,WAAW,KAAA,IAAS,YAAY,WAAW,KAAA,GAAQ,CAAA,IACpF,WAAW,SAAA;wBACb,MAAM,WAAA,CACH,WAAW,MAAA,GAAS,IAAI,MAAA,IAAA,CAAW,WAAW,KAAA,GAAQ,WAAW,cAAA,IAClE,IAAI,WAAW,cAAA,GACf;wBACF,WAAW,SAAA,CAAU,QAAQ,CAAA,GAAI,WAAW,MAAA,CAAO,QAAQ,SAAS;oBACrE;gBACF;YACF;QACF;QAED,OAAO;YACL,QAAQ;YACR,OAAO,WAAW,KAAA;YAClB,QAAQ,WAAW,MAAA;YACnB,MAAM,WAAW,SAAA;YACjB,QAAQ,WAAW,MAAA;YACnB,CAAC,gBAAgB,eAAe,UAAU,CAAA,EAAG,UAAA,CAAW,gBAAgB,eAAe,UAAU,CAAA;YACjG,MAAM,IAAA,CAAK,IAAA;QACZ;IACF;IAED,YAAY,KAAA,EAAO;QACjB,IAAA,CAAK,IAAA,GAAO;QACZ,OAAO,IAAA;IACR;IAED,KAAK,GAAA,EAAK,MAAA,EAAQ,UAAA,EAAY,OAAA,EAAS;QACrC,SAAS,eAAe,OAAA,EAAS,OAAA,EAAS;YACxC,IAAI,eAAe,QAAQ,UAAA,GAAa,QAAQ,UAAA;iBAC3C,QAAQ,QAAA,GAAW,QAAQ,QAAA;YAChC,QAAQ,SAAA,sJAAY,eAAA;YACpB,QAAQ,SAAA,sJAAY,eAAA;YACpB,QAAQ,eAAA,GAAkB;YAC1B,QAAQ,KAAA,GAAQ;YAEhB,IAAI,QAAQ,OAAO,SAAS,OAAO;QACpC;QAED,OAAO,KAAA,CAAM,KAAK,KAAK,gBAAgB,YAAY,OAAO;IAC3D;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2733, "column": 0}, "map": {"version": 3, "file": "helpers.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/src/types/helpers.ts"], "sourcesContent": ["export const getWithKey = <T, K extends keyof T>(obj: T, key: K): T[K] => obj[key]\n"], "names": [], "mappings": ";;;AAAO,MAAM,aAAa,CAAuB,KAAQ,MAAiB,GAAA,CAAI,GAAG,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2745, "column": 0}, "map": {"version": 3, "file": "BufferGeometryUtils.js", "sources": ["file:///C:/LocalDiskD/UI/AUGMENT-AI/PORTFOLIO5/node_modules/src/utils/BufferGeometryUtils.ts"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  Float32BufferAttribute,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  TriangleFanDrawMode,\n  TriangleStripDrawMode,\n  TrianglesDrawMode,\n  Vector3,\n  Mesh,\n  Line,\n  Points,\n  Material,\n  SkinnedMesh,\n} from 'three'\n\nimport { getWithKey } from '../types/helpers'\nimport type { TypedArrayConstructors, TypedArray } from '../types/shared'\n\n/**\n * @param  {Array<BufferGeometry>} geometries\n * @param  {Boolean} useGroups\n * @return {BufferGeometry}\n */\nexport const mergeBufferGeometries = (geometries: BufferGeometry[], useGroups?: boolean): BufferGeometry | null => {\n  const isIndexed = geometries[0].index !== null\n\n  const attributesUsed = new Set(Object.keys(geometries[0].attributes))\n  const morphAttributesUsed = new Set(Object.keys(geometries[0].morphAttributes))\n\n  const attributes: { [key: string]: Array<InterleavedBufferAttribute | BufferAttribute> } = {}\n  const morphAttributes: { [key: string]: Array<BufferAttribute | InterleavedBufferAttribute>[] } = {}\n\n  const morphTargetsRelative = geometries[0].morphTargetsRelative\n\n  const mergedGeometry = new BufferGeometry()\n\n  let offset = 0\n\n  geometries.forEach((geom, i) => {\n    let attributesCount = 0\n\n    // ensure that all geometries are indexed, or none\n\n    if (isIndexed !== (geom.index !== null)) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.',\n      )\n      return null\n    }\n\n    // gather attributes, exit early if they're different\n\n    for (let name in geom.attributes) {\n      if (!attributesUsed.has(name)) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '. All geometries must have compatible attributes; make sure \"' +\n            name +\n            '\" attribute exists among all geometries, or in none of them.',\n        )\n        return null\n      }\n\n      if (attributes[name] === undefined) {\n        attributes[name] = []\n      }\n\n      attributes[name].push(geom.attributes[name])\n\n      attributesCount++\n    }\n\n    // ensure geometries have the same number of attributes\n\n    if (attributesCount !== attributesUsed.size) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. Make sure all geometries have the same number of attributes.',\n      )\n      return null\n    }\n\n    // gather morph attributes, exit early if they're different\n\n    if (morphTargetsRelative !== geom.morphTargetsRelative) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. .morphTargetsRelative must be consistent throughout all geometries.',\n      )\n      return null\n    }\n\n    for (let name in geom.morphAttributes) {\n      if (!morphAttributesUsed.has(name)) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '.  .morphAttributes must be consistent throughout all geometries.',\n        )\n        return null\n      }\n\n      if (morphAttributes[name] === undefined) morphAttributes[name] = []\n\n      morphAttributes[name].push(geom.morphAttributes[name])\n    }\n\n    // gather .userData\n\n    mergedGeometry.userData.mergedUserData = mergedGeometry.userData.mergedUserData || []\n    mergedGeometry.userData.mergedUserData.push(geom.userData)\n\n    if (useGroups) {\n      let count\n\n      if (geom.index) {\n        count = geom.index.count\n      } else if (geom.attributes.position !== undefined) {\n        count = geom.attributes.position.count\n      } else {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '. The geometry must have either an index or a position attribute',\n        )\n        return null\n      }\n\n      mergedGeometry.addGroup(offset, count, i)\n\n      offset += count\n    }\n  })\n\n  // merge indices\n\n  if (isIndexed) {\n    let indexOffset = 0\n    const mergedIndex: number[] = []\n\n    geometries.forEach((geom) => {\n      const index = geom.index as BufferAttribute\n\n      for (let j = 0; j < index.count; ++j) {\n        mergedIndex.push(index.getX(j) + indexOffset)\n      }\n\n      indexOffset += geom.attributes.position.count\n    })\n\n    mergedGeometry.setIndex(mergedIndex)\n  }\n\n  // merge attributes\n\n  for (let name in attributes) {\n    const mergedAttribute = mergeBufferAttributes(attributes[name] as BufferAttribute[])\n\n    if (!mergedAttribute) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the ' + name + ' attribute.',\n      )\n      return null\n    }\n\n    mergedGeometry.setAttribute(name, mergedAttribute)\n  }\n\n  // merge morph attributes\n\n  for (let name in morphAttributes) {\n    const numMorphTargets = morphAttributes[name][0].length\n\n    if (numMorphTargets === 0) break\n\n    mergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {}\n    mergedGeometry.morphAttributes[name] = []\n\n    for (let i = 0; i < numMorphTargets; ++i) {\n      const morphAttributesToMerge = []\n\n      for (let j = 0; j < morphAttributes[name].length; ++j) {\n        morphAttributesToMerge.push(morphAttributes[name][j][i])\n      }\n\n      const mergedMorphAttribute = mergeBufferAttributes(morphAttributesToMerge as BufferAttribute[])\n\n      if (!mergedMorphAttribute) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the ' +\n            name +\n            ' morphAttribute.',\n        )\n        return null\n      }\n\n      mergedGeometry.morphAttributes[name].push(mergedMorphAttribute)\n    }\n  }\n\n  return mergedGeometry\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {BufferAttribute}\n */\nexport const mergeBufferAttributes = (attributes: BufferAttribute[]): BufferAttribute | null | undefined => {\n  let TypedArray: TypedArrayConstructors | undefined = undefined\n  let itemSize: number | undefined = undefined\n  let normalized: boolean | undefined = undefined\n  let arrayLength = 0\n\n  attributes.forEach((attr) => {\n    if (TypedArray === undefined) {\n      TypedArray = attr.array.constructor\n    }\n    if (TypedArray !== attr.array.constructor) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.',\n      )\n      return null\n    }\n\n    if (itemSize === undefined) itemSize = attr.itemSize\n    if (itemSize !== attr.itemSize) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.',\n      )\n      return null\n    }\n\n    if (normalized === undefined) normalized = attr.normalized\n    if (normalized !== attr.normalized) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.',\n      )\n      return null\n    }\n\n    arrayLength += attr.array.length\n  })\n\n  if (TypedArray && itemSize) {\n    // @ts-ignore this works in JS and TS is complaining but it's such a tiny thing I can live with the guilt\n    const array = new TypedArray(arrayLength)\n    let offset = 0\n\n    attributes.forEach((attr) => {\n      array.set(attr.array, offset)\n      offset += attr.array.length\n    })\n\n    return new BufferAttribute(array, itemSize, normalized)\n  }\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {Array<InterleavedBufferAttribute>}\n */\nexport const interleaveAttributes = (attributes: BufferAttribute[]): InterleavedBufferAttribute[] | null => {\n  // Interleaves the provided attributes into an InterleavedBuffer and returns\n  // a set of InterleavedBufferAttributes for each attribute\n  let TypedArray: TypedArrayConstructors | undefined = undefined\n  let arrayLength = 0\n  let stride = 0\n\n  // calculate the the length and type of the interleavedBuffer\n  for (let i = 0, l = attributes.length; i < l; ++i) {\n    const attribute = attributes[i]\n\n    if (TypedArray === undefined) TypedArray = attribute.array.constructor\n    if (TypedArray !== attribute.array.constructor) {\n      console.error('AttributeBuffers of different types cannot be interleaved')\n      return null\n    }\n\n    arrayLength += attribute.array.length\n    stride += attribute.itemSize\n  }\n\n  // Create the set of buffer attributes\n  // @ts-ignore this works in JS and TS is complaining but it's such a tiny thing I can live with the guilt\n  const interleavedBuffer = new InterleavedBuffer(new TypedArray(arrayLength), stride)\n  let offset = 0\n  const res = []\n  const getters = ['getX', 'getY', 'getZ', 'getW']\n  const setters = ['setX', 'setY', 'setZ', 'setW']\n\n  for (let j = 0, l = attributes.length; j < l; j++) {\n    const attribute = attributes[j]\n    const itemSize = attribute.itemSize\n    const count = attribute.count\n    const iba = new InterleavedBufferAttribute(interleavedBuffer, itemSize, offset, attribute.normalized)\n    res.push(iba)\n\n    offset += itemSize\n\n    // Move the data for each attribute into the new interleavedBuffer\n    // at the appropriate offset\n    for (let c = 0; c < count; c++) {\n      for (let k = 0; k < itemSize; k++) {\n        const set = getWithKey(iba, setters[k] as keyof InterleavedBufferAttribute) as InterleavedBufferAttribute[\n          | 'setX'\n          | 'setY'\n          | 'setZ'\n          | 'setW']\n        const get = getWithKey(attribute, getters[k] as keyof BufferAttribute) as BufferAttribute[\n          | 'getX'\n          | 'getY'\n          | 'getZ'\n          | 'getW']\n        set(c, get(c))\n      }\n    }\n  }\n\n  return res\n}\n\n/**\n * @param {Array<BufferGeometry>} geometry\n * @return {number}\n */\nexport function estimateBytesUsed(geometry: BufferGeometry): number {\n  // Return the estimated memory used by this geometry in bytes\n  // Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n  // for InterleavedBufferAttributes.\n  let mem = 0\n  for (let name in geometry.attributes) {\n    const attr = geometry.getAttribute(name)\n    mem += attr.count * attr.itemSize * (attr.array as TypedArray).BYTES_PER_ELEMENT\n  }\n\n  const indices = geometry.getIndex()\n  mem += indices ? indices.count * indices.itemSize * (indices.array as TypedArray).BYTES_PER_ELEMENT : 0\n  return mem\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} tolerance\n * @return {BufferGeometry>}\n */\nexport function mergeVertices(geometry: BufferGeometry, tolerance = 1e-4): BufferGeometry {\n  tolerance = Math.max(tolerance, Number.EPSILON)\n\n  // Generate an index buffer if the geometry doesn't have one, or optimize it\n  // if it's already available.\n  const hashToIndex: {\n    [key: string]: number\n  } = {}\n  const indices = geometry.getIndex()\n  const positions = geometry.getAttribute('position')\n  const vertexCount = indices ? indices.count : positions.count\n\n  // next value for triangle indices\n  let nextIndex = 0\n\n  // attributes and new attribute arrays\n  const attributeNames = Object.keys(geometry.attributes)\n  const attrArrays: {\n    [key: string]: []\n  } = {}\n  const morphAttrsArrays: {\n    [key: string]: Array<Array<BufferAttribute | InterleavedBufferAttribute>>\n  } = {}\n  const newIndices = []\n  const getters = ['getX', 'getY', 'getZ', 'getW']\n\n  // initialize the arrays\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i]\n\n    attrArrays[name] = []\n\n    const morphAttr = geometry.morphAttributes[name]\n    if (morphAttr) {\n      morphAttrsArrays[name] = new Array(morphAttr.length).fill(0).map(() => [])\n    }\n  }\n\n  // convert the error tolerance to an amount of decimal places to truncate to\n  const decimalShift = Math.log10(1 / tolerance)\n  const shiftMultiplier = Math.pow(10, decimalShift)\n  for (let i = 0; i < vertexCount; i++) {\n    const index = indices ? indices.getX(i) : i\n\n    // Generate a hash for the vertex attributes at the current index 'i'\n    let hash = ''\n    for (let j = 0, l = attributeNames.length; j < l; j++) {\n      const name = attributeNames[j]\n      const attribute = geometry.getAttribute(name)\n      const itemSize = attribute.itemSize\n\n      for (let k = 0; k < itemSize; k++) {\n        // double tilde truncates the decimal value\n        // @ts-ignore no\n        hash += `${~~(attribute[getters[k]](index) * shiftMultiplier)},`\n      }\n    }\n\n    // Add another reference to the vertex if it's already\n    // used by another index\n    if (hash in hashToIndex) {\n      newIndices.push(hashToIndex[hash])\n    } else {\n      // copy data to the new index in the attribute arrays\n      for (let j = 0, l = attributeNames.length; j < l; j++) {\n        const name = attributeNames[j]\n        const attribute = geometry.getAttribute(name)\n        const morphAttr = geometry.morphAttributes[name]\n        const itemSize = attribute.itemSize\n        const newarray = attrArrays[name]\n        const newMorphArrays = morphAttrsArrays[name]\n\n        for (let k = 0; k < itemSize; k++) {\n          const getterFunc = getters[k]\n          // @ts-ignore\n          newarray.push(attribute[getterFunc](index))\n\n          if (morphAttr) {\n            for (let m = 0, ml = morphAttr.length; m < ml; m++) {\n              // @ts-ignore\n              newMorphArrays[m].push(morphAttr[m][getterFunc](index))\n            }\n          }\n        }\n      }\n\n      hashToIndex[hash] = nextIndex\n      newIndices.push(nextIndex)\n      nextIndex++\n    }\n  }\n\n  // Generate typed arrays from new attribute arrays and update\n  // the attributeBuffers\n  const result = geometry.clone()\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i]\n    const oldAttribute = geometry.getAttribute(name)\n    //@ts-expect-error  something to do with functions and constructors and new\n    const buffer = new (oldAttribute.array as TypedArray).constructor(attrArrays[name])\n    const attribute = new BufferAttribute(buffer, oldAttribute.itemSize, oldAttribute.normalized)\n\n    result.setAttribute(name, attribute)\n\n    // Update the attribute arrays\n    if (name in morphAttrsArrays) {\n      for (let j = 0; j < morphAttrsArrays[name].length; j++) {\n        const oldMorphAttribute = geometry.morphAttributes[name][j]\n        //@ts-expect-error something to do with functions and constructors and new\n        const buffer = new (oldMorphAttribute.array as TypedArray).constructor(morphAttrsArrays[name][j])\n        const morphAttribute = new BufferAttribute(buffer, oldMorphAttribute.itemSize, oldMorphAttribute.normalized)\n        result.morphAttributes[name][j] = morphAttribute\n      }\n    }\n  }\n\n  // indices\n\n  result.setIndex(newIndices)\n\n  return result\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} drawMode\n * @return {BufferGeometry}\n */\nexport function toTrianglesDrawMode(geometry: BufferGeometry, drawMode: number): BufferGeometry {\n  if (drawMode === TrianglesDrawMode) {\n    console.warn('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.')\n    return geometry\n  }\n\n  if (drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode) {\n    let index = geometry.getIndex()\n\n    // generate index if not present\n\n    if (index === null) {\n      const indices = []\n\n      const position = geometry.getAttribute('position')\n\n      if (position !== undefined) {\n        for (let i = 0; i < position.count; i++) {\n          indices.push(i)\n        }\n\n        geometry.setIndex(indices)\n        index = geometry.getIndex()\n      } else {\n        console.error(\n          'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.',\n        )\n        return geometry\n      }\n    }\n\n    //\n\n    const numberOfTriangles = (index as BufferAttribute).count - 2\n    const newIndices = []\n\n    if (index) {\n      if (drawMode === TriangleFanDrawMode) {\n        // gl.TRIANGLE_FAN\n\n        for (let i = 1; i <= numberOfTriangles; i++) {\n          newIndices.push(index.getX(0))\n          newIndices.push(index.getX(i))\n          newIndices.push(index.getX(i + 1))\n        }\n      } else {\n        // gl.TRIANGLE_STRIP\n\n        for (let i = 0; i < numberOfTriangles; i++) {\n          if (i % 2 === 0) {\n            newIndices.push(index.getX(i))\n            newIndices.push(index.getX(i + 1))\n            newIndices.push(index.getX(i + 2))\n          } else {\n            newIndices.push(index.getX(i + 2))\n            newIndices.push(index.getX(i + 1))\n            newIndices.push(index.getX(i))\n          }\n        }\n      }\n    }\n\n    if (newIndices.length / 3 !== numberOfTriangles) {\n      console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.')\n    }\n\n    // build final geometry\n\n    const newGeometry = geometry.clone()\n    newGeometry.setIndex(newIndices)\n    newGeometry.clearGroups()\n\n    return newGeometry\n  } else {\n    console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode)\n    return geometry\n  }\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n * Helpful for Raytracing or Decals.\n * @param {Mesh | Line | Points} object An instance of Mesh, Line or Points.\n * @return {Object} An Object with original position/normal attributes and morphed ones.\n */\nexport type ComputedMorphedAttribute = {\n  positionAttribute: BufferAttribute | InterleavedBufferAttribute\n  normalAttribute: BufferAttribute | InterleavedBufferAttribute\n  morphedPositionAttribute: Float32BufferAttribute\n  morphedNormalAttribute: Float32BufferAttribute\n}\n\nexport function computeMorphedAttributes(object: Mesh | Line | Points): ComputedMorphedAttribute | null {\n  if (object.geometry.isBufferGeometry !== true) {\n    console.error('THREE.BufferGeometryUtils: Geometry is not of type BufferGeometry.')\n    return null\n  }\n\n  const _vA = new Vector3()\n  const _vB = new Vector3()\n  const _vC = new Vector3()\n\n  const _tempA = new Vector3()\n  const _tempB = new Vector3()\n  const _tempC = new Vector3()\n\n  const _morphA = new Vector3()\n  const _morphB = new Vector3()\n  const _morphC = new Vector3()\n\n  function _calculateMorphedAttributeData(\n    object: Mesh | Line | Points,\n    material: Material,\n    attribute: BufferAttribute | InterleavedBufferAttribute,\n    morphAttribute: (BufferAttribute | InterleavedBufferAttribute)[],\n    morphTargetsRelative: boolean,\n    a: number,\n    b: number,\n    c: number,\n    modifiedAttributeArray: Float32Array,\n  ): void {\n    _vA.fromBufferAttribute(attribute, a)\n    _vB.fromBufferAttribute(attribute, b)\n    _vC.fromBufferAttribute(attribute, c)\n\n    const morphInfluences = object.morphTargetInfluences\n\n    if (\n      // @ts-ignore\n      material.morphTargets &&\n      morphAttribute &&\n      morphInfluences\n    ) {\n      _morphA.set(0, 0, 0)\n      _morphB.set(0, 0, 0)\n      _morphC.set(0, 0, 0)\n\n      for (let i = 0, il = morphAttribute.length; i < il; i++) {\n        const influence = morphInfluences[i]\n        const morph = morphAttribute[i]\n\n        if (influence === 0) continue\n\n        _tempA.fromBufferAttribute(morph, a)\n        _tempB.fromBufferAttribute(morph, b)\n        _tempC.fromBufferAttribute(morph, c)\n\n        if (morphTargetsRelative) {\n          _morphA.addScaledVector(_tempA, influence)\n          _morphB.addScaledVector(_tempB, influence)\n          _morphC.addScaledVector(_tempC, influence)\n        } else {\n          _morphA.addScaledVector(_tempA.sub(_vA), influence)\n          _morphB.addScaledVector(_tempB.sub(_vB), influence)\n          _morphC.addScaledVector(_tempC.sub(_vC), influence)\n        }\n      }\n\n      _vA.add(_morphA)\n      _vB.add(_morphB)\n      _vC.add(_morphC)\n    }\n\n    if ((object as SkinnedMesh).isSkinnedMesh) {\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(a, _vA)\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(b, _vB)\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(c, _vC)\n    }\n\n    modifiedAttributeArray[a * 3 + 0] = _vA.x\n    modifiedAttributeArray[a * 3 + 1] = _vA.y\n    modifiedAttributeArray[a * 3 + 2] = _vA.z\n    modifiedAttributeArray[b * 3 + 0] = _vB.x\n    modifiedAttributeArray[b * 3 + 1] = _vB.y\n    modifiedAttributeArray[b * 3 + 2] = _vB.z\n    modifiedAttributeArray[c * 3 + 0] = _vC.x\n    modifiedAttributeArray[c * 3 + 1] = _vC.y\n    modifiedAttributeArray[c * 3 + 2] = _vC.z\n  }\n\n  const geometry = object.geometry\n  const material = object.material\n\n  let a, b, c\n  const index = geometry.index\n  const positionAttribute = geometry.attributes.position\n  const morphPosition = geometry.morphAttributes.position\n  const morphTargetsRelative = geometry.morphTargetsRelative\n  const normalAttribute = geometry.attributes.normal\n  const morphNormal = geometry.morphAttributes.position\n\n  const groups = geometry.groups\n  const drawRange = geometry.drawRange\n  let i, j, il, jl\n  let group, groupMaterial\n  let start, end\n\n  const modifiedPosition = new Float32Array(positionAttribute.count * positionAttribute.itemSize)\n  const modifiedNormal = new Float32Array(normalAttribute.count * normalAttribute.itemSize)\n\n  if (index !== null) {\n    // indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i]\n        groupMaterial = material[group.materialIndex as number]\n\n        start = Math.max(group.start, drawRange.start)\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count)\n\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = index.getX(j)\n          b = index.getX(j + 1)\n          c = index.getX(j + 2)\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            positionAttribute,\n            morphPosition,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedPosition,\n          )\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            normalAttribute,\n            morphNormal,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedNormal,\n          )\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start)\n      end = Math.min(index.count, drawRange.start + drawRange.count)\n\n      for (i = start, il = end; i < il; i += 3) {\n        a = index.getX(i)\n        b = index.getX(i + 1)\n        c = index.getX(i + 2)\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          positionAttribute,\n          morphPosition,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedPosition,\n        )\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          normalAttribute,\n          morphNormal,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedNormal,\n        )\n      }\n    }\n  } else if (positionAttribute !== undefined) {\n    // non-indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i]\n        groupMaterial = material[group.materialIndex as number]\n\n        start = Math.max(group.start, drawRange.start)\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count)\n\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = j\n          b = j + 1\n          c = j + 2\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            positionAttribute,\n            morphPosition,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedPosition,\n          )\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            normalAttribute,\n            morphNormal,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedNormal,\n          )\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start)\n      end = Math.min(positionAttribute.count, drawRange.start + drawRange.count)\n\n      for (i = start, il = end; i < il; i += 3) {\n        a = i\n        b = i + 1\n        c = i + 2\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          positionAttribute,\n          morphPosition,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedPosition,\n        )\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          normalAttribute,\n          morphNormal,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedNormal,\n        )\n      }\n    }\n  }\n\n  const morphedPositionAttribute = new Float32BufferAttribute(modifiedPosition, 3)\n  const morphedNormalAttribute = new Float32BufferAttribute(modifiedNormal, 3)\n\n  return {\n    positionAttribute: positionAttribute,\n    normalAttribute: normalAttribute,\n    morphedPositionAttribute: morphedPositionAttribute,\n    morphedNormalAttribute: morphedNormalAttribute,\n  }\n}\n\n/**\n * Modifies the supplied geometry if it is non-indexed, otherwise creates a new,\n * non-indexed geometry. Returns the geometry with smooth normals everywhere except\n * faces that meet at an angle greater than the crease angle.\n *\n * Backwards compatible with code such as @react-three/drei's `<RoundedBox>`\n * which uses this method to operate on the original geometry.\n *\n * As of this writing, BufferGeometry.toNonIndexed() warns if the geometry is\n * non-indexed and returns `this`, i.e. the same geometry on which it was called:\n * `BufferGeometry is already non-indexed.`\n *\n * @param geometry\n * @param creaseAngle\n */\nexport function toCreasedNormals(geometry: BufferGeometry, creaseAngle = Math.PI / 3 /* 60 degrees */): BufferGeometry {\n  const creaseDot = Math.cos(creaseAngle)\n  const hashMultiplier = (1 + 1e-10) * 1e2\n\n  // reusable vectors\n  const verts = [new Vector3(), new Vector3(), new Vector3()]\n  const tempVec1 = new Vector3()\n  const tempVec2 = new Vector3()\n  const tempNorm = new Vector3()\n  const tempNorm2 = new Vector3()\n\n  // hashes a vector\n  function hashVertex(v: Vector3): string {\n    const x = ~~(v.x * hashMultiplier)\n    const y = ~~(v.y * hashMultiplier)\n    const z = ~~(v.z * hashMultiplier)\n    return `${x},${y},${z}`\n  }\n\n  const resultGeometry = geometry.index ? geometry.toNonIndexed() : geometry\n  const posAttr = resultGeometry.attributes.position\n  const vertexMap: { [key: string]: Vector3[] } = {}\n\n  // find all the normals shared by commonly located vertices\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    const i3 = 3 * i\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0)\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1)\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2)\n\n    tempVec1.subVectors(c, b)\n    tempVec2.subVectors(a, b)\n\n    // add the normal to the map for all vertices\n    const normal = new Vector3().crossVectors(tempVec1, tempVec2).normalize()\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n]\n      const hash = hashVertex(vert)\n      if (!(hash in vertexMap)) {\n        vertexMap[hash] = []\n      }\n\n      vertexMap[hash].push(normal)\n    }\n  }\n\n  // average normals from all vertices that share a common location if they are within the\n  // provided crease threshold\n  const normalArray = new Float32Array(posAttr.count * 3)\n  const normAttr = new BufferAttribute(normalArray, 3, false)\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    // get the face normal for this vertex\n    const i3 = 3 * i\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0)\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1)\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2)\n\n    tempVec1.subVectors(c, b)\n    tempVec2.subVectors(a, b)\n\n    tempNorm.crossVectors(tempVec1, tempVec2).normalize()\n\n    // average all normals that meet the threshold and set the normal value\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n]\n      const hash = hashVertex(vert)\n      const otherNormals = vertexMap[hash]\n      tempNorm2.set(0, 0, 0)\n\n      for (let k = 0, lk = otherNormals.length; k < lk; k++) {\n        const otherNorm = otherNormals[k]\n        if (tempNorm.dot(otherNorm) > creaseDot) {\n          tempNorm2.add(otherNorm)\n        }\n      }\n\n      tempNorm2.normalize()\n      normAttr.setXYZ(i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z)\n    }\n  }\n\n  resultGeometry.setAttribute('normal', normAttr)\n  return resultGeometry\n}\n"], "names": ["buffer", "object", "material", "morphTargetsRelative", "a", "b", "c", "i", "il"], "mappings": ";;;;;;;;;;;;;;AAyBa,MAAA,wBAAwB,CAAC,YAA8B,cAA+C;IACjH,MAAM,YAAY,UAAA,CAAW,CAAC,CAAA,CAAE,KAAA,KAAU;IAEpC,MAAA,iBAAiB,IAAI,IAAI,OAAO,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,UAAU,CAAC;IAC9D,MAAA,sBAAsB,IAAI,IAAI,OAAO,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,eAAe,CAAC;IAE9E,MAAM,aAAqF,CAAA;IAC3F,MAAM,kBAA4F,CAAA;IAE5F,MAAA,uBAAuB,UAAA,CAAW,CAAC,CAAA,CAAE,oBAAA;IAErC,MAAA,iBAAiB,uJAAI,iBAAA;IAE3B,IAAI,SAAS;IAEF,WAAA,OAAA,CAAQ,CAAC,MAAM,MAAM;QAC9B,IAAI,kBAAkB;QAIlB,IAAA,cAAA,CAAe,KAAK,KAAA,KAAU,IAAA,GAAO;YAC/B,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAIS,IAAA,IAAA,QAAQ,KAAK,UAAA,CAAY;YAChC,IAAI,CAAC,eAAe,GAAA,CAAI,IAAI,GAAG;gBACrB,QAAA,KAAA,CACN,uFACE,IACA,kEACA,OACA;gBAEG,OAAA;YACT;YAEI,IAAA,UAAA,CAAW,IAAI,CAAA,KAAM,KAAA,GAAW;gBACvB,UAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YACrB;YAEA,UAAA,CAAW,IAAI,CAAA,CAAE,IAAA,CAAK,KAAK,UAAA,CAAW,IAAI,CAAC;YAE3C;QACF;QAII,IAAA,oBAAoB,eAAe,IAAA,EAAM;YACnC,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAII,IAAA,yBAAyB,KAAK,oBAAA,EAAsB;YAC9C,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAES,IAAA,IAAA,QAAQ,KAAK,eAAA,CAAiB;YACrC,IAAI,CAAC,oBAAoB,GAAA,CAAI,IAAI,GAAG;gBAC1B,QAAA,KAAA,CACN,uFACE,IACA;gBAEG,OAAA;YACT;YAEI,IAAA,eAAA,CAAgB,IAAI,CAAA,KAAM,KAAA,GAA2B,eAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YAEjE,eAAA,CAAgB,IAAI,CAAA,CAAE,IAAA,CAAK,KAAK,eAAA,CAAgB,IAAI,CAAC;QACvD;QAIA,eAAe,QAAA,CAAS,cAAA,GAAiB,eAAe,QAAA,CAAS,cAAA,IAAkB,EAAA;QACnF,eAAe,QAAA,CAAS,cAAA,CAAe,IAAA,CAAK,KAAK,QAAQ;QAEzD,IAAI,WAAW;YACT,IAAA;YAEJ,IAAI,KAAK,KAAA,EAAO;gBACd,QAAQ,KAAK,KAAA,CAAM,KAAA;YACV,OAAA,IAAA,KAAK,UAAA,CAAW,QAAA,KAAa,KAAA,GAAW;gBACzC,QAAA,KAAK,UAAA,CAAW,QAAA,CAAS,KAAA;YAAA,OAC5B;gBACG,QAAA,KAAA,CACN,uFACE,IACA;gBAEG,OAAA;YACT;YAEe,eAAA,QAAA,CAAS,QAAQ,OAAO,CAAC;YAE9B,UAAA;QACZ;IAAA,CACD;IAID,IAAI,WAAW;QACb,IAAI,cAAc;QAClB,MAAM,cAAwB,CAAA,CAAA;QAEnB,WAAA,OAAA,CAAQ,CAAC,SAAS;YAC3B,MAAM,QAAQ,KAAK,KAAA;YAEnB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,KAAA,EAAO,EAAE,EAAG;gBACpC,YAAY,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,IAAI,WAAW;YAC9C;YAEe,eAAA,KAAK,UAAA,CAAW,QAAA,CAAS,KAAA;QAAA,CACzC;QAED,eAAe,QAAA,CAAS,WAAW;IACrC;IAIA,IAAA,IAAS,QAAQ,WAAY;QAC3B,MAAM,kBAAkB,sBAAsB,UAAA,CAAW,IAAI,CAAsB;QAEnF,IAAI,CAAC,iBAAiB;YACZ,QAAA,KAAA,CACN,0FAA0F,OAAO;YAE5F,OAAA;QACT;QAEe,eAAA,YAAA,CAAa,MAAM,eAAe;IACnD;IAIA,IAAA,IAAS,QAAQ,gBAAiB;QAChC,MAAM,kBAAkB,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,MAAA;QAEjD,IAAI,oBAAoB,GAAG;QAEZ,eAAA,eAAA,GAAkB,eAAe,eAAA,IAAmB,CAAA;QACpD,eAAA,eAAA,CAAgB,IAAI,CAAA,GAAI,EAAA;QAEvC,IAAA,IAAS,IAAI,GAAG,IAAI,iBAAiB,EAAE,EAAG;YACxC,MAAM,yBAAyB,CAAA,CAAA;YAEtB,IAAA,IAAA,IAAI,GAAG,IAAI,eAAA,CAAgB,IAAI,CAAA,CAAE,MAAA,EAAQ,EAAE,EAAG;gBACrD,uBAAuB,IAAA,CAAK,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAC;YACzD;YAEM,MAAA,uBAAuB,sBAAsB,sBAA2C;YAE9F,IAAI,CAAC,sBAAsB;gBACjB,QAAA,KAAA,CACN,0FACE,OACA;gBAEG,OAAA;YACT;YAEA,eAAe,eAAA,CAAgB,IAAI,CAAA,CAAE,IAAA,CAAK,oBAAoB;QAChE;IACF;IAEO,OAAA;AACT;AAMa,MAAA,wBAAwB,CAAC,eAAsE;IAC1G,IAAI,aAAiD,KAAA;IACrD,IAAI,WAA+B,KAAA;IACnC,IAAI,aAAkC,KAAA;IACtC,IAAI,cAAc;IAEP,WAAA,OAAA,CAAQ,CAAC,SAAS;QAC3B,IAAI,eAAe,KAAA,GAAW;YAC5B,aAAa,KAAK,KAAA,CAAM,WAAA;QAC1B;QACI,IAAA,eAAe,KAAK,KAAA,CAAM,WAAA,EAAa;YACjC,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,IAAI,aAAa,KAAA,GAAW,WAAW,KAAK,QAAA;QACxC,IAAA,aAAa,KAAK,QAAA,EAAU;YACtB,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,IAAI,eAAe,KAAA,GAAW,aAAa,KAAK,UAAA;QAC5C,IAAA,eAAe,KAAK,UAAA,EAAY;YAC1B,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,eAAe,KAAK,KAAA,CAAM,MAAA;IAAA,CAC3B;IAED,IAAI,cAAc,UAAU;QAEpB,MAAA,QAAQ,IAAI,WAAW,WAAW;QACxC,IAAI,SAAS;QAEF,WAAA,OAAA,CAAQ,CAAC,SAAS;YACrB,MAAA,GAAA,CAAI,KAAK,KAAA,EAAO,MAAM;YAC5B,UAAU,KAAK,KAAA,CAAM,MAAA;QAAA,CACtB;QAED,OAAO,IAAI,qKAAA,CAAgB,OAAO,UAAU,UAAU;IACxD;AACF;AAMa,MAAA,uBAAuB,CAAC,eAAuE;IAG1G,IAAI,aAAiD,KAAA;IACrD,IAAI,cAAc;IAClB,IAAI,SAAS;IAGJ,IAAA,IAAA,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAI,GAAG,EAAE,EAAG;QAC3C,MAAA,YAAY,UAAA,CAAW,CAAC,CAAA;QAE9B,IAAI,eAAe,KAAA,GAAW,aAAa,UAAU,KAAA,CAAM,WAAA;QACvD,IAAA,eAAe,UAAU,KAAA,CAAM,WAAA,EAAa;YAC9C,QAAQ,KAAA,CAAM,2DAA2D;YAClE,OAAA;QACT;QAEA,eAAe,UAAU,KAAA,CAAM,MAAA;QAC/B,UAAU,UAAU,QAAA;IACtB;IAIA,MAAM,oBAAoB,IAAI,uKAAA,CAAkB,IAAI,WAAW,WAAW,GAAG,MAAM;IACnF,IAAI,SAAS;IACb,MAAM,MAAM,CAAA,CAAA;IACZ,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAC/C,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAE/C,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC3C,MAAA,YAAY,UAAA,CAAW,CAAC,CAAA;QAC9B,MAAM,WAAW,UAAU,QAAA;QAC3B,MAAM,QAAQ,UAAU,KAAA;QACxB,MAAM,MAAM,uJAAI,6BAAA,CAA2B,mBAAmB,UAAU,QAAQ,UAAU,UAAU;QACpG,IAAI,IAAA,CAAK,GAAG;QAEF,UAAA;QAIV,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;gBACjC,MAAM,MAAM,wKAAA,EAAW,KAAK,OAAA,CAAQ,CAAC,CAAqC;gBAK1E,MAAM,iKAAM,aAAA,EAAW,WAAW,OAAA,CAAQ,CAAC,CAA0B;gBAKjE,IAAA,GAAG,IAAI,CAAC,CAAC;YACf;QACF;IACF;IAEO,OAAA;AACT;AAMO,SAAS,kBAAkB,QAAA,EAAkC;IAIlE,IAAI,MAAM;IACD,IAAA,IAAA,QAAQ,SAAS,UAAA,CAAY;QAC9B,MAAA,OAAO,SAAS,YAAA,CAAa,IAAI;QACvC,OAAO,KAAK,KAAA,GAAQ,KAAK,QAAA,GAAY,KAAK,KAAA,CAAqB,iBAAA;IACjE;IAEM,MAAA,UAAU,SAAS,QAAA;IACzB,OAAO,UAAU,QAAQ,KAAA,GAAQ,QAAQ,QAAA,GAAY,QAAQ,KAAA,CAAqB,iBAAA,GAAoB;IAC/F,OAAA;AACT;AAOgB,SAAA,cAAc,QAAA,EAA0B,YAAY,IAAA,EAAsB;IACxF,YAAY,KAAK,GAAA,CAAI,WAAW,OAAO,OAAO;IAI9C,MAAM,cAEF,CAAA;IACE,MAAA,UAAU,SAAS,QAAA;IACnB,MAAA,YAAY,SAAS,YAAA,CAAa,UAAU;IAClD,MAAM,cAAc,UAAU,QAAQ,KAAA,GAAQ,UAAU,KAAA;IAGxD,IAAI,YAAY;IAGhB,MAAM,iBAAiB,OAAO,IAAA,CAAK,SAAS,UAAU;IACtD,MAAM,aAEF,CAAA;IACJ,MAAM,mBAEF,CAAA;IACJ,MAAM,aAAa,CAAA,CAAA;IACnB,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAG/C,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;QAElB,UAAA,CAAA,IAAI,CAAA,GAAI,EAAA;QAEb,MAAA,YAAY,SAAS,eAAA,CAAgB,IAAI,CAAA;QAC/C,IAAI,WAAW;YACb,gBAAA,CAAiB,IAAI,CAAA,GAAI,IAAI,MAAM,UAAU,MAAM,EAAE,IAAA,CAAK,CAAC,EAAE,GAAA,CAAI,IAAM,CAAE,CAAA;QAC3E;IACF;IAGA,MAAM,eAAe,KAAK,KAAA,CAAM,IAAI,SAAS;IAC7C,MAAM,kBAAkB,KAAK,GAAA,CAAI,IAAI,YAAY;IACjD,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,MAAM,QAAQ,UAAU,QAAQ,IAAA,CAAK,CAAC,IAAI;QAG1C,IAAI,OAAO;QACX,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;YAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;YACvB,MAAA,YAAY,SAAS,YAAA,CAAa,IAAI;YAC5C,MAAM,WAAW,UAAU,QAAA;YAE3B,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;gBAGzB,QAAA,GAAG,CAAC,CAAA,CAAE,SAAA,CAAU,OAAA,CAAQ,CAAC,CAAC,CAAA,CAAE,KAAK,IAAI,eAAA,EAAA,CAAA,CAAA;YAC/C;QACF;QAIA,IAAI,QAAQ,aAAa;YACZ,WAAA,IAAA,CAAK,WAAA,CAAY,IAAI,CAAC;QAAA,OAC5B;YAEL,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;gBAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;gBACvB,MAAA,YAAY,SAAS,YAAA,CAAa,IAAI;gBACtC,MAAA,YAAY,SAAS,eAAA,CAAgB,IAAI,CAAA;gBAC/C,MAAM,WAAW,UAAU,QAAA;gBACrB,MAAA,WAAW,UAAA,CAAW,IAAI,CAAA;gBAC1B,MAAA,iBAAiB,gBAAA,CAAiB,IAAI,CAAA;gBAE5C,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;oBAC3B,MAAA,aAAa,OAAA,CAAQ,CAAC,CAAA;oBAE5B,SAAS,IAAA,CAAK,SAAA,CAAU,UAAU,CAAA,CAAE,KAAK,CAAC;oBAE1C,IAAI,WAAW;wBACb,IAAA,IAAS,IAAI,GAAG,KAAK,UAAU,MAAA,EAAQ,IAAI,IAAI,IAAK;4BAEnC,cAAA,CAAA,CAAC,CAAA,CAAE,IAAA,CAAK,SAAA,CAAU,CAAC,CAAA,CAAE,UAAU,CAAA,CAAE,KAAK,CAAC;wBACxD;oBACF;gBACF;YACF;YAEA,WAAA,CAAY,IAAI,CAAA,GAAI;YACpB,WAAW,IAAA,CAAK,SAAS;YACzB;QACF;IACF;IAIM,MAAA,SAAS,SAAS,KAAA;IACxB,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;QACvB,MAAA,eAAe,SAAS,YAAA,CAAa,IAAI;QAE/C,MAAM,SAAS,IAAK,aAAa,KAAA,CAAqB,WAAA,CAAY,UAAA,CAAW,IAAI,CAAC;QAClF,MAAM,YAAY,uJAAI,kBAAA,CAAgB,QAAQ,aAAa,QAAA,EAAU,aAAa,UAAU;QAErF,OAAA,YAAA,CAAa,MAAM,SAAS;QAGnC,IAAI,QAAQ,kBAAkB;YAC5B,IAAA,IAAS,IAAI,GAAG,IAAI,gBAAA,CAAiB,IAAI,CAAA,CAAE,MAAA,EAAQ,IAAK;gBACtD,MAAM,oBAAoB,SAAS,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA;gBAEpDA,MAAAA,UAAS,IAAK,kBAAkB,KAAA,CAAqB,WAAA,CAAY,gBAAA,CAAiB,IAAI,CAAA,CAAE,CAAC,CAAC;gBAChG,MAAM,iBAAiB,uJAAI,kBAAA,CAAgBA,SAAQ,kBAAkB,QAAA,EAAU,kBAAkB,UAAU;gBAC3G,OAAO,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI;YACpC;QACF;IACF;IAIA,OAAO,QAAA,CAAS,UAAU;IAEnB,OAAA;AACT;AAOgB,SAAA,oBAAoB,QAAA,EAA0B,QAAA,EAAkC;IAC9F,IAAI,gKAAa,oBAAA,EAAmB;QAClC,QAAQ,IAAA,CAAK,yFAAyF;QAC/F,OAAA;IACT;IAEI,IAAA,gKAAa,sBAAA,IAAuB,gKAAa,wBAAA,EAAuB;QACtE,IAAA,QAAQ,SAAS,QAAA;QAIrB,IAAI,UAAU,MAAM;YAClB,MAAM,UAAU,CAAA,CAAA;YAEV,MAAA,WAAW,SAAS,YAAA,CAAa,UAAU;YAEjD,IAAI,aAAa,KAAA,GAAW;gBAC1B,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,KAAA,EAAO,IAAK;oBACvC,QAAQ,IAAA,CAAK,CAAC;gBAChB;gBAEA,SAAS,QAAA,CAAS,OAAO;gBACzB,QAAQ,SAAS,QAAA;YAAS,OACrB;gBACG,QAAA,KAAA,CACN;gBAEK,OAAA;YACT;QACF;QAIM,MAAA,oBAAqB,MAA0B,KAAA,GAAQ;QAC7D,MAAM,aAAa,CAAA,CAAA;QAEnB,IAAI,OAAO;YACT,IAAI,gKAAa,sBAAA,EAAqB;gBAGpC,IAAA,IAAS,IAAI,GAAG,KAAK,mBAAmB,IAAK;oBAC3C,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;gBACnC;YAAA,OACK;gBAGL,IAAA,IAAS,IAAI,GAAG,IAAI,mBAAmB,IAAK;oBACtC,IAAA,IAAI,MAAM,GAAG;wBACf,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;wBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;oBAAA,OAC5B;wBACL,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC/B;gBACF;YACF;QACF;QAEI,IAAA,WAAW,MAAA,GAAS,MAAM,mBAAmB;YAC/C,QAAQ,KAAA,CAAM,kGAAkG;QAClH;QAIM,MAAA,cAAc,SAAS,KAAA;QAC7B,YAAY,QAAA,CAAS,UAAU;QAC/B,YAAY,WAAA,CAAY;QAEjB,OAAA;IAAA,OACF;QACG,QAAA,KAAA,CAAM,uEAAuE,QAAQ;QACtF,OAAA;IACT;AACF;AAeO,SAAS,yBAAyB,MAAA,EAA+D;IAClG,IAAA,OAAO,QAAA,CAAS,gBAAA,KAAqB,MAAM;QAC7C,QAAQ,KAAA,CAAM,oEAAoE;QAC3E,OAAA;IACT;IAEM,MAAA,MAAM,uJAAI,UAAA;IACV,MAAA,MAAM,uJAAI,UAAA;IACV,MAAA,MAAM,uJAAI,UAAA;IAEV,MAAA,SAAS,uJAAI,UAAA;IACb,MAAA,SAAS,uJAAI,UAAA;IACb,MAAA,SAAS,uJAAI,UAAA;IAEb,MAAA,UAAU,uJAAI,UAAA;IACd,MAAA,UAAU,uJAAI,UAAA;IACd,MAAA,UAAU,uJAAI,UAAA;IAEX,SAAA,+BACPC,OAAAA,EACAC,SAAAA,EACA,SAAA,EACA,cAAA,EACAC,qBAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACA,sBAAA,EACM;QACF,IAAA,mBAAA,CAAoB,WAAWF,EAAC;QAChC,IAAA,mBAAA,CAAoB,WAAWC,EAAC;QAChC,IAAA,mBAAA,CAAoB,WAAWC,EAAC;QAEpC,MAAM,kBAAkBL,QAAO,qBAAA;QAE/B,IAAA,aAAA;QAEEC,UAAS,YAAA,IACT,kBACA,iBACA;YACQ,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YACX,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YACX,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YAEnB,IAAA,IAASK,KAAI,GAAGC,MAAK,eAAe,MAAA,EAAQD,KAAIC,KAAID,KAAK;gBACjD,MAAA,YAAY,eAAA,CAAgBA,EAAC,CAAA;gBAC7B,MAAA,QAAQ,cAAA,CAAeA,EAAC,CAAA;gBAE9B,IAAI,cAAc,GAAG;gBAEd,OAAA,mBAAA,CAAoB,OAAOH,EAAC;gBAC5B,OAAA,mBAAA,CAAoB,OAAOC,EAAC;gBAC5B,OAAA,mBAAA,CAAoB,OAAOC,EAAC;gBAEnC,IAAIH,uBAAsB;oBAChB,QAAA,eAAA,CAAgB,QAAQ,SAAS;oBACjC,QAAA,eAAA,CAAgB,QAAQ,SAAS;oBACjC,QAAA,eAAA,CAAgB,QAAQ,SAAS;gBAAA,OACpC;oBACL,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;oBAClD,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;oBAClD,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;gBACpD;YACF;YAEA,IAAI,GAAA,CAAI,OAAO;YACf,IAAI,GAAA,CAAI,OAAO;YACf,IAAI,GAAA,CAAI,OAAO;QACjB;QAEA,IAAKF,QAAuB,aAAA,EAAe;YAEzCA,QAAO,aAAA,CAAcG,IAAG,GAAG;YAE3BH,QAAO,aAAA,CAAcI,IAAG,GAAG;YAE3BJ,QAAO,aAAA,CAAcK,IAAG,GAAG;QAC7B;QAEA,sBAAA,CAAuBF,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBC,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBC,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;IAC1C;IAEA,MAAM,WAAW,OAAO,QAAA;IACxB,MAAM,WAAW,OAAO,QAAA;IAExB,IAAI,GAAG,GAAG;IACV,MAAM,QAAQ,SAAS,KAAA;IACjB,MAAA,oBAAoB,SAAS,UAAA,CAAW,QAAA;IACxC,MAAA,gBAAgB,SAAS,eAAA,CAAgB,QAAA;IAC/C,MAAM,uBAAuB,SAAS,oBAAA;IAChC,MAAA,kBAAkB,SAAS,UAAA,CAAW,MAAA;IACtC,MAAA,cAAc,SAAS,eAAA,CAAgB,QAAA;IAE7C,MAAM,SAAS,SAAS,MAAA;IACxB,MAAM,YAAY,SAAS,SAAA;IACvB,IAAA,GAAG,GAAG,IAAI;IACd,IAAI,OAAO;IACX,IAAI,OAAO;IAEX,MAAM,mBAAmB,IAAI,aAAa,kBAAkB,KAAA,GAAQ,kBAAkB,QAAQ;IAC9F,MAAM,iBAAiB,IAAI,aAAa,gBAAgB,KAAA,GAAQ,gBAAgB,QAAQ;IAExF,IAAI,UAAU,MAAM;QAGd,IAAA,MAAM,OAAA,CAAQ,QAAQ,GAAG;YAC3B,IAAK,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC3C,QAAQ,MAAA,CAAO,CAAC,CAAA;gBACA,gBAAA,QAAA,CAAS,MAAM,aAAuB,CAAA;gBAEtD,QAAQ,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAK;gBACvC,MAAA,KAAK,GAAA,CAAI,MAAM,KAAA,GAAQ,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;gBAE3E,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;oBACpC,IAAA,MAAM,IAAA,CAAK,CAAC;oBACZ,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;oBAChB,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;oBAEpB,+BACE,QACA,eACA,mBACA,eACA,sBACA,GACA,GACA,GACA;oBAGF,+BACE,QACA,eACA,iBACA,aACA,sBACA,GACA,GACA,GACA;gBAEJ;YACF;QAAA,OACK;YACL,QAAQ,KAAK,GAAA,CAAI,GAAG,UAAU,KAAK;YACnC,MAAM,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;YAE7D,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;gBACpC,IAAA,MAAM,IAAA,CAAK,CAAC;gBACZ,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;gBAChB,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;gBAEpB,+BACE,QACA,UACA,mBACA,eACA,sBACA,GACA,GACA,GACA;gBAGF,+BACE,QACA,UACA,iBACA,aACA,sBACA,GACA,GACA,GACA;YAEJ;QACF;IAAA,OAAA,IACS,sBAAsB,KAAA,GAAW;QAGtC,IAAA,MAAM,OAAA,CAAQ,QAAQ,GAAG;YAC3B,IAAK,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC3C,QAAQ,MAAA,CAAO,CAAC,CAAA;gBACA,gBAAA,QAAA,CAAS,MAAM,aAAuB,CAAA;gBAEtD,QAAQ,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAK;gBACvC,MAAA,KAAK,GAAA,CAAI,MAAM,KAAA,GAAQ,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;gBAE3E,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;oBACpC,IAAA;oBACJ,IAAI,IAAI;oBACR,IAAI,IAAI;oBAER,+BACE,QACA,eACA,mBACA,eACA,sBACA,GACA,GACA,GACA;oBAGF,+BACE,QACA,eACA,iBACA,aACA,sBACA,GACA,GACA,GACA;gBAEJ;YACF;QAAA,OACK;YACL,QAAQ,KAAK,GAAA,CAAI,GAAG,UAAU,KAAK;YACnC,MAAM,KAAK,GAAA,CAAI,kBAAkB,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;YAEzE,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;gBACpC,IAAA;gBACJ,IAAI,IAAI;gBACR,IAAI,IAAI;gBAER,+BACE,QACA,UACA,mBACA,eACA,sBACA,GACA,GACA,GACA;gBAGF,+BACE,QACA,UACA,iBACA,aACA,sBACA,GACA,GACA,GACA;YAEJ;QACF;IACF;IAEA,MAAM,2BAA2B,uJAAI,yBAAA,CAAuB,kBAAkB,CAAC;IAC/E,MAAM,yBAAyB,IAAI,4KAAA,CAAuB,gBAAgB,CAAC;IAEpE,OAAA;QACL;QACA;QACA;QACA;IAAA;AAEJ;AAiBO,SAAS,iBAAiB,QAAA,EAA0B,cAAc,KAAK,EAAA,GAAK,CAAA,EAAoC;IAC/G,MAAA,YAAY,KAAK,GAAA,CAAI,WAAW;IAChC,MAAA,iBAAA,CAAkB,IAAI,KAAA,IAAS;IAG/B,MAAA,QAAQ;QAAC,IAAI,6JAAA,CAAQ;QAAG,uJAAI,UAAA;QAAW,uJAAI,UAAA,CAAA,CAAS;KAAA;IACpD,MAAA,WAAW,uJAAI,UAAA;IACf,MAAA,WAAW,uJAAI,UAAA;IACf,MAAA,WAAW,IAAI,6JAAA;IACf,MAAA,YAAY,uJAAI,UAAA;IAGtB,SAAS,WAAW,CAAA,EAAoB;QACtC,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACnB,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACnB,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACZ,OAAA,GAAG,EAAA,CAAA,EAAK,EAAA,CAAA,EAAK,GAAA;IACtB;IAEA,MAAM,iBAAiB,SAAS,KAAA,GAAQ,SAAS,YAAA,CAAiB,IAAA;IAC5D,MAAA,UAAU,eAAe,UAAA,CAAW,QAAA;IAC1C,MAAM,YAA0C,CAAA;IAGvC,IAAA,IAAA,IAAI,GAAG,IAAI,QAAQ,KAAA,GAAQ,GAAG,IAAI,GAAG,IAAK;QACjD,MAAM,KAAK,IAAI;QACf,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QAE7C,SAAA,UAAA,CAAW,GAAG,CAAC;QACf,SAAA,UAAA,CAAW,GAAG,CAAC;QAGlB,MAAA,SAAS,uJAAI,UAAA,GAAU,YAAA,CAAa,UAAU,QAAQ,EAAE,SAAA;QAC9D,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAK;YACpB,MAAA,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,MAAA,OAAO,WAAW,IAAI;YACxB,IAAA,CAAA,CAAE,QAAQ,SAAA,GAAY;gBACd,SAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YACpB;YAEU,SAAA,CAAA,IAAI,CAAA,CAAE,IAAA,CAAK,MAAM;QAC7B;IACF;IAIA,MAAM,cAAc,IAAI,aAAa,QAAQ,KAAA,GAAQ,CAAC;IACtD,MAAM,WAAW,IAAI,qKAAA,CAAgB,aAAa,GAAG,KAAK;IACjD,IAAA,IAAA,IAAI,GAAG,IAAI,QAAQ,KAAA,GAAQ,GAAG,IAAI,GAAG,IAAK;QAEjD,MAAM,KAAK,IAAI;QACf,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QAE7C,SAAA,UAAA,CAAW,GAAG,CAAC;QACf,SAAA,UAAA,CAAW,GAAG,CAAC;QAExB,SAAS,YAAA,CAAa,UAAU,QAAQ,EAAE,SAAA,CAAU;QAGpD,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAK;YACpB,MAAA,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,MAAA,OAAO,WAAW,IAAI;YACtB,MAAA,eAAe,SAAA,CAAU,IAAI,CAAA;YACzB,UAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YAErB,IAAA,IAAS,IAAI,GAAG,KAAK,aAAa,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC/C,MAAA,YAAY,YAAA,CAAa,CAAC,CAAA;gBAChC,IAAI,SAAS,GAAA,CAAI,SAAS,IAAI,WAAW;oBACvC,UAAU,GAAA,CAAI,SAAS;gBACzB;YACF;YAEA,UAAU,SAAA,CAAU;YACX,SAAA,MAAA,CAAO,KAAK,GAAG,UAAU,CAAA,EAAG,UAAU,CAAA,EAAG,UAAU,CAAC;QAC/D;IACF;IAEe,eAAA,YAAA,CAAa,UAAU,QAAQ;IACvC,OAAA;AACT", "ignoreList": [0], "debugId": null}}]}
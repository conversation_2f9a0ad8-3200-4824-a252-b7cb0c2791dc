{"name": "portfolio-3d", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "analyze": "ANALYZE=true next build", "export": "next build && next export", "docker:build": "docker build -t 3d-portfolio .", "docker:run": "docker run -p 3000:3000 3d-portfolio", "docker:compose": "docker-compose up -d", "docker:down": "docker-compose down", "prepare": "husky install"}, "dependencies": {"@react-three/drei": "^10.4.2", "@react-three/fiber": "^9.1.4", "@react-three/postprocessing": "^3.0.4", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "gsap": "^3.13.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "three": "^0.178.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.178.0", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}
'use client';

import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ProjectScene } from '@/components/3d/SceneManager';
import { ProjectGrid } from '@/components/3d/ProjectCard';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/hooks/useTheme';
import { useAdaptiveQuality } from '@/hooks/usePerformance';
import { cn } from '@/lib/utils';
import { Project, ProjectCategory } from '@/types';

interface ProjectsSectionProps {
  className?: string;
}

// Sample projects data
const sampleProjects: Project[] = [
  {
    id: '1',
    title: '3D Portfolio Website',
    description: 'An immersive 3D portfolio website built with React, Three.js, and Next.js featuring interactive microchip-themed design.',
    longDescription: 'This project showcases advanced 3D web development techniques using React Three Fiber and Three.js. Features include interactive camera controls, performance optimization, and responsive design.',
    technologies: ['React', 'Three.js', 'Next.js', 'TypeScript', 'Tailwind CSS'],
    imageUrl: '/images/project-1.jpg',
    demoUrl: 'https://demo.example.com',
    githubUrl: 'https://github.com/example/project',
    featured: true,
    category: 'web-development',
    completedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    title: 'AI-Powered Dashboard',
    description: 'A modern dashboard application with AI-driven analytics and real-time data visualization.',
    technologies: ['React', 'Python', 'TensorFlow', 'D3.js', 'PostgreSQL'],
    featured: true,
    category: 'ai-ml',
    completedAt: new Date('2023-11-20')
  },
  {
    id: '3',
    title: 'Mobile E-commerce App',
    description: 'Cross-platform mobile application for e-commerce with advanced features and smooth animations.',
    technologies: ['React Native', 'Node.js', 'MongoDB', 'Stripe', 'Firebase'],
    featured: false,
    category: 'mobile-development',
    completedAt: new Date('2023-09-10')
  },
  {
    id: '4',
    title: 'Microservices Architecture',
    description: 'Scalable backend system using microservices architecture with Docker and Kubernetes.',
    technologies: ['Node.js', 'Docker', 'Kubernetes', 'Redis', 'PostgreSQL'],
    featured: false,
    category: 'backend',
    completedAt: new Date('2023-07-05')
  },
  {
    id: '5',
    title: 'Real-time Chat Platform',
    description: 'Full-stack real-time chat application with video calling and file sharing capabilities.',
    technologies: ['React', 'Socket.io', 'WebRTC', 'Node.js', 'MongoDB'],
    featured: true,
    category: 'fullstack',
    completedAt: new Date('2023-05-15')
  },
  {
    id: '6',
    title: 'DevOps Pipeline',
    description: 'Automated CI/CD pipeline with monitoring and deployment automation for cloud infrastructure.',
    technologies: ['Jenkins', 'Docker', 'AWS', 'Terraform', 'Prometheus'],
    featured: false,
    category: 'devops',
    completedAt: new Date('2023-03-20')
  }
];

const categories: { id: ProjectCategory; label: string; icon: string }[] = [
  { id: 'web-development', label: 'Web Dev', icon: '🌐' },
  { id: 'mobile-development', label: 'Mobile', icon: '📱' },
  { id: 'backend', label: 'Backend', icon: '⚙️' },
  { id: 'fullstack', label: 'Full Stack', icon: '🔧' },
  { id: 'ai-ml', label: 'AI/ML', icon: '🤖' },
  { id: 'devops', label: 'DevOps', icon: '🚀' },
  { id: 'other', label: 'Other', icon: '💡' }
];

const technologies = [
  'React', 'Next.js', 'Three.js', 'TypeScript', 'Node.js', 
  'Python', 'MongoDB', 'PostgreSQL', 'Docker', 'AWS'
];

export function ProjectsSection({ className }: ProjectsSectionProps) {
  const { themeConfig } = useTheme();
  const qualitySettings = useAdaptiveQuality();
  const [selectedCategory, setSelectedCategory] = useState<ProjectCategory | 'all'>('all');
  const [selectedTech, setSelectedTech] = useState<string | 'all'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'featured'>('grid');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  // Filter projects based on selected filters
  const filteredProjects = useMemo(() => {
    let filtered = sampleProjects;

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    if (selectedTech !== 'all') {
      filtered = filtered.filter(project => 
        project.technologies.some(tech => 
          tech.toLowerCase().includes(selectedTech.toLowerCase())
        )
      );
    }

    if (viewMode === 'featured') {
      filtered = filtered.filter(project => project.featured);
    }

    return filtered.sort((a, b) => b.completedAt.getTime() - a.completedAt.getTime());
  }, [selectedCategory, selectedTech, viewMode]);

  const handleProjectClick = (project: Project) => {
    setSelectedProject(selectedProject?.id === project.id ? null : project);
  };

  const clearFilters = () => {
    setSelectedCategory('all');
    setSelectedTech('all');
    setViewMode('grid');
  };

  return (
    <section id="projects" className={cn(
      "min-h-screen bg-gradient-to-br from-bg-tertiary via-bg-primary to-bg-secondary",
      "relative overflow-hidden py-20",
      className
    )}>
      {/* Background Pattern */}
      <div className="absolute inset-0 circuit-bg opacity-5" />
      
      <div className="relative z-10 max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-secondary mb-6">
            My Projects
          </h2>
          <p className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto">
            Explore my portfolio of interactive applications and innovative digital solutions
          </p>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-12 space-y-6"
        >
          {/* View Mode Toggle */}
          <div className="flex justify-center">
            <div className="flex bg-bg-primary/50 rounded-lg p-1 chip-border">
              <Button
                variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                🔲 All Projects
              </Button>
              <Button
                variant={viewMode === 'featured' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('featured')}
              >
                ⭐ Featured
              </Button>
            </div>
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap justify-center gap-2">
            <Button
              variant={selectedCategory === 'all' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setSelectedCategory('all')}
            >
              🌟 All Categories
            </Button>
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'secondary' : 'ghost'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                <span className="mr-1">{category.icon}</span>
                {category.label}
              </Button>
            ))}
          </div>

          {/* Technology Filters */}
          <div className="flex flex-wrap justify-center gap-2">
            <Button
              variant={selectedTech === 'all' ? 'accent' : 'ghost'}
              size="sm"
              onClick={() => setSelectedTech('all')}
            >
              🛠️ All Tech
            </Button>
            {technologies.map((tech) => (
              <Button
                key={tech}
                variant={selectedTech === tech ? 'accent' : 'ghost'}
                size="sm"
                onClick={() => setSelectedTech(tech)}
                className="text-xs"
              >
                {tech}
              </Button>
            ))}
          </div>

          {/* Clear Filters */}
          {(selectedCategory !== 'all' || selectedTech !== 'all' || viewMode !== 'grid') && (
            <div className="flex justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
              >
                ✨ Clear Filters
              </Button>
            </div>
          )}
        </motion.div>

        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center mb-8"
        >
          <p className="text-text-secondary">
            Showing {filteredProjects.length} of {sampleProjects.length} projects
          </p>
        </motion.div>

        {/* 3D Projects Display */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="relative h-96 mb-12"
        >
          <ProjectScene
            enableControls={true}
            enableEnvironment={false}
            shadows={qualitySettings.shadows}
            antialias={qualitySettings.antialias}
            lighting="studio"
          >
            <AnimatePresence mode="wait">
              <ProjectGrid
                key={`${selectedCategory}-${selectedTech}-${viewMode}`}
                projects={filteredProjects}
                position={[0, 0, 0]}
                spacing={4}
                animated={true}
                onProjectClick={handleProjectClick}
              />
            </AnimatePresence>
          </ProjectScene>
        </motion.div>

        {/* Project Details Modal */}
        <AnimatePresence>
          {selectedProject && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
              onClick={() => setSelectedProject(null)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-bg-primary/95 backdrop-blur-md rounded-lg p-6 max-w-2xl w-full chip-border"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-2xl font-bold text-primary">{selectedProject.title}</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedProject(null)}
                  >
                    ✕
                  </Button>
                </div>
                
                <p className="text-text-secondary mb-4">
                  {selectedProject.longDescription || selectedProject.description}
                </p>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="text-lg font-bold text-secondary mb-2">Technologies</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedProject.technologies.map((tech) => (
                        <span
                          key={tech}
                          className="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm chip-border"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex gap-4">
                    {selectedProject.demoUrl && (
                      <Button
                        variant="primary"
                        size="md"
                        onClick={() => window.open(selectedProject.demoUrl, '_blank')}
                      >
                        🚀 Live Demo
                      </Button>
                    )}
                    {selectedProject.githubUrl && (
                      <Button
                        variant="secondary"
                        size="md"
                        onClick={() => window.open(selectedProject.githubUrl, '_blank')}
                      >
                        💻 View Code
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* No Results */}
        {filteredProjects.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold text-text-secondary mb-2">No projects found</h3>
            <p className="text-text-muted mb-6">Try adjusting your filters to see more projects</p>
            <Button variant="primary" onClick={clearFilters}>
              Clear All Filters
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  );
}
